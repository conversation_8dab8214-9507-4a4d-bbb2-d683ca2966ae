import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const headingVariants = cva("font-heading scroll-m-20", {
  variants: {
    variant: {
      default: "",
      primary: "text-primary",
      secondary: "text-secondary",
      muted: "text-muted-foreground",
      accent: "text-accent",
    },
    size: {
      h1: "text-4xl font-extrabold tracking-tight lg:text-5xl",
      h2: "text-3xl font-semibold tracking-tight",
      h3: "text-2xl font-semibold tracking-tight",
      h4: "text-xl font-semibold tracking-tight",
      h5: "text-lg font-semibold tracking-tight",
      h6: "text-base font-semibold tracking-tight",
      display: "text-5xl font-bold tracking-tighter lg:text-6xl",
      hero: "text-6xl font-bold tracking-tighter lg:text-7xl",
    },
    align: {
      left: "text-left",
      center: "text-center",
      right: "text-right",
    },
    weight: {
      normal: "font-normal",
      medium: "font-medium",
      semibold: "font-semibold",
      bold: "font-bold",
      extrabold: "font-extrabold",
    },
    transform: {
      normal: "",
      uppercase: "uppercase",
      lowercase: "lowercase",
      capitalize: "capitalize",
    },
    display: {
      block: "block",
      inline: "inline",
      "inline-block": "inline-block",
      flex: "flex",
      "inline-flex": "inline-flex",
      grid: "grid",
      hidden: "hidden",
    },
  },
  defaultVariants: {
    variant: "default",
    align: "left",
    transform: "normal",
  },
})

export interface HeadingProps
  extends React.HTMLAttributes<HTMLHeadingElement>,
    VariantProps<typeof headingVariants> {
  as?: "h1" | "h2" | "h3" | "h4" | "h5" | "h6"
  truncate?: boolean
  title?: string
  description?: string
}

const Heading = React.forwardRef<HTMLHeadingElement, HeadingProps>(
  (
    {
      className,
      variant,
      size,
      align,
      weight,
      transform,
      display,
      as,
      truncate = false,
      title,
      description,
      ...props
    },
    ref
  ) => {
    const Component =
      as ||
      (size && ["h1", "h2", "h3", "h4", "h5", "h6"].includes(size)
        ? size
        : "h2")
    const effectiveSize: VariantProps<typeof headingVariants>["size"] =
      size || as || "h2"

    return (
      <div className="flex flex-col gap-1">
        <Component
          ref={ref}
          className={cn(
            headingVariants({
              variant,
              size: effectiveSize,
              align,
              weight,
              transform,
              display,
              className,
            }),
            truncate && "truncate"
          )}
          {...props}
        />
        {title && (
          <p className="text-sm font-light text-muted-foreground">{title}</p>
        )}
        {description && (
          <p className="text-sm font-light text-muted-foreground">
            {description}
          </p>
        )}
      </div>
    )
  }
)

Heading.displayName = "Heading"
export { Heading, headingVariants }