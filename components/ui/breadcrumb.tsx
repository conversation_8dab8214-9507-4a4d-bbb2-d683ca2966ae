'use client'

import * as React from 'react'
import Link from 'next/link'
import { ChevronRight, Home } from 'lucide-react'
import { cn } from '@/lib/utils'

// New component API
interface BreadcrumbProps extends React.ComponentPropsWithoutRef<'nav'> {
  children?: React.ReactNode
  className?: string
  items?: {
    label: string
    href?: string
  }[]
}

interface BreadcrumbListProps extends React.ComponentPropsWithoutRef<'ol'> {
  className?: string
}

interface BreadcrumbItemProps extends React.ComponentPropsWithoutRef<'li'> {
  className?: string
}

interface BreadcrumbLinkProps extends React.ComponentPropsWithoutRef<'a'> {
  asChild?: boolean
  className?: string
  href: string
}

interface BreadcrumbPageProps extends React.ComponentPropsWithoutRef<'span'> {
  className?: string
}

interface BreadcrumbSeparatorProps extends React.ComponentPropsWithoutRef<'li'> {
  className?: string
}

// Main Breadcrumb component
export function Breadcrumb({ 
  children, 
  className, 
  items, 
  ...props 
}: BreadcrumbProps) {
  return (
    <nav 
      className={cn("flex items-center space-x-2 text-sm text-muted-foreground mb-6", className)} 
      {...props}
    >
      {children || (
        items && (
          <ol className="flex items-center space-x-2">
            <li>
              <Link 
                href="/" 
                className="hover:text-foreground transition-colors flex items-center"
              >
                <Home className="h-4 w-4" />
              </Link>
            </li>
            {items.map((item, index) => (
              <li key={item.label} className="flex items-center">
                <ChevronRight className="h-4 w-4 mx-1" />
                {item.href && index < items.length - 1 ? (
                  <Link 
                    href={item.href}
                    className="hover:text-foreground transition-colors"
                  >
                    {item.label}
                  </Link>
                ) : (
                  <span className="text-foreground">{item.label}</span>
                )}
              </li>
            ))}
          </ol>
        )
      )}
    </nav>
  )
}

// BreadcrumbList component
export function BreadcrumbList({
  className,
  children,
  ...props
}: BreadcrumbListProps) {
  return (
    <ol
      className={cn("flex items-center space-x-2", className)}
      {...props}
    >
      {children}
    </ol>
  )
}

// BreadcrumbItem component
export function BreadcrumbItem({
  className,
  children,
  ...props
}: BreadcrumbItemProps) {
  return (
    <li
      className={cn("flex items-center", className)}
      {...props}
    >
      {children}
    </li>
  )
}

// BreadcrumbLink component
export function BreadcrumbLink({
  className,
  href,
  children,
  ...props
}: BreadcrumbLinkProps) {
  return (
    <Link
      href={href}
      className={cn("hover:text-foreground transition-colors", className)}
      {...props}
    >
      {children}
    </Link>
  )
}

// BreadcrumbPage component
export function BreadcrumbPage({
  className,
  children,
  ...props
}: BreadcrumbPageProps) {
  return (
    <span
      className={cn("text-foreground", className)}
      aria-current="page"
      {...props}
    >
      {children}
    </span>
  )
}

// BreadcrumbSeparator component
export function BreadcrumbSeparator({
  className,
  children,
  ...props
}: BreadcrumbSeparatorProps) {
  return (
    <li
      className={cn("flex items-center", className)}
      {...props}
    >
      {children || <ChevronRight className="h-4 w-4 mx-1" />}
    </li>
  )
}
