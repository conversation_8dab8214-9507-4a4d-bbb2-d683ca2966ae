"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { 
  Home, 
  ShoppingBag, 
  Heart, 
  User, 
  Settings, 
  Search,
  Tag,
  Truck,
  HelpCircle,
  LogOut,
  ChevronRight
} from "lucide-react"
import { cn } from "@/lib/utils"
import {
  SidebarProvider,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useAuth } from "@/lib/ecommerce/hooks/use-auth"

// Navigation items - Updated to match Header component
const navigationItems = [
  {
    title: "Home",
    url: "/",
    icon: Home,
  },
  {
    title: "Shop",
    url: "/products",
    icon: ShoppingBag,
  },
  {
    title: "Categories",
    icon: Tag,
    items: [
      {
        title: "New",
        url: "/products?category=new-arrivals",
      },
      {
        title: "Girls",
        url: "/products?category=girls",
      },
      {
        title: "Boys",
        url: "/products?category=boys",
      },
      {
        title: "Gender Neutral",
        url: "/products?category=gender-neutral",
      },
      {
        title: "Sale",
        url: "/products?category=sale",
      },
    ],
  },
  {
    title: "Wishlist",
    url: "/wishlist",
    icon: Heart,
  },
]

const accountItems = [
  {
    title: "My Account",
    url: "/account",
    icon: User,
  },
  {
    title: "Orders",
    url: "/account/orders",
    icon: Truck,
  },
  {
    title: "Settings",
    url: "/account/settings",
    icon: Settings,
  },
]

const supportItems = [
  {
    title: "Help Center",
    url: "/help",
    icon: HelpCircle,
  },
]

interface NavigationSidebarProps {
  user?: {
    name: string
    email: string
    avatar?: string
  }
  onSignOut?: () => void
}

// Internal NavigationSidebar component - without SidebarProvider
export function NavigationSidebarContent({ user, onSignOut }: NavigationSidebarProps) {
  const pathname = usePathname()
  const auth = useAuth()
  
  // Use auth hook data if available, otherwise fall back to props
  const currentUser = auth.user ? {
    name: `${auth.user.firstName} ${auth.user.lastName}`,
    email: auth.user.email,
    avatar: undefined // Add avatar field to User type if needed
  } : user

  const handleSignOut = async () => {
    if (onSignOut) {
      onSignOut()
    } else if (auth.logout) {
      await auth.logout()
    }
  }

  return (
    <div className="flex h-full w-full flex-col bg-background">
      {/* Header */}
      <div className="flex items-center gap-2 px-4 py-4 border-b">
        <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
          <ShoppingBag className="size-4" />
        </div>
        <div className="grid flex-1 text-left text-sm leading-tight">
          <span className="truncate font-semibold">Coco Milk Kids</span>
          <span className="truncate text-xs text-muted-foreground">Premium Kids Fashion</span>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4 space-y-6">
        {/* Main Navigation */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground px-2">Navigation</h3>
          <nav className="space-y-1">
            {navigationItems.map((item) => (
              <div key={item.title}>
                {item.items ? (
                  <div className="space-y-1">
                    <div className="flex items-center justify-between px-3 py-2 text-sm font-medium text-foreground">
                      <div className="flex items-center gap-2">
                        {item.icon && <item.icon className="size-4" />}
                        <span>{item.title}</span>
                      </div>
                      <ChevronRight className="size-4" />
                    </div>
                    <div className="ml-6 space-y-1">
                      {item.items.map((subItem) => (
                        <Link
                          key={subItem.title}
                          href={subItem.url}
                          className={cn(
                            "flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors",
                            pathname === subItem.url
                              ? "bg-primary text-primary-foreground"
                              : "text-muted-foreground hover:text-foreground hover:bg-muted"
                          )}
                        >
                          <span>{subItem.title}</span>
                        </Link>
                      ))}
                    </div>
                  </div>
                ) : (
                  <Link
                    href={item.url!}
                    className={cn(
                      "flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors",
                      pathname === item.url
                        ? "bg-primary text-primary-foreground"
                        : "text-muted-foreground hover:text-foreground hover:bg-muted"
                    )}
                  >
                    {item.icon && <item.icon className="size-4" />}
                    <span>{item.title}</span>
                  </Link>
                )}
              </div>
            ))}
          </nav>
        </div>

        {/* Account Section */}
        {currentUser && (
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground px-2">Account</h3>
            <nav className="space-y-1">
              {accountItems.map((item) => (
                <Link
                  key={item.title}
                  href={item.url}
                  className={cn(
                    "flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors",
                    pathname === item.url
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:text-foreground hover:bg-muted"
                  )}
                >
                  <item.icon className="size-4" />
                  <span>{item.title}</span>
                </Link>
              ))}
            </nav>
          </div>
        )}

        {/* Support Section */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground px-2">Support</h3>
          <nav className="space-y-1">
            {supportItems.map((item) => (
              <Link
                key={item.title}
                href={item.url}
                className={cn(
                  "flex items-center gap-2 px-3 py-2 text-sm rounded-md transition-colors",
                  pathname === item.url
                    ? "bg-primary text-primary-foreground"
                    : "text-muted-foreground hover:text-foreground hover:bg-muted"
                )}
              >
                <item.icon className="size-4" />
                <span>{item.title}</span>
              </Link>
            ))}
          </nav>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t p-4">
        {currentUser ? (
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <Avatar className="size-8">
                <AvatarImage src={currentUser.avatar} alt={currentUser.name} />
                <AvatarFallback>{currentUser.name.charAt(0)}</AvatarFallback>
              </Avatar>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-semibold">{currentUser.name}</span>
                <span className="truncate text-xs text-muted-foreground">{currentUser.email}</span>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="w-full justify-start gap-2"
              onClick={handleSignOut}
            >
              <LogOut className="size-4" />
              Sign Out
            </Button>
          </div>
        ) : (
          <div className="space-y-2">
            <Button asChild className="w-full">
              <Link href="/login">Sign In</Link>
            </Button>
            <Button asChild variant="outline" className="w-full">
              <Link href="/register">Sign Up</Link>
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

// Main NavigationSidebar component wrapped with SidebarProvider
export function NavigationSidebar(props: NavigationSidebarProps) {
  return (
    <SidebarProvider>
      <NavigationSidebarContent {...props} />
    </SidebarProvider>
  )
}
