'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Loader2, Bell, BellRing, BellOff, Volume2, VolumeX } from 'lucide-react';
import { useNotificationPreferences, useBrowserNotificationPermission } from '@/lib/notifications/hooks';
import { toast } from '@/components/ui/use-toast';

interface NotificationPreferencesPanelProps {
  userId?: string;
}

export function NotificationPreferencesPanel({ userId }: NotificationPreferencesPanelProps) {
  const {
    permission,
    requesting,
    requestPermission,
    isSupported
  } = useBrowserNotificationPermission();
  
  const [preferences, setPreferences] = useState({
    emailEnabled: true,
    smsEnabled: false,
    pushEnabled: true,
    inAppEnabled: true,
    browserNotifications: true,
    sound: true,
    categories: {
      orders: true,
      payments: true,
      shipping: true,
      promotions: false,
      system: true,
      security: true
    }
  });
  
  const [loading, setLoading] = useState(false);
  
  // Toggle browser notifications
  const toggleBrowserNotifications = async () => {
    if (!preferences.browserNotifications) {
      // If enabling, request permission first
      if (permission !== 'granted' && isSupported) {
        const result = await requestPermission();
        if (result !== 'granted') {
          toast({
            title: "Permission Denied",
            description: "Browser notifications require permission to be enabled.",
            variant: "destructive"
          });
          return; // Don't enable if permission not granted
        }
      }
    }
    
    setPreferences(prev => ({
      ...prev,
      browserNotifications: !prev.browserNotifications
    }));
  };

  // Save notification preferences
  const savePreferences = async () => {
    if (!userId) {
      toast({
        title: "Error",
        description: "You must be logged in to save preferences.",
        variant: "destructive"
      });
      return;
    }
    
    setLoading(true);
    
    try {
      // This would typically save to the server via API
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      toast({
        title: "Preferences Saved",
        description: "Your notification preferences have been updated.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save notification preferences.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      {/* Browser Notifications */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {preferences.browserNotifications ? (
            <BellRing className="h-5 w-5 text-primary" />
          ) : (
            <BellOff className="h-5 w-5 text-muted-foreground" />
          )}
          <div>
            <div className="font-medium">Browser Notifications</div>
            <div className="text-sm text-muted-foreground">
              Show notifications in your browser
            </div>
          </div>
        </div>
        <Switch 
          checked={preferences.browserNotifications}
          onCheckedChange={toggleBrowserNotifications}
          disabled={!isSupported}
        />
      </div>
      
      {permission === 'denied' && (
        <div className="text-sm text-amber-600 bg-amber-50 p-2 rounded">
          Notifications are blocked by your browser. Please update your browser settings to enable notifications.
        </div>
      )}
      
      <Separator />
      
      {/* Sound Notifications */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {preferences.sound ? (
            <Volume2 className="h-5 w-5 text-primary" />
          ) : (
            <VolumeX className="h-5 w-5 text-muted-foreground" />
          )}
          <div>
            <div className="font-medium">Sound Notifications</div>
            <div className="text-sm text-muted-foreground">
              Play a sound when you receive a notification
            </div>
          </div>
        </div>
        <Switch 
          checked={preferences.sound}
          onCheckedChange={(checked) => setPreferences(prev => ({
            ...prev, sound: checked
          }))}
        />
      </div>
      
      <Separator />
      
      {/* Order Updates */}
      <div className="flex items-center justify-between">
        <div>
          <div className="font-medium">Order Updates</div>
          <div className="text-sm text-muted-foreground">
            Receive notifications about your order status
          </div>
        </div>
        <Switch 
          checked={preferences.categories.orders}
          onCheckedChange={(checked) => setPreferences(prev => ({
            ...prev, 
            categories: {
              ...prev.categories,
              orders: checked
            }
          }))}
        />
      </div>
      
      <Separator />
      
      {/* Promotions and Sales */}
      <div className="flex items-center justify-between">
        <div>
          <div className="font-medium">Promotions and Sales</div>
          <div className="text-sm text-muted-foreground">
            Receive notifications about promotions and sales
          </div>
        </div>
        <Switch 
          checked={preferences.categories.promotions}
          onCheckedChange={(checked) => setPreferences(prev => ({
            ...prev, 
            categories: {
              ...prev.categories,
              promotions: checked
            }
          }))}
        />
      </div>
      
      <Separator />
      
      {/* SMS Notifications */}
      <div className="flex items-center justify-between">
        <div>
          <div className="font-medium">SMS Notifications</div>
          <div className="text-sm text-muted-foreground">
            Receive order updates via text message
          </div>
        </div>
        <Switch 
          checked={preferences.smsEnabled}
          onCheckedChange={(checked) => setPreferences(prev => ({
            ...prev, smsEnabled: checked
          }))}
        />
      </div>
      
      <Separator />
      
      {/* Security Alerts */}
      <div className="flex items-center justify-between">
        <div>
          <div className="font-medium">Security Alerts</div>
          <div className="text-sm text-muted-foreground">
            Receive notifications about security events
          </div>
        </div>
        <Switch 
          checked={preferences.categories.security}
          onCheckedChange={(checked) => setPreferences(prev => ({
            ...prev, 
            categories: {
              ...prev.categories,
              security: checked
            }
          }))}
        />
      </div>
      
      <div className="pt-4">
        <Button 
          onClick={savePreferences} 
          disabled={loading}
          className="w-full"
        >
          {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Save Preferences
        </Button>
      </div>
    </div>
  );
}

export default NotificationPreferencesPanel;