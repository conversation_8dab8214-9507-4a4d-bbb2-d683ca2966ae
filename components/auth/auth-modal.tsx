"use client"

import { useState } from "react"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { AuthFormContainer } from "@/components/auth/auth-form-container"

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
  defaultTab?: "login" | "register"
  onSuccess?: () => void
  title?: string
  description?: string
  redirectUrl?: string
  showSocialLogin?: boolean
}

export function AuthModal({
  isOpen,
  onClose,
  defaultTab = "login",
  onSuccess,
  title = "Sign in to your account",
  description = "Enter your details below to continue",
  redirectUrl,
  showSocialLogin = true
}: AuthModalProps) {
  const handleSuccess = () => {
    onClose()
    if (onSuccess) onSuccess()
  }

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        
        <AuthFormContainer 
          defaultMode={defaultTab} 
          onSuccess={handleSuccess}
          redirectUrl={redirectUrl}
          showSocialLogin={showSocialLogin}
        />
      </DialogContent>
    </Dialog>
  )
}