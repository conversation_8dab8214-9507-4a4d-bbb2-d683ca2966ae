"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { toast } from "sonner"
import { useRouter } from "next/navigation"

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Loader2 } from "lucide-react"

// Define the verification code schema
const verificationCodeSchema = z.object({
  code: z.string().min(6, "Verification code must be at least 6 characters"),
})

type VerificationCodeValues = z.infer<typeof verificationCodeSchema>

interface EmailVerificationFormProps {
  email?: string
  token?: string
  onSuccess?: () => void
  redirectUrl?: string
  className?: string
}

export function EmailVerificationForm({ 
  email,
  token,
  onSuccess,
  redirectUrl,
  className 
}: EmailVerificationFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [isSending, setIsSending] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [countdown, setCountdown] = useState(0)

  // Initialize form
  const form = useForm<VerificationCodeValues>({
    resolver: zodResolver(verificationCodeSchema),
    defaultValues: {
      code: "",
    },
  })

  // Handle countdown for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [countdown])

  // Handle automatic verification if token is provided
  useEffect(() => {
    if (token) {
      verifyEmail(token)
    }
  }, [token])

  // Handle form submission
  const onSubmit = async (data: VerificationCodeValues) => {
    await verifyEmail(data.code)
  }

  // Verify email with code or token
  const verifyEmail = async (codeOrToken: string) => {
    setIsLoading(true)
    setError(null)
    
    try {
      // Call API to verify email
      const response = await fetch("/api/auth/verify-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ token: codeOrToken }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to verify email")
      }

      toast.success("Email verified successfully")
      
      if (onSuccess) {
        onSuccess()
      } else if (redirectUrl) {
        router.push(redirectUrl)
      }
    } catch (err: any) {
      setError(err.message || "Invalid or expired verification code")
      toast.error(err.message || "Invalid or expired verification code")
    } finally {
      setIsLoading(false)
    }
  }

  // Resend verification email
  const resendVerificationEmail = async () => {
    if (!email) {
      toast.error("Email address is required")
      return
    }

    setIsSending(true)
    setError(null)
    
    try {
      // Call API to resend verification email
      const response = await fetch("/api/auth/resend-verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to resend verification email")
      }

      toast.success("Verification email sent")
      setCountdown(60) // Start 60 second countdown
    } catch (err: any) {
      setError(err.message || "Failed to resend verification email")
      toast.error(err.message || "Failed to resend verification email")
    } finally {
      setIsSending(false)
    }
  }

  // If verifying with token
  if (token && isLoading) {
    return (
      <div className={`${className} flex flex-col items-center justify-center space-y-4`}>
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <p>Verifying your email...</p>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="space-y-4">
        {email && (
          <div className="text-center mb-6">
            <p className="text-muted-foreground">
              We've sent a verification code to <span className="font-medium text-foreground">{email}</span>
            </p>
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="code"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Verification Code</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter verification code" 
                      {...field} 
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormDescription>
                    Enter the verification code sent to your email.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {error && (
              <div className="text-sm font-medium text-destructive">{error}</div>
            )}
            
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Verifying...
                </>
              ) : (
                "Verify Email"
              )}
            </Button>
          </form>
        </Form>

        {email && (
          <div className="text-center mt-4">
            <p className="text-sm text-muted-foreground mb-2">
              Didn't receive the code?
            </p>
            <Button
              variant="outline"
              onClick={resendVerificationEmail}
              disabled={isSending || countdown > 0}
              size="sm"
            >
              {isSending ? (
                <>
                  <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                  Sending...
                </>
              ) : countdown > 0 ? (
                `Resend in ${countdown}s`
              ) : (
                "Resend Code"
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}