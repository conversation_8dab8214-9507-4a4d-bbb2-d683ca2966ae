"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { AuthCard } from "@/components/auth/auth-card"
import { LoginForm } from "@/components/auth/forms/login-form"
import { RegisterForm } from "@/components/auth/forms/register-form"
import { PasswordResetForm } from "@/components/auth/forms/password-reset-form"
import { SocialLoginButtons } from "@/components/auth/social-login-buttons"
import { Separator } from "@/components/ui/separator"

type AuthMode = "login" | "register" | "reset-password"

interface AuthFormContainerProps {
  defaultMode?: AuthMode
  redirectUrl?: string
  onSuccess?: () => void
  showSocialLogin?: boolean
  className?: string
}

export function AuthFormContainer({
  defaultMode = "login",
  redirectUrl,
  onSuc<PERSON>,
  showSoc<PERSON>Login = true,
  className = "",
}: AuthFormContainerProps) {
  const [mode, setMode] = useState<AuthMode>(defaultMode)
  const router = useRouter()

  const handleSuccess = () => {
    if (onSuccess) {
      onSuccess()
    } else if (redirectUrl) {
      router.push(redirectUrl)
    }
  }

  // Render different content based on mode
  const renderContent = () => {
    switch (mode) {
      case "login":
        return (
          <AuthCard
            title="Sign In"
            description="Enter your credentials to access your account"
            footer={
              <div className="w-full flex flex-col space-y-2 text-center">
                <Button
                  variant="link"
                  onClick={() => setMode("reset-password")}
                  className="mx-auto"
                >
                  Forgot your password?
                </Button>
                <Button
                  variant="link"
                  onClick={() => setMode("register")}
                  className="mx-auto"
                >
                  Don't have an account? Sign up
                </Button>
              </div>
            }
            className={className}
          >
            <div className="space-y-6">
              <LoginForm onSuccess={handleSuccess} />
              
              {showSocialLogin && (
                <>
                  <div className="relative my-4">
                    <div className="absolute inset-0 flex items-center">
                      <Separator />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-background px-2 text-muted-foreground">
                        Or continue with
                      </span>
                    </div>
                  </div>
                  
                  <SocialLoginButtons callbackUrl={redirectUrl} onSuccess={handleSuccess} />
                </>
              )}
            </div>
          </AuthCard>
        )
      
      case "register":
        return (
          <AuthCard
            title="Create Account"
            description="Enter your information to create an account"
            footer={
              <Button
                variant="link"
                onClick={() => setMode("login")}
                className="mx-auto"
              >
                Already have an account? Sign in
              </Button>
            }
            className={className}
          >
            <div className="space-y-6">
              <RegisterForm onSuccess={handleSuccess} />
              
              {showSocialLogin && (
                <>
                  <div className="relative my-4">
                    <div className="absolute inset-0 flex items-center">
                      <Separator />
                    </div>
                    <div className="relative flex justify-center text-xs uppercase">
                      <span className="bg-background px-2 text-muted-foreground">
                        Or continue with
                      </span>
                    </div>
                  </div>
                  
                  <SocialLoginButtons callbackUrl={redirectUrl} onSuccess={handleSuccess} />
                </>
              )}
            </div>
          </AuthCard>
        )
      
      case "reset-password":
        return (
          <AuthCard
            title="Reset Password"
            description="Enter your email to receive a password reset link"
            footer={
              <Button
                variant="link"
                onClick={() => setMode("login")}
                className="mx-auto"
              >
                Back to sign in
              </Button>
            }
            className={className}
          >
            <PasswordResetForm mode="request" onSuccess={() => setMode("login")} />
          </AuthCard>
        )
      
      default:
        return null
    }
  }

  // Alternative tabbed interface
  const renderTabbedInterface = () => {
    return (
      <AuthCard
        title="Welcome"
        description="Sign in to your account or create a new one"
        className={className}
      >
        <Tabs defaultValue={defaultMode} onValueChange={(value) => setMode(value as AuthMode)}>
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="login">Login</TabsTrigger>
            <TabsTrigger value="register">Register</TabsTrigger>
          </TabsList>
          
          <TabsContent value="login" className="space-y-6">
            <LoginForm onSuccess={handleSuccess} />
            
            <Button
              variant="link"
              onClick={() => setMode("reset-password")}
              className="px-0"
            >
              Forgot your password?
            </Button>
            
            {showSocialLogin && (
              <>
                <div className="relative my-4">
                  <div className="absolute inset-0 flex items-center">
                    <Separator />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      Or continue with
                    </span>
                  </div>
                </div>
                
                <SocialLoginButtons callbackUrl={redirectUrl} onSuccess={handleSuccess} />
              </>
            )}
          </TabsContent>
          
          <TabsContent value="register" className="space-y-6">
            <RegisterForm onSuccess={handleSuccess} />
            
            {showSocialLogin && (
              <>
                <div className="relative my-4">
                  <div className="absolute inset-0 flex items-center">
                    <Separator />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-background px-2 text-muted-foreground">
                      Or continue with
                    </span>
                  </div>
                </div>
                
                <SocialLoginButtons callbackUrl={redirectUrl} onSuccess={handleSuccess} />
              </>
            )}
          </TabsContent>
        </Tabs>
      </AuthCard>
    )
  }

  // Use tabbed interface for login/register, but separate card for password reset
  return mode === "reset-password" ? renderContent() : renderTabbedInterface()
}