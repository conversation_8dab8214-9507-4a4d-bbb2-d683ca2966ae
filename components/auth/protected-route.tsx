"use client"

import { <PERSON>actN<PERSON>, useEffect } from "react"
import { useSession } from "next-auth/react"
import { useRouter, usePathname } from "next/navigation"
import { Loader2 } from "lucide-react"

interface ProtectedRouteProps {
  children: ReactNode
  requiredRole?: string | string[]
  fallbackUrl?: string
  loadingComponent?: ReactNode
}

export function ProtectedRoute({
  children,
  requiredRole,
  fallbackUrl = "/login",
  loadingComponent,
}: ProtectedRouteProps) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    // If the user is not authenticated and we're done loading, redirect
    if (status === "unauthenticated") {
      // Add the current path as a redirect parameter
      const encodedRedirect = encodeURIComponent(pathname)
      router.push(`${fallbackUrl}?redirect=${encodedRedirect}`)
    }

    // If role check is required and user doesn't have the required role
    if (status === "authenticated" && requiredRole && session?.user) {
      const userRole = session.user.role
      
      // Check if user has one of the required roles
      const hasRequiredRole = Array.isArray(requiredRole)
        ? requiredRole.includes(userRole)
        : userRole === requiredRole

      if (!hasRequiredRole) {
        router.push("/unauthorized")
      }
    }
  }, [status, session, router, pathname, fallbackUrl, requiredRole])

  // Show loading state while checking authentication
  if (status === "loading") {
    return loadingComponent || (
      <div className="flex h-screen w-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    )
  }

  // If authenticated (and has required role if specified), render children
  if (status === "authenticated") {
    if (requiredRole) {
      const userRole = session.user.role
      
      // Check if user has one of the required roles
      const hasRequiredRole = Array.isArray(requiredRole)
        ? requiredRole.includes(userRole)
        : userRole === requiredRole

      if (hasRequiredRole) {
        return <>{children}</>
      }
      
      // If we're still here, we're waiting for the redirect to happen
      return loadingComponent || (
        <div className="flex h-screen w-full items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      )
    }
    
    return <>{children}</>
  }

  // If we're still here, we're waiting for the redirect to happen
  return loadingComponent || (
    <div className="flex h-screen w-full items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
    </div>
  )
}