"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  Loader2, 
  CreditCard, 
  Building2, 
  Shield, 
  CheckCircle, 
  AlertCircle,
  ExternalLink,
  Lock
} from 'lucide-react'
import { PaymentRequest, PaymentResponse, PaymentMethod } from '@/lib/payments/types'
import { cn } from '@/lib/utils'

interface PayFastPaymentProps {
  paymentRequest: PaymentRequest
  method: PaymentMethod
  onSuccess: (response: PaymentResponse) => void
  onError: (error: string) => void
  onCancel?: () => void
  loading?: boolean
  className?: string
}

interface PayFastFormData {
  // Customer details
  firstName: string
  lastName: string
  email: string
  phone: string
  
  // Card details (for display only - actual processing by PayFast)
  cardNumber: string
  expiryMonth: string
  expiryYear: string
  cvv: string
  nameOnCard: string
  
  // EFT details
  bankName: string
  accountType: 'savings' | 'cheque'
}

const SA_BANKS = [
  'ABSA Bank',
  'Standard Bank',
  'First National Bank (FNB)',
  'Nedbank',
  'Capitec Bank',
  'African Bank',
  'Bidvest Bank',
  'Discovery Bank',
  'TymeBank',
  'Bank Zero',
]

export function PayFastPayment({
  paymentRequest,
  method,
  onSuccess,
  onError,
  onCancel,
  loading = false,
  className,
}: PayFastPaymentProps) {
  const [formData, setFormData] = useState<PayFastFormData>({
    firstName: paymentRequest.customer.firstName,
    lastName: paymentRequest.customer.lastName,
    email: paymentRequest.customer.email,
    phone: paymentRequest.customer.phone || '',
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    nameOnCard: '',
    bankName: '',
    accountType: 'cheque',
  })

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentUrl, setPaymentUrl] = useState<string | null>(null)

  // Generate years for expiry dropdown
  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 10 }, (_, i) => currentYear + i)
  const months = Array.from({ length: 12 }, (_, i) => {
    const month = i + 1
    return { value: month.toString().padStart(2, '0'), label: month.toString().padStart(2, '0') }
  })

  const handleInputChange = (field: keyof PayFastFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}

    // Basic customer validation
    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required'
    }
    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required'
    }
    if (!formData.email.trim()) {
      errors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address'
    }
    if (!formData.phone.trim()) {
      errors.phone = 'Phone number is required'
    }

    // Method-specific validation
    if (method === PaymentMethod.CARD) {
      if (!formData.cardNumber.replace(/\s/g, '')) {
        errors.cardNumber = 'Card number is required'
      } else if (formData.cardNumber.replace(/\s/g, '').length < 13) {
        errors.cardNumber = 'Please enter a valid card number'
      }
      
      if (!formData.expiryMonth) {
        errors.expiryMonth = 'Expiry month is required'
      }
      if (!formData.expiryYear) {
        errors.expiryYear = 'Expiry year is required'
      }
      if (!formData.cvv) {
        errors.cvv = 'CVV is required'
      } else if (formData.cvv.length < 3) {
        errors.cvv = 'CVV must be at least 3 digits'
      }
      if (!formData.nameOnCard.trim()) {
        errors.nameOnCard = 'Name on card is required'
      }
    } else if (method === PaymentMethod.EFT) {
      if (!formData.bankName) {
        errors.bankName = 'Please select your bank'
      }
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const formatCardNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '')
    // Add spaces every 4 digits
    return digits.replace(/(\d{4})(?=\d)/g, '$1 ')
  }

  const handleCardNumberChange = (value: string) => {
    const formatted = formatCardNumber(value)
    if (formatted.replace(/\s/g, '').length <= 19) {
      handleInputChange('cardNumber', formatted)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsProcessing(true)

    try {
      // Create payment request with updated customer info
      const updatedRequest: PaymentRequest = {
        ...paymentRequest,
        customer: {
          ...paymentRequest.customer,
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
        }
      }

      // Call the payment API
      const response = await fetch('/api/payments/payfast/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentRequest: updatedRequest,
          method,
        }),
      })

      const result = await response.json()

      if (result.success && result.paymentUrl) {
        setPaymentUrl(result.paymentUrl)
        onSuccess(result)
      } else {
        onError(result.error?.message || 'Failed to create payment')
      }
    } catch (error) {
      console.error('PayFast payment error:', error)
      onError('An unexpected error occurred. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleRedirectToPayFast = () => {
    if (paymentUrl) {
      window.location.href = paymentUrl
    }
  }

  // If payment URL is generated, show redirect screen
  if (paymentUrl) {
    return (
      <Card className={cn("w-full max-w-md mx-auto", className)}>
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <CardTitle>Ready to Pay</CardTitle>
          <CardDescription>
            Your payment has been prepared. Click below to complete your payment securely with PayFast.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-600">Amount</span>
              <span className="font-medium">R{paymentRequest.amount.amount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Reference</span>
              <span className="text-sm font-mono">{paymentRequest.reference}</span>
            </div>
          </div>
          
          <Button 
            onClick={handleRedirectToPayFast}
            className="w-full"
            size="lg"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Continue to PayFast
          </Button>
          
          {onCancel && (
            <Button 
              onClick={onCancel}
              variant="outline"
              className="w-full"
            >
              Cancel Payment
            </Button>
          )}
          
          <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
            <Lock className="h-3 w-3" />
            <span>Secured by PayFast SSL encryption</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("w-full max-w-md mx-auto", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {method === PaymentMethod.CARD ? (
            <CreditCard className="h-5 w-5" />
          ) : (
            <Building2 className="h-5 w-5" />
          )}
          PayFast Payment
        </CardTitle>
        <CardDescription>
          {method === PaymentMethod.CARD 
            ? 'Enter your card details to pay securely with PayFast'
            : 'Complete your EFT payment through PayFast'
          }
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Customer Information */}
          <div className="space-y-4">
            <h3 className="font-medium text-sm">Customer Information</h3>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className={validationErrors.firstName ? 'border-red-500' : ''}
                />
                {validationErrors.firstName && (
                  <p className="text-xs text-red-500">{validationErrors.firstName}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className={validationErrors.lastName ? 'border-red-500' : ''}
                />
                {validationErrors.lastName && (
                  <p className="text-xs text-red-500">{validationErrors.lastName}</p>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={validationErrors.email ? 'border-red-500' : ''}
              />
              {validationErrors.email && (
                <p className="text-xs text-red-500">{validationErrors.email}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="e.g., 0123456789"
                className={validationErrors.phone ? 'border-red-500' : ''}
              />
              {validationErrors.phone && (
                <p className="text-xs text-red-500">{validationErrors.phone}</p>
              )}
            </div>
          </div>

          <Separator />

          {/* Payment Method Specific Fields */}
          {method === PaymentMethod.CARD && (
            <div className="space-y-4">
              <h3 className="font-medium text-sm">Card Information</h3>
              
              <div className="space-y-2">
                <Label htmlFor="cardNumber">Card Number</Label>
                <Input
                  id="cardNumber"
                  value={formData.cardNumber}
                  onChange={(e) => handleCardNumberChange(e.target.value)}
                  placeholder="1234 5678 9012 3456"
                  maxLength={23}
                  className={validationErrors.cardNumber ? 'border-red-500' : ''}
                />
                {validationErrors.cardNumber && (
                  <p className="text-xs text-red-500">{validationErrors.cardNumber}</p>
                )}
              </div>
              
              <div className="grid grid-cols-3 gap-3">
                <div className="space-y-2">
                  <Label htmlFor="expiryMonth">Month</Label>
                  <select
                    id="expiryMonth"
                    value={formData.expiryMonth}
                    onChange={(e) => handleInputChange('expiryMonth', e.target.value)}
                    className={cn(
                      "w-full px-3 py-2 border border-gray-300 rounded-md text-sm",
                      validationErrors.expiryMonth ? 'border-red-500' : ''
                    )}
                  >
                    <option value="">MM</option>
                    {months.map(month => (
                      <option key={month.value} value={month.value}>
                        {month.label}
                      </option>
                    ))}
                  </select>
                  {validationErrors.expiryMonth && (
                    <p className="text-xs text-red-500">{validationErrors.expiryMonth}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="expiryYear">Year</Label>
                  <select
                    id="expiryYear"
                    value={formData.expiryYear}
                    onChange={(e) => handleInputChange('expiryYear', e.target.value)}
                    className={cn(
                      "w-full px-3 py-2 border border-gray-300 rounded-md text-sm",
                      validationErrors.expiryYear ? 'border-red-500' : ''
                    )}
                  >
                    <option value="">YYYY</option>
                    {years.map(year => (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    ))}
                  </select>
                  {validationErrors.expiryYear && (
                    <p className="text-xs text-red-500">{validationErrors.expiryYear}</p>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="cvv">CVV</Label>
                  <Input
                    id="cvv"
                    value={formData.cvv}
                    onChange={(e) => handleInputChange('cvv', e.target.value.replace(/\D/g, ''))}
                    placeholder="123"
                    maxLength={4}
                    className={validationErrors.cvv ? 'border-red-500' : ''}
                  />
                  {validationErrors.cvv && (
                    <p className="text-xs text-red-500">{validationErrors.cvv}</p>
                  )}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="nameOnCard">Name on Card</Label>
                <Input
                  id="nameOnCard"
                  value={formData.nameOnCard}
                  onChange={(e) => handleInputChange('nameOnCard', e.target.value)}
                  placeholder="John Doe"
                  className={validationErrors.nameOnCard ? 'border-red-500' : ''}
                />
                {validationErrors.nameOnCard && (
                  <p className="text-xs text-red-500">{validationErrors.nameOnCard}</p>
                )}
              </div>
            </div>
          )}

          {method === PaymentMethod.EFT && (
            <div className="space-y-4">
              <h3 className="font-medium text-sm">Bank Information</h3>
              
              <div className="space-y-2">
                <Label htmlFor="bankName">Select Your Bank</Label>
                <select
                  id="bankName"
                  value={formData.bankName}
                  onChange={(e) => handleInputChange('bankName', e.target.value)}
                  className={cn(
                    "w-full px-3 py-2 border border-gray-300 rounded-md text-sm",
                    validationErrors.bankName ? 'border-red-500' : ''
                  )}
                >
                  <option value="">Choose your bank</option>
                  {SA_BANKS.map(bank => (
                    <option key={bank} value={bank}>
                      {bank}
                    </option>
                  ))}
                </select>
                {validationErrors.bankName && (
                  <p className="text-xs text-red-500">{validationErrors.bankName}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label>Account Type</Label>
                <div className="flex gap-4">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="accountType"
                      value="cheque"
                      checked={formData.accountType === 'cheque'}
                      onChange={(e) => handleInputChange('accountType', e.target.value as 'cheque' | 'savings')}
                      className="mr-2"
                    />
                    Cheque
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="accountType"
                      value="savings"
                      checked={formData.accountType === 'savings'}
                      onChange={(e) => handleInputChange('accountType', e.target.value as 'cheque' | 'savings')}
                      className="mr-2"
                    />
                    Savings
                  </label>
                </div>
              </div>
              
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  You will be redirected to your bank's secure login page to complete the EFT payment.
                </AlertDescription>
              </Alert>
            </div>
          )}

          {/* Payment Summary */}
          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Amount</span>
              <span className="font-medium">R{paymentRequest.amount.amount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Reference</span>
              <span className="text-sm font-mono">{paymentRequest.reference}</span>
            </div>
          </div>

          {/* Submit Button */}
          <Button
            type="submit"disabled={isProcessing || loading}
            className="w-full"
            size="lg"
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Processing...
              </>
            ) : (
              <>
                <Shield className="h-4 w-4 mr-2" />
                Pay R{paymentRequest.amount.amount.toFixed(2)}
              </>
            )}
          </Button>

          {onCancel && (
            <Button
              type="button"
              onClick={onCancel}
              variant="outline"
              className="w-full"
              disabled={isProcessing}
            >
              Cancel
            </Button>
          )}
        </form>

        {/* Security Notice */}
        <div className="mt-6 pt-4 border-t">
          <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
            <Shield className="h-3 w-3" />
            <span>Secured by PayFast with 256-bit SSL encryption</span>
          </div>
          <div className="flex justify-center mt-2">
            <Badge variant="secondary" className="text-xs">
              PCI DSS Compliant
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}