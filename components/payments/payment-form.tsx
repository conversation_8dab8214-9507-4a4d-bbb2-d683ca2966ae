"use client"

import React, { useState, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { ArrowLeft, AlertCircle, CheckCircle, XCircle } from 'lucide-react'
import { PaymentGateway, PaymentMethod, PaymentRequest, PaymentResponse } from '@/lib/payments/types'
import { PaymentGatewaySelector } from './payment-gateway-selector'
import { PayFastPayment } from './payfast-payment'
import { OzowPayment } from './ozow-payment'
import { PaymentStatus } from './payment-status'
import { cn } from '@/lib/utils'

interface PaymentFormProps {
  paymentRequest: PaymentRequest
  onSuccess?: (response: PaymentResponse) => void
  onError?: (error: string) => void
  onCancel?: () => void
  className?: string
}

type PaymentStep = 'select' | 'payment' | 'processing' | 'success' | 'error'

export function PaymentForm({
  paymentRequest,
  onSuccess,
  onError,
  onCancel,
  className,
}: PaymentFormProps) {
  const [currentStep, setCurrentStep] = useState<PaymentStep>('select')
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway>()
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>()
  const [paymentResponse, setPaymentResponse] = useState<PaymentResponse | null>(null)
  const [errorMessage, setErrorMessage] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)

  const handleGatewayChange = useCallback((gateway: PaymentGateway) => {
    setSelectedGateway(gateway)
  }, [])

  const handleMethodChange = useCallback((method: PaymentMethod) => {
    setSelectedMethod(method)
  }, [])

  const handleProceedToPayment = useCallback(() => {
    if (selectedGateway && selectedMethod) {
      setCurrentStep('payment')
    }
  }, [selectedGateway, selectedMethod])

  const handlePaymentSuccess = useCallback((response: PaymentResponse) => {
    setPaymentResponse(response)
    setCurrentStep('success')
    onSuccess?.(response)
  }, [onSuccess])

  const handlePaymentError = useCallback((error: string) => {
    setErrorMessage(error)
    setCurrentStep('error')
    onError?.(error)
  }, [onError])

  const handleBackToSelection = useCallback(() => {
    setCurrentStep('select')
    setErrorMessage('')
  }, [])

  const handleRetryPayment = useCallback(() => {
    setCurrentStep('payment')
    setErrorMessage('')
  }, [])

  const handleCancel = useCallback(() => {
    onCancel?.()
  }, [onCancel])

  const renderStepContent = () => {
    switch (currentStep) {
      case 'select':
        return (
          <PaymentGatewaySelector
            amount={paymentRequest.amount.amount}
            selectedGateway={selectedGateway}
            selectedMethod={selectedMethod ?? ""}
            onGatewayChange={handleGatewayChange}
            onMethodChange={()=> handleMethodChange}
            onProceed={handleProceedToPayment}
            loading={isLoading}
          />
        )

      case 'payment':
        if (!selectedGateway || !selectedMethod) {
          return (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Payment configuration error. Please go back and select your payment options again.
              </AlertDescription>
            </Alert>
          )
        }

        switch (selectedGateway) {
          case PaymentGateway.PAYFAST:
            return (
              <PayFastPayment
                paymentRequest={paymentRequest}
                method={selectedMethod}
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
                onCancel={handleBackToSelection}
                loading={isLoading}
              />
            )

          case PaymentGateway.OZOW:
            return (
              <OzowPayment
                paymentRequest={paymentRequest}
                method={selectedMethod}
                onSuccess={handlePaymentSuccess}
                onError={handlePaymentError}
                onCancel={handleBackToSelection}
                loading={isLoading}
              />
            )

          default:
            return (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Unsupported payment gateway: {selectedGateway}
                </AlertDescription>
              </Alert>
            )
        }

      case 'processing':
        return (
          <PaymentStatus
            status="processing"
            title="Processing Payment"
            description="Please wait while we process your payment..."
            showSpinner
          />
        )

      case 'success':
        return (
          <PaymentStatus
            status="success"
            title="Payment Successful!"
            description={
              paymentResponse?.paymentUrl
                ? "Your payment has been initiated. You will be redirected to complete the payment."
                : "Your payment has been processed successfully."
            }
            details={paymentResponse ? {
              reference: paymentResponse.reference || paymentRequest.reference,
              transactionId: paymentResponse.transactionId,
              amount: `R${paymentRequest.amount.amount.toFixed(2)}`,
            } : undefined}
            actions={
              paymentResponse?.paymentUrl ? (
                <Button
                  onClick={() => window.location.href = paymentResponse.paymentUrl!}
                  className="w-full"
                  size="lg"
                >
                  Complete Payment
                </Button>
              ) : undefined
            }
          />
        )

      case 'error':
        return (
          <PaymentStatus
            status="error"
            title="Payment Failed"
            description={errorMessage || "An error occurred while processing your payment."}
            actions={
              <div className="space-y-2 w-full">
                <Button
                  onClick={handleRetryPayment}
                  className="w-full"
                  variant="default"
                >
                  Try Again
                </Button>
                <Button
                  onClick={handleBackToSelection}
                  className="w-full"
                  variant="outline"
                >
                  Choose Different Payment Method
                </Button>
                {onCancel && (
                  <Button
                    onClick={handleCancel}
                    className="w-full"
                    variant="ghost"
                  >
                    Cancel
                  </Button>
                )}
              </div>
            }
          />
        )

      default:
        return null
    }
  }

  return (
    <div className={cn("w-full max-w-2xl mx-auto", className)}>
      {/* Header with back button for payment step */}
      {currentStep === 'payment' && (
        <div className="mb-6">
          <Button
            onClick={handleBackToSelection}
            variant="ghost"
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Payment Options
          </Button>
          
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold">Complete Payment</h2>
              <p className="text-sm text-gray-600">
                {selectedGateway === PaymentGateway.PAYFAST ? 'PayFast' : 'Ozow'} • 
                {selectedMethod === PaymentMethod.CARD ? ' Credit/Debit Card' :
                 selectedMethod === PaymentMethod.INSTANT_EFT ? ' Instant EFT' :
                 selectedMethod === PaymentMethod.EFT ? ' EFT' : ` ${selectedMethod}`}
              </p>
            </div>
            <div className="text-right">
              <div className="text-lg font-semibold">R{paymentRequest.amount.amount.toFixed(2)}</div>
              <div className="text-sm text-gray-600">{paymentRequest.amount.currency}</div>
            </div>
          </div>
          <Separator className="mt-4" />
        </div>
      )}

      {/* Step Content */}
      <div className="space-y-6">
        {renderStepContent()}
      </div>

      {/* Order Summary (shown on selection step) */}
      {currentStep === 'select' && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="text-base">Order Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {paymentRequest.items.map((item, index) => (
              <div key={index} className="flex justify-between items-center">
                <div className="flex-1">
                  <div className="font-medium text-sm">{item.name}</div>
                  <div className="text-xs text-gray-500">
                    Qty: {item.quantity} × R{item.unitPrice.toFixed(2)}
                  </div>
                </div>
                <div className="font-medium">
                  R{item.totalPrice.toFixed(2)}
                </div>
              </div>
            ))}
            
            <Separator />
            
            <div className="flex justify-between items-center font-medium">
              <span>Total</span>
              <span>R{paymentRequest.amount.amount.toFixed(2)}</span>
            </div>
            
            <div className="text-xs text-gray-500">
              Reference: {paymentRequest.reference}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Cancel button for selection step */}
      {currentStep === 'select' && onCancel && (
        <div className="mt-6 text-center">
          <Button
            onClick={handleCancel}
            variant="ghost"
            className="text-gray-500 hover:text-gray-700"
          >
            Cancel Order
          </Button>
        </div>
      )}
    </div>
  )
}