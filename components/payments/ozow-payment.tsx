"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  Loader2, 
  Zap, 
  Building2, 
  Shield, 
  CheckCircle, 
  AlertCircle,
  ExternalLink,
  Lock,
  Clock
} from 'lucide-react'
import { PaymentRequest, PaymentResponse, PaymentMethod } from '@/lib/payments/types'
import { cn } from '@/lib/utils'

interface OzowPaymentProps {
  paymentRequest: PaymentRequest
  method: PaymentMethod
  onSuccess: (response: PaymentResponse) => void
  onError: (error: string) => void
  onCancel?: () => void
  loading?: boolean
  className?: string
}

interface OzowFormData {
  // Customer details
  firstName: string
  lastName: string
  email: string
  phone: string
  
  // Bank selection for EFT
  bankName: string
  accountType: 'savings' | 'cheque' | 'transmission'
}

const SA_BANKS_OZOW = [
  { name: 'ABSA Bank', code: 'ABSA', instantEft: true },
  { name: 'Standard Bank', code: 'SBSA', instantEft: true },
  { name: 'First National Bank (FNB)', code: 'FNB', instantEft: true },
  { name: 'Nedbank', code: 'NEDBANK', instantEft: true },
  { name: 'Capitec Bank', code: 'CAPITEC', instantEft: true },
  { name: 'African Bank', code: 'AFRICAN_BANK', instantEft: false },
  { name: 'Bidvest Bank', code: 'BIDVEST', instantEft: false },
  { name: 'Discovery Bank', code: 'DISCOVERY', instantEft: true },
  { name: 'TymeBank', code: 'TYME', instantEft: true },
  { name: 'Bank Zero', code: 'BANK_ZERO', instantEft: false },
]

export function OzowPayment({
  paymentRequest,
  method,
  onSuccess,
  onError,
  onCancel,
  loading = false,
  className,
}: OzowPaymentProps) {
  const [formData, setFormData] = useState<OzowFormData>({
    firstName: paymentRequest.customer.firstName,
    lastName: paymentRequest.customer.lastName,
    email: paymentRequest.customer.email,
    phone: paymentRequest.customer.phone || '',
    bankName: '',
    accountType: 'cheque',
  })

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  const [isProcessing, setIsProcessing] = useState(false)
  const [paymentUrl, setPaymentUrl] = useState<string | null>(null)
  const [selectedBank, setSelectedBank] = useState<typeof SA_BANKS_OZOW[0] | null>(null)

  const handleInputChange = (field: keyof OzowFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Update selected bank when bank name changes
    if (field === 'bankName') {
      const bank = SA_BANKS_OZOW.find(b => b.code === value)
      setSelectedBank(bank || null)
    }
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}

    // Basic customer validation
    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required'
    }
    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required'
    }
    if (!formData.email.trim()) {
      errors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address'
    }
    if (!formData.phone.trim()) {
      errors.phone = 'Phone number is required'
    } else if (!/^(\+27|0)[0-9]{9}$/.test(formData.phone)) {
      errors.phone = 'Please enter a valid South African phone number'
    }

    // Bank selection validation
    if (!formData.bankName) {
      errors.bankName = 'Please select your bank'
    }

    // Method-specific validation
    if (method === PaymentMethod.INSTANT_EFT && selectedBank && !selectedBank.instantEft) {
      errors.bankName = 'This bank does not support instant EFT. Please choose a different bank or use regular EFT.'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsProcessing(true)

    try {
      // Create payment request with updated customer info
      const updatedRequest: PaymentRequest = {
        ...paymentRequest,
        customer: {
          ...paymentRequest.customer,
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
        },
        metadata: {
          ...paymentRequest.metadata,
          bankCode: formData.bankName,
          accountType: formData.accountType,
        }
      }

      // Call the payment API
      const response = await fetch('/api/payments/ozow/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentRequest: updatedRequest,
          method,
        }),
      })

      const result = await response.json()

      if (result.success && result.paymentUrl) {
        setPaymentUrl(result.paymentUrl)
        onSuccess(result)
      } else {
        onError(result.error?.message || 'Failed to create payment')
      }
    } catch (error) {
      console.error('Ozow payment error:', error)
      onError('An unexpected error occurred. Please try again.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleRedirectToOzow = () => {
    if (paymentUrl) {
      window.location.href = paymentUrl
    }
  }

  // If payment URL is generated, show redirect screen
  if (paymentUrl) {
    return (
      <Card className={cn("w-full max-w-md mx-auto", className)}>
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Zap className="h-6 w-6 text-blue-600" />
          </div>
          <CardTitle>Ready for Instant Payment</CardTitle>
          <CardDescription>
            Your payment has been prepared. Click below to complete your {method === PaymentMethod.INSTANT_EFT ? 'instant' : ''} EFT payment securely with Ozow.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-600">Amount</span>
              <span className="font-medium">R{paymentRequest.amount.amount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-600">Reference</span>
              <span className="text-sm font-mono">{paymentRequest.reference}</span>
            </div>
            {selectedBank && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Bank</span>
                <span className="text-sm">{selectedBank.name}</span>
              </div>
            )}
          </div>
          
          {method === PaymentMethod.INSTANT_EFT && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                <strong>Instant EFT:</strong> Your payment will be processed immediately and you'll receive confirmation within seconds.
              </AlertDescription>
            </Alert>
          )}
          
          <Button 
            onClick={handleRedirectToOzow}
            className="w-full bg-blue-600 hover:bg-blue-700"
            size="lg"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Continue to Ozow
          </Button>
          
          {onCancel && (
            <Button 
              onClick={onCancel}
              variant="outline"
              className="w-full"
            >
              Cancel Payment
            </Button>
          )}
          
          <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
            <Lock className="h-3 w-3" />
            <span>Secured by Ozow with bank-grade security</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("w-full max-w-md mx-auto", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-blue-600" />
          Ozow Payment
        </CardTitle>
        <CardDescription>
          {method === PaymentMethod.INSTANT_EFT 
            ? 'Pay instantly from your bank account with Ozow'
            : 'Complete your EFT payment through Ozow'
          }
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Customer Information */}
          <div className="space-y-4">
            <h3 className="font-medium text-sm">Customer Information</h3>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className={validationErrors.firstName ? 'border-red-500' : ''}
                />
                {validationErrors.firstName && (
                  <p className="text-xs text-red-500">{validationErrors.firstName}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className={validationErrors.lastName ? 'border-red-500' : ''}
                />
                {validationErrors.lastName && (
                  <p className="text-xs text-red-500">{validationErrors.lastName}</p>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={validationErrors.email ? 'border-red-500' : ''}
              />
              {validationErrors.email && (
                <p className="text-xs text-red-500">{validationErrors.email}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="e.g., ********** or +***********"
                className={validationErrors.phone ? 'border-red-500' : ''}
              />
              {validationErrors.phone && (
                <p className="text-xs text-red-500">{validationErrors.phone}</p>
              )}
            </div>
          </div>

          <Separator />

          {/* Bank Selection */}
          <div className="space-y-4">
            <h3 className="font-medium text-sm">Bank Information</h3>
            
            <div className="space-y-2">
              <Label htmlFor="bankName">Select Your Bank</Label>
              <select
                id="bankName"
                value={formData.bankName}
                onChange={(e) => handleInputChange('bankName', e.target.value)}
                className={cn(
                  "w-full px-3 py-2 border border-gray-300 rounded-md text-sm",
                  validationErrors.bankName ? 'border-red-500' : ''
                )}
              >
                <option value="">Choose your bank</option>
                {SA_BANKS_OZOW.map(bank => (
                  <option key={bank.code} value={bank.code}>
                    {bank.name}
                    {method === PaymentMethod.INSTANT_EFT && bank.instantEft && ' ⚡'}
                    {method === PaymentMethod.INSTANT_EFT && !bank.instantEft && ' (EFT only)'}
                  </option>
                ))}
              </select>
              {validationErrors.bankName && (
                <p className="text-xs text-red-500">{validationErrors.bankName}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label>Account Type</Label>
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="accountType"
                    value="cheque"
                    checked={formData.accountType === 'cheque'}
                    onChange={(e) => handleInputChange('accountType', e.target.value as 'cheque' | 'savings' | 'transmission')}
                    className="mr-2"
                  />
                  Cheque
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="accountType"
                    value="savings"
                    checked={formData.accountType === 'savings'}
                    onChange={(e) => handleInputChange('accountType', e.target.value as 'cheque' | 'savings' | 'transmission')}
                    className="mr-2"
                  />
                  Savings
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="accountType"
                    value="transmission"
                    checked={formData.accountType === 'transmission'}
                    onChange={(e) => handleInputChange('accountType', e.target.value as 'cheque' | 'savings' | 'transmission')}
                    className="mr-2"
                  />
                  Transmission
                </label>
              </div>
            </div>

            {/* Bank-specific information */}
            {selectedBank && (
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Building2 className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-sm">{selectedBank.name}</span>
                  {selectedBank.instantEft && method === PaymentMethod.INSTANT_EFT && (
                    <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                      <Zap className="h-3 w-3 mr-1" />
                      Instant
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-gray-600">
                  {method === PaymentMethod.INSTANT_EFT && selectedBank.instantEft
                    ? 'This bank supports instant EFT payments. Your payment will be processed immediately.'
                    : 'You will be redirected to your bank\'s secure login page to complete the payment.'
                  }
                </p>
              </div>
            )}
            
            {method === PaymentMethod.INSTANT_EFT && (
              <Alert>
                <Zap className="h-4 w-4" />
                <AlertDescription>
                  <strong>Instant EFT Benefits:</strong>
                  <ul className="list-disc list-inside mt-1 text-sm">
                    <li>Real-time payment processing</li>
                    <li>Immediate order confirmation</li>
                    <li>No waiting for bank clearance</li>
                    <li>Secure bank-grade encryption</li>
                  </ul>
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Payment Summary */}
          <div className="bg-gray-50 p-4 rounded-lg space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Amount</span>
              <span className="font-medium">R{paymentRequest.amount.amount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Reference</span>
              <span className="text-sm font-mono">{paymentRequest.reference}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Processing Time</span>
              <span className="text-sm">
                {method === PaymentMethod.INSTANT_EFT ? 'Instant' : '1-3 business days'}
              </span>
            </div>
          </div>

          {/* Submit Button */}
          <Button
            type="submit"disabled={isProcessing || loading}
            className="w-full bg-blue-600 hover:bg-blue-700"
            size="lg"
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Processing...
              </>
            ) : (
              <>
                {method === PaymentMethod.INSTANT_EFT ? (
                  <Zap className="h-4 w-4 mr-2" />
                ) : (
                  <Building2 className="h-4 w-4 mr-2" />
                )}
                Pay R{paymentRequest.amount.amount.toFixed(2)}
                {method === PaymentMethod.INSTANT_EFT && ' Instantly'}
              </>
            )}
          </Button>

          {onCancel && (
            <Button
              type="button"
              onClick={onCancel}
              variant="outline"
              className="w-full"
              disabled={isProcessing}
            >
              Cancel
            </Button>
          )}
        </form>

        {/* Security Notice */}
        <div className="mt-6 pt-4 border-t">
          <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
            <Shield className="h-3 w-3" />
            <span>Secured by Ozow with bank-grade security</span>
          </div>
          <div className="flex justify-center mt-2 gap-2">
            <Badge variant="secondary" className="text-xs">
              PCI DSS Compliant
            </Badge>
            <Badge variant="secondary" className="text-xs">
              POPIA Compliant
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}