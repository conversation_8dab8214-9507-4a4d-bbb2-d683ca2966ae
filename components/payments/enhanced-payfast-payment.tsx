"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  Loader2, 
  CreditCard, 
  Building2, 
  Shield, 
  CheckCircle, 
  AlertCircle,
  ExternalLink,
  Lock,
  Smartphone,
  Wallet
} from 'lucide-react'
import { usePayfastPayment } from '@/hooks/use-payfast-payment'
import { 
  PaymentRequest, 
  PaymentResponse, 
  PaymentMethod, 
  PaymentStatus 
} from '@/lib/payment-core/types'
import { cn } from '@/lib/utils'

interface EnhancedPayfastPaymentProps {
  paymentRequest: PaymentRequest
  method?: PaymentMethod
  onSuccess?: (response: PaymentResponse) => void
  onError?: (error: string) => void
  onCancel?: () => void
  className?: string
  showLogo?: boolean
  theme?: 'light' | 'dark' | 'auto'
  redirectAutomatically?: boolean
  showSecurityBadge?: boolean
}

interface PayfastFormData {
  firstName: string
  lastName: string
  email: string
  phone: string
}

export function EnhancedPayfastPayment({
  paymentRequest,
  method = PaymentMethod.CARD,
  onSuccess,
  onError,
  onCancel,
  className,
  showLogo = true,
  theme = 'light',
  redirectAutomatically = false,
  showSecurityBadge = true
}: EnhancedPayfastPaymentProps) {
  const [formData, setFormData] = useState<PayfastFormData>({
    firstName: paymentRequest.customer.firstName,
    lastName: paymentRequest.customer.lastName,
    email: paymentRequest.customer.email,
    phone: paymentRequest.customer.phone || '',
  })

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  
  const { 
    createPayment, 
    redirectToPayment, 
    loading, 
    error, 
    paymentResponse 
  } = usePayfastPayment({
    onSuccess,
    onError
  })

  const handleInputChange = (field: keyof PayfastFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {}

    // Basic customer validation
    if (!formData.firstName.trim()) {
      errors.firstName = 'First name is required'
    }
    if (!formData.lastName.trim()) {
      errors.lastName = 'Last name is required'
    }
    if (!formData.email.trim()) {
      errors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address'
    }
    if (!formData.phone.trim()) {
      errors.phone = 'Phone number is required'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    // Create payment request with updated customer info
    const updatedRequest: PaymentRequest = {
      ...paymentRequest,
      customer: {
        ...paymentRequest.customer,
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone,
      }
    }

    const response = await createPayment(updatedRequest, method)
    
    if (response.success && redirectAutomatically && response.paymentUrl) {
      redirectToPayment(response.paymentUrl)
    }
  }

  const handleRedirectToPayfast = () => {
    if (paymentResponse?.paymentUrl) {
      redirectToPayment(paymentResponse.paymentUrl)
    }
  }

  // If payment URL is generated, show redirect screen
  if (paymentResponse?.paymentUrl) {
    return (
      <Card className={cn("w-full max-w-md mx-auto", className)}>
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <CardTitle>Ready to Pay</CardTitle>
          <CardDescription>
            Your payment has been prepared. Click below to complete your payment securely with PayFast.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-600">Amount</span>
              <span className="font-medium">R{paymentRequest.amount.amount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Reference</span>
              <span className="text-sm font-mono">{paymentRequest.reference}</span>
            </div>
          </div>
          
          <Button 
            onClick={handleRedirectToPayfast}
            className="w-full"
            size="lg"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Continue to PayFast
          </Button>
          
          {onCancel && (
            <Button 
              onClick={onCancel}
              variant="outline"
              className="w-full"
            >
              Cancel Payment
            </Button>
          )}
          
          {showSecurityBadge && (
            <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
              <Lock className="h-3 w-3" />
              <span>Secured by PayFast SSL encryption</span>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("w-full max-w-md mx-auto", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {method === PaymentMethod.CARD ? (
              <CreditCard className="h-5 w-5" />
            ) : method === PaymentMethod.EFT ? (
              <Building2 className="h-5 w-5" />
            ) : method === PaymentMethod.MOBILE_MONEY ? (
              <Smartphone className="h-5 w-5" />
            ) : (
              <Wallet className="h-5 w-5" />
            )}
            PayFast Payment
          </CardTitle>
          {showLogo && (
            <div className="h-8 w-20 bg-contain bg-no-repeat bg-center" 
                 style={{ backgroundImage: "url('/images/payment/payfast-logo.png')" }} />
          )}
        </div>
        <CardDescription>
          {method === PaymentMethod.CARD 
            ? 'Enter your details to pay securely with PayFast'
            : method === PaymentMethod.EFT
            ? 'Complete your EFT payment through PayFast'
            : method === PaymentMethod.MOBILE_MONEY
            ? 'Pay using your mobile money account via PayFast'
            : 'Complete your payment securely with PayFast'
          }
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Customer Information */}
          <div className="space-y-4">
            <h3 className="font-medium text-sm">Customer Information</h3>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className={validationErrors.firstName ? 'border-red-500' : ''}
                />
                {validationErrors.firstName && (
                  <p className="text-xs text-red-500">{validationErrors.firstName}</p>
                )}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className={validationErrors.lastName ? 'border-red-500' : ''}
                />
                {validationErrors.lastName && (
                  <p className="text-xs text-red-500">{validationErrors.lastName}</p>
                )}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={validationErrors.email ? 'border-red-500' : ''}
              />
              {validationErrors.email && (
                <p className="text-xs text-red-500">{validationErrors.email}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                type="tel"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="e.g., 0123456789"
                className={validationErrors.phone ? 'border-red-500' : ''}
              />
              {validationErrors.phone && (
                <p className="text-xs text-red-500">{validationErrors.phone}</p>
              )}
            </div>
          </div>

          <Separator />

          {/* Payment Summary */}
          <div className="bg-gray-50 p-4 rounded-md">
            <div className="flex justify-between mb-2">
              <span>Total Amount:</span>
              <span className="font-bold">R {paymentRequest.amount.amount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-sm text-gray-600">
              <span>Reference:</span>
              <span>{paymentRequest.reference}</span>
            </div>
          </div>

          <Button 
            type="submit"
            className="w-full"
            disabled={loading}
          >
            {loading ? (
              <span className="flex items-center justify-center">
                <Loader2 className="animate-spin h-4 w-4 mr-2" />
                Processing...
              </span>
            ) : (
              'Proceed to Payment'
            )}
          </Button>
        </form>
      </CardContent>
      
      {showSecurityBadge && (
        <CardFooter className="flex flex-col space-y-2 pt-0">
          <div className="flex items-center justify-center gap-2 text-xs text-gray-500 mt-2">
            <Shield className="h-3 w-3" />
            <span>Secured by PayFast payment protection</span>
          </div>
          <div className="flex justify-center gap-2">
            <img 
              src="/images/payment/visa.svg" 
              alt="Visa" 
              className="h-6" 
            />
            <img 
              src="/images/payment/mastercard.svg" 
              alt="Mastercard" 
              className="h-6" 
            />
            <img 
              src="/images/payment/instant-eft.svg" 
              alt="Instant EFT" 
              className="h-6" 
            />
          </div>
        </CardFooter>
      )}
    </Card>
  )
}