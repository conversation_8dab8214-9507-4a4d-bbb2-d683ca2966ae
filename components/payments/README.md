# Payment Components

A comprehensive set of React components for handling payments with PayFast and Ozow gateways in South Africa.

## Features

- 🏦 **Multiple Gateways**: Support for PayFast and Ozow
- 💳 **Payment Methods**: Credit/Debit Cards, EFT, Instant EFT
- 🔒 **Security**: PCI DSS compliant with secure payment processing
- 📱 **Responsive**: Mobile-first design with responsive layouts
- 🎨 **Customizable**: Tailwind CSS styling with theme support
- ⚡ **Real-time**: Instant payment processing with Ozow
- 🔄 **Status Tracking**: Comprehensive payment status management
- 🪝 **Webhooks**: Built-in webhook handling for payment updates

## Components

### PaymentForm
The main component that orchestrates the entire payment flow.

```tsx
import { PaymentForm } from '@/components/payments'

<PaymentForm
  paymentRequest={paymentRequest}
  onSuccess={(response) => console.log('Payment successful:', response)}
  onError={(error) => console.error('Payment failed:', error)}
  onCancel={() => console.log('Payment cancelled')}
/>
```

### PaymentGatewaySelector
Allows users to choose between available payment gateways and methods.

```tsx
import { PaymentGatewaySelector } from '@/components/payments'

<PaymentGatewaySelector
  amount={299.99}
  currency="ZAR"
  selectedGateway={gateway}
  selectedMethod={method}
  onGatewayChange={setGateway}
  onMethodChange={setMethod}
  onProceed={handleProceed}
/>
```

### PayFastPayment
Dedicated component for PayFast payments.

```tsx
import { PayFastPayment } from '@/components/payments'

<PayFastPayment
  paymentRequest={paymentRequest}
  method={PaymentMethod.CARD}
  onSuccess={handleSuccess}
  onError={handleError}
  onCancel={handleCancel}
/>
```

### OzowPayment
Dedicated component for Ozow payments.

```tsx
import { OzowPayment } from '@/components/payments'

<OzowPayment
  paymentRequest={paymentRequest}
  method={PaymentMethod.INSTANT_EFT}
  onSuccess={handleSuccess}
  onError={handleError}
  onCancel={handleCancel}
/>
```

### PaymentStatus
Displays payment status with appropriate styling and actions.

```tsx
import { PaymentStatus } from '@/components/payments'

<PaymentStatus
  status={PaymentStatusEnum.COMPLETED}
  title="Payment Successful!"
  description="Your payment has been processed."
  details={{
    reference: 'PAY-123456',
    transactionId: 'TXN-789012',
    amount: 'R299.99'
  }}
  actions={<Button>Continue</Button>}
/>
```

## Payment Request Structure

```tsx
interface PaymentRequest {
  amount: {
    amount: number
    currency: string
    formatted?: string
  }
  customer: {
    id?: string
    email: string
    firstName: string
    lastName: string
    phone?: string
    address?: PaymentAddress
  }
  items: PaymentItem[]
  metadata: {
    orderId: string
    customerId?: string
    source: string
    [key: string]: any
  }
  returnUrl: string
  cancelUrl: string
  notifyUrl: string
  reference: string
  description: string
}
```

## Setup

### 1. Environment Variables

Add the following environment variables to your `.env.local`:

```env
# PayFast Configuration
PAYFAST_MERCHANT_ID=your_merchant_id
PAYFAST_MERCHANT_KEY=your_merchant_key
PAYFAST_PASSPHRASE=your_passphrase

# Ozow Configuration
OZOW_API_KEY=your_api_key
OZOW_PRIVATE_KEY=your_private_key
OZOW_SITE_CODE=your_site_code

# Security
PAYMENT_ENCRYPTION_KEY=your_encryption_key
```

### 2. API Routes

The components automatically use the following API routes:

- `POST /api/payments/payfast/create` - Create PayFast payment
- `POST /api/payments/ozow/create` - Create Ozow payment
- `POST /api/webhooks/payfast` - PayFast webhook handler
- `POST /api/webhooks/ozow` - Ozow webhook handler

### 3. Webhook URLs

Configure these webhook URLs in your payment gateway dashboards:

- **PayFast**: `https://yourdomain.com/api/webhooks/payfast`
- **Ozow**: `https://yourdomain.com/api/webhooks/ozow`

## Usage Examples

### Basic Payment Flow

```tsx
import { useState } from 'react'
import { PaymentForm, PaymentRequest } from '@/components/payments'

export function CheckoutPage() {
  const [paymentRequest] = useState<PaymentRequest>({
    amount: { amount: 299.99, currency: 'ZAR' },
    customer: {
      email: '<EMAIL>',
      firstName: 'John',
      lastName: 'Doe',
      phone: '0123456789'
    },
    items: [{
      id: '1',
      name: 'Product Name',
      quantity: 1,
      unitPrice: 299.99,
      totalPrice: 299.99
    }],
    metadata: {
      orderId: 'ORDER-123',
      source: 'web'
    },
    returnUrl: '/payment/success',
    cancelUrl: '/payment/cancelled',
    notifyUrl: '/api/webhooks/payment',
    reference: `PAY-${Date.now()}`,
    description: 'Payment for order'
  })

  return (
    <PaymentForm
      paymentRequest={paymentRequest}
      onSuccess={(response) => {
        // Handle successful payment
        if (response.paymentUrl) {
          window.location.href = response.paymentUrl
        }
      }}
      onError={(error) => {
        // Handle payment error
        console.error('Payment failed:', error)
      }}
    />
  )
}
```

### Custom Gateway Selection

```tsx
import { useState } from 'react'
import { PaymentGatewaySelector, PayFastPayment, OzowPayment } from '@/components/payments'
import { PaymentGateway, PaymentMethod } from '@/lib/payments/types'

export function CustomPaymentFlow() {
  const [gateway, setGateway] = useState<PaymentGateway>()
  const [method, setMethod] = useState<PaymentMethod>()
  const [step, setStep] = useState<'select' | 'payment'>('select')

  if (step === 'select') {
    return (
      <PaymentGatewaySelector
        amount={299.99}
        currency="ZAR"
        selectedGateway={gateway}
        selectedMethod={method}
        onGatewayChange={setGateway}
        onMethodChange={setMethod}
        onProceed={() => setStep('payment')}
      />
    )
  }

  if (gateway === PaymentGateway.PAYFAST) {
    return (
      <PayFastPayment
        paymentRequest={paymentRequest}
        method={method!}
        onSuccess={handleSuccess}
        onError={handleError}
        onCancel={() => setStep('select')}
      />
    )
  }

  if (gateway === PaymentGateway.OZOW) {
    return (
      <OzowPayment
        paymentRequest={paymentRequest}
        method={method!}
        onSuccess={handleSuccess}
        onError={handleError}
        onCancel={() => setStep('select')}
      />
    )
  }

  return null
}
```

## Supported Payment Methods

### PayFast
- ✅ Credit/Debit Cards (Visa, Mastercard, American Express)
- ✅ EFT (Electronic Funds Transfer)
- ✅ Instant EFT (selected banks)

### Ozow
- ✅ Instant EFT (real-time processing)
- ✅ Regular EFT
- ✅ All major South African banks

## Supported Banks

### Instant EFT Support
- ABSA Bank ⚡
- Standard Bank ⚡
- First National Bank (FNB) ⚡
- Nedbank ⚡
- Capitec Bank ⚡
- Discovery Bank ⚡
- TymeBank ⚡

### Regular EFT Support
- All South African banks
- African Bank
- Bidvest Bank
- Bank Zero

## Security Features

- 🔒 PCI DSS Compliant
- 🛡️ 256-bit SSL Encryption
- 🔐 Secure signature verification
- 📋 POPIA Compliant (South African data protection)
- 🚫 No sensitive data storage
- ✅ Webhook signature validation

## Styling

Components use Tailwind CSS and can be customized with:

```tsx
<PaymentForm
  className="custom-payment-form"
  // ... other props
/>
```

## Demo

Visit `/payments-demo` to see all components in action with interactive examples.

## Error Handling

The components provide comprehensive error handling:

```tsx
<PaymentForm
  onError={(error) => {
    switch (error) {
      case 'INVALID_AMOUNT':
        // Handle invalid amount
        break
      case 'GATEWAY_ERROR':
        // Handle gateway error
        break
      default:
        // Handle generic error
    }
  }}
/>
```

## TypeScript Support

All components are fully typed with TypeScript interfaces:

```tsx
import type {
  PaymentRequest,
  PaymentResponse,
  PaymentGateway,
  PaymentMethod,
  PaymentStatus
} from '@/components/payments'
```

## Contributing

1. Follow the existing code style
2. Add tests for new features
3. Update documentation
4. Ensure TypeScript compliance

## License

MIT License - see LICENSE file for details.