"use client"

import React from 'react'
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { 
  CreditCard, 
  Banknote, 
  QrCode, 
  Smartphone, 
  Bitcoin,
  Clock,
  Shield,
  Zap
} from "lucide-react"
import { PaymentMethodOption } from '@/lib/ecommerce/hooks/use-payments'
import { cn } from '@/lib/utils'

interface PaymentGatewaySelectorProps {
  paymentMethods: PaymentMethodOption[]
  selectedMethod: string
  onMethodChange: (method: string) => void
  amount?: number
  showFees?: boolean
  className?: string
}

const getPaymentIcon = (iconName: string) => {
  const iconMap: Record<string, React.ComponentType<any>> = {
    'credit-card': CreditCard,
    'card': CreditCard,
    'bank': Banknote,
    'banknote': Banknote,
    'qr-code': QrCode,
    'smartphone': Smartphone,
    'bitcoin': Bitcoin,
    'mobile': Smartphone
  }
  
  return iconMap[iconName] || CreditCard
}

const getProcessingIcon = (processingTime: string) => {
  if (processingTime.toLowerCase().includes('instant')) {
    return <Zap className="h-3 w-3 text-green-500" />
  } else if (processingTime.toLowerCase().includes('minute')) {
    return <Clock className="h-3 w-3 text-blue-500" />
  } else {
    return <Clock className="h-3 w-3 text-orange-500" />
  }
}

export function PaymentGatewaySelector({
  paymentMethods,
  selectedMethod,
  onMethodChange,
  amount = 0,
  showFees = true,
  className
}: PaymentGatewaySelectorProps) {
  const calculateFee = (method: PaymentMethodOption): number => {
    if (!amount) return 0
    const percentageFee = (amount * method.fees.percentage) / 100
    return percentageFee + method.fees.fixed
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const enabledMethods = paymentMethods.filter(method => method.enabled)

  if (enabledMethods.length === 0) {
    return (
      <Card className="p-6">
        <div className="text-center text-muted-foreground">
          <CreditCard className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No payment methods available</p>
          <p className="text-sm">Please contact support for assistance.</p>
        </div>
      </Card>
    )
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center gap-2 mb-4">
        <Shield className="h-5 w-5 text-green-600" />
        <span className="text-sm text-muted-foreground">
          Secure payment processing with industry-standard encryption
        </span>
      </div>

      <RadioGroup 
        value={selectedMethod} 
        onValueChange={onMethodChange}
        className="space-y-3"
      >
        {enabledMethods.map((method) => {
          const IconComponent = getPaymentIcon(method.icon)
          const fee = calculateFee(method)
          const isSelected = selectedMethod === method.id

          return (
            <div key={method.id}>
              <Card className={cn(
                "cursor-pointer transition-all duration-200 hover:shadow-md",
                isSelected && "ring-2 ring-primary ring-offset-2 bg-primary/5"
              )}>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem 
                      value={method.id} 
                      id={method.id}
                      className="mt-1"
                    />
                    <Label 
                      htmlFor={method.id} 
                      className="flex-1 cursor-pointer"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={cn(
                            "p-2 rounded-lg",
                            isSelected ? "bg-primary text-primary-foreground" : "bg-muted"
                          )}>
                            <IconComponent className="h-5 w-5" />
                          </div>
                          
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{method.name}</span>
                              {method.supportedGateways.length > 0 && (
                                <Badge variant="secondary" className="text-xs">
                                  {method.supportedGateways.length} gateway{method.supportedGateways.length > 1 ? 's' : ''}
                                </Badge>
                              )}
                            </div>
                            
                            <p className="text-sm text-muted-foreground">
                              {method.description}
                            </p>
                            
                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                {getProcessingIcon(method.processingTime)}
                                <span>{method.processingTime}</span>
                              </div>
                              
                              {showFees && fee > 0 && (
                                <div className="flex items-center gap-1">
                                  <span>Fee: {formatCurrency(fee)}</span>
                                  {method.fees.percentage > 0 && (
                                    <span className="text-xs opacity-75">
                                      ({method.fees.percentage}%{method.fees.fixed > 0 ? ` + ${formatCurrency(method.fees.fixed)}` : ''})
                                    </span>
                                  )}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </Label>
                  </div>
                </CardContent>
              </Card>
            </div>
          )
        })}
      </RadioGroup>

      {showFees && amount > 0 && (
        <>
          <Separator />
          <div className="text-sm text-muted-foreground">
            <p className="mb-2">Payment processing fees:</p>
            <div className="space-y-1">
              {enabledMethods.map((method) => {
                const fee = calculateFee(method)
                return (
                  <div key={method.id} className="flex justify-between">
                    <span>{method.name}:</span>
                    <span>{fee > 0 ? formatCurrency(fee) : 'Free'}</span>
                  </div>
                )
              })}
            </div>
          </div>
        </>
      )}
    </div>
  )
}