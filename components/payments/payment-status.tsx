"use client"

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle, 
  Loader2,
  CreditCard,
  RefreshCw,
  Ban
} from 'lucide-react'
import { PaymentStatus as PaymentStatusEnum } from '@/lib/payments/types'
import { cn } from '@/lib/utils'

interface PaymentStatusProps {
  status: 'processing' | 'success' | 'error' | 'pending' | 'cancelled' | 'refunded' | PaymentStatusEnum
  title?: string
  description?: string
  details?: {
    reference?: string
    transactionId?: string
    amount?: string
    gateway?: string
    method?: string
    timestamp?: string
    [key: string]: any
  }
  actions?: React.ReactNode
  showSpinner?: boolean
  className?: string
}

const STATUS_CONFIG = {
  [PaymentStatusEnum.PENDING]: {
    icon: Clock,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-100',
    badgeVariant: 'secondary' as const,
    title: 'Payment Pending',
    description: 'Your payment is being processed. Please wait...',
  },
  [PaymentStatusEnum.PROCESSING]: {
    icon: Loader2,
    color: 'text-blue-600',
    bgColor: 'bg-blue-100',
    badgeVariant: 'secondary' as const,
    title: 'Processing Payment',
    description: 'Please wait while we process your payment...',
    animate: true,
  },
  [PaymentStatusEnum.COMPLETED]: {
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    badgeVariant: 'default' as const,
    title: 'Payment Successful',
    description: 'Your payment has been processed successfully.',
  },
  [PaymentStatusEnum.FAILED]: {
    icon: XCircle,
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    badgeVariant: 'destructive' as const,
    title: 'Payment Failed',
    description: 'Your payment could not be processed. Please try again.',
  },
  [PaymentStatusEnum.CANCELLED]: {
    icon: Ban,
    color: 'text-gray-600',
    bgColor: 'bg-gray-100',
    badgeVariant: 'secondary' as const,
    title: 'Payment Cancelled',
    description: 'Your payment has been cancelled.',
  },
  [PaymentStatusEnum.REFUNDED]: {
    icon: RefreshCw,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
    badgeVariant: 'secondary' as const,
    title: 'Payment Refunded',
    description: 'Your payment has been refunded.',
  },
  [PaymentStatusEnum.PARTIALLY_REFUNDED]: {
    icon: RefreshCw,
    color: 'text-purple-600',
    bgColor: 'bg-purple-100',
    badgeVariant: 'secondary' as const,
    title: 'Partially Refunded',
    description: 'Your payment has been partially refunded.',
  },
  [PaymentStatusEnum.EXPIRED]: {
    icon: Clock,
    color: 'text-orange-600',
    bgColor: 'bg-orange-100',
    badgeVariant: 'secondary' as const,
    title: 'Payment Expired',
    description: 'Your payment session has expired. Please try again.',
  },
  // Legacy status mappings
  success: {
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-100',
    badgeVariant: 'default' as const,
    title: 'Payment Successful',
    description: 'Your payment has been processed successfully.',
  },
  error: {
    icon: XCircle,
    color: 'text-red-600',
    bgColor: 'bg-red-100',
    badgeVariant: 'destructive' as const,
    title: 'Payment Failed',
    description: 'Your payment could not be processed. Please try again.',
  }
} as const

export function PaymentStatus({
  status,
  title,
  description,
  details,
  actions,
  showSpinner = false,
  className,
}: PaymentStatusProps) {
  const config = STATUS_CONFIG[status as keyof typeof STATUS_CONFIG]
  
  if (!config) {
    console.warn(`Unknown payment status: ${status}`)
    return (
      <Alert variant="destructive" className={className}>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Unknown payment status: {status}
        </AlertDescription>
      </Alert>
    )
  }

  const Icon = config.icon
  const shouldAnimate = showSpinner
  const displayTitle = title || config.title
  const displayDescription = description || config.description

  return (
    <Card className={cn("w-full max-w-md mx-auto", className)}>
      <CardHeader className="text-center pb-4">
        <div className={cn(
          "mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4",
          config.bgColor
        )}>
          <Icon 
            className={cn(
              "h-8 w-8",
              config.color,
              shouldAnimate && "animate-spin"
            )} 
          />
        </div>
        
        <CardTitle className="text-xl">{displayTitle}</CardTitle>
        
        <CardDescription className="text-base">
          {displayDescription}
        </CardDescription>
        
        <div className="flex justify-center mt-2">
          <Badge variant={config.badgeVariant} className="capitalize">
            {status.replace('_', ' ')}
          </Badge>
        </div>
      </CardHeader>
      
      {details && (
        <CardContent className="pt-0">
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <h4 className="font-medium text-sm text-gray-900">Payment Details</h4>
            
            {details.reference && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Reference</span>
                <span className="text-sm font-mono">{details.reference}</span>
              </div>
            )}
            
            {details.transactionId && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Transaction ID</span>
                <span className="text-sm font-mono">{details.transactionId}</span>
              </div>
            )}
            
            {details.amount && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Amount</span>
                <span className="text-sm font-medium">{details.amount}</span>
              </div>
            )}
            
            {details.gateway && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Gateway</span>
                <span className="text-sm capitalize">{details.gateway}</span>
              </div>
            )}
            
            {details.method && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Method</span>
                <span className="text-sm capitalize">{details.method.replace('_', ' ')}</span>
              </div>
            )}
            
            {details.timestamp && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Time</span>
                <span className="text-sm">{details.timestamp}</span>
              </div>
            )}
            
            {/* Additional details */}
            {Object.entries(details).map(([key, value]) => {
              if (['reference', 'transactionId', 'amount', 'gateway', 'method', 'timestamp'].includes(key)) {
                return null
              }
              
              return (
                <div key={key} className="flex justify-between items-center">
                  <span className="text-sm text-gray-600 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                  </span>
                  <span className="text-sm">{String(value)}</span>
                </div>
              )
            })}
          </div>
        </CardContent>
      )}
      
      {actions && (
        <CardContent className={details ? "pt-4" : ""}>
          {details && <Separator className="mb-4" />}
          {actions}
        </CardContent>
      )}
    </Card>
  )
}

// Convenience components for specific statuses
export function PaymentProcessing({ 
  title, 
  description, 
  details, 
  className 
}: Omit<PaymentStatusProps, 'status'>) {
  return (
    <PaymentStatus
      status="processing"
      title={title}
      description={description}
      details={details}
      showSpinner
      className={className}
    />
  )
}

export function PaymentSuccess({ 
  title, 
  description, 
  details, 
  actions, 
  className 
}: Omit<PaymentStatusProps, 'status'>) {
  return (
    <PaymentStatus
      status="success"
      title={title}
      description={description}
      details={details}
      actions={actions}
      className={className}
    />
  )
}

export function PaymentError({ 
  title, 
  description, 
  details, 
  actions, 
  className 
}: Omit<PaymentStatusProps, 'status'>) {
  return (
    <PaymentStatus
      status="error"
      title={title}
      description={description}
      details={details}
      actions={actions}
      className={className}
    />
  )
}

export function PaymentPending({ 
  title, 
  description, 
  details, 
  className 
}: Omit<PaymentStatusProps, 'status'>) {
  return (
    <PaymentStatus
      status="pending"
      title={title}
      description={description}
      details={details}
      className={className}
    />
  )
}