"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@/components/ui/sheet";
import { Search, Menu, ShoppingBag, User, Heart } from "lucide-react";
import { useCart } from "@/lib/ecommerce/hooks/use-cart";
import { useCartStore2 } from "@/lib/ecommerce/hooks/use-cart-store";
import { useCartStore } from "@/lib/ecommerce/stores/cart-store";
import { NavigationSidebarContent } from "@/components/navigation-sidebar";
import { CartDrawer } from "@/components/storefront/cart/cart-drawer";
import { SearchDialog } from "@/components/search-dialog";
import { NotificationBellMenu } from "@/lib/notifications/components/notification-bell";
import { useSession } from "next-auth/react";
export default function Header() {
  const pathname = usePathname();
  const [sessionId, setSessionId] = useState<string | null>(null);
  // Use the cart store instead of the useCart hook
  const { cart, enhancedCart, setSessionId: setStoreSessionId } = useCartStore2({ autoFetch: true });

  // Debug cart data
  useEffect(() => {
    console.log('Header: Cart data:', cart);
    console.log('Header: Enhanced cart data:', enhancedCart);
  }, [cart, enhancedCart]);
  const { data: session } = useSession();
  const itemCount = cart?.itemCount || 0;
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // Get sessionId from sessionStorage and set it in the cart store
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedSessionId = sessionStorage.getItem('sessionId');
      console.log('Header: Retrieved sessionId from storage:', storedSessionId);

      // If no sessionId exists, create one
      if (!storedSessionId) {
        const newSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        console.log('Header: Creating new sessionId:', newSessionId);
        sessionStorage.setItem('sessionId', newSessionId);
        setSessionId(newSessionId);
      } else {
        setSessionId(storedSessionId);
      }
    }
  }, []); // Remove dependency on setStoreSessionId

  // User data from session
  const user = {
    name: session?.user?.name || "Guest",
    email: session?.user?.email || "",
    avatar: session?.user?.image || "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg",
    isLoggedIn: !!session?.user,
  };

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Clean navigation items - Zara/Selfi style
  const navigationItems = [
    {
      title: "New",
      href: "/products?category=new-arrivals",
    },
    {
      title: "Girls",
      href: "/products?category=girls",
    },
    {
      title: "Boys",
      href: "/products?category=boys",
    },
    {
      title: "Gender Neutral",
      href: "/products?category=gender-neutral",
    },
    {
      title: "Sale",
      href: "/products?category=sale",
    },
  ];

  return (
    <>
      {/* Clean Header - Zara/Selfi Style */}
      <header
        className={cn(
          "sticky top-0 z-50 w-full transition-all duration-200",
          scrolled
            ? "bg-white/95 backdrop-blur-sm border-b border-gray-200"
            : "bg-white border-b border-gray-100"
        )}
      >
        {/* Main Header Content */}
        <div className="container flex h-16 items-center px-4 md:px-6">
          {/* Mobile Navigation */}
          <Sheet open={isMobileNavOpen} onOpenChange={setIsMobileNavOpen}>
            {/* Mobile Menu Button */}
            <SheetTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="md:hidden hover:bg-gray-100 transition-colors mr-2"
              >
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>

            <SheetContent side="left" className="p-0 w-80">
              <SheetHeader className="sr-only">
                <SheetTitle>Navigation Menu</SheetTitle>
              </SheetHeader>
              <div className="h-full">
                <NavigationSidebarContent
                  user={user}
                  onSignOut={() => {
                    console.log("Sign out");
                    setIsMobileNavOpen(false);
                  }}
                />
              </div>
            </SheetContent>
          </Sheet>

          {/* Logo - Using actual SVG */}
          <Link href="/" className="flex items-center">
            <img
              src="/assets/coco-logo-hori.svg"
              alt="Coco Milk Kids"
              className="h-8 w-auto object-contain"
              style={{ maxWidth: "140px" }}
            />
          </Link>

          {/* Desktop Navigation - Clean Zara/Selfi Style */}
          <nav className="hidden md:flex items-center space-x-8 text-sm font-normal flex-1 justify-center">
            {navigationItems.map((item) => (
              <Link
                key={item.title}
                href={item.href}
                className={cn(
                  "relative py-2 transition-colors duration-200 hover-underline",
                  pathname === item.href || pathname.startsWith(item.href)
                    ? "text-black font-medium"
                    : "text-gray-700 hover:text-black"
                )}
              >
                {item.title}
              </Link>
            ))}


          </nav>

          {/* Action Buttons - Clean Zara/Selfi Style */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsSearchOpen(true)}
              className="hover:bg-transparent transition-colors"
              title="Search"
            >
              <Search className="h-5 w-5 text-gray-700" />
              <span className="sr-only">Search</span>
            </Button>

            {/* User Account */}
            <Button
              variant="ghost"
              size="icon"
              asChild
              className="hover:bg-transparent transition-colors"
              title="Account"
            >
              <Link href="/account">
                <User className="h-5 w-5 text-gray-700" />
                <span className="sr-only">Account</span>
              </Link>
            </Button>

            {/* Notifications */}
            {user.isLoggedIn && (
              <NotificationBellMenu
                userId={session?.user?.id}
                enableRealTime={true}
                size="sm"
                variant="ghost"
                maxMenuItems={3}
                onViewAllClick={() => window.location.href = '/account/notifications'}
              />
            )}

            {/* Shopping Cart - Clean Style */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsCartOpen(true)}
              className="hover:bg-transparent transition-colors relative"
              title="Shopping Cart"
            >
              <ShoppingBag className="h-5 w-5 text-gray-700" />
              {itemCount > 0 && (
                <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-black text-[10px] font-medium text-white">
                  {itemCount > 9 ? "9+" : itemCount}
                </span>
              )}
              <span className="sr-only">Shopping cart</span>
            </Button>
          </div>
        </div>
      </header>

      {/* Drawers and Dialogs */}
      <CartDrawer
        isOpen={isCartOpen}
        onOpenChange={setIsCartOpen}
        sessionId={sessionId || undefined}
        trigger={
          <Button
            variant="ghost"
            size="icon"
            className="hidden" // Hide this duplicate trigger
          >
            <ShoppingBag className="h-5 w-5" />
          </Button>
        }
      />
      <SearchDialog open={isSearchOpen} onOpenChange={setIsSearchOpen} />

      {/* Debug button - only visible in development 
      {process.env.NODE_ENV !== 'production' && (
        <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2">
          <Button
            onClick={() => {
              console.log('Debug: Opening cart drawer with sessionId:', sessionId);
              setIsCartOpen(true);
            }}
            variant="default"
            size="sm"
          >
            Debug: Open Cart
          </Button>
          <Button
            onClick={() => {
              console.log('Debug: Refreshing cart with sessionId:', sessionId);
              useCartStore.getState().refetch();
            }}
            variant="outline"
            size="sm"
          >
            Debug: Refresh Cart
          </Button>
        </div>
      )}*/}
    </>
  );
}
