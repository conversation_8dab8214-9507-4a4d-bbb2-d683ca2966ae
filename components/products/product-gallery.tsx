"use client"

import { useState } from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Product } from "@/lib/ecommerce/types/product"

interface ProductGalleryProps {
  product: Product
}

export function ProductGallery({ product }: ProductGalleryProps) {
  const [currentImage, setCurrentImage] = useState(0)
  const images = product.images || []

  const handlePrevious = () => {
    setCurrentImage((current) => (current === 0 ? images.length - 1 : current - 1))
  }

  const handleNext = () => {
    setCurrentImage((current) => (current === images.length - 1 ? 0 : current + 1))
  }

  return (
    <div className="relative">
      {/* Main Image */}
      <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100">
        <Image
          src={images[currentImage]?.url || "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"}
          alt={`${product.title} - Image ${currentImage + 1}`}
          fill
          priority
          className="object-cover object-center"
        />
        
        {images.length > 1 && (
          <>
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white"
              onClick={handlePrevious}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white"
              onClick={handleNext}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </>
        )}
      </div>

      {/* Thumbnail Strip */}
      {images.length > 1 && (
        <div className="mt-4">
          <div className="grid grid-cols-6 gap-2">
            {images.map((image, index) => (
              <button
                key={image.url}
                onClick={() => setCurrentImage(index)}
                className={cn(
                  "relative aspect-square overflow-hidden rounded-md",
                  currentImage === index ? "ring-2 ring-black" : "hover:opacity-75"
                )}
              >
                <Image
                  src={image.url}
                  alt={`${product.title} - Thumbnail ${index + 1}`}
                  fill
                  className="object-cover object-center"
                />
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}