/**
 * Payment Method Selector Component
 * 
 * This component uses the payment-core library to display and select
 * payment methods during checkout.
 */

'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { PaymentMethod, PaymentGateway } from '@/lib/payment-core/types'

interface PaymentMethodOption {
  id: string
  name: string
  description: string
  icon: string
  method: PaymentMethod
  gateways: PaymentGateway[]
}

interface PaymentMethodSelectorProps {
  onSelect: (method: PaymentMethod, gateway: PaymentGateway) => void
  selectedMethod?: PaymentMethod
  selectedGateway?: PaymentGateway
  orderId?: string
}

// Payment method icons
const PAYMENT_ICONS = {
  [PaymentMethod.CARD]: '/images/payment/card.svg',
  [PaymentMethod.INSTANT_EFT]: '/images/payment/eft.svg',
  [PaymentMethod.EFT]: '/images/payment/bank.svg',
  [PaymentMethod.QR_CODE]: '/images/payment/qr.svg',
  [PaymentMethod.WALLET]: '/images/payment/wallet.svg',
  [PaymentMethod.VOUCHER]: '/images/payment/voucher.svg',
  [PaymentMethod.CRYPTO]: '/images/payment/crypto.svg',
  [PaymentMethod.MANUAL]: '/images/payment/manual.svg',
  default: '/images/payment/default.svg'
}

// Payment method descriptions
const PAYMENT_DESCRIPTIONS = {
  [PaymentMethod.CARD]: 'Pay securely with your credit or debit card',
  [PaymentMethod.INSTANT_EFT]: 'Pay directly from your bank account',
  [PaymentMethod.EFT]: 'Make a manual bank transfer',
  [PaymentMethod.QR_CODE]: 'Scan a QR code with your banking app',
  [PaymentMethod.WALLET]: 'Pay with your digital wallet',
  [PaymentMethod.VOUCHER]: 'Redeem a voucher or gift card',
  [PaymentMethod.CRYPTO]: 'Pay with cryptocurrency',
  [PaymentMethod.MANUAL]: 'Pay via manual bank transfer',
  default: 'Select this payment method'
}

export default function PaymentMethodSelector({
  onSelect,
  selectedMethod,
  selectedGateway,
  orderId
}: PaymentMethodSelectorProps) {
  const [methods, setMethods] = useState<PaymentMethodOption[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selected, setSelected] = useState<PaymentMethod | undefined>(selectedMethod)

  useEffect(() => {
    async function loadPaymentMethods() {
      try {
        setLoading(true)
        
        // Fetch payment methods from API
        const response = await fetch('/api/payments/methods')
        const data = await response.json()
        
        if (data.success && data.methods) {
          setMethods(data.methods)
        } else {
          // If API fails, use fallback methods
          setMethods(getFallbackMethods())
        }
      } catch (err) {
        console.error('Error loading payment methods:', err)
        setError('Failed to load payment methods. Using default options.')
        setMethods(getFallbackMethods())
      } finally {
        setLoading(false)
      }
    }
    
    loadPaymentMethods()
  }, [])

  const getFallbackMethods = (): PaymentMethodOption[] => {
    return [
      {
        id: PaymentMethod.CARD,
        name: 'Credit/Debit Card',
        description: PAYMENT_DESCRIPTIONS[PaymentMethod.CARD],
        icon: PAYMENT_ICONS[PaymentMethod.CARD],
        method: PaymentMethod.CARD,
        gateways: [PaymentGateway.PAYFAST, PaymentGateway.YOCO]
      },
      {
        id: PaymentMethod.INSTANT_EFT,
        name: 'Instant EFT',
        description: PAYMENT_DESCRIPTIONS[PaymentMethod.INSTANT_EFT],
        icon: PAYMENT_ICONS[PaymentMethod.INSTANT_EFT],
        method: PaymentMethod.INSTANT_EFT,
        gateways: [PaymentGateway.OZOW]
      },
      {
        id: PaymentMethod.QR_CODE,
        name: 'Scan to Pay',
        description: PAYMENT_DESCRIPTIONS[PaymentMethod.QR_CODE],
        icon: PAYMENT_ICONS[PaymentMethod.QR_CODE],
        method: PaymentMethod.QR_CODE,
        gateways: [PaymentGateway.SNAPSCAN]
      },
      {
        id: PaymentMethod.EFT,
        name: 'Manual EFT',
        description: PAYMENT_DESCRIPTIONS[PaymentMethod.EFT],
        icon: PAYMENT_ICONS[PaymentMethod.EFT],
        method: PaymentMethod.EFT,
        gateways: [PaymentGateway.MANUAL]
      }
    ]
  }

  const handleSelectMethod = (methodId: PaymentMethod) => {
    setSelected(methodId)
    
    // Find the selected method
    const method = methods.find(m => m.method === methodId)
    
    if (method && method.gateways.length > 0) {
      // Use the first gateway for this method
      onSelect(methodId, method.gateways[0])
    }
  }

  const getMethodIcon = (method: PaymentMethod): string => {
    return PAYMENT_ICONS[method] || PAYMENT_ICONS.default
  }

  const getMethodDescription = (method: PaymentMethod): string => {
    return PAYMENT_DESCRIPTIONS[method] || PAYMENT_DESCRIPTIONS.default
  }

  if (loading) {
    return (
      <div className="p-4 border rounded-md">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-3/4"></div>
          <div className="space-y-2">
            <div className="h-12 bg-gray-200 rounded"></div>
            <div className="h-12 bg-gray-200 rounded"></div>
            <div className="h-12 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-4 border border-red-300 bg-red-50 rounded-md text-red-700">
        <p>{error}</p>
        <button 
          className="mt-2 text-sm underline"
          onClick={() => window.location.reload()}
        >
          Try again
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Payment Method</h3>
      
      <div className="grid gap-3">
        {methods.map((method) => (
          <div 
            key={method.id}
            className={`p-4 border rounded-md cursor-pointer transition-colors ${
              selected === method.method 
                ? 'border-primary bg-primary-50' 
                : 'border-gray-200 hover:border-primary/30'
            }`}
            onClick={() => handleSelectMethod(method.method)}
          >
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 relative flex-shrink-0">
                <Image
                  src={method.icon || getMethodIcon(method.method)}
                  alt={method.name}
                  width={40}
                  height={40}
                  className="object-contain"
                  onError={(e) => {
                    // Fallback for missing icons
                    e.currentTarget.src = PAYMENT_ICONS.default
                  }}
                />
              </div>
              <div>
                <div className="font-medium">{method.name}</div>
                <div className="text-sm text-gray-600">
                  {method.description || getMethodDescription(method.method)}
                </div>
              </div>
              <div className="ml-auto">
                <div className={`w-5 h-5 rounded-full border ${
                  selected === method.method 
                    ? 'border-primary bg-primary' 
                    : 'border-gray-300'
                }`}>
                  {selected === method.method && (
                    <svg 
                      xmlns="http://www.w3.org/2000/svg" 
                      viewBox="0 0 24 24" 
                      fill="white" 
                      className="w-5 h-5"
                    >
                      <path 
                        fillRule="evenodd" 
                        d="M19.916 4.626a.75.75 0 01.208 1.04l-9 13.5a.75.75 0 01-1.154.114l-6-6a.75.75 0 011.06-1.06l5.353 5.353 8.493-12.739a.75.75 0 011.04-.208z" 
                        clipRule="evenodd" 
                      />
                    </svg>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {methods.length === 0 && (
        <div className="p-4 border border-yellow-300 bg-yellow-50 rounded-md text-yellow-700">
          No payment methods available. Please contact support.
        </div>
      )}
    </div>
  )
}