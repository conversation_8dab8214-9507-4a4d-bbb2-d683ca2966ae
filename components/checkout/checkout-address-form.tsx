"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { toast } from "sonner"
import { UserAddress } from "@/components/user/address-list"
import { useUserAddresses } from "@/lib/hooks/use-user-addresses"
import { useAuth } from "@/components/auth-provider"

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Card, CardContent } from "@/components/ui/card"
import { Loader2, Plus } from "lucide-react"

// Define the form schema with Zod
const checkoutAddressSchema = z.object({
  useExistingAddress: z.boolean().default(true),
  selectedAddressId: z.string().optional(),
  saveAddress: z.boolean().default(true),
  firstName: z.string().min(1, "First name is required").optional(),
  lastName: z.string().min(1, "Last name is required").optional(),
  company: z.string().optional(),
  address1: z.string().min(1, "Address line 1 is required").optional(),
  address2: z.string().optional(),
  city: z.string().min(1, "City is required").optional(),
  province: z.string().min(1, "Province/State is required").optional(),
  country: z.string().min(1, "Country is required").optional(),
  postalCode: z.string().min(1, "Postal code is required").optional(),
  phone: z.string().optional(),
})
  .refine(
    (data) => {
      // If using existing address, selectedAddressId must be provided
      if (data.useExistingAddress) {
        return !!data.selectedAddressId;
      }
      return true;
    },
    {
      message: "Please select an address",
      path: ["selectedAddressId"],
    }
  )
  .refine(
    (data) => {
      // If not using existing address, these fields are required
      if (!data.useExistingAddress) {
        return !!data.firstName && !!data.lastName && !!data.address1 && !!data.city && !!data.province && !!data.country && !!data.postalCode;
      }
      return true;
    },
    {
      message: "Please fill in all required fields",
      path: ["firstName"],
    }
  );

// Define the form values type from the schema
type CheckoutAddressFormValues = z.infer<typeof checkoutAddressSchema>

// Define the props for the component
interface CheckoutAddressFormProps {
  type: "shipping" | "billing"
  onAddressSelected: (address: UserAddress) => void
  onSubmit?: (address: UserAddress) => void
  initialAddressId?: string
  showSaveOption?: boolean
}

export function CheckoutAddressForm({
  type,
  onAddressSelected,
  onSubmit,
  initialAddressId,
  showSaveOption = true,
}: CheckoutAddressFormProps) {
  const { user } = useAuth()
  const {
    addresses,
    isLoading: addressesLoading,
    addAddress,
  } = useUserAddresses(user?.id)
  
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Filter addresses by type
  const filteredAddresses = addresses.filter(
    addr => addr.type === type || addr.type === "both"
  )

  // Find default address
  const defaultAddress = filteredAddresses.find(addr => addr.isDefault)

  // Initialize the form with react-hook-form and zod resolver
  const form = useForm<CheckoutAddressFormValues>({
    resolver: zodResolver(checkoutAddressSchema),
    defaultValues: {
      useExistingAddress: filteredAddresses.length > 0,
      selectedAddressId: initialAddressId || defaultAddress?.id || "",
      saveAddress: true,
      firstName: "",
      lastName: "",
      company: "",
      address1: "",
      address2: "",
      city: "",
      province: "",
      country: "South Africa",
      postalCode: "",
      phone: "",
    },
  })

  // Watch for changes to useExistingAddress and selectedAddressId
  const useExistingAddress = form.watch("useExistingAddress")
  const selectedAddressId = form.watch("selectedAddressId")

  // When selectedAddressId changes, notify parent component
  useEffect(() => {
    if (useExistingAddress && selectedAddressId) {
      const selectedAddress = addresses.find(addr => addr.id === selectedAddressId)
      if (selectedAddress) {
        onAddressSelected(selectedAddress)
      }
    }
  }, [selectedAddressId, useExistingAddress, addresses, onAddressSelected])

  // Handle form submission
  const handleSubmit = async (data: CheckoutAddressFormValues) => {
    setIsSubmitting(true)
    
    try {
      if (data.useExistingAddress) {
        // Using an existing address
        const selectedAddress = addresses.find(addr => addr.id === data.selectedAddressId)
        if (!selectedAddress) {
          toast.error("Please select a valid address")
          return
        }
        
        if (onSubmit) {
          onSubmit(selectedAddress)
        }
      } else {
        // Creating a new address
        if (!data.firstName || !data.lastName || !data.address1 || !data.city || 
            !data.province || !data.country || !data.postalCode) {
          toast.error("Please fill in all required fields")
          return
        }
        
        const newAddress: Omit<UserAddress, "id"> = {
          firstName: data.firstName,
          lastName: data.lastName,
          company: data.company,
          address1: data.address1,
          address2: data.address2,
          city: data.city,
          province: data.province,
          country: data.country,
          postalCode: data.postalCode,
          phone: data.phone,
          isDefault: false,
          type,
          label: `${type === "shipping" ? "Shipping" : "Billing"} Address`,
        }
        
        // Save the address if requested
        if (data.saveAddress && user) {
          const savedAddress = await addAddress(newAddress)
          if (onSubmit) {
            onSubmit(savedAddress)
          }
          onAddressSelected(savedAddress)
        } else {
          // Create a temporary address (not saved)
          const tempAddress: UserAddress = {
            ...newAddress,
            id: `temp_${Date.now()}`,
          }
          if (onSubmit) {
            onSubmit(tempAddress)
          }
          onAddressSelected(tempAddress)
        }
      }
    } catch (error) {
      console.error(`Error processing ${type} address:`, error)
      toast.error(`Failed to process ${type} address`)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
        {/* Loading State */}
        {addressesLoading && (
          <div className="flex h-20 items-center justify-center">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <span className="ml-2 text-muted-foreground">Loading addresses...</span>
          </div>
        )}

        {/* Address Selection Options */}
        {!addressesLoading && (
          <>
            {filteredAddresses.length > 0 && (
              <FormField
                control={form.control}
                name="useExistingAddress"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Select Address Option</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={(value) => field.onChange(value === "existing")}
                        defaultValue={field.value ? "existing" : "new"}
                        className="flex flex-col space-y-1"
                      >
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="existing" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Use an existing address
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="new" />
                          </FormControl>
                          <FormLabel className="font-normal">
                            Enter a new address
                          </FormLabel>
                        </FormItem>
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Existing Addresses */}
            {useExistingAddress && filteredAddresses.length > 0 && (
              <FormField
                control={form.control}
                name="selectedAddressId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select Address</FormLabel>
                    <div className="grid gap-3 pt-1">
                      {filteredAddresses.map((address) => (
                        <FormItem
                          key={address.id}
                          className="flex flex-row items-start space-x-3 space-y-0"
                        >
                          <FormControl>
                            <RadioGroup
                              onValueChange={field.onChange}
                              defaultValue={field.value}
                              className="flex flex-col space-y-1"
                            >
                              <FormItem className="flex items-center space-x-3 space-y-0">
                                <FormControl>
                                  <RadioGroupItem value={address.id} />
                                </FormControl>
                                <Card className="flex-1">
                                  <CardContent className="p-3">
                                    <div className="space-y-1">
                                      <p className="font-medium">
                                        {address.firstName} {address.lastName}
                                        {address.company && ` • ${address.company}`}
                                      </p>
                                      <p className="text-sm text-muted-foreground">
                                        {address.address1}
                                        {address.address2 && `, ${address.address2}`}
                                      </p>
                                      <p className="text-sm text-muted-foreground">
                                        {address.city}, {address.province} {address.postalCode}
                                      </p>
                                      <p className="text-sm text-muted-foreground">
                                        {address.country}
                                      </p>
                                      {address.phone && (
                                        <p className="text-sm text-muted-foreground">
                                          {address.phone}
                                        </p>
                                      )}
                                    </div>
                                  </CardContent>
                                </Card>
                              </FormItem>
                            </RadioGroup>
                          </FormControl>
                        </FormItem>
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* New Address Form */}
            {(!useExistingAddress || filteredAddresses.length === 0) && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {/* First Name */}
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>First Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="John" {...field} disabled={isSubmitting} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Last Name */}
                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Last Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="Doe" {...field} disabled={isSubmitting} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Company */}
                <FormField
                  control={form.control}
                  name="company"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Company Name" {...field} disabled={isSubmitting} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Address Line 1 */}
                <FormField
                  control={form.control}
                  name="address1"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address Line 1 *</FormLabel>
                      <FormControl>
                        <Input placeholder="123 Main St" {...field} disabled={isSubmitting} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Address Line 2 */}
                <FormField
                  control={form.control}
                  name="address2"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address Line 2 (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="Apt 4B" {...field} disabled={isSubmitting} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {/* City */}
                  <FormField
                    control={form.control}
                    name="city"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City *</FormLabel>
                        <FormControl>
                          <Input placeholder="Cape Town" {...field} disabled={isSubmitting} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Province/State */}
                  <FormField
                    control={form.control}
                    name="province"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Province/State *</FormLabel>
                        <FormControl>
                          <Input placeholder="Western Cape" {...field} disabled={isSubmitting} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {/* Country */}
                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country *</FormLabel>
                        <Select 
                          onValueChange={field.onChange} 
                          defaultValue={field.value}
                          disabled={isSubmitting}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a country" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="South Africa">South Africa</SelectItem>
                            <SelectItem value="Namibia">Namibia</SelectItem>
                            <SelectItem value="Botswana">Botswana</SelectItem>
                            <SelectItem value="Zimbabwe">Zimbabwe</SelectItem>
                            <SelectItem value="Mozambique">Mozambique</SelectItem>
                            <SelectItem value="Lesotho">Lesotho</SelectItem>
                            <SelectItem value="Eswatini">Eswatini</SelectItem>
                            {/* Add more countries as needed */}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Postal Code */}
                  <FormField
                    control={form.control}
                    name="postalCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Postal Code *</FormLabel>
                        <FormControl>
                          <Input placeholder="8001" {...field} disabled={isSubmitting} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Phone */}
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone (Optional)</FormLabel>
                      <FormControl>
                        <Input placeholder="+27 12 345 6789" {...field} disabled={isSubmitting} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Save Address Option */}
                {showSaveOption && user && (
                  <FormField
                    control={form.control}
                    name="saveAddress"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={isSubmitting}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>
                            Save this address for future orders
                          </FormLabel>
                          <FormDescription>
                            This address will be saved to your account
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />
                )}
              </div>
            )}
          </>
        )}

        {/* Submit Button */}
        {onSubmit && (
          <Button type="submit" className="w-full" disabled={isSubmitting || addressesLoading}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Plus className="mr-2 h-4 w-4" />
                {`Use this ${type} address`}
              </>
            )}
          </Button>
        )}
      </form>
    </Form>
  )
}