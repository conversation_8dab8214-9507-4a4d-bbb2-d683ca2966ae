/**
 * Payment Processor Component
 * 
 * This component processes payments using the payment-core library.
 * It supports multiple payment gateways including PayFast.
 */

'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  PaymentGateway, 
  PaymentMethod, 
  PaymentStatus 
} from '@/lib/payment-core/types'
import { Loader2, AlertCircle, CreditCard, Building2, Smartphone } from 'lucide-react'

interface PaymentProcessorProps {
  orderId: string
  method: string
  gateway: string
  amount: number
  currency?: string
  customerEmail?: string
  customerFirstName?: string
  customerLastName?: string
  customerPhone?: string
}

export default function PaymentProcessor({
  orderId,
  method,
  gateway,
  amount,
  currency = 'ZAR',
  customerEmail,
  customerFirstName,
  customerLastName,
  customerPhone
}: PaymentProcessorProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isClient, setIsClient] = useState(false)

  // Ensure we're running on client side before accessing window
  useEffect(() => {
    setIsClient(true)
  }, [])

  const processPayment = async () => {
    if (!isClient) return
    
    try {
      setLoading(true)
      setError(null)
      
      // Call the payment API
      const response = await fetch('/api/checkout/payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          orderId,
          gateway,
          method,
          returnUrl: `${window.location.origin}/checkout/success?orderId=${orderId}`,
          cancelUrl: `${window.location.origin}/checkout/cancel?orderId=${orderId}`,
          notifyUrl: `${window.location.origin}/api/webhooks/payfast/notify`
        })
      })
      
      const data = await response.json()
      
      if (data.success && data.paymentUrl) {
        // Redirect to payment page
        window.location.href = data.paymentUrl
      } else {
        throw new Error(data.error || 'Payment processing failed')
      }
    } catch (err) {
      console.error('Payment error:', err)
      setError(err instanceof Error ? err.message : 'Payment processing failed')
    } finally {
      setLoading(false)
    }
  }

  // Get payment method icon
  const getMethodIcon = () => {
    switch (method) {
      case PaymentMethod.CARD:
        return <CreditCard className="h-5 w-5 mr-2" />
      case PaymentMethod.EFT:
      case PaymentMethod.BANK_TRANSFER:
        return <Building2 className="h-5 w-5 mr-2" />
      case PaymentMethod.MOBILE_MONEY:
        return <Smartphone className="h-5 w-5 mr-2" />
      default:
        return <CreditCard className="h-5 w-5 mr-2" />
    }
  }

  // Get payment method name
  const getMethodName = () => {
    switch (method) {
      case PaymentMethod.CARD:
        return 'Card'
      case PaymentMethod.EFT:
        return 'EFT'
      case PaymentMethod.BANK_TRANSFER:
        return 'Bank Transfer'
      case PaymentMethod.MOBILE_MONEY:
        return 'Mobile Money'
      case PaymentMethod.QR_CODE:
        return 'QR Code'
      default:
        return 'Card'
    }
  }

  return (
    <div className="mt-6">
      <div className="mb-4 p-4 bg-gray-50 rounded-md">
        <div className="flex justify-between">
          <span>Total Amount:</span>
          <span className="font-bold">{currency} {amount.toFixed(2)}</span>
        </div>
        {gateway === PaymentGateway.PAYFAST && (
          <div className="mt-2 pt-2 border-t border-gray-200">
            <div className="flex justify-between text-sm text-gray-600">
              <span>Payment Method:</span>
              <span className="flex items-center">
                {getMethodIcon()}
                {getMethodName()}
              </span>
            </div>
          </div>
        )}
      </div>
      
      {error && (
        <div className="mb-4 p-3 border border-red-300 bg-red-50 rounded-md text-red-700">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>{error}</span>
          </div>
        </div>
      )}
      
      <button
        onClick={processPayment}
        disabled={loading}
        className={`w-full py-3 px-4 rounded-md text-white font-medium ${
          loading 
            ? 'bg-blue-400 cursor-not-allowed' 
            : 'bg-blue-600 hover:bg-blue-700'
        }`}
      >
        {loading ? (
          <span className="flex items-center justify-center">
            <Loader2 className="animate-spin h-5 w-5 mr-2" />
            Processing...
          </span>
        ) : (
          gateway === PaymentGateway.PAYFAST ? `Pay with PayFast` : 'Pay Now'
        )}
      </button>
      
      <p className="mt-4 text-sm text-gray-600 text-center">
        By clicking "Pay Now", you agree to our terms and conditions.
      </p>
      
      {gateway === PaymentGateway.PAYFAST && (
        <div className="mt-2 flex justify-center">
          <div className="flex items-center justify-center gap-2 text-xs text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-3 w-3">
              <rect width="18" height="11" x="3" y="11" rx="2" ry="2" />
              <path d="M7 11V7a5 5 0 0 1 10 0v4" />
            </svg>
            <span>Secured by PayFast SSL encryption</span>
          </div>
        </div>
      )}
    </div>
  )
}