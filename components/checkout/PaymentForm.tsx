'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { PaymentMethod, PaymentGateway } from '@/lib/payment-core/types'
import PaymentMethodSelector from './PaymentMethodSelector'

interface PaymentFormProps {
  amount: number
  currency?: string
  orderId?: string
  customerEmail?: string
  customerName?: string
  items?: Array<{
    id: string
    name: string
    quantity: number
    unitPrice: number
    totalPrice: number
  }>
  onSuccess?: (data: any) => void
  onError?: (error: any) => void
}

export default function PaymentForm({
  amount,
  currency = 'ZAR',
  orderId,
  customerEmail,
  customerName,
  items,
  onSuccess,
  onError
}: PaymentFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null)
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway | null>(null)
  
  const handleSelectMethod = (method: PaymentMethod, gateway: PaymentGateway) => {
    setSelectedMethod(method)
    setSelectedGateway(gateway)
  }
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedMethod || !selectedGateway) {
      setError('Please select a payment method')
      return
    }
    
    try {
      setLoading(true)
      setError(null)
      
      // Split customer name into first and last name
      let firstName = '', lastName = ''
      if (customerName) {
        const nameParts = customerName.split(' ')
        firstName = nameParts[0]
        lastName = nameParts.slice(1).join(' ')
      }
      
      // Create payment request
      const response = await fetch('/api/payments/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          amount,
          currency,
          gateway: selectedGateway,
          paymentMethod: selectedMethod,
          orderId,
          email: customerEmail,
          firstName,
          lastName,
          items,
          returnUrl: `${window.location.origin}/checkout/payment-status?orderId=${orderId}`,
          cancelUrl: `${window.location.origin}/checkout/payment-status?orderId=${orderId}&cancelled=true`
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        // Call success callback if provided
        if (onSuccess) {
          onSuccess(data)
        }
        
        // Redirect to payment URL if available
        if (data.paymentUrl) {
          window.location.href = data.paymentUrl
        } else {
          // Redirect to payment status page
          router.push(`/checkout/payment-status?transactionId=${data.transactionId}&reference=${data.reference}&gateway=${selectedGateway}`)
        }
      } else {
        throw new Error(data.error?.message || 'Payment creation failed')
      }
    } catch (err) {
      console.error('Payment error:', err)
      setError(err instanceof Error ? err.message : 'An error occurred')
      
      if (onError) {
        onError(err)
      }
    } finally {
      setLoading(false)
    }
  }
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-gray-50 p-4 rounded-md mb-4">
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Total Amount:</span>
          <span className="text-xl font-semibold">
            {currency === 'ZAR' ? 'R ' : currency + ' '}
            {amount.toFixed(2)}
          </span>
        </div>
      </div>
      
      <PaymentMethodSelector 
        onSelect={handleSelectMethod}
        selectedMethod={selectedMethod || undefined}
        selectedGateway={selectedGateway || undefined}
        orderId={orderId}
      />
      
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md text-red-600">
          {error}
        </div>
      )}
      
      <button
        type="submit"
        disabled={loading || !selectedMethod}
        className={`w-full py-3 px-4 rounded-md text-white font-medium ${
          loading || !selectedMethod
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-primary hover:bg-primary-dark'
        }`}
      >
        {loading ? (
          <span className="flex items-center justify-center">
            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
          </span>
        ) : (
          'Pay Now'
        )}
      </button>
      
      <div className="text-xs text-gray-500 text-center mt-4">
        By clicking "Pay Now", you agree to our terms and conditions.
        All payments are processed securely.
      </div>
    </form>
  )
}