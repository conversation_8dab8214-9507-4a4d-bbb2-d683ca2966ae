/**
 * PayFast Payment Processor Component
 * 
 * This component processes payments using the PayFast payment gateway.
 */

'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  PaymentGateway, 
  PaymentMethod, 
  PaymentStatus 
} from '@/lib/payment-core/types'
import { usePayfastPayment } from '@/hooks/use-payfast-payment'
import { Loader2, AlertCircle } from 'lucide-react'

interface PayfastPaymentProcessorProps {
  orderId: string
  method: string
  amount: number
  currency?: string
  customerEmail: string
  customerFirstName: string
  customerLastName: string
  customerPhone?: string
  returnUrl?: string
  cancelUrl?: string
  notifyUrl?: string
  onSuccess?: (transactionId: string) => void
  onError?: (error: string) => void
  onStatusChange?: (status: PaymentStatus) => void
}

export default function PayfastPaymentProcessor({
  orderId,
  method,
  amount,
  currency = 'ZAR',
  customerEmail,
  customerFirstName,
  customerLastName,
  customerPhone,
  returnUrl,
  cancelUrl,
  notifyUrl,
  onSuccess,
  onError,
  onStatusChange
}: PayfastPaymentProcessorProps) {
  const router = useRouter()
  const [isClient, setIsClient] = useState(false)
  
  // Convert method string to PaymentMethod enum
  const paymentMethod = method as PaymentMethod
  
  const { 
    createPayment, 
    loading, 
    error, 
    paymentResponse,
    paymentStatus
  } = usePayfastPayment({
    onSuccess: (response) => {
      if (response.transactionId) {
        onSuccess?.(response.transactionId)
      }
    },
    onError: (errorMessage) => {
      onError?.(errorMessage)
    },
    onStatusChange: (status) => {
      onStatusChange?.(status)
    }
  })

  // Ensure we're running on client side before accessing window
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Process payment when component mounts
  useEffect(() => {
    if (isClient) {
      processPayment()
    }
  }, [isClient])

  // Redirect to payment URL when available
  useEffect(() => {
    if (paymentResponse?.paymentUrl) {
      window.location.href = paymentResponse.paymentUrl
    }
  }, [paymentResponse])

  const processPayment = async () => {
    if (!isClient) return
    
    try {
      // Create payment request
      const paymentRequest = {
        amount: {
          amount,
          currency
        },
        customer: {
          email: customerEmail,
          firstName: customerFirstName,
          lastName: customerLastName,
          phone: customerPhone
        },
        items: [
          {
            id: orderId,
            name: `Order #${orderId}`,
            description: `Payment for order #${orderId}`,
            quantity: 1,
            unitPrice: amount,
            totalPrice: amount
          }
        ],
        metadata: {
          orderId,
          source: 'checkout'
        },
        returnUrl: returnUrl || `${window.location.origin}/checkout/success?orderId=${orderId}`,
        cancelUrl: cancelUrl || `${window.location.origin}/checkout/cancel?orderId=${orderId}`,
        notifyUrl: notifyUrl || `${window.location.origin}/api/webhooks/payfast/notify`,
        reference: `ORD-${orderId}`,
        description: `Payment for order #${orderId}`
      }
      
      // Create payment
      await createPayment(paymentRequest, paymentMethod)
      
    } catch (err) {
      console.error('Payment error:', err)
      onError?.(err instanceof Error ? err.message : 'Payment processing failed')
    }
  }

  return (
    <div className="mt-6">
      <div className="mb-4 p-4 bg-gray-50 rounded-md">
        <div className="flex justify-between">
          <span>Total Amount:</span>
          <span className="font-bold">{currency} {amount.toFixed(2)}</span>
        </div>
      </div>
      
      {error && (
        <div className="mb-4 p-3 border border-red-300 bg-red-50 rounded-md text-red-700">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 mr-2" />
            {error}
          </div>
        </div>
      )}
      
      <div className="flex justify-center">
        {loading ? (
          <div className="flex items-center justify-center p-4">
            <Loader2 className="animate-spin h-6 w-6 text-blue-600 mr-2" />
            <span>Preparing secure payment...</span>
          </div>
        ) : paymentResponse ? (
          <div className="text-center p-4">
            <p className="text-sm text-gray-600">
              You will be redirected to PayFast to complete your payment...
            </p>
          </div>
        ) : (
          <button
            onClick={processPayment}
            disabled={loading}
            className={`w-full py-3 px-4 rounded-md text-white font-medium ${
              loading 
                ? 'bg-blue-400 cursor-not-allowed' 
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {loading ? (
              <span className="flex items-center justify-center">
                <Loader2 className="animate-spin h-5 w-5 mr-2" />
                Processing...
              </span>
            ) : (
              'Pay with PayFast'
            )}
          </button>
        )}
      </div>
      
      <p className="mt-4 text-sm text-gray-600 text-center">
        By clicking "Pay with PayFast", you will be redirected to PayFast's secure payment page.
      </p>
    </div>
  )
}