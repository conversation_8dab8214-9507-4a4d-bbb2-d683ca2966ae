/**
 * PayFast Payment Method Selector
 * 
 * Component for selecting PayFast payment methods
 */

'use client'

import { useState } from 'react'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { 
  CreditCard, 
  Building2, 
  Smartphone, 
  QrCode, 
  Wallet 
} from 'lucide-react'
import { PaymentMethod } from '@/lib/payment-core/types'
import { cn } from '@/lib/utils'

interface PayfastMethodSelectorProps {
  selectedMethod: PaymentMethod
  onMethodChange: (method: PaymentMethod) => void
  className?: string
}

interface PaymentMethodOption {
  id: PaymentMethod
  name: string
  description: string
  icon: React.ReactNode
}

export default function PayfastMethodSelector({
  selectedMethod,
  onMethodChange,
  className
}: PayfastMethodSelectorProps) {
  // Define available payment methods
  const paymentMethods: PaymentMethodOption[] = [
    {
      id: PaymentMethod.CARD,
      name: 'Credit/Debit Card',
      description: 'Pay securely with your credit or debit card',
      icon: <CreditCard className="h-5 w-5" />
    },
    {
      id: PaymentMethod.EFT,
      name: 'Instant EFT',
      description: 'Pay directly from your bank account',
      icon: <Building2 className="h-5 w-5" />
    },
    {
      id: PaymentMethod.MOBILE_MONEY,
      name: 'Mobile Payment',
      description: 'Pay using your mobile banking app',
      icon: <Smartphone className="h-5 w-5" />
    },
    {
      id: PaymentMethod.QR_CODE,
      name: 'Scan to Pay',
      description: 'Scan a QR code with your banking app',
      icon: <QrCode className="h-5 w-5" />
    },
    {
      id: PaymentMethod.BANK_TRANSFER,
      name: 'Bank Transfer',
      description: 'Manual bank transfer (may take longer to process)',
      icon: <Wallet className="h-5 w-5" />
    }
  ]

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle>Payment Method</CardTitle>
        <CardDescription>
          Select how you would like to pay
        </CardDescription>
      </CardHeader>
      <CardContent>
        <RadioGroup 
          value={selectedMethod} 
          onValueChange={(value) => onMethodChange(value as PaymentMethod)}
          className="space-y-3"
        >
          {paymentMethods.map((method) => (
            <div key={method.id} className={cn(
              "flex items-center space-x-3 space-y-0 rounded-md border p-4 cursor-pointer transition-colors",
              selectedMethod === method.id 
                ? "border-blue-600 bg-blue-50" 
                : "hover:bg-gray-50"
            )}
            onClick={() => onMethodChange(method.id)}
            >
              <RadioGroupItem value={method.id} id={`method-${method.id}`} />
              <div className="flex flex-1 items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-gray-600">
                    {method.icon}
                  </div>
                  <div>
                    <Label 
                      htmlFor={`method-${method.id}`}
                      className="text-base font-medium cursor-pointer"
                    >
                      {method.name}
                    </Label>
                    <p className="text-sm text-gray-500">
                      {method.description}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </RadioGroup>
      </CardContent>
    </Card>
  )
}