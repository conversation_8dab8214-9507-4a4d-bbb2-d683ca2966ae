/**
 * Payment Status Display Component
 * 
 * Displays the current status of a payment
 */

'use client'

import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertCircle, 
  RefreshCcw, 
  ArrowLeftRight 
} from 'lucide-react'
import { PaymentStatus } from '@/lib/payment-core/types'
import { cn } from '@/lib/utils'

interface PaymentStatusDisplayProps {
  status: PaymentStatus
  transactionId?: string
  reference?: string
  className?: string
  showDetails?: boolean
  showIcon?: boolean
  iconSize?: 'sm' | 'md' | 'lg'
}

export default function PaymentStatusDisplay({
  status,
  transactionId,
  reference,
  className,
  showDetails = true,
  showIcon = true,
  iconSize = 'md'
}: PaymentStatusDisplayProps) {
  // Define status configurations
  const statusConfig = {
    [PaymentStatus.COMPLETED]: {
      label: 'Payment Completed',
      description: 'Your payment has been successfully processed.',
      icon: <CheckCircle />,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    [PaymentStatus.PENDING]: {
      label: 'Payment Pending',
      description: 'Your payment is being processed.',
      icon: <Clock />,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200'
    },
    [PaymentStatus.PROCESSING]: {
      label: 'Payment Processing',
      description: 'Your payment is being processed.',
      icon: <RefreshCcw />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    [PaymentStatus.FAILED]: {
      label: 'Payment Failed',
      description: 'Your payment could not be processed.',
      icon: <XCircle />,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    },
    [PaymentStatus.CANCELLED]: {
      label: 'Payment Cancelled',
      description: 'Your payment was cancelled.',
      icon: <XCircle />,
      color: 'text-gray-600',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200'
    },
    [PaymentStatus.REFUNDED]: {
      label: 'Payment Refunded',
      description: 'Your payment has been refunded.',
      icon: <ArrowLeftRight />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    [PaymentStatus.PARTIALLY_REFUNDED]: {
      label: 'Partially Refunded',
      description: 'Your payment has been partially refunded.',
      icon: <ArrowLeftRight />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    [PaymentStatus.EXPIRED]: {
      label: 'Payment Expired',
      description: 'Your payment session has expired.',
      icon: <AlertCircle />,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200'
    }
  }

  const config = statusConfig[status] || statusConfig[PaymentStatus.PENDING]
  
  // Determine icon size
  const iconSizeClass = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  }[iconSize]

  return (
    <div className={cn(
      "p-4 rounded-md border",
      config.bgColor,
      config.borderColor,
      className
    )}>
      <div className="flex items-start">
        {showIcon && (
          <div className={cn("flex-shrink-0 mr-3", config.color)}>
            <div className={iconSizeClass}>
              {config.icon}
            </div>
          </div>
        )}
        
        <div className="flex-1">
          <h4 className={cn("font-medium", config.color)}>
            {config.label}
          </h4>
          
          {showDetails && (
            <>
              <p className="text-sm mt-1 text-gray-600">
                {config.description}
              </p>
              
              {(transactionId || reference) && (
                <div className="mt-2 text-xs text-gray-500 space-y-1">
                  {transactionId && (
                    <div className="flex justify-between">
                      <span>Transaction ID:</span>
                      <span className="font-mono">{transactionId}</span>
                    </div>
                  )}
                  {reference && (
                    <div className="flex justify-between">
                      <span>Reference:</span>
                      <span className="font-mono">{reference}</span>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}