'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  PaymentMethod, 
  PaymentGateway, 
  PaymentStatus 
} from '@/lib/payment-core/types'
import PayfastMethodSelector from './PayfastMethodSelector'
import { EnhancedPayfastPayment } from '@/components/payments/enhanced-payfast-payment'
import PaymentStatusDisplay from './PaymentStatusDisplay'
import { usePayfastPayment } from '@/hooks/use-payfast-payment'
import { Loader2, AlertCircle } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface EnhancedPaymentFormProps {
  amount: number
  currency?: string
  orderId?: string
  customerEmail?: string
  customerFirstName?: string
  customerLastName?: string
  customerPhone?: string
  items?: Array<{
    id: string
    name: string
    quantity: number
    unitPrice: number
    totalPrice: number
  }>
  onSuccess?: (data: any) => void
  onError?: (error: any) => void
  onStatusChange?: (status: PaymentStatus) => void
}

export default function EnhancedPaymentForm({
  amount,
  currency = 'ZAR',
  orderId = `ORD-${Date.now()}`,
  customerEmail = '',
  customerFirstName = '',
  customerLastName = '',
  customerPhone = '',
  items = [],
  onSuccess,
  onError,
  onStatusChange
}: EnhancedPaymentFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>(PaymentMethod.CARD)
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null)
  const [showPaymentForm, setShowPaymentForm] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // Initialize Payfast payment hook
  const { 
    createPayment, 
    loading: payfastLoading, 
    error: payfastError, 
    paymentResponse, 
    paymentStatus: payfastStatus 
  } = usePayfastPayment({
    onSuccess: (response) => {
      if (onSuccess) onSuccess(response)
      setPaymentStatus(response.status)
      if (onStatusChange) onStatusChange(response.status)
    },
    onError: (errorMessage) => {
      setError(errorMessage)
      if (onError) onError(errorMessage)
    },
    onStatusChange: (status) => {
      setPaymentStatus(status)
      if (onStatusChange) onStatusChange(status)
    }
  })

  // Ensure we're running on client side before accessing window
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Handle method selection
  const handleMethodChange = (method: PaymentMethod) => {
    setSelectedMethod(method)
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedMethod) {
      setError('Please select a payment method')
      return
    }
    
    setShowPaymentForm(true)
  }

  // Create payment request object
  const createPaymentRequest = () => {
    return {
      amount: {
        amount,
        currency
      },
      customer: {
        email: customerEmail,
        firstName: customerFirstName,
        lastName: customerLastName,
        phone: customerPhone
      },
      items: items.length > 0 ? items : [
        {
          id: '1',
          name: `Order #${orderId}`,
          description: `Payment for order #${orderId}`,
          quantity: 1,
          unitPrice: amount,
          totalPrice: amount
        }
      ],
      metadata: {
        orderId,
        source: 'checkout'
      },
      returnUrl: isClient ? `${window.location.origin}/checkout/success?orderId=${orderId}` : `/checkout/success?orderId=${orderId}`,
      cancelUrl: isClient ? `${window.location.origin}/checkout/cancel?orderId=${orderId}` : `/checkout/cancel?orderId=${orderId}`,
      notifyUrl: isClient ? `${window.location.origin}/api/webhooks/payfast/notify` : `/api/webhooks/payfast/notify`,
      reference: `ORD-${orderId}`,
      description: `Payment for order #${orderId}`
    }
  }

  // If payment is in progress or completed, show status
  if (paymentStatus) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Payment Status</CardTitle>
          <CardDescription>
            Current status of your payment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <PaymentStatusDisplay 
            status={paymentStatus}
            transactionId={paymentResponse?.transactionId}
            reference={paymentResponse?.reference}
            showDetails={true}
          />
          
          {paymentStatus === PaymentStatus.PENDING && (
            <div className="mt-4 flex justify-center">
              <button
                onClick={() => {
                  if (paymentResponse?.paymentUrl) {
                    window.location.href = paymentResponse.paymentUrl
                  }
                }}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
              >
                Continue to Payment
              </button>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  // If showing Payfast payment form
  if (showPaymentForm) {
    return (
      <EnhancedPayfastPayment
        paymentRequest={createPaymentRequest()}
        method={selectedMethod}
        onSuccess={onSuccess}
        onError={(errorMessage) => {
          setError(errorMessage)
          setShowPaymentForm(false)
          if (onError) onError(errorMessage)
        }}
        onCancel={() => setShowPaymentForm(false)}
        showLogo={true}
        showSecurityBadge={true}
      />
    )
  }

  // Initial payment method selection form
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-gray-50 p-4 rounded-md mb-4">
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Total Amount:</span>
          <span className="text-xl font-semibold">
            {currency === 'ZAR' ? 'R ' : currency + ' '}
            {amount.toFixed(2)}
          </span>
        </div>
      </div>
      
      <PayfastMethodSelector 
        selectedMethod={selectedMethod}
        onMethodChange={handleMethodChange}
      />
      
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      <button
        type="submit"
        disabled={loading}
        className={`w-full py-3 px-4 rounded-md text-white font-medium ${
          loading 
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-primary hover:bg-primary-dark'
        }`}
      >
        {loading ? (
          <span className="flex items-center justify-center">
            <Loader2 className="animate-spin h-5 w-5 mr-2" />
            Processing...
          </span>
        ) : (
          'Continue to Payment'
        )}
      </button>
      
      <div className="text-xs text-gray-500 text-center mt-4">
        By clicking "Continue to Payment", you agree to our terms and conditions.
        All payments are processed securely by PayFast.
      </div>
    </form>
  )
}