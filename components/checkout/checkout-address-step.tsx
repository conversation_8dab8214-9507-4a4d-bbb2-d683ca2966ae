"use client"

import * as React from "react"
import { useState } from "react"
import { UserAddress } from "@/components/user/address-list"
import { CheckoutAddressForm } from "@/components/checkout/checkout-address-form"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { ArrowRight, Truck, Building } from "lucide-react"

interface CheckoutAddressStepProps {
  onComplete: (shippingAddress: UserAddress, billingAddress: UserAddress) => void
  initialShippingAddressId?: string
  initialBillingAddressId?: string
}

export function CheckoutAddressStep({
  onComplete,
  initialShippingAddressId,
  initialBillingAddressId,
}: CheckoutAddressStepProps) {
  const [shippingAddress, setShippingAddress] = useState<UserAddress | null>(null)
  const [billingAddress, setBillingAddress] = useState<UserAddress | null>(null)
  const [useSameAddress, setUseSameAddress] = useState(true)
  const [step, setStep] = useState<"shipping" | "billing">("shipping")

  // Handle shipping address selection
  const handleShippingAddressSelected = (address: UserAddress) => {
    setShippingAddress(address)
    if (useSameAddress) {
      setBillingAddress(address)
    }
  }

  // Handle billing address selection
  const handleBillingAddressSelected = (address: UserAddress) => {
    setBillingAddress(address)
  }

  // Handle "use same address" checkbox change
  const handleUseSameAddressChange = (checked: boolean) => {
    setUseSameAddress(checked)
    if (checked && shippingAddress) {
      setBillingAddress(shippingAddress)
    } else if (!checked) {
      setBillingAddress(null)
    }
  }

  // Handle shipping address form submission
  const handleShippingSubmit = (address: UserAddress) => {
    setShippingAddress(address)
    if (useSameAddress) {
      setBillingAddress(address)
      // If using same address, complete the step
      onComplete(address, address)
    } else {
      // Move to billing address step
      setStep("billing")
    }
  }

  // Handle billing address form submission
  const handleBillingSubmit = (address: UserAddress) => {
    setBillingAddress(address)
    if (shippingAddress) {
      // Complete the step
      onComplete(shippingAddress, address)
    }
  }

  return (
    <div className="space-y-6">
      {step === "shipping" && (
        <>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Truck className="mr-2 h-5 w-5" />
                Shipping Address
              </CardTitle>
              <CardDescription>
                Where should we send your order?
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CheckoutAddressForm
                type="shipping"
                onAddressSelected={handleShippingAddressSelected}
                onSubmit={handleShippingSubmit}
                initialAddressId={initialShippingAddressId}
              />
            </CardContent>
          </Card>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="useSameAddress"
              checked={useSameAddress}
              onCheckedChange={handleUseSameAddressChange}
            />
            <label
              htmlFor="useSameAddress"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Use same address for billing
            </label>
          </div>

          {!useSameAddress && shippingAddress && (
            <Button
              onClick={() => setStep("billing")}
              className="w-full"
            >
              Continue to Billing Address
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}

          {useSameAddress && shippingAddress && (
            <Button
              onClick={() => onComplete(shippingAddress, shippingAddress)}
              className="w-full"
            >
              Continue to Payment
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </>
      )}

      {step === "billing" && (
        <>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building className="mr-2 h-5 w-5" />
                Billing Address
              </CardTitle>
              <CardDescription>
                Where should we send your invoice?
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CheckoutAddressForm
                type="billing"
                onAddressSelected={handleBillingAddressSelected}
                onSubmit={handleBillingSubmit}
                initialAddressId={initialBillingAddressId}
              />
            </CardContent>
          </Card>

          {billingAddress && (
            <Button
              onClick={() => {
                if (shippingAddress) {
                  onComplete(shippingAddress, billingAddress)
                }
              }}
              className="w-full"
              disabled={!shippingAddress}
            >
              Continue to Payment
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}

          <Button
            variant="outline"
            onClick={() => setStep("shipping")}
            className="w-full"
          >
            Back to Shipping Address
          </Button>
        </>
      )}
    </div>
  )
}