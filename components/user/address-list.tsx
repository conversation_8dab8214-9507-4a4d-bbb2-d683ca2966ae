"use client"

import * as React from "react"
import { useState } from "react"
import { toast } from "sonner"
import { 
  Home, 
  Building2, 
  MapPin, 
  Phone, 
  Edit, 
  Trash2, 
  Plus, 
  Star, 
  <PERSON>Off,
  Loader2
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { UserAddressForm } from "@/components/user/address-form"
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"

// Define the address type
export interface UserAddress {
  id: string
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  province: string
  country: string
  postalCode: string
  phone?: string
  isDefault: boolean
  type: "shipping" | "billing" | "both"
  label?: string
}

interface UserAddressListProps {
  addresses: UserAddress[]
  onAddAddress: (address: Omit<UserAddress, "id">) => Promise<void>
  onUpdateAddress: (id: string, address: Omit<UserAddress, "id">) => Promise<void>
  onDeleteAddress: (id: string) => Promise<void>
  onSetDefaultAddress: (id: string, type: "shipping" | "billing" | "both") => Promise<void>
  isLoading?: boolean
}

export function UserAddressList({
  addresses,
  onAddAddress,
  onUpdateAddress,
  onDeleteAddress,
  onSetDefaultAddress,
  isLoading = false,
}: UserAddressListProps) {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingAddress, setEditingAddress] = useState<UserAddress | null>(null)
  const [deletingAddressId, setDeletingAddressId] = useState<string | null>(null)
  const [actionLoading, setActionLoading] = useState(false)

  // Group addresses by type
  const shippingAddresses = addresses.filter(
    addr => addr.type === "shipping" || addr.type === "both"
  )
  const billingAddresses = addresses.filter(
    addr => addr.type === "billing" || addr.type === "both"
  )

  // Handle adding a new address
  const handleAddAddress = async (data: Omit<UserAddress, "id">) => {
    setActionLoading(true)
    try {
      await onAddAddress(data)
      setIsAddDialogOpen(false)
    } catch (error) {
      console.error("Error adding address:", error)
      toast.error("Failed to add address")
    } finally {
      setActionLoading(false)
    }
  }

  // Handle updating an address
  const handleUpdateAddress = async (data: Omit<UserAddress, "id">) => {
    if (!editingAddress) return
    
    setActionLoading(true)
    try {
      await onUpdateAddress(editingAddress.id, data)
      setEditingAddress(null)
    } catch (error) {
      console.error("Error updating address:", error)
      toast.error("Failed to update address")
    } finally {
      setActionLoading(false)
    }
  }

  // Handle deleting an address
  const handleDeleteAddress = async () => {
    if (!deletingAddressId) return
    
    setActionLoading(true)
    try {
      await onDeleteAddress(deletingAddressId)
      setDeletingAddressId(null)
      toast.success("Address deleted successfully")
    } catch (error) {
      console.error("Error deleting address:", error)
      toast.error("Failed to delete address")
    } finally {
      setActionLoading(false)
    }
  }

  // Handle setting an address as default
  const handleSetDefault = async (id: string, type: "shipping" | "billing" | "both") => {
    setActionLoading(true)
    try {
      await onSetDefaultAddress(id, type)
      toast.success(`Default ${type === "both" ? "" : type + " "}address updated`)
    } catch (error) {
      console.error("Error setting default address:", error)
      toast.error("Failed to set default address")
    } finally {
      setActionLoading(false)
    }
  }

  // Render an address card
  const renderAddressCard = (address: UserAddress, addressType?: "shipping" | "billing") => {
    const isDefaultForType = address.isDefault && 
      (addressType ? address.type === addressType || address.type === "both" : true)
    
    return (
      <Card key={address.id} className="relative">
        {isDefaultForType && (
          <Badge className="absolute right-2 top-2 bg-primary">
            Default {addressType || ""}
          </Badge>
        )}
        <CardHeader>
          <CardTitle className="flex items-center">
            {address.label ? (
              <>
                <span className="mr-2">{address.label}</span>
                {address.type === "shipping" && <MapPin className="h-4 w-4 text-muted-foreground" />}
                {address.type === "billing" && <Building2 className="h-4 w-4 text-muted-foreground" />}
                {address.type === "both" && <Home className="h-4 w-4 text-muted-foreground" />}
              </>
            ) : (
              <>
                {address.type === "shipping" && "Shipping Address"}
                {address.type === "billing" && "Billing Address"}
                {address.type === "both" && "Shipping & Billing Address"}
              </>
            )}
          </CardTitle>
          <CardDescription>
            {address.firstName} {address.lastName}
            {address.company && ` • ${address.company}`}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-1 text-sm">
          <p>{address.address1}</p>
          {address.address2 && <p>{address.address2}</p>}
          <p>
            {address.city}, {address.province} {address.postalCode}
          </p>
          <p>{address.country}</p>
          {address.phone && (
            <p className="flex items-center pt-2">
              <Phone className="mr-2 h-4 w-4" /> {address.phone}
            </p>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setEditingAddress(address)}
              disabled={isLoading || actionLoading}
            >
              <Edit className="mr-2 h-4 w-4" /> Edit
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setDeletingAddressId(address.id)}
              disabled={isLoading || actionLoading}
            >
              <Trash2 className="mr-2 h-4 w-4" /> Delete
            </Button>
          </div>
          {!isDefaultForType && addressType && (
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => handleSetDefault(address.id, addressType === "shipping" ? "shipping" : "billing")}
              disabled={isLoading || actionLoading}
            >
              <Star className="mr-2 h-4 w-4" /> Set as Default
            </Button>
          )}
        </CardFooter>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Add Address Button */}
      <div className="flex justify-end">
        <Button 
          onClick={() => setIsAddDialogOpen(true)}
          disabled={isLoading || actionLoading}
        >
          <Plus className="mr-2 h-4 w-4" /> Add New Address
        </Button>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex h-40 items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <span className="ml-2 text-muted-foreground">Loading addresses...</span>
        </div>
      )}

      {/* No Addresses State */}
      {!isLoading && addresses.length === 0 && (
        <div className="flex h-40 flex-col items-center justify-center rounded-lg border border-dashed p-8 text-center">
          <MapPin className="h-10 w-10 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">No addresses found</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            You haven't added any addresses yet. Add an address to make checkout faster.
          </p>
          <Button 
            className="mt-4" 
            onClick={() => setIsAddDialogOpen(true)}
            disabled={actionLoading}
          >
            <Plus className="mr-2 h-4 w-4" /> Add Address
          </Button>
        </div>
      )}

      {/* Shipping Addresses */}
      {!isLoading && shippingAddresses.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Shipping Addresses</h2>
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {shippingAddresses.map(address => renderAddressCard(address, "shipping"))}
          </div>
        </div>
      )}

      {/* Billing Addresses */}
      {!isLoading && billingAddresses.length > 0 && (
        <div className="space-y-4 pt-6">
          <h2 className="text-xl font-semibold">Billing Addresses</h2>
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {billingAddresses.map(address => renderAddressCard(address, "billing"))}
          </div>
        </div>
      )}

      {/* Add Address Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Address</DialogTitle>
            <DialogDescription>
              Fill in the details below to add a new address to your account.
            </DialogDescription>
          </DialogHeader>
          <UserAddressForm 
            onSubmit={handleAddAddress} 
            onCancel={() => setIsAddDialogOpen(false)}
            isLoading={actionLoading}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Address Dialog */}
      <Dialog open={!!editingAddress} onOpenChange={(open) => !open && setEditingAddress(null)}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Address</DialogTitle>
            <DialogDescription>
              Update the details of your address.
            </DialogDescription>
          </DialogHeader>
          {editingAddress && (
            <UserAddressForm 
              initialData={editingAddress}
              onSubmit={handleUpdateAddress} 
              onCancel={() => setEditingAddress(null)}
              isLoading={actionLoading}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingAddressId} onOpenChange={(open) => !open && setDeletingAddressId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete this address from your account.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={actionLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteAddress}
              disabled={actionLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {actionLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Address"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}