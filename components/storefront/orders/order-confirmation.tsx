"use client"

import { useEffect } from "react"
import { <PERSON><PERSON>ircle, Package, ArrowRight, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import Link from "next/link"
import { useCart } from "@/lib/ecommerce/hooks/use-cart"
import { useOrder } from "@/lib/ecommerce/hooks/use-orders"
import { usePriceFormatter } from "@/hooks/use-price-formatter"

interface OrderConfirmationProps {
  orderId: string
  email?: string
}

export function OrderConfirmation({ orderId, email }: OrderConfirmationProps) {
  const { clearCart } = useCart()
  const { order, loading, error } = useOrder({ orderId, autoFetch: true })
  const { formatPrice } = usePriceFormatter()

  // Clear cart when component unmounts
  useEffect(() => {
    return () => {
      clearCart()
    }
  }, [clearCart])
  
  // Show loading state
  if (loading) {
    return (
      <div className="max-w-3xl mx-auto px-4 py-8 flex flex-col items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Loading order details...</p>
      </div>
    )
  }

  // Show error state
  if (error || !order) {
    return (
      <div className="max-w-3xl mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Unable to load order details</h1>
        <p className="text-muted-foreground mb-6">{error || "Order not found"}</p>
        <Button asChild className="bg-[#012169] hover:bg-[#012169]/90">
          <Link href="/">
            Return to Home
          </Link>
        </Button>
      </div>
    )
  }

  // Get customer email from order or use provided email as fallback
  const customerEmail = order.customer?.email || email || "<EMAIL>"
  
  // Get shipping method details
  const shippingMethod = order.shippingMethod
  const isExpress = shippingMethod?.title?.toLowerCase().includes('express')
  
  // Use estimated delivery date from order if available, otherwise calculate it
  const deliveryDate = shippingMethod?.estimatedDelivery || new Date(
    Date.now() + (isExpress ? 2 : 5) * 24 * 60 * 60 * 1000
  )

  // Format date as "Month Day, Year" using South African locale
  const formattedDate = deliveryDate.toLocaleDateString("en-ZA", {
    month: "long",
    day: "numeric",
    year: "numeric",
  })
  
  return (
    <div className="max-w-3xl mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
          <CheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h1 className="text-2xl md:text-3xl font-bold mb-2">Order Confirmed!</h1>
        <p className="text-muted-foreground">
          Thank you for your purchase. We've sent a confirmation email to {customerEmail}.
        </p>
      </div>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg font-medium">Order Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Order Number:</span>
            <span className="font-medium">{order.orderNumber || orderId}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Order Date:</span>
            <span className="font-medium">
              {new Date(order.createdAt).toLocaleDateString()}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Payment Method:</span>
            <span className="font-medium">
              {order.paymentMethod?.type === 'credit_card' ? 'Credit Card' : 
               order.paymentMethod?.type === 'debit_card' ? 'Debit Card' : 
               order.paymentMethod?.type || 'Credit Card'}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Shipping Method:</span>
            <span className="font-medium">{shippingMethod?.title || (isExpress ? "Express" : "Standard")}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Estimated Delivery:</span>
            <span className="font-medium">{formattedDate}</span>
          </div>
        </CardContent>
      </Card>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg font-medium">Order Summary</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2 max-h-60 overflow-y-auto">
            {order.items?.map((item) => (
              <div key={item.id} className="flex justify-between text-sm">
                <div className="flex items-center">
                  <Package className="h-3 w-3 mr-2 text-muted-foreground" />
                  <span>
                    {item.name} 
                    {item.variantName && `(${item.variantName})`} x {item.quantity}
                  </span>
                </div>
                <span>{formatPrice(item.price)}</span>
              </div>
            ))}
          </div>
          
          <Separator />
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Subtotal</span>
              <span>{order.subtotal}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>Shipping</span>
              <span>{order.shipping}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span>VAT (15%)</span>
              <span>{order.tax}</span>
            </div>
          </div>

          <Separator />

          <div className="flex justify-between font-medium">
            <span>Total</span>
            <span>{order.total?.amount}</span>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" asChild>
            <Link href="/account/orders">
              View All Orders
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/account/orders/track">
              Track Order <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </CardFooter>
      </Card>
      
      <div className="flex justify-center">
        <Button asChild className="bg-[#012169] hover:bg-[#012169]/90">
          <Link href="/">
            Continue Shopping
          </Link>
        </Button>
      </div>
    </div>
  )
}
