import Link from "next/link"
import { ProductCard } from "@/components/storefront/products/product-card"
import { getProducts } from "@/lib/products"

export async function FeaturedCollection() {
  const products = await getProducts({ sort: "featured" })
  const featuredProducts = products.slice(0, 4)

  return (
    <section className="py-16 md:py-24">
      <div className="flex flex-col md:flex-row justify-between items-baseline mb-12">
        <h2 className="text-2xl md:text-3xl font-light font-montserrat tracking-wide">Featured Collection</h2>
        <Link href="/products" className="text-sm text-muted-foreground hover:text-foreground mt-2 md:mt-0 btn-minimal">
          View All Products
        </Link>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
        {featuredProducts.map((product, index) => (
          <div key={product.id} className="fade-in" style={{ animationDelay: `${0.1 * index}s` }}>
            <ProductCard product={product} />
          </div>
        ))}
      </div>
    </section>
  )
}
