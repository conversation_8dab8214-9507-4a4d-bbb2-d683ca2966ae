'use client'

import { useState, useEffect } from "react"
import { ProductCard } from "@/components/storefront/products/product-card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { RefreshCw, AlertCircle, Package, ArrowRight } from "lucide-react"
import Link from "next/link"
import { Product } from "@/lib/ecommerce/types"

interface NewArrivalsSectionProps {
  limit?: number
  showViewAll?: boolean
  className?: string
}

// Fallback products when API is not available
const getFallbackProducts = (): Product[] => [
  {
    id: 'fallback-1',
    createdAt: new Date(),
    updatedAt: new Date(),
    title: 'Premium Cotton T-Shirt',
    slug: 'premium-cotton-tshirt',
    description: 'Soft, comfortable cotton t-shirt perfect for everyday wear.',
    vendor: 'Coco Milk Kids',
    productType: 'Clothing',
    handle: 'premium-cotton-tshirt',
    status: 'active',
    price: { amount: 299, currency: 'ZAR' },
    compareAtPrice: { amount: 399, currency: 'ZAR' },
    trackQuantity: true,
    continueSellingWhenOutOfStock: false,
    inventoryQuantity: 50,
    images: [{
      id: 'img-1',
      url: '/assets/images/cocomilk_kids-20220927_125643-1359487094.jpg',
      altText: 'Premium Cotton T-Shirt',
      position: 0
    }],
    hasVariants: false,
    variants: [],
    options: [],
    categories: [],
    tags: [{ id: 'tag-1', name: 'new-arrival', slug: 'new-arrival' }],
    collections: [],
    seo: { title: 'Premium Cotton T-Shirt', description: 'Soft cotton t-shirt', keywords: [] },
    metafields: {},
    isGiftCard: false,
    requiresShipping: true,
    isTaxable: true,
    isVisible: true,
    isAvailable: true,
    availableForSale: true
  },
  {
    id: 'fallback-2',
    createdAt: new Date(),
    updatedAt: new Date(),
    title: 'Denim Overalls',
    slug: 'denim-overalls',
    description: 'Durable denim overalls with adjustable straps.',
    vendor: 'Coco Milk Kids',
    productType: 'Clothing',
    handle: 'denim-overalls',
    status: 'active',
    price: { amount: 599, currency: 'ZAR' },
    trackQuantity: true,
    continueSellingWhenOutOfStock: false,
    inventoryQuantity: 30,
    images: [{
      id: 'img-2',
      url: '/assets/images/cocomilk_kids-20210912_114630-3065525289.jpg',
      altText: 'Denim Overalls',
      position: 0
    }],
    hasVariants: false,
    variants: [],
    options: [],
    categories: [],
    tags: [{ id: 'tag-2', name: 'new-arrival', slug: 'new-arrival' }],
    collections: [],
    seo: { title: 'Denim Overalls', description: 'Durable denim overalls', keywords: [] },
    metafields: {},
    isGiftCard: false,
    requiresShipping: true,
    isTaxable: true,
    isVisible: true,
    isAvailable: true,
    availableForSale: true
  },
  {
    id: 'fallback-3',
    createdAt: new Date(),
    updatedAt: new Date(),
    title: 'Colorful Summer Dress',
    slug: 'colorful-summer-dress',
    description: 'Bright and cheerful summer dress for special occasions.',
    vendor: 'Coco Milk Kids',
    productType: 'Clothing',
    handle: 'colorful-summer-dress',
    status: 'active',
    price: { amount: 449, currency: 'ZAR' },
    trackQuantity: true,
    continueSellingWhenOutOfStock: false,
    inventoryQuantity: 25,
    images: [{
      id: 'img-3',
      url: '/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg',
      altText: 'Colorful Summer Dress',
      position: 0
    }],
    hasVariants: false,
    variants: [],
    options: [],
    categories: [],
    tags: [{ id: 'tag-3', name: 'new-arrival', slug: 'new-arrival' }],
    collections: [],
    seo: { title: 'Colorful Summer Dress', description: 'Bright summer dress', keywords: [] },
    metafields: {},
    isGiftCard: false,
    requiresShipping: true,
    isTaxable: true,
    isVisible: true,
    isAvailable: true,
    availableForSale: true
  }
]

export function NewArrivalsSection({
  limit = 6,
  showViewAll = true,
  className = ""
}: NewArrivalsSectionProps) {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const [usingFallback, setUsingFallback] = useState(false)

  const fetchNewArrivals = async () => {
    try {
      setLoading(true)
      setError(null)

      // Fetch new arrivals from the API with timeout
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 second timeout

      const response = await fetch(
        `/api/e-commerce/products?tags=new-arrival&limit=${limit}&sortBy=createdAt&sortOrder=desc&status=active`,
        {
          signal: controller.signal,
          headers: {
            'Cache-Control': 'max-age=300', // 5 minute cache
          }
        }
      )

      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        // Transform API data to match Product interface
        const transformedProducts = (data.data || []).map((product: any): Product => ({
          // Base entity properties
          id: product.id,
          createdAt: new Date(product.createdAt || Date.now()),
          updatedAt: new Date(product.updatedAt || Date.now()),

          // Core product properties
          title: product.title,
          slug: product.slug || product.handle,
          description: product.description || '',
          descriptionHtml: product.descriptionHtml,
          vendor: product.vendor || 'Coco Milk Kids',
          productType: product.productType,
          handle: product.handle || product.slug,
          status: product.status || 'active',
          publishedAt: product.publishedAt ? new Date(product.publishedAt) : undefined,

          // Pricing
          price: product.price || { amount: 0, currency: 'ZAR' },
          compareAtPrice: product.compareAtPrice,
          costPerItem: product.costPerItem,

          // Inventory
          trackQuantity: product.trackQuantity ?? true,
          continueSellingWhenOutOfStock: product.continueSellingWhenOutOfStock ?? false,
          inventoryQuantity: product.inventoryQuantity || 0,

          // Physical properties
          weight: product.weight,
          weightUnit: product.weightUnit || 'kg',
          dimensions: product.dimensions,

          // Media
          images: product.images?.map((img: any) => ({
            id: img.id || `img-${Math.random()}`,
            url: img.url || img.src,
            altText: img.alt || img.altText || product.title,
            position: img.position || 0,
            width: img.width,
            height: img.height
          })) || [],
          featuredImage: product.featuredImage,

          // Variants and options
          hasVariants: product.hasVariants || false,
          variants: product.variants || [],
          options: product.options || [],

          // Organization
          categories: product.categories || [],
          tags: product.tags?.map((tag: any) => ({
            id: tag.id || tag,
            name: typeof tag === 'string' ? tag : tag.name,
            slug: typeof tag === 'string' ? tag.toLowerCase().replace(/\s+/g, '-') : tag.slug
          })) || [],
          collections: product.collections || [],

          // SEO
          seo: product.seo || {
            title: product.title,
            description: product.description?.substring(0, 160) || '',
            keywords: []
          },

          // Reviews
          reviews: product.reviews,
          averageRating: product.averageRating,
          reviewCount: product.reviewCount,

          // Metadata
          metafields: product.metafields,
          isGiftCard: product.isGiftCard || false,
          requiresShipping: product.requiresShipping ?? true,
          isTaxable: product.isTaxable ?? true,

          // Visibility
          isVisible: product.isVisible ?? true,
          isAvailable: product.isAvailable ?? true,
          availableForSale: product.availableForSale ?? true,

          // Related products
          relatedProductIds: product.relatedProductIds,
          crossSellProductIds: product.crossSellProductIds,
          upSellProductIds: product.upSellProductIds
        }))

        setProducts(transformedProducts)
        setUsingFallback(false)
      } else {
        // API returned error, use fallback data
        console.warn('API returned error, using fallback data:', data.error)
        setProducts(getFallbackProducts().slice(0, limit))
        setUsingFallback(true)
      }
    } catch (err) {
      console.error('Error fetching new arrivals:', err)

      // Use fallback data when API fails completely
      console.warn('API request failed, using fallback data:', err)
      setProducts(getFallbackProducts().slice(0, limit))
      setUsingFallback(true)
      setError(null) // Clear error since we have fallback data
    } finally {
      setLoading(false)
    }
  }

  const handleRetry = () => {
    setRetryCount(prev => prev + 1)
    setUsingFallback(false) // Reset fallback state on retry
    setError(null) // Clear any previous errors

    // Track retry attempts for analytics
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'new_arrivals_retry', {
        event_category: 'engagement',
        event_label: 'retry_attempt',
        value: retryCount + 1
      })
    }
  }

  // Track successful loads for analytics
  useEffect(() => {
    if (products.length > 0 && typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'new_arrivals_loaded', {
        event_category: 'content',
        event_label: 'products_loaded',
        value: products.length
      })
    }
  }, [products.length])

  useEffect(() => {
    fetchNewArrivals()
  }, [limit, retryCount])

  // Loading State
  if (loading) {
    return (
      <section className={`py-16 md:py-24 bg-white ${className}`}>
        <div className="container px-4 md:px-6 max-w-7xl">
          {/* Header Skeleton */}
          <div className="mb-12 md:mb-16">
            <Skeleton className="h-8 w-32 mb-8" />
          </div>

          {/* Product Grid Skeleton */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 md:gap-6">
            {Array.from({ length: limit }).map((_, index) => (
              <div key={index} className="space-y-4">
                <Skeleton className="aspect-[3/4] w-full rounded-lg" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </div>
            ))}
          </div>

          {/* View All Skeleton */}
          {showViewAll && (
            <div className="mt-12 md:mt-16 text-center">
              <Skeleton className="h-6 w-40 mx-auto" />
            </div>
          )}
        </div>
      </section>
    )
  }

  // Error State
  if (error) {
    return (
      <section className={`py-16 md:py-24 bg-white ${className}`}>
        <div className="container px-4 md:px-6 max-w-7xl">
          <div className="mb-12 md:mb-16">
            <h2 className="text-2xl md:text-3xl font-light tracking-wide mb-8">
              NEW IN
            </h2>
          </div>

          <div className="max-w-md mx-auto text-center">
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-left">
                <strong>Unable to load new arrivals</strong>
                <br />
                {error}
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              <Button
                onClick={handleRetry}
                variant="outline"
                className="w-full"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>

              <p className="text-sm text-muted-foreground">
                If the problem persists, please{" "}
                <Link href="/contact" className="underline hover:no-underline">
                  contact support
                </Link>
              </p>
            </div>
          </div>
        </div>
      </section>
    )
  }

  // Empty State
  if (products.length === 0) {
    return (
      <section className={`py-16 md:py-24 bg-white ${className}`}>
        <div className="container px-4 md:px-6 max-w-7xl">
          <div className="mb-12 md:mb-16">
            <h2 className="text-2xl md:text-3xl font-light tracking-wide mb-8">
              NEW IN
            </h2>
          </div>

          <div className="max-w-md mx-auto text-center">
            <div className="mb-6">
              <Package className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No New Arrivals Yet</h3>
              <p className="text-muted-foreground">
                We're working on bringing you the latest styles. Check back soon for new arrivals!
              </p>
            </div>

            <div className="space-y-4">
              <Button
                onClick={handleRetry}
                variant="outline"
                className="w-full"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </Button>

              <Button asChild className="w-full">
                <Link href="/products">
                  Browse All Products
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    )
  }

  // Success State with Products
  return (
    <section className={`py-16 md:py-24 bg-white ${className}`}>
      <div className="container px-4 md:px-6 max-w-7xl">
        {/* Zara-style minimal header */}
        <div className="mb-12 md:mb-16">
          <h2 className="text-2xl md:text-3xl font-light tracking-wide mb-8">
            NEW IN
          </h2>
          <p className="text-sm text-muted-foreground max-w-2xl">
            Discover our latest collection of premium children's clothing.
            Fresh styles added weekly.
          </p>
          {usingFallback && (
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-xs text-blue-700">
                📦 Showing sample products - Our catalog is being updated
              </p>
            </div>
          )}
        </div>

        {/* Zara-style product grid */}
        <div
          className="grid grid-cols-2 md:grid-cols-3 gap-4 md:gap-6"
          role="grid"
          aria-label="New arrivals product grid"
        >
          {products.map((product, index) => (
            <div
              key={product.id}
              className="group animate-in fade-in-0 duration-500"
              style={{ animationDelay: `${index * 100}ms` }}
              role="gridcell"
              aria-label={`Product ${index + 1} of ${products.length}: ${product.title}`}
            >
              <ProductCard product={product} />
            </div>
          ))}
        </div>

        {/* Zara-style view all link */}
        {showViewAll && (
          <div className="mt-12 md:mt-16 text-center">
            <Link
              href="/products?tags=new-arrival"
              className="inline-flex items-center text-sm font-medium tracking-wider border-b border-black pb-1 hover:border-gray-400 transition-colors duration-200 group"
            >
              VIEW ALL NEW IN
              <ArrowRight className="ml-2 h-3 w-3 transition-transform group-hover:translate-x-1" />
            </Link>
          </div>
        )}

        {/* Product count indicator */}
        <div className="mt-8 text-center">
          <p className="text-xs text-muted-foreground">
            Showing {products.length} of our latest arrivals
          </p>
        </div>
      </div>
    </section>
  )
}
