"use client"

import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"

export function HeroSection() {
  return (
    <section className="relative h-screen overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image
          src="/assets/images/cocomilk_kids-20210912_114630-3065525289.jpg"
          alt="Coco Milk Kids Collection"
          fill
          priority
          className="object-cover"
        />
        <div className="absolute inset-0 bg-black/20" />
      </div>

      {/* Zara-style minimal content */}
      <div className="absolute inset-0 flex items-end">
        <div className="w-full p-8 md:p-16">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="max-w-md"
          >
            <h1 className="text-white text-3xl md:text-4xl lg:text-5xl font-light leading-tight mb-6">
              KIDS
              <br />
              COLLECTION
            </h1>
            <div className="space-y-4">
              <Link
                href="/products"
                className="inline-block bg-white text-black px-8 py-3 text-sm font-medium tracking-wider hover:bg-gray-100 transition-colors duration-200"
              >
                SHOP NOW
              </Link>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
