"use client"

import { memo, useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { Check } from "lucide-react"
import { useCheckoutStore } from "@/lib/ecommerce/stores/checkout-store"
import { motion, AnimatePresence } from "framer-motion"

// Create a component that doesn't use hooks for rendering the steps
const StepIndicator = memo(function StepIndicator({ 
  step, 
  onStepClick,
  index,
  totalSteps
}: { 
  step: any, 
  onStepClick: (id: string) => void,
  index: number,
  totalSteps: number
}) {
  return (
    <div className="flex flex-col items-center relative">
      {/* Connector line */}
      {index < totalSteps - 1 && (
        <div className="absolute top-3 left-[50%] w-full h-[1px] bg-gray-200" style={{ transform: 'translateX(50%)' }} />
      )}
      
      <motion.button
        key={step.id}
        onClick={() => step.canAccess && onStepClick(step.id)}
        disabled={!step.canAccess}
        className={cn(
          "flex flex-col items-center group z-10",
          step.canAccess ? "cursor-pointer" : "cursor-not-allowed opacity-70"
        )}
        whileTap={{ scale: step.canAccess ? 0.95 : 1 }}
      >
        {/* Circle indicator */}
        <motion.div
          className={cn(
            "w-6 h-6 rounded-full flex items-center justify-center mb-2",
            step.completed
              ? "bg-primary text-white"
              : step.current
              ? "bg-white border border-primary text-primary"
              : "bg-white border border-gray-200 text-gray-400"
          )}
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ 
            scale: 1, 
            opacity: 1,
            backgroundColor: step.completed ? "var(--primary)" : step.current ? "white" : "white",
            borderColor: step.completed ? "var(--primary)" : step.current ? "var(--primary)" : "var(--gray-200)"
          }}
          transition={{ 
            duration: 0.3,
            type: "spring",
            stiffness: 300,
            damping: 20
          }}
        >
          <AnimatePresence mode="wait">
            {step.completed ? (
              <motion.div
                key="check"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <Check className="w-3 h-3" />
              </motion.div>
            ) : (
              <motion.div
                key="circle"
                initial={{ scale: 0, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className={cn(
                  "w-2 h-2 rounded-full",
                  step.current ? "bg-primary" : "bg-gray-300"
                )}
              />
            )}
          </AnimatePresence>
        </motion.div>
        
        {/* Step title */}
        <motion.span
          className={cn(
            "text-xs font-medium",
            step.current
              ? "text-primary"
              : step.completed
              ? "text-gray-700"
              : "text-gray-400"
          )}
          animate={{ 
            color: step.current 
              ? "var(--primary)" 
              : step.completed 
              ? "var(--gray-700)" 
              : "var(--gray-400)"
          }}
          transition={{ duration: 0.3 }}
        >
          {step.title}
        </motion.span>
      </motion.button>
    </div>
  )
});

// Create a component that doesn't use hooks for the progress bar
const ProgressBar = memo(function ProgressBar({ percentage }: { percentage: number }) {
  return (
    <div className="relative mb-10 mt-2">
      <div className="absolute top-1/2 left-0 w-full h-[1px] bg-gray-100 -translate-y-1/2" />
      <motion.div 
        className="absolute top-1/2 left-0 h-[2px] bg-primary -translate-y-1/2"
        initial={{ width: 0 }}
        animate={{ width: `${percentage}%` }}
        transition={{ 
          duration: 0.5,
          ease: "easeInOut"
        }}
      />
    </div>
  );
});

// Create a component that doesn't use hooks for the mobile progress
const MobileProgress = memo(function MobileProgress({ 
  currentStep, 
  totalSteps, 
  currentTitle,
  percentage 
}: { 
  currentStep: number, 
  totalSteps: number, 
  currentTitle: string,
  percentage: number 
}) {
  return (
    <motion.div 
      className="md:hidden text-center mt-6"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2, duration: 0.3 }}
    >
      <motion.p 
        className="text-xs text-gray-500 mb-2"
        layout
      >
        Step {currentStep} of {totalSteps}
      </motion.p>
      <motion.p
        className="text-sm font-medium text-gray-700 mb-3"
        layout
        key={currentTitle}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        {currentTitle}
      </motion.p>
      <div className="w-full bg-gray-100 rounded-full h-1 overflow-hidden">
        <motion.div 
          className="bg-primary h-1 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${percentage}%` }}
          transition={{ 
            duration: 0.5,
            ease: "easeInOut"
          }}
        />
      </div>
    </motion.div>
  );
});

// Main component with local state
export function CheckoutProgress() {
  // Use local state to store the checkout state
  const [checkoutState, setCheckoutState] = useState({
    steps: [],
    currentStep: '',
    progressPercentage: 0,
    currentStepIndex: 0,
    currentStepTitle: ''
  });
  
  // Get the setCurrentStep function from the store - using direct access to avoid hooks
  const setCurrentStep = useCheckoutStore.getState().setCurrentStep;
  
  // Effect to sync the local state with the store
  useEffect(() => {
    // Get the current state from the store
    const state = useCheckoutStore.getState();
    
    // Calculate the progress percentage
    const completedSteps = state.steps.filter(step => step.completed).length;
    const progressPercentage = Math.round((completedSteps / state.steps.length) * 100);
    
    // Find the current step index and title
    const currentStepIndex = state.steps.findIndex(s => s.current) + 1;
    const currentStepTitle = state.steps.find(s => s.current)?.title || '';
    
    // Update the local state
    setCheckoutState({
      steps: state.steps,
      currentStep: state.currentStep,
      progressPercentage,
      currentStepIndex,
      currentStepTitle
    });
    
    // Subscribe to store changes
    const unsubscribe = useCheckoutStore.subscribe((state) => {
      const completedSteps = state.steps.filter(step => step.completed).length;
      const progressPercentage = Math.round((completedSteps / state.steps.length) * 100);
      const currentStepIndex = state.steps.findIndex(s => s.current) + 1;
      const currentStepTitle = state.steps.find(s => s.current)?.title || '';
      
      setCheckoutState({
        steps: state.steps,
        currentStep: state.currentStep,
        progressPercentage,
        currentStepIndex,
        currentStepTitle
      });
    });
    
    // Cleanup subscription
    return () => {
      unsubscribe();
    };
  }, []);
  
  // Handle step click - using direct store access to avoid re-renders
  const handleStepClick = (stepId: string) => {
    useCheckoutStore.getState().setCurrentStep(stepId);
  };
  
  return (
    <motion.div 
      className="w-full py-6"
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      {/* Desktop view */}
      <div className="hidden md:block">
        {/* Step indicators */}
        <div className="relative flex justify-between px-6 mb-2">
          {checkoutState.steps.map((step, index) => (
            <StepIndicator 
              key={step.id} 
              step={step} 
              onStepClick={handleStepClick}
              index={index}
              totalSteps={checkoutState.steps.length}
            />
          ))}
        </div>
        
        {/* Progress bar */}
        <ProgressBar percentage={checkoutState.progressPercentage} />
      </div>
      
      {/* Mobile progress text */}
      <MobileProgress 
        currentStep={checkoutState.currentStepIndex}
        totalSteps={checkoutState.steps.length}
        currentTitle={checkoutState.currentStepTitle}
        percentage={checkoutState.progressPercentage}
      />
    </motion.div>
  )
}