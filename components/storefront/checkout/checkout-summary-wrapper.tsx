"use client"

import { memo, useState, useEffect } from "react"
import { useCheckoutStore } from "@/lib/ecommerce/stores/checkout-store"
import { CheckoutSummaryContent } from "./checkout-summary-content"

interface CheckoutSummaryWrapperProps {
  sessionId?: string | null;
}

export const CheckoutSummaryWrapper = memo(function CheckoutSummaryWrapper({ 
  sessionId 
}: CheckoutSummaryWrapperProps) {
  // Use local state to store checkout summary data
  const [summaryState, setSummaryState] = useState({
    showOrderSummary: false,
    selectedShippingMethod: 'standard',
    selectedPaymentMethod: '',
    giftWrap: false,
    giftWrapMessage: '',
    appliedPromoCodes: [] as string[],
    currentStep: 'shipping'
  });
  
  // Effect to sync with store and subscribe to changes
  useEffect(() => {
    // Get initial state
    const state = useCheckoutStore.getState();
    
    setSummaryState({
      showOrderSummary: state.showOrderSummary,
      selectedShippingMethod: state.selectedShippingMethod,
      selectedPaymentMethod: state.selectedPaymentMethod,
      giftWrap: state.giftWrap,
      giftWrapMessage: state.giftWrapMessage,
      appliedPromoCodes: state.appliedPromoCodes,
      currentStep: state.currentStep
    });
    
    // Subscribe to store changes
    const unsubscribe = useCheckoutStore.subscribe((state) => {
      setSummaryState({
        showOrderSummary: state.showOrderSummary,
        selectedShippingMethod: state.selectedShippingMethod,
        selectedPaymentMethod: state.selectedPaymentMethod,
        giftWrap: state.giftWrap,
        giftWrapMessage: state.giftWrapMessage,
        appliedPromoCodes: state.appliedPromoCodes,
        currentStep: state.currentStep
      });
    });
    
    return () => {
      unsubscribe();
    };
  }, []);
  
  // Store actions
  const storeActions = {
    toggleOrderSummary: () => useCheckoutStore.getState().toggleOrderSummary(),
    setPromoCodeInput: (input: string) => useCheckoutStore.getState().setPromoCodeInput(input),
    applyPromoCode: (code: string) => useCheckoutStore.getState().applyPromoCode(code),
    removePromoCode: (code: string) => useCheckoutStore.getState().removePromoCode(code),
    setGiftWrap: (enabled: boolean, message?: string) => 
      useCheckoutStore.getState().setGiftWrap(enabled, message)
  };
  
  return (
    <CheckoutSummaryContent 
      sessionId={sessionId}
      summaryState={summaryState}
      storeActions={storeActions}
    />
  );
});