"use client"

import { useCartStore2 } from "@/lib/ecommerce/hooks/use-cart-store"
import { usePaymentGateways } from "@/lib/ecommerce/hooks/use-payments"
import { usePriceFormatter } from "@/hooks/use-price-formatter"
import { Separator } from "@/components/ui/separator"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { useState, useEffect, useMemo, memo } from "react"
import {
  Package,
  ChevronDown,
  ChevronUp,
  CreditCard,
  ShieldCheck,
  Gift,
  Truck,
  AlertCircle
} from "lucide-react"

interface CheckoutSummaryContentProps {
  sessionId?: string | null;
  summaryState: {
    showOrderSummary: boolean;
    selectedShippingMethod: string;
    selectedPaymentMethod: string;
    giftWrap: boolean;
    giftWrapMessage: string;
    appliedPromoCodes: string[];
    currentStep: string;
  };
  storeActions: {
    toggleOrderSummary: () => void;
    setPromoCodeInput: (input: string) => void;
    applyPromoCode: (code: string) => Promise<boolean>;
    removePromoCode: (code: string) => void;
    setGiftWrap: (enabled: boolean, message?: string) => void;
  };
}

export const CheckoutSummaryContent = memo(function CheckoutSummaryContent({ 
  sessionId, 
  summaryState,
  storeActions
}: CheckoutSummaryContentProps) {
  // Use the enhanced cart store - don't auto-fetch since parent component handles it
  const cartStoreOptions = useMemo(() => ({ 
    autoFetch: false, 
    sessionId: sessionId || undefined 
  }), [sessionId])
  
  const cartStore = useCartStore2(cartStoreOptions)
  
  // Use payment gateways for fee calculations
  const { 
    paymentMethods, 
    calculateFees, 
    loading: paymentLoading,
    error: paymentError 
  } = usePaymentGateways()
  
  const { formatPrice } = usePriceFormatter()
  
  // Local state
  const [isOpen, setIsOpen] = useState(false)
  const [promoCodeInput, setPromoCodeInput] = useState("")
  const [isApplyingPromo, setIsApplyingPromo] = useState(false)
  const [promoError, setPromoError] = useState<string | null>(null)
  
  // Destructure summary state
  const {
    selectedShippingMethod,
    selectedPaymentMethod,
    giftWrap,
    giftWrapMessage,
    appliedPromoCodes,
    currentStep
  } = summaryState
  
  // Get cart data from the store
  const cart = cartStore.cart
  const items = cart?.items || []
  const subtotal = cart?.subtotal?.amount || 0
  const appliedDiscounts = cart?.appliedDiscounts || []

  // Shipping costs in ZAR - these could be fetched from shipping service
  const shippingCosts = {
    standard: 99,
    express: 199
  }

  // Estimated delivery dates
  const deliveryDates = {
    standard: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now
    express: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000)   // 2 days from now
  }

  // Use shipping method from checkout store, fallback to standard
  const currentShippingMethod = selectedShippingMethod || 'standard'
  const shippingPrice = shippingCosts[currentShippingMethod as keyof typeof shippingCosts]
  const estimatedDelivery = deliveryDates[currentShippingMethod as keyof typeof deliveryDates]
  const giftWrapPrice = giftWrap ? 89 : 0 // Gift wrap price in ZAR
  const taxRate = 0.15 // South African VAT rate
  
  // Calculate discount amount from applied discounts
  const totalDiscountAmount = appliedDiscounts.reduce((sum, discount) => sum + discount.amount.amount, 0)
  const subtotalAfterDiscount = subtotal - totalDiscountAmount
  const taxPrice = subtotalAfterDiscount * taxRate
  
  // Calculate payment processing fees if a payment method is selected
  const paymentFees = currentStep === "payment" && paymentMethods.length > 0 
    ? calculateFees(subtotalAfterDiscount + shippingPrice + giftWrapPrice, paymentMethods[0].id)
    : 0
    
  const totalWithTax = subtotalAfterDiscount + shippingPrice + taxPrice + giftWrapPrice + paymentFees

  // Format date to display as "Month Day" using South African locale
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-ZA', { month: 'long', day: 'numeric' });
  };

  // Handle promo code application
  const handleApplyPromo = async () => {
    if (!promoCodeInput.trim()) {
      return
    }

    setIsApplyingPromo(true)
    setPromoError(null)
    
    try {
      // Update store with promo code input
      storeActions.setPromoCodeInput(promoCodeInput)
      
      // Apply promo code
      const success = await storeActions.applyPromoCode(promoCodeInput.trim())
      
      if (success) {
        setPromoCodeInput("")
      }
    } catch (error) {
      setPromoError(error instanceof Error ? error.message : "Failed to apply promo code")
    } finally {
      setIsApplyingPromo(false)
    }
  }

  // Handle removing a promo code
  const handleRemovePromo = (code: string) => {
    storeActions.removePromoCode(code)
  }

  // Handle gift wrap toggle
  const handleGiftWrapToggle = (checked: boolean) => {
    storeActions.setGiftWrap(checked, giftWrapMessage)
  }

  return (
    <div className="border rounded-md p-4 md:p-6 space-y-4 sticky top-6 bg-white shadow-sm">
      <Collapsible open={isOpen || typeof window !== 'undefined' && window.innerWidth >= 768}>
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-medium">Order Summary</h2>
          <div className="md:hidden">
            <CollapsibleTrigger
              onClick={() => setIsOpen(!isOpen)}
              className="flex items-center text-sm text-muted-foreground"
            >
              {isOpen ? (
                <>
                  <span>Hide details</span>
                  <ChevronUp className="ml-1 h-4 w-4" />
                </>
              ) : (
                <>
                  <span>Show details</span>
                  <ChevronDown className="ml-1 h-4 w-4" />
                </>
              )}
            </CollapsibleTrigger>
          </div>
        </div>

        <CollapsibleContent className="space-y-4 md:block">
          <div className="space-y-2 max-h-40 overflow-y-auto pr-2">
            {items.map((item) => (
              <div key={`${item.id}-${item.quantity}`} className="flex items-center space-x-3 text-sm">
                <img
                  src={item.productImage || '/placeholder-product.jpg'}
                  alt={item.productTitle || 'Product'}
                  className="w-10 h-10 object-cover rounded border"
                />
                <div className="flex-1">
                  <div className="font-medium">{item.productTitle}</div>
                  <div className="text-xs text-muted-foreground">
                    Qty: {item.quantity}
                    {item.variantTitle && (
                      <span className="ml-2">• {item.variantTitle}</span>
                    )}
                  </div>
                </div>
                <div className="font-medium">
                  {formatPrice(Number(item.unitPrice) * Number(item.quantity))}
                </div>
              </div>
            ))}
          </div>
          
          {/* Show loading state */}
          {cartStore.loading && (
            <div className="flex items-center justify-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
            </div>
          )}
          
          {/* Show cart errors */}
          {cartStore.error && (
            <div className="flex items-center space-x-2 text-red-600 text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>{cartStore.error.message}</span>
            </div>
          )}
        </CollapsibleContent>
      </Collapsible>

      <Separator />

      {/* Promo code section */}
      <div className="space-y-2">
        <Label htmlFor="promo-code" className="text-sm font-medium">Promo Code</Label>
        <div className="flex space-x-2">
          <Input
            id="promo-code"
            placeholder="Enter code"
            value={promoCodeInput}
            onChange={(e) => setPromoCodeInput(e.target.value)}
            className="h-9"
            disabled={isApplyingPromo}
          />
          <Button
            onClick={handleApplyPromo}
            variant="outline"
            size="sm"
            className="whitespace-nowrap"
            disabled={isApplyingPromo || !promoCodeInput.trim()}
          >
            {isApplyingPromo ? "Applying..." : "Apply"}
          </Button>
        </div>
        
        {/* Show promo error */}
        {promoError && (
          <div className="flex items-center space-x-1 text-red-600 text-xs">
            <AlertCircle className="h-3 w-3" />
            <span>{promoError}</span>
          </div>
        )}
        
        {/* Show applied promo codes */}
        {appliedPromoCodes.length > 0 && (
          <div className="space-y-1">
            {appliedPromoCodes.map((code) => (
              <div key={code} className="flex items-center justify-between text-green-600 text-xs">
                <span>✓ {code}</span>
                <button
                  onClick={() => handleRemovePromo(code)}
                  className="text-red-500 hover:text-red-700 ml-2"
                  title="Remove promo code"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        )}
        
        {/* Show cart discounts */}
        {appliedDiscounts.length > 0 && (
          <div className="space-y-1">
            {appliedDiscounts.map((discount) => (
              <div key={discount.id} className="flex items-center justify-between text-green-600 text-xs">
                <span>✓ {discount.code || discount.title}</span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Gift wrap option */}
      <div className="flex items-center space-x-2">
        <Checkbox
          id="gift-wrap"
          checked={giftWrap}
          onCheckedChange={(checked) => handleGiftWrapToggle(checked === true)}
        />
        <div className="grid gap-1.5 leading-none">
          <Label htmlFor="gift-wrap" className="text-sm font-medium flex items-center">
            <Gift className="h-3 w-3 mr-1" />
            Gift wrap
          </Label>
          <p className="text-xs text-muted-foreground">Add gift wrapping for {formatPrice(89)}</p>
        </div>
      </div>

      <Separator />

      <div className="space-y-2">
        <div className="flex justify-between">
          <span>Subtotal</span>
          <span>{formatPrice(subtotal)}</span>
        </div>
        
        {/* Show applied discounts */}
        {appliedDiscounts.map((discount) => (
          <div key={discount.id} className="flex justify-between text-green-600">
            <span>Discount ({discount.code || discount.title})</span>
            <span>-{formatPrice(discount.amount.amount)}</span>
          </div>
        ))}
        
        <div className="flex justify-between">
          <span>Shipping ({currentShippingMethod === 'express' ? 'Express' : 'Standard'})</span>
          <span>{formatPrice(shippingPrice)}</span>
        </div>
        
        {giftWrap && (
          <div className="flex justify-between">
            <span>Gift Wrap</span>
            <span>{formatPrice(giftWrapPrice)}</span>
          </div>
        )}
        
        {/* Show payment processing fees if applicable */}
        {paymentFees > 0 && (
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Payment Processing Fee</span>
            <span>{formatPrice(paymentFees)}</span>
          </div>
        )}
        
        <div className="flex justify-between">
          <span>VAT (15%)</span>
          <span>{formatPrice(taxPrice)}</span>
        </div>
      </div>

      <Separator />

      <div className="flex justify-between text-lg font-medium">
        <span>Total</span>
        <span>{formatPrice(totalWithTax)}</span>
      </div>

      {/* Estimated delivery */}
      <div className="bg-gray-50 p-3 rounded-md text-sm">
        <div className="flex items-center text-muted-foreground mb-1">
          <Truck className="h-4 w-4 mr-2" />
          <span>Estimated Delivery</span>
        </div>
        <p className="font-medium">
          {formatDate(estimatedDelivery)}
          {currentShippingMethod === 'express' ? ' (Express)' : ' (Standard)'}
        </p>
      </div>

      {/* Security badges */}
      <div className="flex items-center justify-center space-x-4 text-xs text-muted-foreground">
        <div className="flex items-center">
          <ShieldCheck className="h-3 w-3 mr-1" />
          <span>Secure Checkout</span>
        </div>
        <div className="flex items-center">
          <CreditCard className="h-3 w-3 mr-1" />
          <span>Encrypted Payment</span>
        </div>
      </div>

      {/* Show payment gateway loading/error states */}
      {paymentLoading && currentStep === "payment" && (
        <div className="flex items-center justify-center space-x-2 text-sm text-muted-foreground">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
          <span>Loading payment options...</span>
        </div>
      )}
      
      {paymentError && currentStep === "payment" && (
        <div className="flex items-center space-x-2 text-red-600 text-sm">
          <AlertCircle className="h-4 w-4" />
          <span>{paymentError.message}</span>
        </div>
      )}

      <div className="pt-2">
        <Button
          type="submit"
          form="checkout-form"
          className="w-full bg-[#012169] hover:bg-[#012169]/90 font-medium"
          disabled={cartStore.loading || (currentStep === "payment" && paymentLoading)}
        >
          {cartStore.loading || (currentStep === "payment" && paymentLoading) 
            ? "Loading..." 
            : currentStep === "shipping" 
              ? "Continue to Payment" 
              : "Complete Order"
          }
        </Button>
      </div>
    </div>
  )
})