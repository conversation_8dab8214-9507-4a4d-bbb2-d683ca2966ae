import { CreditCard, Smartphone, Building2, Banknote, Zap, Shield } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { getEnabledGateways, PAYMENT_METHOD_CONFIG } from "@/lib/payments/config"
import { PaymentGateway, PaymentMethod } from "@/lib/payments/types"

interface PaymentMethodsProps {
  showGateways?: boolean
  className?: string
}

export function PaymentMethods({ showGateways = false, className }: PaymentMethodsProps) {
  // Use a try-catch to handle potential server-side only functions
  let enabledGateways: PaymentGateway[] = []
  try {
    enabledGateways = getEnabledGateways()
  } catch (error) {
    // Fallback to showing all gateways if config is not available
    enabledGateways = [PaymentGateway.PAYFAST, PaymentGateway.OZOW]
  }
  
  const paymentMethods = [
    {
      name: "Credit & Debit Cards",
      description: "Visa, Mastercard, American Express",
      icon: CreditCard,
      popular: true,
      method: PaymentMethod.CARD,
      gateways: [PaymentGateway.PAYFAST],
      processingTime: "1-2 minutes",
    },
    {
      name: "Instant EFT",
      description: "Real-time bank payments",
      icon: Zap,
      popular: true,
      method: PaymentMethod.INSTANT_EFT,
      gateways: [PaymentGateway.OZOW],
      processingTime: "Instant",
    },
    {
      name: "EFT / Bank Transfer",
      description: "Electronic Funds Transfer",
      icon: Building2,
      popular: false,
      method: PaymentMethod.EFT,
      gateways: [PaymentGateway.PAYFAST, PaymentGateway.OZOW],
      processingTime: "1-3 business days",
    },
    {
      name: "SnapScan",
      description: "Scan to pay with your banking app",
      icon: Smartphone,
      popular: true,
      method: PaymentMethod.QR_CODE,
      gateways: [PaymentGateway.SNAPSCAN],
      processingTime: "Instant",
    },
    {
      name: "Cash on Collection",
      description: "Pay cash when collecting in-store",
      icon: Banknote,
      popular: false,
      method: undefined,
      gateways: [],
      processingTime: "At collection",
    },
  ]

  // Filter methods based on enabled gateways
  const availableMethods = paymentMethods.filter(method => {
    if (!method.method) return true // Cash on collection
    return method.gateways.some(gateway => enabledGateways.includes(gateway))
  })

  const getGatewayName = (gateway: PaymentGateway) => {
    switch (gateway) {
      case PaymentGateway.PAYFAST:
        return "PayFast"
      case PaymentGateway.OZOW:
        return "Ozow"
      case PaymentGateway.SNAPSCAN:
        return "SnapScan"
      default:
        return gateway
    }
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="font-medium text-sm">Accepted Payment Methods</h3>
        <div className="flex items-center gap-1 text-xs text-gray-500">
          <Shield className="h-3 w-3" />
          <span>Secure</span>
        </div>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {availableMethods.map((method) => (
          <div
            key={method.name}
            className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
          >
            <method.icon className="h-5 w-5 text-gray-600" />
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {method.name}
                </p>
                {method.popular && (
                  <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                    Popular
                  </Badge>
                )}
              </div>
              <p className="text-xs text-gray-500 truncate mb-1">
                {method.description}
              </p>
              <p className="text-xs text-gray-400">
                {method.processingTime}
              </p>
              
              {showGateways && method.gateways.length > 0 && (
                <div className="flex gap-1 mt-1">
                  {method.gateways
                    .filter(gateway => enabledGateways.includes(gateway))
                    .map(gateway => (
                      <Badge 
                        key={gateway} 
                        variant="outline" 
                        className="text-xs px-1 py-0"
                      >
                        {getGatewayName(gateway)}
                      </Badge>
                    ))}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
      
      <div className="space-y-2">
        <p className="text-xs text-gray-500">
          All payments are processed securely. We never store your payment information.
        </p>
        
        {enabledGateways.length > 0 && (
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <span>Powered by:</span>
            {enabledGateways.map((gateway, index) => (
              <span key={gateway}>
                {getGatewayName(gateway)}
                {index < enabledGateways.length - 1 && ", "}
              </span>
            ))}
          </div>
        )}
        
        <div className="flex items-center gap-4 text-xs text-gray-400">
          <div className="flex items-center gap-1">
            <Shield className="h-3 w-3" />
            <span>PCI DSS Compliant</span>
          </div>
          <div className="flex items-center gap-1">
            <Shield className="h-3 w-3" />
            <span>256-bit SSL</span>
          </div>
        </div>
      </div>
    </div>
  )
}
