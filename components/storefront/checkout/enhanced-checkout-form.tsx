"use client"

import { useCheckout, useCheckoutStep, useCheckoutForm, useCheckoutShipping, useCheckoutPayment } from "@/lib/ecommerce/hooks/use-checkout-store"
import { useCartStore2 } from "@/lib/ecommerce/hooks/use-cart-store"
import { usePaymentGateways } from "@/lib/ecommerce/hooks/use-payments"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckoutNavigation } from "./checkout-navigation"
import { PaymentGatewaySelector } from "@/components/payments/payment-gateway-selector"
import { User, MapPin, CreditCard, FileText, AlertCircle, Truck, Clock } from "lucide-react"
import { cn } from "@/lib/utils"
import { useMemo } from "react"

interface EnhancedCheckoutFormProps {
  sessionId?: string | null
  onCompleteOrder?: (orderId: string, paymentUrl?: string) => void
}

export function EnhancedCheckoutForm({ sessionId, onCompleteOrder }: EnhancedCheckoutFormProps) {
  const { currentStep } = useCheckoutStep()
  const { formData, validation, updateFormData } = useCheckoutForm()
  const { selectedShippingMethod, setShippingMethod } = useCheckoutShipping()
  const { selectedPaymentMethod, selectedPaymentGateway, setPaymentMethod, setPaymentGateway } = useCheckoutPayment()
  
  // Cart store for order processing
  const cartStore = useCartStore2(useMemo(() => ({
    autoFetch: false,
    sessionId: sessionId || undefined
  }), [sessionId]))
  
  // Payment gateways
  const { paymentMethods, gateways, loading: gatewaysLoading } = usePaymentGateways()
  const { isProcessing, setProcessing } = useCheckoutPayment()
  
  const handleCompleteOrder = async () => {
    if (!cartStore.cart) return
    
    setProcessing(true)
    
    try {
      // Prepare checkout data according to the API structure
      const checkoutData = {
        customerEmail: formData.email,
        customerPhone: formData.phone,
        customerName: `${formData.firstName} ${formData.lastName}`,
        shippingAddress: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          address1: formData.shippingAddress.address1,
          address2: formData.shippingAddress.address2,
          city: formData.shippingAddress.city,
          province: formData.shippingAddress.province,
          postalCode: formData.shippingAddress.postalCode,
          country: formData.shippingAddress.country,
          phone: formData.phone
        },
        billingAddress: formData.useBillingAsShipping ? undefined : {
          firstName: formData.firstName,
          lastName: formData.lastName,
          address1: formData.billingAddress?.address1 || '',
          address2: formData.billingAddress?.address2,
          city: formData.billingAddress?.city || '',
          province: formData.billingAddress?.province || '',
          postalCode: formData.billingAddress?.postalCode || '',
          country: formData.billingAddress?.country || 'ZA',
          phone: formData.phone
        },
        shippingMethod: selectedShippingMethod,
        paymentMethod: selectedPaymentMethod,
        preferredGateway: selectedPaymentGateway,
        items: cartStore.cart.items.map(item => ({
          productId: item.productId,
          variantId: item.variantId,
          quantity: item.quantity,
          price: item.unitPrice.amount,
          name: item.productTitle,
          image: item.productImage,
          color: item.variantOptions?.find(opt => opt.name === 'color')?.value,
          size: item.variantOptions?.find(opt => opt.name === 'size')?.value
        })),
        notes: formData.orderNotes,
        userId: undefined, // Add user ID if available
        sessionId: sessionId
      }
      
      const response = await fetch('/api/e-commerce/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(checkoutData),
      })
      
      const result = await response.json()
      
      if (result.success && result.data && onCompleteOrder) {
        onCompleteOrder(result.data.orderId, result.data.paymentUrl)
      } else {
        console.error('Checkout failed:', result.error || result.message)
        // Handle error - you might want to show an error message to the user
      }
    } catch (error) {
      console.error('Checkout failed:', error)
      // Handle error - you might want to show an error message to the user
    } finally {
      setProcessing(false)
    }
  }
  
  const renderCustomerStep = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Customer Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name *</Label>
            <Input
              id="firstName"
              value={formData.firstName}
              onChange={(e) => updateFormData({ firstName: e.target.value })}
              className={cn(validation.errors.firstName && "border-red-500")}
            />
            {validation.errors.firstName && (
              <p className="text-sm text-red-500">{validation.errors.firstName}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name *</Label>
            <Input
              id="lastName"
              value={formData.lastName}
              onChange={(e) => updateFormData({ lastName: e.target.value })}
              className={cn(validation.errors.lastName && "border-red-500")}
            />
            {validation.errors.lastName && (
              <p className="text-sm text-red-500">{validation.errors.lastName}</p>
            )}
          </div>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="email">Email Address *</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => updateFormData({ email: e.target.value })}
            className={cn(validation.errors.email && "border-red-500")}
          />
          {validation.errors.email && (
            <p className="text-sm text-red-500">{validation.errors.email}</p>
          )}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number *</Label>
          <Input
            id="phone"
            type="tel"
            value={formData.phone}
            onChange={(e) => updateFormData({ phone: e.target.value })}
            placeholder="+27 XX XXX XXXX"
            className={cn(validation.errors.phone && "border-red-500")}
          />
          {validation.errors.phone && (
            <p className="text-sm text-red-500">{validation.errors.phone}</p>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <Checkbox
            id="newsletter"
            checked={formData.subscribeToNewsletter}
            onCheckedChange={(checked) => updateFormData({ subscribeToNewsletter: checked === true })}
          />
          <Label htmlFor="newsletter" className="text-sm">
            Subscribe to our newsletter for updates and special offers
          </Label>
        </div>
        
        <CheckoutNavigation />
      </CardContent>
    </Card>
  )
  
  const renderShippingStep = () => (
    <div className="space-y-6">
      {/* Shipping Address */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Shipping Address
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="address1">Street Address *</Label>
            <Input
              id="address1"
              value={formData.shippingAddress.address1}
              onChange={(e) => updateFormData({ 
                shippingAddress: { ...formData.shippingAddress, address1: e.target.value }
              })}
              className={cn(validation.errors.address1 && "border-red-500")}
            />
            {validation.errors.address1 && (
              <p className="text-sm text-red-500">{validation.errors.address1}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="address2">Apartment, suite, etc. (optional)</Label>
            <Input
              id="address2"
              value={formData.shippingAddress.address2 || ''}
              onChange={(e) => updateFormData({ 
                shippingAddress: { ...formData.shippingAddress, address2: e.target.value }
              })}
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">City *</Label>
              <Input
                id="city"
                value={formData.shippingAddress.city}
                onChange={(e) => updateFormData({ 
                  shippingAddress: { ...formData.shippingAddress, city: e.target.value }
                })}
                className={cn(validation.errors.city && "border-red-500")}
              />
              {validation.errors.city && (
                <p className="text-sm text-red-500">{validation.errors.city}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="province">Province *</Label>
              <Input
                id="province"
                value={formData.shippingAddress.province}
                onChange={(e) => updateFormData({ 
                  shippingAddress: { ...formData.shippingAddress, province: e.target.value }
                })}
                className={cn(validation.errors.province && "border-red-500")}
              />
              {validation.errors.province && (
                <p className="text-sm text-red-500">{validation.errors.province}</p>
              )}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="postalCode">Postal Code *</Label>
              <Input
                id="postalCode"
                value={formData.shippingAddress.postalCode}
                onChange={(e) => updateFormData({ 
                  shippingAddress: { ...formData.shippingAddress, postalCode: e.target.value }
                })}
                className={cn(validation.errors.postalCode && "border-red-500")}
              />
              {validation.errors.postalCode && (
                <p className="text-sm text-red-500">{validation.errors.postalCode}</p>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="saveAddress"
              checked={formData.saveAddressForFuture}
              onCheckedChange={(checked) => updateFormData({ saveAddressForFuture: checked === true })}
            />
            <Label htmlFor="saveAddress" className="text-sm">
              Save this address for future orders
            </Label>
          </div>
        </CardContent>
      </Card>
      
      {/* Shipping Method */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Shipping Method
          </CardTitle>
        </CardHeader>
        <CardContent>
          <RadioGroup value={selectedShippingMethod} onValueChange={setShippingMethod}>
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="standard" id="standard" />
              <div className="flex-1">
                <Label htmlFor="standard" className="font-medium">Standard Shipping</Label>
                <p className="text-sm text-muted-foreground">3-5 business days • R99</p>
              </div>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </div>
            
            <div className="flex items-center space-x-2 p-3 border rounded-lg">
              <RadioGroupItem value="express" id="express" />
              <div className="flex-1">
                <Label htmlFor="express" className="font-medium">Express Shipping</Label>
                <p className="text-sm text-muted-foreground">1-2 business days • R199</p>
              </div>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </div>
          </RadioGroup>
        </CardContent>
      </Card>
      
      <CheckoutNavigation />
    </div>
  )
  
  const renderPaymentStep = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Method
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <RadioGroup value={selectedPaymentMethod} onValueChange={setPaymentMethod}>
            {paymentMethods.map((method) => (
              <div key={method.id} className="flex items-center space-x-2 p-3 border rounded-lg">
                <RadioGroupItem value={method.id} id={method.id} />
                <div className="flex-1">
                  <Label htmlFor={method.id} className="font-medium">{method.name}</Label>
                  <p className="text-sm text-muted-foreground">{method.description}</p>
                </div>
              </div>
            ))}
          </RadioGroup>
          
          {selectedPaymentMethod === 'card' && (
            <div className="mt-4">
              <PaymentGatewaySelector
                selectedMethod={selectedPaymentGateway || ''}
                onMethodChange={setPaymentGateway}
                paymentMethods={paymentMethods}
              />
            </div>
          )}
          
          {validation.errors.paymentMethod && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{validation.errors.paymentMethod}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
      
      <CheckoutNavigation />
    </div>
  )
  
  const renderReviewStep = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Order Review
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Customer Info Summary */}
          <div>
            <h4 className="font-medium mb-2">Customer Information</h4>
            <p className="text-sm text-muted-foreground">
              {formData.firstName} {formData.lastName}<br />
              {formData.email}<br />
              {formData.phone}
            </p>
          </div>
          
          <Separator />
          
          {/* Shipping Info Summary */}
          <div>
            <h4 className="font-medium mb-2">Shipping Address</h4>
            <p className="text-sm text-muted-foreground">
              {formData.shippingAddress.address1}<br />
              {formData.shippingAddress.address2 && `${formData.shippingAddress.address2}\n`}
              {formData.shippingAddress.city}, {formData.shippingAddress.province} {formData.shippingAddress.postalCode}
            </p>
          </div>
          
          <Separator />
          
          {/* Payment Info Summary */}
          <div>
            <h4 className="font-medium mb-2">Payment Method</h4>
            <p className="text-sm text-muted-foreground">
              {paymentMethods.find(m => m.id === selectedPaymentMethod)?.name}
              {selectedPaymentGateway && ` via ${gateways.find(g => g.id === selectedPaymentGateway)?.name}`}
            </p>
          </div>
          
          {/* Order Notes */}
          <div className="space-y-2">
            <Label htmlFor="orderNotes">Order Notes (optional)</Label>
            <Textarea
              id="orderNotes"
              value={formData.orderNotes || ''}
              onChange={(e) => updateFormData({ orderNotes: e.target.value })}
              placeholder="Any special instructions for your order..."
              rows={3}
            />
          </div>
          
          {/* Gift Options */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isGift"
                checked={formData.isGift}
                onCheckedChange={(checked) => updateFormData({ isGift: checked === true })}
              />
              <Label htmlFor="isGift">This is a gift</Label>
            </div>
            
            {formData.isGift && (
              <div className="space-y-2">
                <Label htmlFor="giftMessage">Gift Message (optional)</Label>
                <Textarea
                  id="giftMessage"
                  value={formData.giftMessage || ''}
                  onChange={(e) => updateFormData({ giftMessage: e.target.value })}
                  placeholder="Enter a personal message for the recipient..."
                  rows={3}
                />
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      
      <CheckoutNavigation
        continueText="Place Order"
        isLoading={isProcessing}
        onContinue={handleCompleteOrder}
      />
    </div>
  )
  
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'customer':
        return renderCustomerStep()
      case 'shipping':
        return renderShippingStep()
      case 'payment':
        return renderPaymentStep()
      case 'review':
        return renderReviewStep()
      default:
        return renderCustomerStep()
    }
  }
  
  return (
    <div className="space-y-6">
      {renderCurrentStep()}
    </div>
  )
}