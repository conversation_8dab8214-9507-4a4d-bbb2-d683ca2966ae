"use client"

import { useCheckoutPromo } from "@/lib/ecommerce/hooks/use-checkout-store"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AlertCircle, X } from "lucide-react"

export function PromoCodeSection() {
  const {
    appliedPromoCodes,
    promoCodeInput,
    isApplyingPromo,
    promoError,
    applyPromoCode,
    removePromoCode,
    setPromoCodeInput
  } = useCheckoutPromo()
  
  const handleApplyPromo = async () => {
    if (!promoCodeInput.trim()) return
    await applyPromoCode(promoCodeInput.trim())
  }
  
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleApplyPromo()
    }
  }
  
  return (
    <div className="space-y-3">
      <div className="space-y-2">
        <Label htmlFor="promo-code" className="text-sm font-medium">Promo Code</Label>
        <div className="flex space-x-2">
          <Input
            id="promo-code"
            placeholder="Enter code"
            value={promoCodeInput}
            onChange={(e) => setPromoCodeInput(e.target.value)}
            onKeyPress={handleKeyPress}
            className="h-9"
            disabled={isApplyingPromo}
          />
          <Button
            onClick={handleApplyPromo}
            variant="outline"
            size="sm"
            className="whitespace-nowrap"
            disabled={isApplyingPromo || !promoCodeInput.trim()}
          >
            {isApplyingPromo ? "Applying..." : "Apply"}
          </Button>
        </div>
        
        {/* Show promo error */}
        {promoError && (
          <div className="flex items-center space-x-1 text-red-600 text-xs">
            <AlertCircle className="h-3 w-3" />
            <span>{promoError}</span>
          </div>
        )}
      </div>
      
      {/* Show applied promo codes */}
      {appliedPromoCodes.length > 0 && (
        <div className="space-y-1">
          <p className="text-xs font-medium text-green-600">Applied Codes:</p>
          {appliedPromoCodes.map((code) => (
            <div key={code} className="flex items-center justify-between bg-green-50 text-green-700 text-xs px-2 py-1 rounded">
              <span>✓ {code}</span>
              <button
                onClick={() => removePromoCode(code)}
                className="text-green-600 hover:text-green-800 ml-2"
                title="Remove promo code"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}