"use client"

import { useCheckoutStep } from "@/lib/ecommerce/hooks/use-checkout-store"
import { Button } from "@/components/ui/button"
import { ArrowLeft, ArrowRight, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"

interface CheckoutNavigationProps {
  onContinue?: () => void
  onBack?: () => void
  continueText?: string
  backText?: string
  isLoading?: boolean
  disabled?: boolean
  className?: string
}

export function CheckoutNavigation({
  onContinue,
  onBack,
  continueText,
  backText,
  isLoading = false,
  disabled = false,
  className
}: CheckoutNavigationProps) {
  const { currentStep, steps, setCurrentStep, canProceedToNextStep } = useCheckoutStep()
  
  const currentStepIndex = steps.findIndex(step => step.current)
  const isFirstStep = currentStepIndex === 0
  const isLastStep = currentStepIndex === steps.length - 1
  
  const handleBack = () => {
    if (onBack) {
      onBack()
    } else if (!isFirstStep) {
      const previousStep = steps[currentStepIndex - 1]
      setCurrentStep(previousStep.id)
    }
  }
  
  const handleContinue = () => {
    if (onContinue) {
      onContinue()
    } else if (!isLastStep && canProceedToNextStep()) {
      const nextStep = steps[currentStepIndex + 1]
      setCurrentStep(nextStep.id)
    }
  }
  
  const getButtonText = () => {
    if (continueText) return continueText
    
    switch (currentStep) {
      case 'customer':
        return 'Continue to Shipping'
      case 'shipping':
        return 'Continue to Payment'
      case 'payment':
        return 'Review Order'
      case 'review':
        return 'Place Order'
      default:
        return 'Continue'
    }
  }
  
  const canContinue = canProceedToNextStep() && !disabled
  
  return (
    <div className={cn("flex items-center justify-between pt-6 border-t", className)}>
      {/* Back button */}
      <Button
        variant="outline"
        onClick={handleBack}
        disabled={isFirstStep || isLoading}
        className="flex items-center gap-2"
      >
        <ArrowLeft className="w-4 h-4" />
        {backText || 'Back'}
      </Button>
      
      {/* Continue button */}
      <Button
        onClick={handleContinue}
        disabled={!canContinue || isLoading}
        className="flex items-center gap-2 min-w-[140px]"
      >
        {isLoading ? (
          <>
            <Loader2 className="w-4 h-4 animate-spin" />
            Processing...
          </>
        ) : (
          <>
            {getButtonText()}
            <ArrowRight className="w-4 h-4" />
          </>
        )}
      </Button>
    </div>
  )
}

// Specialized navigation components for specific steps
export function CustomerStepNavigation(props: Omit<CheckoutNavigationProps, 'continueText'>) {
  return (
    <CheckoutNavigation
      {...props}
      continueText="Continue to Shipping"
    />
  )
}

export function ShippingStepNavigation(props: Omit<CheckoutNavigationProps, 'continueText'>) {
  return (
    <CheckoutNavigation
      {...props}
      continueText="Continue to Payment"
    />
  )
}

export function PaymentStepNavigation(props: Omit<CheckoutNavigationProps, 'continueText'>) {
  return (
    <CheckoutNavigation
      {...props}
      continueText="Review Order"
    />
  )
}

export function ReviewStepNavigation(props: Omit<CheckoutNavigationProps, 'continueText'>) {
  return (
    <CheckoutNavigation
      {...props}
      continueText="Place Order"
    />
  )
}