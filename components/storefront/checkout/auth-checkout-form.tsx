"use client"

import { useState, use<PERSON><PERSON>back, useEffect, useMemo } from "react"
import { useAuth } from "@/components/auth-provider"
import { AuthModal } from "@/components/auth/auth-modal"
import { EnhancedCheckoutForm } from "@/components/storefront/checkout/enhanced-checkout-form"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, User, UserPlus, LogIn } from "lucide-react"
import { useCheckoutStore } from "@/lib/ecommerce/stores/checkout-store"

interface AuthCheckoutFormProps {
  onCompleteOrder?: (orderId: string, paymentUrl?: string) => void
  sessionId?: string | null
}

export function AuthCheckoutForm({
  onCompleteOrder,
  sessionId
}: AuthCheckoutFormProps) {
  const { isAuthenticated, user, customer, isLoading } = useAuth()
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [authModalTab, setAuthModalTab] = useState<"login" | "register">("login")

  // If the user is authenticated, we can pre-fill the checkout form with their information
  const [prefillData, setPrefillData] = useState({
    email: "",
    phone: "",
    firstName: "",
    lastName: "",
    address1: "",
    address2: "",
    city: "",
    province: "",
    postalCode: ""
  })

  // Update prefill data when user/customer data changes
  useEffect(() => {
    if (user && customer) {
      setPrefillData({
        email: user.email || "",
        phone: customer.billing?.phone || "",
        firstName: user.first_name || customer.billing?.first_name || "",
        lastName: user.last_name || customer.billing?.last_name || "",
        address1: customer.shipping?.address_1 || customer.billing?.address_1 || "",
        address2: customer.shipping?.address_2 || customer.billing?.address_2 || "",
        city: customer.shipping?.city || customer.billing?.city || "",
        province: customer.shipping?.state || customer.billing?.state || "",
        postalCode: customer.shipping?.postcode || customer.billing?.postcode || ""
      })
    }
  }, [user, customer])

  const handleOpenAuthModal = (tab: "login" | "register") => {
    setAuthModalTab(tab)
    setShowAuthModal(true)
  }

  const handleAuthSuccess = () => {
    // The user has successfully authenticated, we can continue with checkout
    console.log("Authentication successful")
  }

  // Get the setCurrentStep function directly from the store to avoid hooks
  const setCurrentStep = useAuth().isAuthenticated 
    ? () => {} // Dummy function if authenticated
    : (stepId: string) => useCheckoutStore.getState().setCurrentStep(stepId);

  // If the user is not authenticated, show the auth options
  if (!isLoading && !isAuthenticated) {
    return (
      <div className="space-y-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Please sign in or create an account to continue with checkout
          </AlertDescription>
        </Alert>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="border rounded-lg p-6 space-y-4">
            <div className="flex items-center space-x-2">
              <LogIn className="h-5 w-5" />
              <h3 className="text-lg font-medium">Existing Customer</h3>
            </div>
            <p className="text-sm text-muted-foreground">
              Sign in to your account to access your saved addresses and speed up checkout
            </p>
            <Button
              onClick={() => handleOpenAuthModal("login")}
              className="w-full"
            >
              Sign In
            </Button>
          </div>

          <div className="border rounded-lg p-6 space-y-4">
            <div className="flex items-center space-x-2">
              <UserPlus className="h-5 w-5" />
              <h3 className="text-lg font-medium">New Customer</h3>
            </div>
            <p className="text-sm text-muted-foreground">
              Create an account to track your orders and save your information for next time
            </p>
            <Button
              onClick={() => handleOpenAuthModal("register")}
              className="w-full"
            >
              Create Account
            </Button>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          <div className="h-px flex-1 bg-border"></div>
          <span className="text-sm text-muted-foreground">OR</span>
          <div className="h-px flex-1 bg-border"></div>
        </div>

        <Button
          variant="outline"
          className="w-full"
          onClick={() => setCurrentStep("shipping")}
        >
          Continue as Guest
        </Button>

        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          defaultTab={authModalTab}
          onSuccess={handleAuthSuccess}
        />
      </div>
    )
  }

  // If the user is authenticated or continuing as guest, show the regular checkout form
  return (
    <div className="space-y-4">
      {isAuthenticated && (
        <div className="bg-muted p-4 rounded-lg mb-6">
          <div className="flex items-center space-x-2">
            <User className="h-5 w-5" />
            <div>
              <p className="font-medium">Signed in as {user?.display_name || user?.email}</p>
              <p className="text-sm text-muted-foreground">Your saved information has been pre-filled</p>
            </div>
          </div>
        </div>
      )}

      <EnhancedCheckoutForm
        onCompleteOrder={onCompleteOrder}
        sessionId={sessionId}
      />
    </div>
  )
}