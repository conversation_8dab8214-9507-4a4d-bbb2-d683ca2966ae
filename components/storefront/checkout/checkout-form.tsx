"use client"

import React from "react"

import { useState, use<PERSON><PERSON>back, useMemo, useEffect } from "react"
import { Address } from "@/lib/ecommerce/types/base"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CreditCard, Truck, Loader2, AlertCircle, Shield, Clock } from "lucide-react"
import { useCartStore2 } from "@/lib/ecommerce/hooks/use-cart-store"
import { useCheckout, usePaymentGateways, CheckoutData } from "@/lib/ecommerce/hooks/use-payments"
import { PaymentGatewaySelector } from "@/components/payments/payment-gateway-selector"
import { toast } from "sonner"
import { cn } from "@/lib/utils"

interface CheckoutFormProps {
  step: "shipping" | "payment";
  setStep: (step: "shipping" | "payment") => void;
  shippingMethod: string;
  setShippingMethod: (method: string) => void;
  onCompleteOrder?: (orderId: string, paymentUrl?: string) => void;
  prefillData?: {
    email: string;
    phone: string;
    firstName: string;
    lastName: string;
    address1: string;
    address2: string;
    city: string;
    province: string;
    postalCode: string;
  };
  sessionId?: string | null;
}

// Form validation schemas
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
const phoneRegex = /^(\+27|0)[0-9]{9}$/
const postalCodeRegex = /^[0-9]{4}$/

// South African provinces
const SA_PROVINCES = [
  { value: "gp", label: "Gauteng" },
  { value: "wc", label: "Western Cape" },
  { value: "kzn", label: "KwaZulu-Natal" },
  { value: "ec", label: "Eastern Cape" },
  { value: "fs", label: "Free State" },
  { value: "lp", label: "Limpopo" },
  { value: "mp", label: "Mpumalanga" },
  { value: "nw", label: "North West" },
  { value: "nc", label: "Northern Cape" },
]

// Shipping options with enhanced details
const SHIPPING_OPTIONS = [
  {
    id: 'standard',
    name: 'Standard Shipping',
    description: '3-5 business days',
    price: 99,
    icon: Truck,
    estimatedDays: 5
  },
  {
    id: 'express',
    name: 'Express Shipping',
    description: '1-2 business days',
    price: 199,
    icon: Clock,
    estimatedDays: 2
  },
  {
    id: 'collection',
    name: 'Collection',
    description: 'Sandton Store',
    price: 0,
    icon: '🏪',
    estimatedDays: 0
  }
]

export function CheckoutForm({
  step,
  setStep,
  shippingMethod,
  setShippingMethod,
  onCompleteOrder,
  prefillData,
  sessionId
}: CheckoutFormProps) {
  // Use the cart store instead of the useCart hook
  const {
    cart,
    enhancedCart,
    loading: cartLoading,
    error: cartError,
    setSessionId,
    refetch
  } = useCartStore2(useMemo(() => ({
    autoFetch: false,
    sessionId
  }), [sessionId]))

  // Session ID is managed by the parent component, no need to refetch here

  const items = cart?.items || []
  const { processCheckout, loading: checkoutLoading, error: checkoutError } = useCheckout()
  const {
    paymentMethods,
    gateways,
    defaultGateway,
    defaultMethod,
    loading: gatewaysLoading,
    error: gatewaysError,
    calculateFees
  } = usePaymentGateways()

  const [paymentMethod, setPaymentMethod] = useState(defaultMethod || "card")
  const [selectedGateway, setSelectedGateway] = useState<string | null>(null)

  // Update payment method when default method loads
  React.useEffect(() => {
    if (defaultMethod && paymentMethod === "card" && defaultMethod !== "card") {
      setPaymentMethod(defaultMethod)
    }
  }, [defaultMethod, paymentMethod])

  // Set default gateway when gateways load
  React.useEffect(() => {
    if (defaultGateway && !selectedGateway) {
      setSelectedGateway(defaultGateway)
    }
  }, [defaultGateway, selectedGateway])
  const [isProcessing, setIsProcessing] = useState(false)
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  const [formData, setFormData] = useState({
    email: prefillData?.email || '',
    phone: prefillData?.phone || '',
    firstName: prefillData?.firstName || '',
    lastName: prefillData?.lastName || '',
    address1: prefillData?.address1 || '',
    address2: prefillData?.address2 || '',
    city: prefillData?.city || '',
    province: prefillData?.province || '',
    postalCode: prefillData?.postalCode || '',
    billingAddressSame: true,
    saveCard: false,
    agreeToTerms: false,
    // Card details (for display only - actual processing handled by payment gateway)
    cardNumber: '',
    expiryDate: '',
    cvc: '',
    nameOnCard: ''
  })

  // Update form data when prefill data changes
  useEffect(() => {
    if (prefillData) {
      setFormData(prev => ({
        ...prev,
        email: prefillData.email || prev.email,
        phone: prefillData.phone || prev.phone,
        firstName: prefillData.firstName || prev.firstName,
        lastName: prefillData.lastName || prev.lastName,
        address1: prefillData.address1 || prev.address1,
        address2: prefillData.address2 || prev.address2,
        city: prefillData.city || prev.city,
        province: prefillData.province || prev.province,
        postalCode: prefillData.postalCode || prev.postalCode,
      }))
    }
  }, [prefillData])

  // Enhanced input change handler with validation
  const handleInputChange = useCallback((field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }, [validationErrors])

  // Comprehensive form validation
  const validateForm = useCallback((data: typeof formData, isShippingStep = true) => {
    const errors: Record<string, string> = {}

    // Email validation
    if (!data.email) {
      errors.email = "Email is required"
    } else if (!emailRegex.test(data.email)) {
      errors.email = "Please enter a valid email address"
    }

    // Phone validation
    if (!data.phone) {
      errors.phone = "Phone number is required"
    } else if (!phoneRegex.test(data.phone)) {
      errors.phone = "Please enter a valid South African phone number"
    }

    // Name validation
    if (!data.firstName.trim()) {
      errors.firstName = "First name is required"
    }
    if (!data.lastName.trim()) {
      errors.lastName = "Last name is required"
    }

    // Address validation
    if (!data.address1.trim()) {
      errors.address1 = "Address is required"
    }
    if (!data.city.trim()) {
      errors.city = "City is required"
    }
    if (!data.province) {
      errors.province = "Province is required"
    }
    if (!data.postalCode) {
      errors.postalCode = "Postal code is required"
    } else if (!postalCodeRegex.test(data.postalCode)) {
      errors.postalCode = "Please enter a valid 4-digit postal code"
    }

    // Payment step validation
    if (!isShippingStep) {
      if (!data.agreeToTerms) {
        errors.agreeToTerms = "You must agree to the terms and conditions"
      }
    }

    return errors
  }, [])

  const handleSubmitShipping = useCallback((e: React.FormEvent) => {
    e.preventDefault()

    const errors = validateForm(formData, true)
    setValidationErrors(errors)

    if (Object.keys(errors).length > 0) {
      toast.error("Please fix the errors below")
      return
    }

    setStep("payment")
  }, [formData, validateForm, setStep])

  const handleSubmitPayment = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate entire form
    const errors = validateForm(formData, false)
    setValidationErrors(errors)

    if (Object.keys(errors).length > 0) {
      toast.error("Please fix the errors below")
      return
    }

    if (items.length === 0) {
      toast.error("Your cart is empty. Please add items before checking out.")
      return
    }

    setIsProcessing(true)

    try {
      const checkoutData: CheckoutData = {
        customerEmail: formData.email,
        customerPhone: formData.phone,
        customerName: `${formData.firstName} ${formData.lastName}`,
        shippingAddress: {
          firstName: formData.firstName,
          lastName: formData.lastName,
          address1: formData.address1,
          address2: formData.address2,
          city: formData.city,
          province: formData.province,
          postalCode: formData.postalCode,
          country: 'ZA',
          phone: formData.phone
        },
        billingAddress: formData.billingAddressSame ? undefined : {
          firstName: formData.firstName,
          lastName: formData.lastName,
          address1: formData.address1,
          address2: formData.address2,
          city: formData.city,
          province: formData.province,
          postalCode: formData.postalCode,
          country: 'ZA',
          phone: formData.phone
        },
        shippingMethod,
        paymentMethod,
        preferredGateway: selectedGateway || defaultGateway || undefined,
        items: items.map(item => {
          // Find size from variant options if available
          const sizeOption = item.variantOptions?.find(opt => opt.name.toLowerCase() === 'size')?.value;

          return {
            productId: item.id,
            variantId: item.variantId,
            quantity: item.quantity,
            price: item.unitPrice.amount,
            name: item.productTitle,
            image: item.productImage,
            color: item.variantId,
            size: sizeOption // Use the size from variant options if available
          };
        })
      }

      const result = await processCheckout(checkoutData)

      if (result?.success) {
        toast.success(`Order ${result.orderId} created successfully!`)

        // Call the onCompleteOrder callback with order ID and payment URL
        if (onCompleteOrder) {
          onCompleteOrder(result.orderId, result.paymentUrl)
        }

        // If there's a payment URL, redirect to payment gateway
        if (result.paymentUrl) {
          // Add a small delay to ensure the success message is seen
          setTimeout(() => {
            if (result.paymentUrl) {
              window.location.href = result.paymentUrl
            }
          }, 1000)
        }
      } else {
        const errorMessage = result?.error || checkoutError || "Failed to process checkout. Please try again."
        toast.error(typeof errorMessage === "object" && errorMessage !== null && "message" in errorMessage ? errorMessage.message : errorMessage)
      }
    } catch (error) {
      console.error('Checkout error:', error)
      toast.error("An unexpected error occurred. Please try again.")
    } finally {
      setIsProcessing(false)
    }
  }, [formData, validateForm, items, processCheckout, checkoutError, shippingMethod, paymentMethod, onCompleteOrder])

  // Calculate estimated delivery date
  const estimatedDeliveryDate = useMemo(() => {
    const selectedOption = SHIPPING_OPTIONS.find(option => option.id === shippingMethod)
    if (!selectedOption || selectedOption.estimatedDays === 0) return null

    const deliveryDate = new Date()
    deliveryDate.setDate(deliveryDate.getDate() + selectedOption.estimatedDays)
    return deliveryDate.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    })
  }, [shippingMethod])

  // Show loading state if cart is loading
  if (cartLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading checkout...</span>
      </div>
    )
  }

  // Show error state if cart has error
  if (cartError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load cart. Please refresh the page and try again.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <Tabs value={step} className="space-y-6">
      <TabsList className="grid grid-cols-2">
        <TabsTrigger value="shipping" disabled={step === "payment"}>
          Shipping
        </TabsTrigger>
        <TabsTrigger value="payment" disabled={step === "shipping"}>
          Payment
        </TabsTrigger>
      </TabsList>

      <TabsContent value="shipping" className="space-y-6">
        <div>
          <h2 className="text-lg font-medium mb-4">Contact Information</h2>
          <form onSubmit={handleSubmitShipping} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className={validationErrors.email ? "border-red-500" : ""}
                  required
                />
                {validationErrors.email && (
                  <p className="text-sm text-red-500">{validationErrors.email}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className={validationErrors.phone ? "border-red-500" : ""}
                  required
                />
                {validationErrors.phone && (
                  <p className="text-sm text-red-500">{validationErrors.phone}</p>
                )}
              </div>
            </div>

            <Separator />

            <h2 className="text-lg font-medium">Shipping Address</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className={validationErrors.firstName ? "border-red-500" : ""}
                  required
                />
                {validationErrors.firstName && (
                  <p className="text-sm text-red-500">{validationErrors.firstName}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className={validationErrors.lastName ? "border-red-500" : ""}
                  required
                />
                {validationErrors.lastName && (
                  <p className="text-sm text-red-500">{validationErrors.lastName}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="address1">Address</Label>
              <Input
                id="address1"
                value={formData.address1}
                onChange={(e) => handleInputChange('address1', e.target.value)}
                className={validationErrors.address1 ? "border-red-500" : ""}
                required
              />
              {validationErrors.address1 && (
                <p className="text-sm text-red-500">{validationErrors.address1}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="address2">Apartment, suite, etc. (optional)</Label>
              <Input
                id="address2"
                value={formData.address2}
                onChange={(e) => handleInputChange('address2', e.target.value)}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  className={validationErrors.city ? "border-red-500" : ""}
                  required
                />
                {validationErrors.city && (
                  <p className="text-sm text-red-500">{validationErrors.city}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="province">Province</Label>
                <Select
                  value={formData.province}
                  onValueChange={(value) => handleInputChange('province', value)}
                >
                  <SelectTrigger
                    id="province"
                    className={validationErrors.province ? "border-red-500" : ""}
                  >
                    <SelectValue placeholder="Select province" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gp">Gauteng</SelectItem>
                    <SelectItem value="wc">Western Cape</SelectItem>
                    <SelectItem value="kzn">KwaZulu-Natal</SelectItem>
                    <SelectItem value="ec">Eastern Cape</SelectItem>
                    <SelectItem value="fs">Free State</SelectItem>
                    <SelectItem value="lp">Limpopo</SelectItem>
                    <SelectItem value="mp">Mpumalanga</SelectItem>
                    <SelectItem value="nw">North West</SelectItem>
                    <SelectItem value="nc">Northern Cape</SelectItem>
                  </SelectContent>
                </Select>
                {validationErrors.province && (
                  <p className="text-sm text-red-500">{validationErrors.province}</p>
                )}
              </div>
              <div className="space-y-2">
                <Label htmlFor="postalCode">Postal Code</Label>
                <Input
                  id="postalCode"
                  placeholder="0000"
                  value={formData.postalCode}
                  onChange={(e) => handleInputChange('postalCode', e.target.value)}
                  className={validationErrors.postalCode ? "border-red-500" : ""}
                  required
                />
                {validationErrors.postalCode && (
                  <p className="text-sm text-red-500">{validationErrors.postalCode}</p>
                )}
              </div>
            </div>

            <Separator />

            <h2 className="text-lg font-medium">Shipping Method</h2>
            <RadioGroup
              value={shippingMethod}
              onValueChange={setShippingMethod}
              className="space-y-2"
            >
              <div className={`flex items-center space-x-2 border rounded-md p-3 ${shippingMethod === 'standard' ? 'border-[#012169]/50 bg-[#012169]/5' : ''}`}>
                <RadioGroupItem value="standard" id="standard" />
                <Label htmlFor="standard" className="flex-1 cursor-pointer">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <Truck className="h-4 w-4" />
                      <span>Standard Shipping (3-5 business days)</span>
                    </div>
                    <span>R99</span>
                  </div>
                </Label>
              </div>
              <div className={`flex items-center space-x-2 border rounded-md p-3 ${shippingMethod === 'express' ? 'border-[#012169]/50 bg-[#012169]/5' : ''}`}>
                <RadioGroupItem value="express" id="express" />
                <Label htmlFor="express" className="flex-1 cursor-pointer">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <Truck className="h-4 w-4" />
                      <span>Express Shipping (1-2 business days)</span>
                    </div>
                    <span>R199</span>
                  </div>
                </Label>
              </div>
              <div className={`flex items-center space-x-2 border rounded-md p-3 ${shippingMethod === 'collection' ? 'border-[#012169]/50 bg-[#012169]/5' : ''}`}>
                <RadioGroupItem value="collection" id="collection" />
                <Label htmlFor="collection" className="flex-1 cursor-pointer">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center space-x-2">
                      <span>🏪</span>
                      <span>Collection (Sandton Store)</span>
                    </div>
                    <span className="text-green-600 font-medium">Free</span>
                  </div>
                </Label>
              </div>
            </RadioGroup>

            {/* Estimated delivery date */}
            <div className="mt-2 text-sm text-muted-foreground">
              <p>
                Estimated delivery:
                <span className="font-medium ml-1">
                  {shippingMethod === 'express'
                    ? new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { month: 'long', day: 'numeric' })
                    : new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { month: 'long', day: 'numeric' })
                  }
                </span>
              </p>
            </div>

            <div className="flex justify-end">
              <Button type="submit" className="bg-[#012169] hover:bg-[#012169]/90">
                Continue to Payment
              </Button>
            </div>
          </form>
        </div>
      </TabsContent>

      <TabsContent value="payment" className="space-y-6">
        <div>
          <h2 className="text-lg font-medium mb-4">Payment Method</h2>
          <form onSubmit={handleSubmitPayment} className="space-y-6">
            {/* Show loading state while payment gateways are loading */}
            {gatewaysLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Loading payment methods...</span>
              </div>
            ) : gatewaysError ? (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Failed to load payment methods. Please refresh the page and try again.
                </AlertDescription>
              </Alert>
            ) : (
              <>
                {/* Payment Gateway Selector */}
                <PaymentGatewaySelector
                  paymentMethods={paymentMethods}
                  selectedMethod={paymentMethod}
                  onMethodChange={setPaymentMethod}
                  amount={items.reduce((sum, item) => sum + (item.unitPrice.amount * item.quantity), 0)}
                  showFees={true}
                />

                {/* Cash on Collection option for collection orders */}
                {shippingMethod === "collection" && (
                  <div className="border-t pt-4">
                    <div className="flex items-center space-x-2 border rounded-md p-3">
                      <input
                        type="radio"
                        id="cash"
                        name="paymentMethod"
                        value="cash"
                        checked={paymentMethod === "cash"}
                        onChange={(e) => setPaymentMethod(e.target.value)}
                        className="h-4 w-4"
                      />
                      <Label htmlFor="cash" className="flex-1 cursor-pointer">
                        <div className="flex items-center space-x-2">
                          <span>Cash on Collection</span>
                          <span className="text-xs text-gray-500">(Pay when collecting)</span>
                        </div>
                      </Label>
                    </div>
                  </div>
                )}
              </>
            )}

            {paymentMethod === "card" && (
              <div className="space-y-4 border rounded-md p-4">
                <div className="space-y-2">
                  <Label htmlFor="cardNumber">Card Number</Label>
                  <Input
                    id="cardNumber"
                    placeholder="1234 5678 9012 3456"
                    value={formData.cardNumber}
                    onChange={(e) => handleInputChange('cardNumber', e.target.value)}
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="expiryDate">Expiry Date</Label>
                    <Input
                      id="expiryDate"
                      placeholder="MM/YY"
                      value={formData.expiryDate}
                      onChange={(e) => handleInputChange('expiryDate', e.target.value)}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cvc">CVC</Label>
                    <Input
                      id="cvc"
                      placeholder="123"
                      value={formData.cvc}
                      onChange={(e) => handleInputChange('cvc', e.target.value)}
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nameOnCard">Name on Card</Label>
                  <Input
                    id="nameOnCard"
                    value={formData.nameOnCard}
                    onChange={(e) => handleInputChange('nameOnCard', e.target.value)}
                    required
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="saveCard"
                    checked={formData.saveCard}
                    onCheckedChange={(checked) => handleInputChange('saveCard', checked as boolean)}
                  />
                  <Label htmlFor="saveCard" className="text-sm">
                    Save card for future purchases
                  </Label>
                </div>
              </div>
            )}

            <Separator />

            <div className="space-y-2">
              <Label htmlFor="billingAddress">Billing Address</Label>
              <Select
                value={formData.billingAddressSame ? "same" : "different"}
                onValueChange={(value) => handleInputChange('billingAddressSame', value === "same")}
              >
                <SelectTrigger id="billingAddress">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="same">Same as shipping address</SelectItem>
                  <SelectItem value="different">Use a different billing address</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="terms"
                  checked={formData.agreeToTerms}
                  onCheckedChange={(checked) => handleInputChange('agreeToTerms', checked as boolean)}
                  className={validationErrors.agreeToTerms ? "border-red-500" : ""}
                  required
                />
                <Label htmlFor="terms" className="text-sm">
                  I agree to the terms and conditions and privacy policy
                </Label>
              </div>
              {validationErrors.agreeToTerms && (
                <p className="text-sm text-red-500">{validationErrors.agreeToTerms}</p>
              )}
            </div>

            <div className="flex justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={() => setStep("shipping")}
                disabled={isProcessing}
              >
                Back to Shipping
              </Button>
              <Button
                type="submit"
                className="bg-[#012169] hover:bg-[#012169]/90"
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processing...
                  </>
                ) : (
                  "Place Order"
                )}
              </Button>
            </div>
          </form>
        </div>
      </TabsContent>
    </Tabs>
  )
}
