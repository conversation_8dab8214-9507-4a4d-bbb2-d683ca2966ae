"use client"

import { useState } from "react"
import { useCart } from "@/lib/ecommerce/hooks/use-cart"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Truck, Package, AlertTriangle, Gift } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

interface CartSummaryProps {
  userId?: string
  sessionId?: string
  showDiscountCode?: boolean
  showShippingEstimate?: boolean
  className?: string
}

export function CartSummary({ 
  userId, 
  sessionId, 
  showDiscountCode = true,
  showShippingEstimate = true,
  className 
}: CartSummaryProps) {
  const [discountCode, setDiscountCode] = useState("")
  const [applyingDiscount, setApplyingDiscount] = useState(false)
  const [discountError, setDiscountError] = useState<string | null>(null)

  const {
    cart,
    enhancedCart,
    loading,
    error,
    applyDiscount,
    removeDiscount,
    hasOutOfStockItems,
    getCartWeight,
    getAvailableBundles,
    validateCart
  } = useCart({
    userId,
    sessionId,
    autoFetch: true
  })

  const pathname = usePathname()
  const isCheckout = pathname === "/checkout"

  const formatPrice = (amount: number) => `R${amount.toFixed(2)}`

  // Get cart totals from enhanced cart
  const subtotal = cart?.subtotal.amount || 0
  const totalDiscount = cart?.totalDiscount?.amount || 0
  const totalTax = cart?.totalTax?.amount || 0
  const shippingCost = cart?.shippingCost?.amount || (subtotal > 500 ? 0 : 99)
  const total = cart?.total.amount || (subtotal + shippingCost + totalTax - totalDiscount)

  const handleApplyDiscount = async () => {
    if (!discountCode.trim()) return

    setApplyingDiscount(true)
    setDiscountError(null)

    try {
      await applyDiscount({ 
        code: discountCode.trim(),
        cartId: cart?.id 
      })
      setDiscountCode("")
    } catch (error) {
      setDiscountError(error instanceof Error ? error.message : 'Failed to apply discount code')
    } finally {
      setApplyingDiscount(false)
    }
  }

  const handleRemoveDiscount = async (discountId: string) => {
    try {
      await removeDiscount({ discountId })
    } catch (error) {
      console.error('Failed to remove discount:', error)
    }
  }

  if (loading) {
    return (
      <div className="border rounded-md p-4 md:p-6 space-y-4">
        <div className="animate-pulse">
          <div className="h-6 bg-muted rounded mb-4"></div>
          <div className="space-y-2">
            <div className="h-4 bg-muted rounded"></div>
            <div className="h-4 bg-muted rounded"></div>
            <div className="h-4 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="border rounded-md p-4 md:p-6">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Failed to load cart summary. Please try again.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  if (!cart || cart.itemCount === 0) {
    return (
      <div className="border rounded-md p-4 md:p-6 text-center">
        <Package className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
        <p className="text-muted-foreground">Your cart is empty</p>
      </div>
    )
  }

  return (
    <div className={`border rounded-md p-4 md:p-6 space-y-4 ${className || ''}`}>
      <h2 className="text-lg font-medium">Order Summary</h2>

      {/* Out of stock warning */}
      {hasOutOfStockItems() && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Some items in your cart are out of stock. Please review your cart before checkout.
          </AlertDescription>
        </Alert>
      )}

      {/* Discount code section */}
      {showDiscountCode && !isCheckout && (
        <div className="space-y-2">
          <div className="flex space-x-2">
            <Input 
              placeholder="Discount code" 
              value={discountCode}
              onChange={(e) => setDiscountCode(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleApplyDiscount()}
            />
            <Button 
              variant="outline" 
              onClick={handleApplyDiscount}
              disabled={applyingDiscount || !discountCode.trim()}
            >
              {applyingDiscount ? 'Applying...' : 'Apply'}
            </Button>
          </div>
          {discountError && (
            <p className="text-sm text-destructive">{discountError}</p>
          )}
        </div>
      )}

      {/* Applied discounts */}
      {cart.appliedDiscounts && cart.appliedDiscounts.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Applied Discounts</h3>
          {cart.appliedDiscounts.map((discount) => (
            <div key={discount.id} className="flex items-center justify-between p-2 bg-green-50 rounded-md">
              <div className="flex items-center gap-2">
                <Gift className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">{discount.title}</span>
                <Badge variant="secondary" className="text-xs">
                  {discount.type === 'percentage' ? `${discount.value}%` : formatPrice(discount.value)}
                </Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleRemoveDiscount(discount.id)}
                className="text-muted-foreground hover:text-destructive"
              >
                Remove
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Available bundles */}
      {getAvailableBundles().length > 0 && !isCheckout && (
        <div className="space-y-2">
          <h3 className="text-sm font-medium">Save More</h3>
          {getAvailableBundles().slice(0, 2).map((bundle) => (
            <div key={bundle.bundleId} className="flex items-center justify-between p-2 bg-blue-50 rounded-md">
              <div>
                <div className="text-sm font-medium">{bundle.title}</div>
                <div className="text-xs text-green-600">Save {formatPrice(bundle.savings)}</div>
              </div>
              <Button size="sm" variant="outline">
                Add Bundle
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Cart weight info */}
      {showShippingEstimate && getCartWeight() > 0 && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Package className="h-4 w-4" />
          <span>Total weight: {getCartWeight().toFixed(2)}kg</span>
        </div>
      )}

      {/* Order breakdown */}
      <div className="space-y-2">
        <div className="flex justify-between">
          <span>Subtotal ({cart.itemCount} items)</span>
          <span>{formatPrice(subtotal)}</span>
        </div>
        
        {totalDiscount > 0 && (
          <div className="flex justify-between text-green-600">
            <span>Discount</span>
            <span>-{formatPrice(totalDiscount)}</span>
          </div>
        )}
        
        <div className="flex justify-between">
          <div className="flex items-center gap-1">
            <Truck className="h-4 w-4" />
            <span>Shipping</span>
          </div>
          <span>
            {shippingCost === 0 ? (
              <span className="text-green-600">Free</span>
            ) : (
              formatPrice(shippingCost)
            )}
          </span>
        </div>

        {/* Free shipping progress */}
        {showShippingEstimate && shippingCost > 0 && subtotal < 500 && (
          <div className="text-xs text-muted-foreground">
            Add {formatPrice(500 - subtotal)} for free shipping
          </div>
        )}
        
        {totalTax > 0 && (
          <div className="flex justify-between">
            <span>VAT (15%)</span>
            <span>{formatPrice(totalTax)}</span>
          </div>
        )}
      </div>

      <Separator />

      <div className="flex justify-between text-lg font-medium">
        <span>Total</span>
        <span>{formatPrice(total)}</span>
      </div>

      {/* Checkout button */}
      {!isCheckout && (
        <Button 
          asChild 
          className="w-full bg-[#012169] hover:bg-[#012169]/90"
          disabled={hasOutOfStockItems()}
        >
          <Link href="/checkout">
            {hasOutOfStockItems() ? 'Review Cart' : 'Proceed to Checkout'}
          </Link>
        </Button>
      )}

      {/* Additional info */}
      <div className="text-xs text-muted-foreground space-y-1">
        <p>• Free returns within 30 days</p>
        <p>• Secure checkout with SSL encryption</p>
        {cart.estimatedDelivery && (
          <p>• Estimated delivery: {cart.estimatedDelivery.toLocaleDateString()}</p>
        )}
      </div>
    </div>
  )
}
