"use client"

import * as React from "react"
import { useEffect, useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { Minus, Plus, X, ShoppingBag, Truck, AlertTriangle, Package } from "lucide-react"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet"
import { ScrollArea } from "@/components/ui/scroll-area"
import { useCart } from "@/lib/ecommerce/hooks/use-cart"
import { EnhancedCartItem, ProductAvailabilityCheck } from "@/lib/ecommerce/types/cart"
import { Product } from "@/lib/ecommerce/types/product"

interface CartSidebarProps {
  isOpen?: boolean
  onOpenChange?: (open: boolean) => void
  className?: string
  trigger?: React.ReactNode
  userId?: string
  sessionId?: string
}

export function CartSidebar({
  isOpen,
  onOpenChange,
  className,
  trigger,
  userId,
  sessionId
}: CartSidebarProps) {
  const [recommendations, setRecommendations] = useState<Product[]>([])
  const [showRecommendations, setShowRecommendations] = useState(false)
  const [availabilityChecks, setAvailabilityChecks] = useState<Record<string, ProductAvailabilityCheck>>({})

  const {
    cart,
    enhancedCart,
    loading,
    error,
    updateCartItem,
    removeFromCart,
    clearCart,
    validateCart,
    checkProductAvailability,
    getRecommendations,
    getCartValue,
    getCartWeight,
    hasOutOfStockItems,
    getAvailableBundles
  } = useCart({
    userId,
    sessionId,
    autoFetch: true
  })

  const items = enhancedCart?.items || []
  const totalItems = cart?.itemCount || 0
  const subtotal = cart?.subtotal.amount || 0
  const shipping = subtotal > 500 ? 0 : 50 // Free shipping over R500
  const total = cart?.total.amount || (subtotal + shipping)

  // Load recommendations when cart changes
  useEffect(() => {
    if (cart && cart.items.length > 0) {
      getRecommendations().then(setRecommendations).catch(console.error)
    }
  }, [cart, getRecommendations])

  // Check availability for items that might be out of stock
  useEffect(() => {
    if (items.length > 0) {
      items.forEach(async (item) => {
        if (!item.isAvailable) {
          try {
            const availability = await checkProductAvailability(
              item.productId, 
              item.variantId, 
              item.quantity
            )
            setAvailabilityChecks(prev => ({
              ...prev,
              [item.id]: availability
            }))
          } catch (error) {
            console.error('Failed to check availability:', error)
          }
        }
      })
    }
  }, [items, checkProductAvailability])

  const handleUpdateQuantity = async (itemId: string, quantity: number) => {
    try {
      if (quantity === 0) {
        await removeFromCart({ itemId })
      } else {
        await updateCartItem({ itemId, quantity })
      }
    } catch (error) {
      console.error('Failed to update quantity:', error)
    }
  }

  const handleRemoveItem = async (itemId: string) => {
    try {
      await removeFromCart({ itemId })
    } catch (error) {
      console.error('Failed to remove item:', error)
    }
  }

  const handleClearCart = async () => {
    try {
      await clearCart()
    } catch (error) {
      console.error('Failed to clear cart:', error)
    }
  }

  const formatPrice = (amount: number) => `R${amount.toFixed(2)}`

  const getItemImage = (item: EnhancedCartItem) => {
    return item.variantImage?.url || 
           item.productImages?.[0]?.url || 
           item.productImage || 
           '/placeholder-product.jpg'
  }

  const getItemSlug = (item: EnhancedCartItem) => {
    return item.product?.slug || item.productSlug || '#'
  }

  const defaultTrigger = (
    <Button variant="outline" size="icon" className="relative">
      <ShoppingBag className="h-4 w-4" />
      {totalItems > 0 && (
        <Badge
          variant="destructive"
          className="absolute -right-2 -top-2 h-5 w-5 rounded-full p-0 text-xs"
        >
          {totalItems}
        </Badge>
      )}
    </Button>
  )

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>
        {trigger || defaultTrigger}
      </SheetTrigger>
      <SheetContent className={cn("flex w-full flex-col sm:max-w-lg", className)}>
        <SheetHeader className="space-y-2.5 pr-6">
          <SheetTitle className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5" />
            Shopping Cart ({totalItems})
          </SheetTitle>
          <SheetDescription>
            {items.length === 0 
              ? "Your cart is empty" 
              : `${totalItems} item${totalItems !== 1 ? 's' : ''} in your cart`
            }
          </SheetDescription>
        </SheetHeader>

        {items.length === 0 ? (
          <div className="flex flex-1 flex-col items-center justify-center space-y-4">
            <div className="rounded-full bg-muted p-4">
              <ShoppingBag className="h-8 w-8 text-muted-foreground" />
            </div>
            <div className="text-center">
              <h3 className="font-semibold">Your cart is empty</h3>
              <p className="text-sm text-muted-foreground">
                Add some items to get started
              </p>
            </div>
            <Button asChild>
              <Link href="/products">Continue Shopping</Link>
            </Button>
          </div>
        ) : (
          <>
            {/* Out of stock warning */}
            {hasOutOfStockItems() && (
              <Alert className="mx-6 mb-4">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  Some items in your cart are currently out of stock. Please review your cart.
                </AlertDescription>
              </Alert>
            )}

            {/* Loading state */}
            {loading && (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            )}

            {/* Error state */}
            {error && (
              <Alert className="mx-6 mb-4" variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {error.message}
                </AlertDescription>
              </Alert>
            )}

            <ScrollArea className="flex-1 -mx-6 px-6">
              <div className="space-y-4">
                {items.map((item) => {
                  const availability = availabilityChecks[item.id]
                  const variantOptions = item.variantOptions || []
                  
                  return (
                    <div key={item.id} className="flex gap-4">
                      <div className="relative h-16 w-16 overflow-hidden rounded-md bg-muted">
                        <Image
                          src={getItemImage(item)}
                          alt={item.productTitle}
                          fill
                          className="object-cover"
                        />
                        {!item.isAvailable && (
                          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                            <AlertTriangle className="h-4 w-4 text-white" />
                          </div>
                        )}
                      </div>
                      
                      <div className="flex flex-1 flex-col gap-1">
                        <Link
                          href={`/products/${getItemSlug(item)}`}
                          className="text-sm font-medium hover:underline line-clamp-2"
                        >
                          {item.productTitle}
                        </Link>
                        
                        {/* Variant options */}
                        {variantOptions.length > 0 && (
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            {variantOptions.map((option, index) => (
                              <span key={index}>
                                {option.name}: {option.value}
                              </span>
                            ))}
                          </div>
                        )}

                        {/* Variant title */}
                        {item.variantTitle && (
                          <div className="text-xs text-muted-foreground">
                            {item.variantTitle}
                          </div>
                        )}

                        {/* Availability warning */}
                        {!item.isAvailable && (
                          <div className="text-xs text-destructive flex items-center gap-1">
                            <AlertTriangle className="h-3 w-3" />
                            {availability?.isBackordered ? 'Backordered' : 'Out of stock'}
                            {availability?.estimatedRestockDate && (
                              <span className="text-muted-foreground">
                                - Restocking {availability.estimatedRestockDate.toLocaleDateString()}
                              </span>
                            )}
                          </div>
                        )}

                        {/* Low stock warning */}
                        {item.isAvailable && item.inventoryQuantity && item.inventoryQuantity <= 5 && (
                          <div className="text-xs text-orange-600">
                            Only {item.inventoryQuantity} left in stock
                          </div>
                        )}
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1">
                            <span className="text-sm font-medium">
                              {formatPrice(item.unitPrice.amount)}
                            </span>
                            {item.compareAtPrice && (
                              <span className="text-xs text-muted-foreground line-through">
                                {formatPrice(item.compareAtPrice.amount)}
                              </span>
                            )}
                            {item.discountAmount && item.discountAmount.amount > 0 && (
                              <Badge variant="secondary" className="text-xs">
                                Save {formatPrice(item.discountAmount.amount)}
                              </Badge>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-1">
                            <Button
                              variant="outline"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => handleUpdateQuantity(item.id, Math.max(0, item.quantity - 1))}
                              disabled={loading}
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="w-8 text-center text-sm">{item.quantity}</span>
                            <Button
                              variant="outline"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                              disabled={loading || (!item.isAvailable && item.inventoryPolicy === 'deny')}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>

                        {/* Custom attributes */}
                        {item.customAttributes && Object.keys(item.customAttributes).length > 0 && (
                          <div className="text-xs text-muted-foreground">
                            {Object.entries(item.customAttributes).map(([key, value]) => (
                              <span key={key} className="mr-2">
                                {key}: {String(value)}
                              </span>
                            ))}
                          </div>
                        )}

                        {/* Personalized message */}
                        {item.personalizedMessage && (
                          <div className="text-xs text-muted-foreground italic">
                            Message: "{item.personalizedMessage}"
                          </div>
                        )}

                        {/* Gift wrap indicator */}
                        {item.giftWrap && (
                          <div className="text-xs text-green-600 flex items-center gap-1">
                            <Package className="h-3 w-3" />
                            Gift wrapped
                          </div>
                        )}
                      </div>
                      
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 text-muted-foreground hover:text-destructive"
                        onClick={() => handleRemoveItem(item.id)}
                        disabled={loading}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )
                })}
              </div>
            </ScrollArea>

            <div className="space-y-4 pt-4">
              <Separator />
              
              {/* Cart Weight Info */}
              {getCartWeight() > 0 && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Package className="h-4 w-4" />
                  <span>Total weight: {getCartWeight().toFixed(2)}kg</span>
                </div>
              )}

              {/* Shipping Info */}
              <div className="flex items-center gap-2 text-sm">
                <Truck className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">
                  {shipping === 0 
                    ? "Free shipping!" 
                    : `Add ${formatPrice(500 - subtotal)} for free shipping`
                  }
                </span>
              </div>

              {/* Available Bundles */}
              {getAvailableBundles().length > 0 && (
                <div className="space-y-2">
                  <div className="text-sm font-medium">Available Bundles</div>
                  {getAvailableBundles().slice(0, 2).map((bundle) => (
                    <div key={bundle.bundleId} className="flex items-center justify-between p-2 bg-muted rounded-md">
                      <div className="text-xs">
                        <div className="font-medium">{bundle.title}</div>
                        <div className="text-green-600">Save {formatPrice(bundle.savings)}</div>
                      </div>
                      <Button size="sm" variant="outline" className="text-xs h-6">
                        Add Bundle
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              {/* Order Summary */}
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>{formatPrice(subtotal)}</span>
                </div>
                {cart?.totalDiscount && cart.totalDiscount.amount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount</span>
                    <span>-{formatPrice(cart.totalDiscount.amount)}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Shipping</span>
                  <span>{shipping === 0 ? "Free" : formatPrice(shipping)}</span>
                </div>
                {cart?.totalTax && cart.totalTax.amount > 0 && (
                  <div className="flex justify-between">
                    <span>Tax</span>
                    <span>{formatPrice(cart.totalTax.amount)}</span>
                  </div>
                )}
                <Separator />
                <div className="flex justify-between font-medium">
                  <span>Total</span>
                  <span>{formatPrice(total)}</span>
                </div>
              </div>

              {/* Recommendations */}
              {recommendations.length > 0 && (
                <div className="space-y-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full text-xs"
                    onClick={() => setShowRecommendations(!showRecommendations)}
                  >
                    {showRecommendations ? 'Hide' : 'Show'} Recommendations ({recommendations.length})
                  </Button>
                  
                  {showRecommendations && (
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {recommendations.slice(0, 3).map((product) => (
                        <div key={product.id} className="flex items-center gap-2 p-2 bg-muted rounded-md">
                          <div className="flex-1">
                            <div className="text-xs font-medium line-clamp-1">{product.title}</div>
                            <div className="text-xs text-muted-foreground">{formatPrice(product.price.amount)}</div>
                          </div>
                          <Button size="sm" variant="outline" className="text-xs h-6">
                            Add
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="space-y-2">
                <Button 
                  asChild 
                  className="w-full"
                  disabled={loading || hasOutOfStockItems()}
                >
                  <Link href="/checkout">
                    {hasOutOfStockItems() ? 'Review Cart' : 'Checkout'}
                  </Link>
                </Button>
                <Button asChild variant="outline" className="w-full">
                  <Link href="/cart">
                    View Cart
                  </Link>
                </Button>
                {items.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full text-muted-foreground"
                    onClick={handleClearCart}
                    disabled={loading}
                  >
                    Clear Cart
                  </Button>
                )}
              </div>
            </div>
          </>
        )}
      </SheetContent>
    </Sheet>
  )
}
