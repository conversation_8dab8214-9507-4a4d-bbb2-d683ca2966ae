"use client"

import type React from "react"

import { createContext, useContext, useEffect, useState, useCallback, useMemo } from "react"
import { useCart as useEcommerceCart } from "@/lib/ecommerce/hooks/use-cart"
import { CartItem as EcommerceCartItem } from "@/lib/ecommerce/types/cart"
import { toast } from "sonner"

type CartItem = {
  id: string
  name: string
  price: number
  color: string
  size: string
  quantity: number
  image: string
  maxQuantity?: number
  isAvailable?: boolean
}

type CartContextType = {
  items: CartItem[]
  itemCount: number
  totalPrice: number
  isEmpty: boolean
  addItem: (item: CartItem) => Promise<void>
  updateItemQuantity: (id: string, size: string, quantity: number) => Promise<void>
  removeItem: (id: string, size: string) => Promise<void>
  clearCart: () => Promise<void>
  loading: boolean
  error: { code: string; message: string } | null
  isHydrated: boolean
  retrySync: () => void
}

export const CartContext = createContext<CartContextType | undefined>(undefined)

// Constants for better maintainability
const CART_STORAGE_KEY = "cart"
const SESSION_STORAGE_KEY = "sessionId"
const SYNC_RETRY_DELAY = 2000
const MAX_RETRY_ATTEMPTS = 3

export function CartProvider({ children }: { children: React.ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([])
  const [isHydrated, setIsHydrated] = useState(false)
  const [sessionId, setSessionId] = useState<string | undefined>(undefined)
  const [syncRetryCount, setSyncRetryCount] = useState(0)

  // Initialize session ID on client side with error handling
  useEffect(() => {
    try {
      setIsHydrated(true)

      // Get or create session ID
      let currentSessionId = sessionStorage.getItem(SESSION_STORAGE_KEY)
      if (!currentSessionId) {
        currentSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        console.log('CartProvider: Creating new session ID', currentSessionId)
        sessionStorage.setItem(SESSION_STORAGE_KEY, currentSessionId)
      } else {
        console.log('CartProvider: Using existing session ID', currentSessionId)
      }
      setSessionId(currentSessionId)
    } catch (error) {
      console.error("Failed to initialize session:", error)
      // Fallback session ID for cases where sessionStorage is not available
      const fallbackId = `fallback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      console.log('CartProvider: Using fallback session ID', fallbackId)
      setSessionId(fallbackId)
      setIsHydrated(true)
    }
  }, [])

  // Use e-commerce cart hook for real cart functionality
  const {
    cart: ecommerceCart,
    loading: ecommerceLoading,
    error: ecommerceError,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart: clearEcommerceCart
  } = useEcommerceCart({
    sessionId,
    autoFetch: isHydrated && !!sessionId
  })

  // Initialize localStorage cart for backward compatibility with error handling
  useEffect(() => {
    if (isHydrated) {
      try {
        const storedCart = localStorage.getItem(CART_STORAGE_KEY)
        if (storedCart) {
          const localItems = JSON.parse(storedCart)
          // Validate cart items structure
          if (Array.isArray(localItems)) {
            const validItems = localItems.filter(item =>
              item &&
              typeof item.id === 'string' &&
              typeof item.quantity === 'number' &&
              item.quantity > 0
            )
            setItems(validItems)
          }
        }
      } catch (error) {
        console.error("Failed to parse cart from localStorage:", error)
        // Clear corrupted cart data
        localStorage.removeItem(CART_STORAGE_KEY)
        setItems([])
      }
    }
  }, [isHydrated])

  // Convert e-commerce cart items to local format with memoization
  const convertedEcommerceItems = useMemo(() => {
    if (!ecommerceCart?.items) return []

    return ecommerceCart.items.map((item: EcommerceCartItem) => ({
      id: item.productId,
      name: item.productTitle,
      price: typeof item.unitPrice === 'object' ? item.unitPrice.amount : item.unitPrice,
      color: item.variantOptions?.find(opt => opt.name.toLowerCase() === 'color')?.value || '',
      size: item.variantOptions?.find(opt => opt.name.toLowerCase() === 'size')?.value || '',
      quantity: item.quantity,
      image: item.productImage || '',
      maxQuantity: item.maxQuantity,
      isAvailable: item.isAvailable ?? true
    }))
  }, [ecommerceCart?.items])

  // Sync with e-commerce cart when available with debouncing
  useEffect(() => {
    if (convertedEcommerceItems.length > 0 && isHydrated) {
      // Only update if items have actually changed to prevent infinite loops
      setItems(prevItems => {
        const hasChanged = JSON.stringify(prevItems) !== JSON.stringify(convertedEcommerceItems)
        if (hasChanged) {
          try {
            // Update localStorage for backward compatibility
            localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(convertedEcommerceItems))
            setSyncRetryCount(0) // Reset retry count on successful sync
          } catch (error) {
            console.error("Failed to save cart to localStorage:", error)
          }
          return convertedEcommerceItems
        }
        return prevItems
      })
    }
  }, [convertedEcommerceItems, isHydrated])

  // Persist local cart to localStorage when not synced with e-commerce cart
  useEffect(() => {
    if (isHydrated && items.length > 0 && !ecommerceCart?.items?.length) {
      try {
        localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(items))
      } catch (error) {
        console.error("Failed to persist cart to localStorage:", error)
      }
    }
  }, [items, isHydrated, ecommerceCart?.items?.length])

  // Memoized calculations for better performance
  const itemCount = useMemo(() =>
    items.reduce((total, item) => total + item.quantity, 0),
    [items]
  )

  const totalPrice = useMemo(() =>
    items.reduce((total, item) => total + (item.price * item.quantity), 0),
    [items]
  )

  const isEmpty = useMemo(() => items.length === 0, [items])

  // Retry sync function for error recovery
  const retrySync = useCallback(() => {
    if (syncRetryCount < MAX_RETRY_ATTEMPTS) {
      setSyncRetryCount(prev => prev + 1)
      setTimeout(() => {
        // Trigger a re-fetch of the e-commerce cart
        if (sessionId) {
          window.location.reload() // Simple retry - could be improved with more sophisticated retry logic
        }
      }, SYNC_RETRY_DELAY)
    }
  }, [syncRetryCount, sessionId])

  const addItem = useCallback(async (newItem: CartItem): Promise<void> => {
    // Validate item before adding
    if (!newItem.id || newItem.quantity <= 0) {
      toast.error("Invalid item data")
      return
    }

    // Check if item is available
    if (newItem.isAvailable === false) {
      toast.error("This item is currently unavailable")
      return
    }

    // Check max quantity if specified
    if (newItem.maxQuantity && newItem.quantity > newItem.maxQuantity) {
      toast.error(`Maximum quantity for this item is ${newItem.maxQuantity}`)
      return
    }

    try {
      console.log('CartProvider: Adding item to cart', {
        newItem,
        sessionId,
        sessionIdFromStorage: typeof window !== 'undefined' ? sessionStorage.getItem(SESSION_STORAGE_KEY) : null
      })

      // Try to add to e-commerce cart first
      if (addToCart && sessionId) {
        // Ensure we're using the latest session ID
        const currentSessionId = sessionId ||
          (typeof window !== 'undefined' ? sessionStorage.getItem(SESSION_STORAGE_KEY) : null) ||
          `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

        console.log('CartProvider: Using session ID for cart operation', currentSessionId)

        await addToCart({
          productId: newItem.id,
          quantity: newItem.quantity,
          customAttributes: {
            color: newItem.color,
            size: newItem.size
          }
        })
        toast.success(`${newItem.name} added to cart`)
        console.log('CartProvider: Successfully added to e-commerce cart')
      } else {
        console.log('CartProvider: Falling back to localStorage', {
          addToCartFunction: !!addToCart,
          sessionId,
          sessionStorage: typeof window !== 'undefined' ? !!sessionStorage.getItem(SESSION_STORAGE_KEY) : false
        })
        // Fallback to localStorage
        setItems((prevItems) => {
          const existingItemIndex = prevItems.findIndex((item) => item.id === newItem.id && item.size === newItem.size)

          if (existingItemIndex > -1) {
            const updatedItems = [...prevItems]
            const newQuantity = updatedItems[existingItemIndex].quantity + newItem.quantity

            // Check max quantity for existing item
            if (newItem.maxQuantity && newQuantity > newItem.maxQuantity) {
              toast.error(`Maximum quantity for this item is ${newItem.maxQuantity}`)
              return prevItems
            }

            updatedItems[existingItemIndex].quantity = newQuantity
            return updatedItems
          } else {
            return [...prevItems, newItem]
          }
        })
        toast.success(`${newItem.name} added to cart`)
      }
    } catch (error) {
      console.error('Failed to add item to cart:', error)
      toast.error("Failed to add item to cart. Please try again.")

      // Fallback to localStorage on error
      setItems((prevItems) => {
        const existingItemIndex = prevItems.findIndex((item) => item.id === newItem.id && item.size === newItem.size)

        if (existingItemIndex > -1) {
          const updatedItems = [...prevItems]
          const newQuantity = updatedItems[existingItemIndex].quantity + newItem.quantity

          if (newItem.maxQuantity && newQuantity > newItem.maxQuantity) {
            return prevItems
          }

          updatedItems[existingItemIndex].quantity = newQuantity
          return updatedItems
        } else {
          return [...prevItems, newItem]
        }
      })
    }
  }, [addToCart, sessionId])

  const updateItemQuantity = useCallback(async (id: string, size: string, quantity: number): Promise<void> => {
    // Validate quantity
    if (quantity < 0) {
      toast.error("Quantity cannot be negative")
      return
    }

    // Handle zero quantity by removing the item
    if (quantity === 0) {
      try {
        // Try to remove from e-commerce cart first
        if (removeFromCart && ecommerceCart) {
          const cartItem = ecommerceCart.items.find(item =>
            item.productId === id &&
            item.variantOptions?.some(opt => opt.name === 'size' && opt.value === size)
          )

          if (cartItem) {
            await removeFromCart({
              itemId: cartItem.id
            })
            toast.success("Item removed from cart")
          }
        } else {
          // Fallback to localStorage
          setItems((prevItems) => {
            const removedItem = prevItems.find((item) => item.id === id && item.size === size)
            if (removedItem) {
              toast.success(`${removedItem.name} removed from cart`)
            }
            return prevItems.filter((item) => !(item.id === id && item.size === size))
          })
        }
      } catch (error) {
        console.error('Failed to remove cart item:', error)
        toast.error("Failed to remove item. Please try again.")

        // Fallback to localStorage on error
        setItems((prevItems) => {
          const removedItem = prevItems.find((item) => item.id === id && item.size === size)
          if (removedItem) {
            toast.success(`${removedItem.name} removed from cart`)
          }
          return prevItems.filter((item) => !(item.id === id && item.size === size))
        })
      }
      return
    }

    try {
      // Try to update e-commerce cart first
      if (updateCartItem && ecommerceCart) {
        const cartItem = ecommerceCart.items.find(item =>
          item.productId === id &&
          item.variantOptions?.some(opt => opt.name === 'size' && opt.value === size)
        )

        if (cartItem) {
          await updateCartItem({
            itemId: cartItem.id,
            quantity
          })
          toast.success("Cart updated")
        }
      } else {
        // Fallback to localStorage
        setItems((prevItems) => {
          const updatedItems = prevItems.map((item) => {
            if (item.id === id && item.size === size) {
              // Check max quantity if specified
              if (item.maxQuantity && quantity > item.maxQuantity) {
                toast.error(`Maximum quantity for this item is ${item.maxQuantity}`)
                return item
              }
              return { ...item, quantity }
            }
            return item
          })
          return updatedItems
        })
        toast.success("Cart updated")
      }
    } catch (error) {
      console.error('Failed to update cart item:', error)
      toast.error("Failed to update cart. Please try again.")

      // Fallback to localStorage on error
      setItems((prevItems) =>
        prevItems.map((item) => {
          if (item.id === id && item.size === size) {
            if (item.maxQuantity && quantity > item.maxQuantity) {
              return item
            }
            return { ...item, quantity }
          }
          return item
        })
      )
    }
  }, [updateCartItem, ecommerceCart, removeFromCart])

  const removeItem = useCallback(async (id: string, size: string): Promise<void> => {
    try {
      // Try to remove from e-commerce cart first
      if (removeFromCart && ecommerceCart) {
        const cartItem = ecommerceCart.items.find(item =>
          item.productId === id &&
          item.variantOptions?.some(opt => opt.name === 'size' && opt.value === size)
        )

        if (cartItem) {
          await removeFromCart({
            itemId: cartItem.id
          })
          toast.success("Item removed from cart")
        }
      } else {
        // Fallback to localStorage
        setItems((prevItems) => {
          const removedItem = prevItems.find((item) => item.id === id && item.size === size)
          if (removedItem) {
            toast.success(`${removedItem.name} removed from cart`)
          }
          return prevItems.filter((item) => !(item.id === id && item.size === size))
        })
      }
    } catch (error) {
      console.error('Failed to remove cart item:', error)
      toast.error("Failed to remove item. Please try again.")

      // Fallback to localStorage on error
      setItems((prevItems) => {
        const removedItem = prevItems.find((item) => item.id === id && item.size === size)
        if (removedItem) {
          toast.success(`${removedItem.name} removed from cart`)
        }
        return prevItems.filter((item) => !(item.id === id && item.size === size))
      })
    }
  }, [removeFromCart, ecommerceCart])

  const clearCart = useCallback(async (): Promise<void> => {
    try {
      // Try to clear e-commerce cart first
      if (clearEcommerceCart) {
        await clearEcommerceCart()
      }

      // Clear localStorage
      setItems([])
      if (isHydrated) {
        localStorage.removeItem(CART_STORAGE_KEY)
      }
      toast.success("Cart cleared")
    } catch (error) {
      console.error('Failed to clear cart:', error)
      toast.error("Failed to clear cart. Please try again.")

      // Fallback to localStorage on error
      setItems([])
      if (isHydrated) {
        localStorage.removeItem(CART_STORAGE_KEY)
      }
    }
  }, [clearEcommerceCart, isHydrated])

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    items,
    itemCount,
    totalPrice,
    isEmpty,
    addItem,
    updateItemQuantity,
    removeItem,
    clearCart,
    loading: ecommerceLoading,
    error: ecommerceError,
    isHydrated,
    retrySync,
  }), [
    items,
    itemCount,
    totalPrice,
    isEmpty,
    addItem,
    updateItemQuantity,
    removeItem,
    clearCart,
    ecommerceLoading,
    ecommerceError,
    isHydrated,
    retrySync,
  ])

  return (
    <CartContext.Provider value={contextValue}>
      {children}
    </CartContext.Provider>
  )
}

// This hook has been deprecated in favor of the ecommerce library's useCart hook
// All components should import useCart from '@/lib/ecommerce/hooks/use-cart'
