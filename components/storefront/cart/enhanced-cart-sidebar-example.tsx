// Example of how to use the enhanced CartSidebar component
'use client'

import React, { useState } from 'react'
import { CartSidebar } from './cart-sidebar'
import { Button } from '@/components/ui/button'
import { ShoppingBag } from 'lucide-react'

interface EnhancedCartSidebarExampleProps {
  userId?: string
  sessionId?: string
}

export function EnhancedCartSidebarExample({ 
  userId, 
  sessionId 
}: EnhancedCartSidebarExampleProps) {
  const [isOpen, setIsOpen] = useState(false)

  // Custom trigger button
  const customTrigger = (
    <Button variant="outline" size="sm" className="relative">
      <ShoppingBag className="h-4 w-4 mr-2" />
      Cart
    </Button>
  )

  return (
    <div className="flex items-center gap-4">
      {/* Basic usage with default trigger */}
      <CartSidebar
        userId={userId}
        sessionId={sessionId}
      />

      {/* Usage with custom trigger */}
      <CartSidebar
        userId={userId}
        sessionId={sessionId}
        trigger={customTrigger}
      />

      {/* Usage with controlled open state */}
      <CartSidebar
        userId={userId}
        sessionId={sessionId}
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        trigger={
          <Button variant="ghost" onClick={() => setIsOpen(true)}>
            Open Cart
          </Button>
        }
      />
    </div>
  )
}

// Example of using the cart sidebar in a header component
export function HeaderWithCart({ userId }: { userId?: string }) {
  return (
    <header className="flex items-center justify-between p-4 border-b">
      <div className="flex items-center gap-4">
        <h1 className="text-xl font-bold">Coco Milk Store</h1>
      </div>
      
      <div className="flex items-center gap-4">
        <CartSidebar
          userId={userId}
          sessionId={`session-${Date.now()}`} // In real app, get from session
          className="w-full max-w-md"
        />
      </div>
    </header>
  )
}

export default EnhancedCartSidebarExample