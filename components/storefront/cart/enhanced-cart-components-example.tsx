// Complete example showing how to use all enhanced cart components together
'use client'

import React, { useState } from 'react'
import { CartSidebar } from './cart-sidebar'
import { CartDrawer } from './cart-drawer'
import { CartItem } from './cart-item'
import { CartSummary } from './cart-summary'
import { useCart } from '@/lib/ecommerce/hooks/use-cart'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ShoppingBag, Package, Truck } from 'lucide-react'

interface EnhancedCartComponentsExampleProps {
  userId?: string
  sessionId?: string
}

export function EnhancedCartComponentsExample({ 
  userId, 
  sessionId 
}: EnhancedCartComponentsExampleProps) {
  const [activeView, setActiveView] = useState<'sidebar' | 'drawer' | 'page'>('page')

  const {
    cart,
    enhancedCart,
    loading,
    error,
    addToCart,
    getCartCategories,
    getCartValue,
    hasOutOfStockItems,
    getAvailableBundles
  } = useCart({
    userId,
    sessionId,
    autoFetch: true
  })

  // Sample product for testing
  const sampleProduct = {
    id: 'sample-product-1',
    title: 'Sample Kids T-Shirt',
    price: { amount: 299, currency: 'ZAR' },
    variants: [
      { id: 'var-1', title: 'Small - Blue', options: [{ name: 'Size', value: 'Small' }, { name: 'Color', value: 'Blue' }] },
      { id: 'var-2', title: 'Medium - Red', options: [{ name: 'Size', value: 'Medium' }, { name: 'Color', value: 'Red' }] }
    ]
  }

  const handleAddSampleProduct = async () => {
    try {
      await addToCart({
        productId: sampleProduct.id,
        variantId: sampleProduct.variants[0].id,
        quantity: 1,
        validateInventory: true,
        customization: [
          { attributeId: 'color', value: 'Blue' },
          { attributeId: 'size', value: 'Small' }
        ]
      })
    } catch (error) {
      console.error('Failed to add sample product:', error)
    }
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">Enhanced Cart Components Demo</h1>
        <p className="text-muted-foreground">
          Comprehensive cart system with product integration, inventory management, and smart features
        </p>
        
        {/* View Toggle */}
        <div className="flex justify-center gap-2">
          <Button
            variant={activeView === 'page' ? 'default' : 'outline'}
            onClick={() => setActiveView('page')}
          >
            Cart Page View
          </Button>
          <Button
            variant={activeView === 'sidebar' ? 'default' : 'outline'}
            onClick={() => setActiveView('sidebar')}
          >
            Sidebar View
          </Button>
          <Button
            variant={activeView === 'drawer' ? 'default' : 'outline'}
            onClick={() => setActiveView('drawer')}
          >
            Drawer View
          </Button>
        </div>

        {/* Add Sample Product */}
        <Button onClick={handleAddSampleProduct} className="ml-4">
          <ShoppingBag className="h-4 w-4 mr-2" />
          Add Sample Product
        </Button>
      </div>

      {/* Cart Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-card p-4 rounded-lg border">
          <div className="flex items-center gap-2">
            <ShoppingBag className="h-5 w-5 text-primary" />
            <span className="font-medium">Items</span>
          </div>
          <p className="text-2xl font-bold">{cart?.itemCount || 0}</p>
        </div>
        
        <div className="bg-card p-4 rounded-lg border">
          <div className="flex items-center gap-2">
            <Package className="h-5 w-5 text-primary" />
            <span className="font-medium">Categories</span>
          </div>
          <p className="text-2xl font-bold">{getCartCategories().length}</p>
        </div>
        
        <div className="bg-card p-4 rounded-lg border">
          <div className="flex items-center gap-2">
            <Truck className="h-5 w-5 text-primary" />
            <span className="font-medium">Total Value</span>
          </div>
          <p className="text-2xl font-bold">R{getCartValue().toFixed(2)}</p>
        </div>
        
        <div className="bg-card p-4 rounded-lg border">
          <div className="flex items-center gap-2">
            <Badge variant={hasOutOfStockItems() ? 'destructive' : 'secondary'}>
              {hasOutOfStockItems() ? 'Issues' : 'All Good'}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground">
            {hasOutOfStockItems() ? 'Some items out of stock' : 'All items available'}
          </p>
        </div>
      </div>

      {/* Main Content Based on Active View */}
      {activeView === 'page' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-6">
            <h2 className="text-2xl font-bold">Shopping Cart</h2>
            
            {loading && (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="animate-pulse">
                    <div className="flex space-x-4 p-4 border rounded-md">
                      <div className="w-24 h-24 bg-muted rounded-md"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-muted rounded w-3/4"></div>
                        <div className="h-4 bg-muted rounded w-1/2"></div>
                        <div className="h-4 bg-muted rounded w-1/4"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {error && (
              <div className="text-center py-8">
                <p className="text-destructive">Error loading cart: {error.message}</p>
              </div>
            )}

            {!loading && !error && enhancedCart?.items && (
              <div className="space-y-4">
                {enhancedCart.items.map((item) => (
                  <CartItem
                    key={item.id}
                    item={item}
                    isCompact={false}
                    showProductLink={true}
                    showAvailabilityCheck={true}
                  />
                ))}
              </div>
            )}

            {!loading && !error && (!enhancedCart?.items || enhancedCart.items.length === 0) && (
              <div className="text-center py-12">
                <ShoppingBag className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Your cart is empty</h3>
                <p className="text-muted-foreground mb-4">Add some items to get started</p>
                <Button onClick={handleAddSampleProduct}>
                  Add Sample Product
                </Button>
              </div>
            )}

            {/* Cart Categories */}
            {getCartCategories().length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Categories in Cart</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {getCartCategories().map((category) => (
                    <div key={category.categoryId} className="p-3 border rounded-md">
                      <div className="font-medium">{category.categoryName}</div>
                      <div className="text-sm text-muted-foreground">
                        {category.itemCount} items • R{category.totalValue.toFixed(2)}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Available Bundles */}
            {getAvailableBundles().length > 0 && (
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Save More with Bundles</h3>
                <div className="space-y-3">
                  {getAvailableBundles().map((bundle) => (
                    <div key={bundle.bundleId} className="flex items-center justify-between p-4 border rounded-md bg-blue-50">
                      <div>
                        <div className="font-medium">{bundle.title}</div>
                        <div className="text-sm text-green-600">
                          Save R{bundle.savings.toFixed(2)}
                        </div>
                      </div>
                      <Button variant="outline">Add Bundle</Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Cart Summary */}
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <CartSummary
                userId={userId}
                sessionId={sessionId}
                showDiscountCode={true}
                showShippingEstimate={true}
              />
            </div>
          </div>
        </div>
      )}

      {activeView === 'sidebar' && (
        <div className="text-center space-y-4">
          <h2 className="text-2xl font-bold">Cart Sidebar Demo</h2>
          <p className="text-muted-foreground">
            Click the button below to open the cart sidebar
          </p>
          <CartSidebar
            userId={userId}
            sessionId={sessionId}
            trigger={
              <Button size="lg">
                <ShoppingBag className="h-5 w-5 mr-2" />
                Open Cart Sidebar
                {cart && cart.itemCount > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {cart.itemCount}
                  </Badge>
                )}
              </Button>
            }
          />
        </div>
      )}

      {activeView === 'drawer' && (
        <div className="text-center space-y-4">
          <h2 className="text-2xl font-bold">Cart Drawer Demo</h2>
          <p className="text-muted-foreground">
            Click the button below to open the cart drawer
          </p>
          <CartDrawer
            userId={userId}
            sessionId={sessionId}
            trigger={
              <Button size="lg">
                <ShoppingBag className="h-5 w-5 mr-2" />
                Open Cart Drawer
                {cart && cart.itemCount > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {cart.itemCount}
                  </Badge>
                )}
              </Button>
            }
          />
        </div>
      )}

      {/* Component Features */}
      <div className="space-y-6">
        <Separator />
        <h2 className="text-2xl font-bold text-center">Enhanced Features</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="p-6 border rounded-lg">
            <h3 className="font-bold mb-3">🛒 Smart Cart Management</h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• Real-time inventory checking</li>
              <li>• Automatic price validation</li>
              <li>• Product availability alerts</li>
              <li>• Optimistic UI updates</li>
            </ul>
          </div>

          <div className="p-6 border rounded-lg">
            <h3 className="font-bold mb-3">📦 Product Integration</h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• Full product information</li>
              <li>• Variant support with images</li>
              <li>• Custom attributes & personalization</li>
              <li>• Gift wrap options</li>
            </ul>
          </div>

          <div className="p-6 border rounded-lg">
            <h3 className="font-bold mb-3">🎯 Smart Recommendations</h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• AI-powered product suggestions</li>
              <li>• Bundle opportunities</li>
              <li>• Alternative product options</li>
              <li>• Cross-sell recommendations</li>
            </ul>
          </div>

          <div className="p-6 border rounded-lg">
            <h3 className="font-bold mb-3">💰 Advanced Pricing</h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• Discount code support</li>
              <li>• Compare at pricing</li>
              <li>• Tax calculations</li>
              <li>• Shipping estimates</li>
            </ul>
          </div>

          <div className="p-6 border rounded-lg">
            <h3 className="font-bold mb-3">📊 Cart Analytics</h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• Category breakdown</li>
              <li>• Weight calculations</li>
              <li>• Value tracking</li>
              <li>• Performance metrics</li>
            </ul>
          </div>

          <div className="p-6 border rounded-lg">
            <h3 className="font-bold mb-3">🔄 State Management</h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• Automatic synchronization</li>
              <li>• Error handling & recovery</li>
              <li>• Loading states</li>
              <li>• Offline support</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EnhancedCartComponentsExample