"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import Link from "next/link"
import { useCart } from "@/lib/ecommerce/hooks/use-cart"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { X, AlertTriangle, Package, Heart } from "lucide-react"
import { EnhancedCartItem, ProductAvailabilityCheck } from "@/lib/ecommerce/types/cart"
import { cn } from "@/lib/utils"

interface CartItemProps {
  item: EnhancedCartItem
  isCompact?: boolean
  showProductLink?: boolean
  showAvailabilityCheck?: boolean
  className?: string
}

export function CartItem({ 
  item, 
  isCompact = false, 
  showProductLink = true,
  showAvailabilityCheck = true,
  className 
}: CartItemProps) {
  const [availability, setAvailability] = useState<ProductAvailabilityCheck | null>(null)
  const [loading, setLoading] = useState(false)

  const { 
    updateCartItem, 
    removeFromCart, 
    checkProductAvailability,
    getCartItemProduct,
    getCartItemVariant
  } = useCart()

  const product = getCartItemProduct(item.id)
  const variant = getCartItemVariant(item.id)

  // Check availability for out-of-stock items
  useEffect(() => {
    if (showAvailabilityCheck && !item.isAvailable) {
      checkProductAvailability(item.productId, item.variantId, item.quantity)
        .then(setAvailability)
        .catch(console.error)
    }
  }, [item.isAvailable, item.productId, item.variantId, item.quantity, showAvailabilityCheck, checkProductAvailability])

  const handleQuantityChange = async (value: string) => {
    const quantity = Number.parseInt(value)
    setLoading(true)
    try {
      await updateCartItem({ itemId: item.id, quantity })
    } catch (error) {
      console.error('Failed to update quantity:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRemove = async () => {
    setLoading(true)
    try {
      await removeFromCart({ itemId: item.id })
    } catch (error) {
      console.error('Failed to remove item:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatPrice = (amount: number) => `R${amount.toFixed(2)}`

  const getItemImage = () => {
    return item.variantImage?.url || 
           item.productImages?.[0]?.url || 
           item.productImage || 
           '/placeholder-product.jpg'
  }

  const getItemSlug = () => {
    return product?.slug || item.productSlug || '#'
  }

  const getVariantOptions = () => {
    return item.variantOptions || []
  }

  const ItemContent = ({ children }: { children: React.ReactNode }) => {
    if (showProductLink && getItemSlug() !== '#') {
      return (
        <Link href={`/products/${getItemSlug()}`} className="block hover:bg-muted/50 transition-colors rounded-md">
          {children}
        </Link>
      )
    }
    return <div>{children}</div>
  }

  if (isCompact) {
    return (
      <div className={cn("flex items-center space-x-4", className)}>
        <div className="relative w-16 h-16 bg-muted rounded-md overflow-hidden flex-shrink-0">
          <Image
            src={getItemImage()}
            alt={item.productTitle}
            fill
            className="object-cover"
          />
          {!item.isAvailable && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <AlertTriangle className="h-3 w-3 text-white" />
            </div>
          )}
        </div>

        <div className="flex-1 min-w-0">
          <ItemContent>
            <h4 className="text-sm font-medium truncate">{item.productTitle}</h4>
          </ItemContent>
          
          {/* Variant options */}
          <div className="text-xs text-muted-foreground">
            {getVariantOptions().map((option, index) => (
              <span key={index}>
                {option.name}: {option.value}
                {index < getVariantOptions().length - 1 && ' / '}
              </span>
            ))}
            {getVariantOptions().length > 0 && ' / '}
            Qty: {item.quantity}
          </div>

          {/* Availability warning */}
          {!item.isAvailable && (
            <div className="text-xs text-destructive flex items-center gap-1">
              <AlertTriangle className="h-3 w-3" />
              {availability?.isBackordered ? 'Backordered' : 'Out of stock'}
            </div>
          )}

          {/* Low stock warning */}
          {item.isAvailable && item.inventoryQuantity && item.inventoryQuantity <= 5 && (
            <div className="text-xs text-orange-600">
              Only {item.inventoryQuantity} left
            </div>
          )}

          <div className="flex items-center gap-2">
            <p className="text-sm font-medium">{formatPrice(item.unitPrice.amount * item.quantity)}</p>
            {item.compareAtPrice && (
              <p className="text-xs text-muted-foreground line-through">
                {formatPrice(item.compareAtPrice.amount * item.quantity)}
              </p>
            )}
            {item.discountAmount && item.discountAmount.amount > 0 && (
              <Badge variant="secondary" className="text-xs">
                Save {formatPrice(item.discountAmount.amount * item.quantity)}
              </Badge>
            )}
          </div>

          {/* Gift wrap indicator */}
          {item.giftWrap && (
            <div className="text-xs text-green-600 flex items-center gap-1">
              <Package className="h-3 w-3" />
              Gift wrapped
            </div>
          )}
        </div>

        <Button 
          variant="ghost" 
          size="icon" 
          className="h-8 w-8" 
          onClick={handleRemove}
          disabled={loading}
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Remove</span>
        </Button>
      </div>
    )
  }

  return (
    <div className={cn("flex items-start space-x-4 border rounded-md p-4", className)}>
      <div className="relative w-24 h-24 bg-muted rounded-md overflow-hidden flex-shrink-0">
        <Image
          src={getItemImage()}
          alt={item.productTitle}
          fill
          className="object-cover"
        />
        {!item.isAvailable && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <AlertTriangle className="h-4 w-4 text-white" />
          </div>
        )}
      </div>

      <div className="flex-1 min-w-0">
        <div className="flex justify-between">
          <ItemContent>
            <h4 className="text-base font-medium">{item.productTitle}</h4>
          </ItemContent>
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-8 w-8" 
            onClick={handleRemove}
            disabled={loading}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Remove</span>
          </Button>
        </div>

        {/* Variant options */}
        {getVariantOptions().length > 0 && (
          <div className="text-sm text-muted-foreground mb-2">
            {getVariantOptions().map((option, index) => (
              <span key={index}>
                {option.name}: {option.value}
                {index < getVariantOptions().length - 1 && ' / '}
              </span>
            ))}
          </div>
        )}

        {/* Variant title */}
        {item.variantTitle && (
          <div className="text-sm text-muted-foreground mb-2">
            {item.variantTitle}
          </div>
        )}

        {/* Availability warnings */}
        {!item.isAvailable && (
          <div className="text-sm text-destructive flex items-center gap-1 mb-2">
            <AlertTriangle className="h-4 w-4" />
            {availability?.isBackordered ? 'Backordered' : 'Out of stock'}
            {availability?.estimatedRestockDate && (
              <span className="text-muted-foreground">
                - Restocking {availability.estimatedRestockDate.toLocaleDateString()}
              </span>
            )}
          </div>
        )}

        {/* Low stock warning */}
        {item.isAvailable && item.inventoryQuantity && item.inventoryQuantity <= 5 && (
          <div className="text-sm text-orange-600 mb-2">
            Only {item.inventoryQuantity} left in stock
          </div>
        )}

        {/* Custom attributes */}
        {item.customAttributes && Object.keys(item.customAttributes).length > 0 && (
          <div className="text-sm text-muted-foreground mb-2">
            {Object.entries(item.customAttributes).map(([key, value]) => (
              <span key={key} className="mr-3">
                {key}: {String(value)}
              </span>
            ))}
          </div>
        )}

        {/* Personalized message */}
        {item.personalizedMessage && (
          <div className="text-sm text-muted-foreground italic mb-2">
            Message: "{item.personalizedMessage}"
          </div>
        )}

        {/* Gift wrap indicator */}
        {item.giftWrap && (
          <div className="text-sm text-green-600 flex items-center gap-1 mb-2">
            <Package className="h-4 w-4" />
            Gift wrapped
          </div>
        )}

        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Select 
              value={item.quantity.toString()} 
              onValueChange={handleQuantityChange}
              disabled={loading || (!item.isAvailable && item.inventoryPolicy === 'deny')}
            >
              <SelectTrigger className="w-20">
                <SelectValue placeholder="1" />
              </SelectTrigger>
              <SelectContent>
                {Array.from({ length: Math.min(10, item.inventoryQuantity || 10) }, (_, i) => i + 1).map((num) => (
                  <SelectItem key={num} value={num.toString()}>
                    {num}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Add to wishlist button */}
            <Button variant="ghost" size="sm" className="text-muted-foreground">
              <Heart className="h-4 w-4 mr-1" />
              Save for later
            </Button>
          </div>

          <div className="text-right">
            <div className="flex items-center gap-2">
              <p className="text-base font-medium">{formatPrice(item.unitPrice.amount * item.quantity)}</p>
              {item.compareAtPrice && (
                <p className="text-sm text-muted-foreground line-through">
                  {formatPrice(item.compareAtPrice.amount * item.quantity)}
                </p>
              )}
            </div>
            {item.discountAmount && item.discountAmount.amount > 0 && (
              <Badge variant="secondary" className="text-xs mt-1">
                Save {formatPrice(item.discountAmount.amount * item.quantity)}
              </Badge>
            )}
            <div className="text-xs text-muted-foreground mt-1">
              {formatPrice(item.unitPrice.amount)} each
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
