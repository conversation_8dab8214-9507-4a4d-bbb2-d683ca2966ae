# Enhanced Cart Sidebar Component

The enhanced `CartSidebar` component provides a comprehensive shopping cart experience with deep integration to the product type system and advanced e-commerce features.

## Features

### 🛒 **Core Cart Functionality**
- Real-time cart updates using the enhanced `useCart` hook
- Automatic cart fetching and synchronization
- Loading states and error handling
- Optimistic UI updates

### 📦 **Product-Aware Features**
- **Full Product Information**: Display complete product details including images, variants, and attributes
- **Inventory Management**: Real-time stock checking with low stock warnings
- **Variant Support**: Display variant options, titles, and specific variant images
- **Product Customization**: Show custom attributes, personalized messages, and gift wrap options

### 🚨 **Smart Notifications**
- **Out of Stock Alerts**: Visual indicators for unavailable items
- **Low Stock Warnings**: Alert when items are running low
- **Availability Checking**: Real-time inventory validation
- **Restock Notifications**: Show estimated restock dates

### 💰 **Enhanced Pricing**
- **Discount Display**: Show savings and applied discounts
- **Compare Prices**: Display original vs. sale prices
- **Tax and Shipping**: Comprehensive order summary
- **Bundle Opportunities**: Suggest money-saving bundles

### 🎯 **Smart Recommendations**
- **AI-Powered Suggestions**: Product recommendations based on cart contents
- **Collapsible Interface**: Show/hide recommendations to save space
- **Quick Add**: One-click addition of recommended products

### 📊 **Cart Analytics**
- **Weight Calculation**: Total cart weight for shipping estimates
- **Category Breakdown**: Understand cart composition
- **Value Tracking**: Real-time cart value updates

## Usage

### Basic Usage

```tsx
import { CartSidebar } from '@/components/storefront/cart/cart-sidebar'

function MyComponent() {
  return (
    <CartSidebar
      userId="user-123"
      sessionId="session-456"
    />
  )
}
```

### Advanced Usage

```tsx
import { CartSidebar } from '@/components/storefront/cart/cart-sidebar'
import { Button } from '@/components/ui/button'
import { ShoppingBag } from 'lucide-react'

function HeaderComponent() {
  const [isCartOpen, setIsCartOpen] = useState(false)

  const customTrigger = (
    <Button variant="outline" className="relative">
      <ShoppingBag className="h-4 w-4" />
      <span className="ml-2">My Cart</span>
    </Button>
  )

  return (
    <header className="flex justify-between items-center p-4">
      <h1>My Store</h1>
      
      <CartSidebar
        userId="user-123"
        sessionId="session-456"
        isOpen={isCartOpen}
        onOpenChange={setIsCartOpen}
        trigger={customTrigger}
        className="w-full max-w-lg"
      />
    </header>
  )
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `isOpen` | `boolean` | `undefined` | Controlled open state |
| `onOpenChange` | `(open: boolean) => void` | `undefined` | Open state change handler |
| `className` | `string` | `undefined` | Additional CSS classes |
| `trigger` | `React.ReactNode` | Default cart button | Custom trigger element |
| `userId` | `string` | `undefined` | User ID for cart association |
| `sessionId` | `string` | `undefined` | Session ID for guest carts |

## Enhanced Features

### 1. **Product Information Display**

The component automatically displays:
- Product images (with fallback to variant or placeholder images)
- Product titles with clickable links to product pages
- Variant information (size, color, etc.)
- Custom attributes and personalization
- Gift wrap indicators

### 2. **Inventory Management**

- **Real-time Stock Checking**: Validates inventory when items are added/updated
- **Visual Indicators**: Out-of-stock items show warning overlays
- **Low Stock Alerts**: Warns when items are running low (≤5 in stock)
- **Backorder Support**: Shows backorder status and estimated restock dates

### 3. **Smart Pricing**

```tsx
// Displays multiple price points
<div className="pricing">
  <span className="current-price">R299.99</span>
  <span className="compare-price">R399.99</span>
  <Badge className="savings">Save R100.00</Badge>
</div>
```

### 4. **Bundle Recommendations**

Automatically suggests product bundles that can save money:

```tsx
{getAvailableBundles().map(bundle => (
  <div key={bundle.bundleId} className="bundle-suggestion">
    <span>{bundle.title}</span>
    <span className="savings">Save {formatPrice(bundle.savings)}</span>
    <Button onClick={() => addBundle(bundle.bundleId)}>
      Add Bundle
    </Button>
  </div>
))}
```

### 5. **Product Recommendations**

Shows AI-powered product suggestions:

```tsx
{recommendations.map(product => (
  <div key={product.id} className="recommendation">
    <span>{product.title}</span>
    <span>{formatPrice(product.price.amount)}</span>
    <Button onClick={() => addToCart(product.id)}>Add</Button>
  </div>
))}
```

## State Management

The component uses the enhanced `useCart` hook which provides:

- **Automatic Fetching**: Cart data is fetched automatically
- **Real-time Updates**: Changes are reflected immediately
- **Error Handling**: Graceful error states and recovery
- **Loading States**: Visual feedback during operations

## Styling

The component uses Tailwind CSS classes and shadcn/ui components:

- **Responsive Design**: Works on mobile and desktop
- **Dark Mode Support**: Automatically adapts to theme
- **Customizable**: Override styles with the `className` prop

## Error Handling

The component handles various error states:

- **Network Errors**: Shows retry options
- **Inventory Issues**: Prevents invalid operations
- **Validation Errors**: Clear error messages
- **Loading States**: Visual feedback during operations

## Performance Optimizations

- **Lazy Loading**: Recommendations loaded on demand
- **Debounced Updates**: Quantity changes are debounced
- **Optimistic Updates**: UI updates immediately
- **Efficient Re-renders**: Minimal unnecessary re-renders

## Integration with Product Types

The component seamlessly integrates with the comprehensive product type system:

```typescript
// Automatically handles complex product structures
interface EnhancedCartItem {
  product?: Product              // Full product information
  variant?: ProductVariant       // Variant details
  availability?: ProductAvailability  // Real-time availability
  productImages?: ProductImage[] // Multiple images
  variantOptions?: VariantOption[] // Size, color, etc.
  customAttributes?: Record<string, any> // Customization
  // ... and much more
}
```

## Migration from Basic Cart

To migrate from a basic cart implementation:

1. **Remove old props**: No need to pass `items`, `onUpdateQuantity`, etc.
2. **Add user/session IDs**: Pass `userId` and `sessionId` for cart association
3. **Update styling**: The component now handles all cart logic internally
4. **Remove manual state management**: The hook handles all cart state

### Before (Basic Cart)
```tsx
<CartSidebar
  items={cartItems}
  onUpdateQuantity={handleUpdateQuantity}
  onRemoveItem={handleRemoveItem}
  onClearCart={handleClearCart}
/>
```

### After (Enhanced Cart)
```tsx
<CartSidebar
  userId="user-123"
  sessionId="session-456"
/>
```

## API Requirements

The enhanced cart sidebar expects these API endpoints:

- `GET /api/e-commerce/cart?includeProducts=true` - Fetch enhanced cart
- `PUT /api/e-commerce/cart/update` - Update cart items
- `DELETE /api/e-commerce/cart/remove` - Remove cart items
- `DELETE /api/e-commerce/cart/clear` - Clear cart
- `GET /api/e-commerce/products/availability` - Check availability
- `POST /api/e-commerce/cart/recommendations` - Get recommendations

## Benefits

1. **Better UX**: Rich product information and smart features
2. **Increased Sales**: Recommendations and bundle suggestions
3. **Reduced Abandonment**: Proactive inventory management
4. **Real-time Updates**: Always accurate cart state
5. **Mobile Optimized**: Great experience on all devices
6. **Accessible**: Proper ARIA labels and keyboard navigation

## Examples

See `enhanced-cart-sidebar-example.tsx` for complete usage examples including:

- Basic implementation
- Custom triggers
- Controlled state
- Header integration
- Error handling patterns