# Enhanced Cart Components Suite

A comprehensive collection of cart components that provide a complete e-commerce cart experience with deep product integration, smart features, and modern UX patterns.

## 🚀 Components Overview

### 1. **CartSidebar** - Slide-out cart panel
- **Purpose**: Quick cart access from any page
- **Features**: Compact view, recommendations, bundle suggestions
- **Best for**: Header integration, quick checkout flows

### 2. **CartDrawer** - Full-screen mobile cart
- **Purpose**: Mobile-optimized cart experience
- **Features**: Full product details, enhanced mobile UX
- **Best for**: Mobile devices, detailed cart management

### 3. **CartItem** - Individual cart item component
- **Purpose**: Display and manage individual cart items
- **Features**: Product info, availability checking, customization display
- **Best for**: Cart pages, order summaries

### 4. **CartSummary** - Order summary and checkout
- **Purpose**: Display totals, discounts, and checkout options
- **Features**: Discount codes, bundle suggestions, shipping estimates
- **Best for**: Checkout flows, cart pages

### 5. **CartProvider** - Legacy compatibility layer
- **Purpose**: Backward compatibility with existing cart implementations
- **Features**: Automatic migration, fallback support
- **Best for**: Gradual migration from old cart systems

## 🛠 Installation & Setup

### Basic Setup

```tsx
// 1. Import the enhanced cart hook
import { useCart } from '@/lib/ecommerce/hooks/use-cart'

// 2. Use in your component
function MyComponent() {
  const { cart, addToCart, loading } = useCart({
    userId: 'user-123',
    sessionId: 'session-456',
    autoFetch: true
  })

  return (
    <CartSidebar userId="user-123" sessionId="session-456" />
  )
}
```

### Advanced Setup with All Components

```tsx
import { 
  CartSidebar, 
  CartDrawer, 
  CartItem, 
  CartSummary 
} from '@/components/storefront/cart'

function CompleteCartExample() {
  const userId = 'user-123'
  const sessionId = 'session-456'

  return (
    <div>
      {/* Header with cart sidebar */}
      <header>
        <CartSidebar userId={userId} sessionId={sessionId} />
      </header>

      {/* Mobile cart drawer */}
      <CartDrawer userId={userId} sessionId={sessionId} />

      {/* Full cart page */}
      <div className="cart-page">
        <div className="cart-items">
          {/* Cart items will be automatically loaded */}
        </div>
        <div className="cart-summary">
          <CartSummary userId={userId} sessionId={sessionId} />
        </div>
      </div>
    </div>
  )
}
```

## 📱 Component Details

### CartSidebar

**Props:**
```tsx
interface CartSidebarProps {
  isOpen?: boolean
  onOpenChange?: (open: boolean) => void
  className?: string
  trigger?: React.ReactNode
  userId?: string
  sessionId?: string
}
```

**Features:**
- Slide-out panel design
- Compact item display
- Quick quantity adjustments
- Smart recommendations
- Bundle suggestions
- Real-time inventory checking

**Usage:**
```tsx
<CartSidebar
  userId="user-123"
  sessionId="session-456"
  trigger={<Button>Cart ({itemCount})</Button>}
  className="w-full max-w-md"
/>
```

### CartDrawer

**Props:**
```tsx
interface CartDrawerProps {
  isOpen?: boolean
  onOpenChange?: (open: boolean) => void
  className?: string
  trigger?: React.ReactNode
  userId?: string
  sessionId?: string
}
```

**Features:**
- Full-screen mobile experience
- Detailed product information
- Enhanced mobile interactions
- Swipe gestures support
- Progressive loading

**Usage:**
```tsx
<CartDrawer
  userId="user-123"
  sessionId="session-456"
  className="sm:max-w-lg"
/>
```

### CartItem

**Props:**
```tsx
interface CartItemProps {
  item: EnhancedCartItem
  isCompact?: boolean
  showProductLink?: boolean
  showAvailabilityCheck?: boolean
  className?: string
}
```

**Features:**
- Two display modes (compact/full)
- Product link integration
- Availability checking
- Custom attributes display
- Gift wrap indicators
- Personalization support

**Usage:**
```tsx
{cartItems.map(item => (
  <CartItem
    key={item.id}
    item={item}
    isCompact={false}
    showProductLink={true}
    showAvailabilityCheck={true}
  />
))}
```

### CartSummary

**Props:**
```tsx
interface CartSummaryProps {
  userId?: string
  sessionId?: string
  showDiscountCode?: boolean
  showShippingEstimate?: boolean
  className?: string
}
```

**Features:**
- Order total calculations
- Discount code application
- Shipping estimates
- Tax calculations
- Bundle recommendations
- Checkout integration

**Usage:**
```tsx
<CartSummary
  userId="user-123"
  sessionId="session-456"
  showDiscountCode={true}
  showShippingEstimate={true}
/>
```

## 🎨 Styling & Customization

### Theme Integration

All components use Tailwind CSS and shadcn/ui components:

```tsx
// Custom styling example
<CartSidebar
  className="w-full max-w-lg border-l-2 border-primary"
  userId={userId}
  sessionId={sessionId}
/>
```

### Dark Mode Support

Components automatically adapt to your theme:

```tsx
// No additional configuration needed
<div className="dark">
  <CartSidebar userId={userId} sessionId={sessionId} />
</div>
```

### Custom Triggers

Create custom cart triggers:

```tsx
const CustomTrigger = () => (
  <Button variant="ghost" className="relative">
    <ShoppingBag className="h-5 w-5" />
    {itemCount > 0 && (
      <Badge className="absolute -top-2 -right-2">
        {itemCount}
      </Badge>
    )}
  </Button>
)

<CartSidebar
  trigger={<CustomTrigger />}
  userId={userId}
  sessionId={sessionId}
/>
```

## 🔧 Advanced Features

### Product Integration

Components automatically display:
- Product images and galleries
- Variant information (size, color, etc.)
- Custom attributes and personalization
- Gift wrap options
- Inventory status

### Smart Recommendations

AI-powered features include:
- Frequently bought together
- Similar products
- Bundle opportunities
- Upsell suggestions

### Inventory Management

Real-time inventory features:
- Stock level checking
- Out-of-stock warnings
- Low stock alerts
- Backorder support
- Restock notifications

### Pricing Intelligence

Advanced pricing features:
- Dynamic pricing updates
- Discount calculations
- Compare-at pricing
- Bundle savings
- Tax calculations

## 📊 Analytics & Insights

### Cart Analytics

Built-in analytics provide:
- Category breakdown
- Cart value tracking
- Conversion insights
- Abandonment detection

### Performance Monitoring

Components include:
- Loading state management
- Error boundary protection
- Performance optimization
- Memory leak prevention

## 🔄 State Management

### Automatic Synchronization

- Real-time cart updates
- Cross-device synchronization
- Offline support
- Conflict resolution

### Error Handling

- Network error recovery
- Validation error display
- Graceful degradation
- Retry mechanisms

## 🚀 Migration Guide

### From Basic Cart

1. **Update imports:**
```tsx
// Before
import { useCart } from '@/hooks/use-cart'

// After
import { useCart } from '@/lib/ecommerce/hooks/use-cart'
```

2. **Update component usage:**
```tsx
// Before
<CartSidebar
  items={cartItems}
  onUpdateQuantity={handleUpdate}
  onRemoveItem={handleRemove}
/>

// After
<CartSidebar
  userId="user-123"
  sessionId="session-456"
/>
```

3. **Remove manual state management:**
The enhanced components handle all cart state automatically.

### Gradual Migration

Use `CartProvider` for gradual migration:

```tsx
// Wrap your app to maintain compatibility
<CartProvider>
  <YourExistingApp />
  {/* New enhanced components work alongside old ones */}
  <CartSidebar userId={userId} sessionId={sessionId} />
</CartProvider>
```

## 🎯 Best Practices

### Performance

1. **Use autoFetch wisely:**
```tsx
// Only fetch when needed
const { cart } = useCart({
  userId,
  sessionId,
  autoFetch: isCartVisible
})
```

2. **Implement proper loading states:**
```tsx
{loading ? <CartSkeleton /> : <CartItems />}
```

### User Experience

1. **Provide clear feedback:**
```tsx
// Components automatically show loading/error states
// But you can customize them
{error && <CustomErrorMessage error={error} />}
```

2. **Handle edge cases:**
```tsx
// Out of stock handling
{hasOutOfStockItems() && <OutOfStockWarning />}
```

### Accessibility

Components include:
- Proper ARIA labels
- Keyboard navigation
- Screen reader support
- Focus management

## 🔗 API Integration

### Required Endpoints

Components expect these API endpoints:

```
GET    /api/e-commerce/cart?includeProducts=true
POST   /api/e-commerce/cart/add
PUT    /api/e-commerce/cart/update
DELETE /api/e-commerce/cart/remove
DELETE /api/e-commerce/cart/clear
POST   /api/e-commerce/cart/discount/apply
DELETE /api/e-commerce/cart/discount/remove
POST   /api/e-commerce/cart/validate
GET    /api/e-commerce/products/availability
POST   /api/e-commerce/cart/recommendations
```

### Response Formats

See the enhanced cart types for expected response formats.

## 🎉 Benefits

### For Developers
- **Reduced Development Time**: Pre-built, tested components
- **Type Safety**: Full TypeScript support
- **Flexibility**: Highly customizable and extensible
- **Performance**: Optimized for speed and efficiency

### For Users
- **Better Experience**: Smooth, responsive interactions
- **Smart Features**: AI-powered recommendations
- **Real-time Updates**: Always accurate cart state
- **Mobile Optimized**: Great experience on all devices

### For Business
- **Increased Sales**: Smart recommendations and bundles
- **Reduced Abandonment**: Proactive inventory management
- **Better Analytics**: Comprehensive cart insights
- **Scalability**: Built for high-traffic applications

## 📚 Examples

See the `enhanced-cart-components-example.tsx` file for a complete working example that demonstrates all components and features.

## 🆘 Troubleshooting

### Common Issues

1. **Cart not loading:**
   - Check userId/sessionId are provided
   - Verify API endpoints are accessible
   - Check network connectivity

2. **Items not updating:**
   - Ensure proper error handling
   - Check API response formats
   - Verify cart synchronization

3. **Performance issues:**
   - Use autoFetch conditionally
   - Implement proper loading states
   - Check for memory leaks

### Debug Mode

Enable debug logging:
```tsx
const { cart } = useCart({
  userId,
  sessionId,
  debug: true // Enables console logging
})
```

## 🔮 Future Enhancements

Planned features:
- Voice commerce integration
- AR/VR cart visualization
- Advanced personalization
- Multi-currency support
- Social commerce features

---

The enhanced cart components provide a complete, production-ready cart solution that scales with your business needs while providing an exceptional user experience.