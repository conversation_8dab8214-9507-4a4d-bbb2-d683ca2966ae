"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Ruler } from "lucide-react"

export function SizeGuideSA() {
  const [open, setOpen] = useState(false)

  const sizeCharts = {
    tops: [
      { size: "XS", age: "2-3 years", chest: "53-55", length: "35-37", weight: "12-14" },
      { size: "S", age: "4-5 years", chest: "56-58", length: "38-40", weight: "15-18" },
      { size: "M", age: "6-7 years", chest: "59-61", length: "41-43", weight: "19-22" },
      { size: "L", age: "8-9 years", chest: "62-65", length: "44-46", weight: "23-27" },
      { size: "XL", age: "10-11 years", chest: "66-69", length: "47-49", weight: "28-32" },
    ],
    bottoms: [
      { size: "XS", age: "2-3 years", waist: "48-50", hip: "54-56", inseam: "35-38", weight: "12-14" },
      { size: "S", age: "4-5 years", waist: "51-53", hip: "57-59", inseam: "39-42", weight: "15-18" },
      { size: "M", age: "6-7 years", waist: "54-56", hip: "60-62", inseam: "43-46", weight: "19-22" },
      { size: "L", age: "8-9 years", waist: "57-60", hip: "63-66", inseam: "47-50", weight: "23-27" },
      { size: "XL", age: "10-11 years", waist: "61-64", hip: "67-70", inseam: "51-54", weight: "28-32" },
    ],
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="text-xs">
          <Ruler className="h-3 w-3 mr-1" />
          Size Guide
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>South African Size Guide</DialogTitle>
          <DialogDescription>
            Find the perfect fit for your little one. All measurements are in centimeters and weights in kilograms.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="tops" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="tops">Tops & Dresses</TabsTrigger>
            <TabsTrigger value="bottoms">Bottoms</TabsTrigger>
          </TabsList>

          <TabsContent value="tops" className="space-y-4">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Size</th>
                    <th className="text-left py-2">Age</th>
                    <th className="text-left py-2">Chest (cm)</th>
                    <th className="text-left py-2">Length (cm)</th>
                    <th className="text-left py-2">Weight (kg)</th>
                  </tr>
                </thead>
                <tbody>
                  {sizeCharts.tops.map((row) => (
                    <tr key={row.size} className="border-b">
                      <td className="py-2 font-medium">{row.size}</td>
                      <td className="py-2">{row.age}</td>
                      <td className="py-2">{row.chest}</td>
                      <td className="py-2">{row.length}</td>
                      <td className="py-2">{row.weight}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>

          <TabsContent value="bottoms" className="space-y-4">
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Size</th>
                    <th className="text-left py-2">Age</th>
                    <th className="text-left py-2">Waist (cm)</th>
                    <th className="text-left py-2">Hip (cm)</th>
                    <th className="text-left py-2">Inseam (cm)</th>
                    <th className="text-left py-2">Weight (kg)</th>
                  </tr>
                </thead>
                <tbody>
                  {sizeCharts.bottoms.map((row) => (
                    <tr key={row.size} className="border-b">
                      <td className="py-2 font-medium">{row.size}</td>
                      <td className="py-2">{row.age}</td>
                      <td className="py-2">{row.waist}</td>
                      <td className="py-2">{row.hip}</td>
                      <td className="py-2">{row.inseam}</td>
                      <td className="py-2">{row.weight}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>
        </Tabs>

        <div className="space-y-4 pt-4 border-t">
          <h4 className="font-medium">How to Measure</h4>
          <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-600">
            <div>
              <h5 className="font-medium text-gray-900 mb-2">For Tops:</h5>
              <ul className="space-y-1">
                <li><strong>Chest:</strong> Measure around the fullest part of the chest</li>
                <li><strong>Length:</strong> Measure from shoulder to hem</li>
              </ul>
            </div>
            <div>
              <h5 className="font-medium text-gray-900 mb-2">For Bottoms:</h5>
              <ul className="space-y-1">
                <li><strong>Waist:</strong> Measure around the natural waistline</li>
                <li><strong>Hip:</strong> Measure around the fullest part of the hips</li>
                <li><strong>Inseam:</strong> Measure from crotch to ankle</li>
              </ul>
            </div>
          </div>
          <p className="text-xs text-gray-500">
            Our clothes are designed with a relaxed, oversized fit. When in doubt, size up for growing room.
          </p>
        </div>
      </DialogContent>
    </Dialog>
  )
}
