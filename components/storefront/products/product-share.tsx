'use client'

import { But<PERSON> } from "@/components/ui/button"
import { Share } from "lucide-react"
import { toast } from "sonner"

interface ProductShareProps {
  productName: string
  productUrl: string
}

export function ProductShare({ productName, productUrl }: ProductShareProps) {
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: productName,
          url: productUrl,
        })
      } catch (error) {
        console.error('Error sharing:', error)
      }
    } else {
      try {
        await navigator.clipboard.writeText(productUrl)
        toast.success('Link copied to clipboard!')
      } catch (error) {
        console.error('Error copying to clipboard:', error)
        toast.error('Failed to copy link')
      }
    }
  }

  return (
    <Button 
      variant="outline" 
      size="sm" 
      onClick={handleShare}
      className="flex items-center gap-2"
    >
      <Share className="h-4 w-4" />
      Share
    </Button>
  )
}