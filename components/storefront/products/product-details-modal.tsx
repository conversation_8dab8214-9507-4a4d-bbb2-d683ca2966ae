'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel"
import { Badge } from "@/components/ui/badge"
import { 
  ShoppingBag, 
  Heart,
  Star,
  Truck,
  ArrowRight,
  Zap,
  TrendingUp,
  Clock
} from "lucide-react"
import { cn } from "@/lib/utils"
import Image from "next/image"
import { useState } from "react"
import { EcommerceProduct, LegacyProduct } from "@/types"

interface ProductDetailsModalProps {
  product: EcommerceProduct | LegacyProduct
  isOpen: boolean
  onOpenChange: (open: boolean) => void
  onAddToCart: (e: React.MouseEvent) => Promise<void>
  onWishlistToggle: (e: React.MouseEvent) => void
  isWishlisted: boolean
  formatPrice: (price: number) => string
  isLegacyProduct: (product: EcommerceProduct | LegacyProduct) => product is LegacyProduct
}

export function ProductDetailsModal({
  product,
  isOpen,
  onOpenChange,
  onAddToCart,
  onWishlistToggle,
  isWishlisted,
  formatPrice,
  isLegacyProduct
}: ProductDetailsModalProps) {
  const [selectedImage, setSelectedImage] = useState(0)
  const [isAddingToCart, setIsAddingToCart] = useState(false)

  // Handle both legacy and new product formats
  const price = isLegacyProduct(product) ? 
    product.price : 
    (typeof product.price === 'object' ? product.price.amount : product.price)
  
  const compareAtPrice = isLegacyProduct(product) ?
    product.compareAtPrice :
    (product.compareAtPrice ? 
      (typeof product.compareAtPrice === 'object' ? product.compareAtPrice.amount : product.compareAtPrice) :
      undefined)

  const title = isLegacyProduct(product) ? product.name : product.title
  const images = isLegacyProduct(product) ? 
    product.images : 
    (product.images?.map(img => img.url) || [])
  const isOnSale = compareAtPrice ? compareAtPrice > price : false
  const isNew = isLegacyProduct(product) ? 
    product.isNew || false : 
    (product.createdAt && new Date(product.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
  const vendor = isLegacyProduct(product) ? 'Coco Milk Kids' : (product.vendor || 'Coco Milk Kids')
  const colors = isLegacyProduct(product) ? product.colors : []
  const sizes = isLegacyProduct(product) ? product.sizes : []

  const discount = compareAtPrice
    ? Math.round(((compareAtPrice - price) / compareAtPrice) * 100)
    : 0

  const rating = 4.2 + Math.random() * 0.8 // Mock rating
  const reviewCount = Math.floor(Math.random() * 50) + 5 // Mock review count

  const handleAddToCart = async (e: React.MouseEvent) => {
    setIsAddingToCart(true)
    await onAddToCart(e)
    setIsAddingToCart(false)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">{title}</DialogTitle>
        </DialogHeader>
        
        <div className="grid gap-6 md:grid-cols-2">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative aspect-square overflow-hidden rounded-lg bg-gray-100">
              <Image
                src={images[selectedImage] || "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"}
                alt={title}
                fill
                className="object-cover"
                priority
              />
              {isOnSale && (
                <Badge variant="destructive" className="absolute top-2 left-2 text-xs font-medium">
                  <Zap className="w-3 h-3 mr-1" />
                  {discount}% OFF
                </Badge>
              )}
              {isNew && !isOnSale && (
                <Badge className="absolute top-2 left-2 text-xs font-medium bg-green-500 hover:bg-green-600">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  New
                </Badge>
              )}
            </div>
            {images.length > 1 && (
              <div className="flex space-x-2">
                {images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(index)}
                    className={cn(
                      "relative aspect-square w-16 overflow-hidden rounded-md bg-gray-100",
                      selectedImage === index && "ring-2 ring-primary"
                    )}
                  >
                    <Image
                      src={image}
                      alt={`${title} - Image ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <p className="text-sm text-gray-500">{vendor}</p>
              
              {/* Rating */}
              <div className="mt-2 flex items-center space-x-1">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={cn(
                        "w-4 h-4",
                        i < Math.floor(rating) 
                          ? "fill-yellow-400 text-yellow-400" 
                          : "text-gray-300"
                      )}
                    />
                  ))}
                </div>
                <span className="text-sm text-gray-500">
                  {rating.toFixed(1)} ({reviewCount})
                </span>
              </div>

              {/* Price */}
              <div className="mt-4 flex items-end space-x-3">
                <span className="text-2xl font-semibold text-gray-900">
                  {formatPrice(price)}
                </span>
                {compareAtPrice && (
                  <span className="text-lg text-gray-500 line-through">
                    {formatPrice(compareAtPrice)}
                  </span>
                )}
              </div>
            </div>

            {/* Color Options */}
            {colors.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium">Color</p>
                <div className="flex flex-wrap gap-2">
                  {colors.map((color) => (
                    <div
                      key={color.name}
                      className="relative h-8 w-8 cursor-pointer rounded-full border"
                      style={{ backgroundColor: color.value }}
                      title={color.name}
                    />
                  ))}
                </div>
              </div>
            )}

            {/* Size Options */}
            {sizes.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium">Size</p>
                <div className="flex flex-wrap gap-2">
                  {sizes.map((size) => (
                    <Button
                      key={size}
                      variant="outline"
                      className="h-8 px-3"
                      size="sm"
                    >
                      {size}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex space-x-2">
              <Button 
                className="flex-1" 
                onClick={handleAddToCart}
                disabled={isAddingToCart}
              >
                <ShoppingBag className="w-4 h-4 mr-2" />
                {isAddingToCart ? "Adding..." : "Add to Cart"}
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={onWishlistToggle}
              >
                <Heart className={cn(
                  "h-4 w-4 transition-colors",
                  isWishlisted ? "fill-red-500 text-red-500" : "text-gray-600"
                )} />
              </Button>
            </div>

            {/* Shipping Info */}
            <div className="space-y-3 border-t pt-4">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Truck className="h-4 w-4" />
                <span>Free shipping on orders over $50</span>
              </div>
              {isOnSale && (
                <div className="flex items-center space-x-2 text-sm text-red-600">
                  <Clock className="h-4 w-4" />
                  <span>Limited time offer</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
