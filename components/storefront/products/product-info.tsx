"use client"

import { cn } from "@/lib/utils"

import { useState, useEffect } from "react"
import { useCart } from "@/lib/ecommerce/hooks/use-cart"
import { useWishlist } from "@/lib/ecommerce/hooks/use-wishlist"
import { useAuth } from "@/lib/ecommerce/hooks/use-auth"
import { usePriceFormatter } from "@/hooks/use-price-formatter"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Heart, ShoppingBag } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { SocialShare } from "@/components/social-share"
import { SizeGuide } from "@/components/storefront/products/size-guide"
import { InventoryStatus } from "@/components/inventory-status"

interface ProductInfoProps {
  product: {
    id: string
    name: string
    description: string
    price: number
    compareAtPrice?: number
    colors: { name: string; value: string }[]
    sizes: string[]
    slug: string
    categoryId: string
  }
}

export function ProductInfo({ product }: ProductInfoProps) {
  const [selectedColor, setSelectedColor] = useState(product.colors[0]?.value || "")
  const [selectedSize, setSelectedSize] = useState(product.sizes[0] || "")
  const [quantity, setQuantity] = useState(1)
  const { addToCart } = useCart()
  const { user } = useAuth()
  const {
    isInWishlist,
    toggleWishlist,
    loading: wishlistLoading
  } = useWishlist({
    userId: user?.id,
    autoFetch: true
  })
  const { formatPrice } = usePriceFormatter()
  const isWishlisted = isInWishlist(product.id)

  const handleAddToCart = async () => {
    try {
      console.log('Adding to cart:', {
        productId: product.id,
        quantity,
        customAttributes: {
          color: selectedColor,
          size: selectedSize
        }
      })

      await addToCart({
        productId: product.id,
        quantity,
        customAttributes: {
          color: selectedColor,
          size: selectedSize
        }
      })

      toast({
        title: "Added to cart",
        description: `${product.name} has been added to your cart.`,
      })
    } catch (error) {
      console.error('Failed to add to cart:', error)
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive"
      })
    }
  }

  const handleToggleWishlist = async () => {
    try {
      await toggleWishlist(product.id)

      toast({
        title: isWishlisted ? "Removed from wishlist" : "Added to wishlist",
        description: `${product.name} has been ${isWishlisted ? "removed from" : "added to"} your wishlist.`,
      })
    } catch (error) {
      console.error('Failed to toggle wishlist:', error)
      toast({
        title: "Error",
        description: "Failed to update wishlist. Please try again.",
        variant: "destructive"
      })
    }
  }

  const discount = product.compareAtPrice
    ? Math.round(((product.compareAtPrice - product.price) / product.compareAtPrice) * 100)
    : 0

  // Use useEffect to set the URL after component mounts to avoid hydration mismatch
  const [productUrl, setProductUrl] = useState("")

  useEffect(() => {
    setProductUrl(window.location.href)
  }, [])

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl md:text-3xl font-light font-montserrat tracking-wide">{product.name}</h1>

        <div className="flex items-center space-x-3 mt-3">
          <span className="text-xl font-light">{formatPrice(product.price)}</span>

          {product.compareAtPrice && (
            <>
              <span className="text-muted-foreground line-through font-light">
                {formatPrice(product.compareAtPrice)}
              </span>
              <span className="bg-[#6C1411] text-white text-xs font-light px-2 py-1">Save {discount}%</span>
            </>
          )}
        </div>
      </div>

      <div className="flex items-center space-x-4">
        <InventoryStatus stock={15} />
        <SizeGuide category={product.categoryId} />
      </div>

      <div className="space-y-6">
        <div>
          <h3 className="text-sm font-light mb-3 tracking-wide">Color</h3>
          <RadioGroup value={selectedColor} onValueChange={setSelectedColor} className="flex flex-wrap gap-3">
            {product.colors.map((color) => (
              <div key={color.value} className="flex items-center space-x-2">
                <RadioGroupItem value={color.value} id={`color-${color.value}`} className="sr-only" />
                <Label
                  htmlFor={`color-${color.value}`}
                  className={cn(
                    "flex items-center justify-center w-8 h-8 rounded-full cursor-pointer transition-all duration-300",
                    "border-2 border-transparent hover:border-muted-foreground",
                    selectedColor === color.value && "border-black",
                  )}
                >
                  <span className="w-6 h-6 rounded-full" style={{ backgroundColor: color.value }} />
                  <span className="sr-only">{color.name}</span>
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        <div>
          <h3 className="text-sm font-light mb-3 tracking-wide">Size</h3>
          <RadioGroup value={selectedSize} onValueChange={setSelectedSize} className="flex flex-wrap gap-3">
            {product.sizes.map((size) => (
              <div key={size} className="flex items-center space-x-2">
                <RadioGroupItem value={size} id={`size-${size}`} className="sr-only" />
                <Label
                  htmlFor={`size-${size}`}
                  className={cn(
                    "flex items-center justify-center w-10 h-10 cursor-pointer transition-all duration-300",
                    "border hover:bg-muted",
                    selectedSize === size && "bg-black text-white hover:bg-black/90",
                  )}
                >
                  {size}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        <div>
          <h3 className="text-sm font-light mb-3 tracking-wide">Quantity</h3>
          <Select value={quantity.toString()} onValueChange={(value) => setQuantity(Number.parseInt(value))}>
            <SelectTrigger className="w-20 rounded-none font-light">
              <SelectValue placeholder="1" />
            </SelectTrigger>
            <SelectContent>
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((num) => (
                <SelectItem key={num} value={num.toString()} className="font-light">
                  {num}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <Button
          onClick={handleAddToCart}
          className="flex-1 bg-[#012169] hover:bg-[#012169]/90 rounded-none font-light tracking-widest text-sm py-6"
        >
          <ShoppingBag className="w-4 h-4 mr-2" />
          ADD TO CART
        </Button>
        <Button
          variant="outline"
          className={cn(
            "flex-1 rounded-none font-light tracking-widest text-sm py-6",
            isWishlisted && "bg-pink-50 border-pink-200 text-pink-600",
          )}
          onClick={handleToggleWishlist}
          disabled={wishlistLoading}
        >
          <Heart className={cn("w-4 h-4 mr-2", isWishlisted && "fill-current")} />
          {wishlistLoading ? "UPDATING..." : (isWishlisted ? "IN WISHLIST" : "ADD TO WISHLIST")}
        </Button>
      </div>

      <div className="flex justify-end">
        <SocialShare
          url={productUrl}
          title={`Check out ${product.name} at Coco Milk Kids`}
          description={product.description}
        />
      </div>

      <div className="prose prose-sm max-w-none">
        <h3 className="text-lg font-light tracking-wide">Description</h3>
        <p className="font-light text-muted-foreground">{product.description}</p>
      </div>
    </div>
  )
}
