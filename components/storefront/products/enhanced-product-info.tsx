"use client"

import { cn } from "@/lib/utils"
import { useState, useEffect, useMemo } from "react"
import { useCartStore2 } from "@/lib/ecommerce/hooks/use-cart-store"
import { useCartStore } from "@/lib/ecommerce/stores/cart-store"
import { useWishlist } from "@/lib/ecommerce/hooks/use-wishlist"
import { useAuth } from "@/lib/ecommerce/hooks/use-auth"
import { usePriceFormatter } from "@/hooks/use-price-formatter"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Heart, ShoppingBag, AlertCircle, CheckCircle, Package } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { SocialShare } from "@/components/social-share"
import { SizeGuide } from "@/components/storefront/products/size-guide"
import { InventoryStatus } from "@/components/inventory-status"
import type { ProductInfoProduct, StorefrontVariant } from "@/lib/ecommerce/utils/product-transformers"

interface EnhancedProductInfoProps {
  product: ProductInfoProduct
  onVariantChange?: (variant: StorefrontVariant | null) => void
}

export function EnhancedProductInfo({ product, onVariantChange }: EnhancedProductInfoProps) {
  const [selectedColor, setSelectedColor] = useState(product.colors[0]?.value || "")
  const [selectedSize, setSelectedSize] = useState(product.sizes[0] || "")
  const [quantity, setQuantity] = useState(1)
  const [selectedVariantId, setSelectedVariantId] = useState(product.defaultVariantId || "")
  const [isAddingToCart, setIsAddingToCart] = useState(false)
  
  // Use the cart store instead of the useCart hook
  const { addToCart, sessionId } = useCartStore2()
  const { user } = useAuth()
  const {
    isInWishlist,
    toggleWishlist,
    loading: wishlistLoading
  } = useWishlist({
    userId: user?.id,
    autoFetch: true
  })
  const { formatPrice } = usePriceFormatter()
  const { toast } = useToast()
  const isWishlisted = isInWishlist(product.id)

  // Make sure we have a session ID
  useEffect(() => {
    if (typeof window !== 'undefined' && !sessionId) {
      const storedSessionId = sessionStorage.getItem('sessionId')
      if (storedSessionId) {
        useCartStore.getState().setSessionId(storedSessionId)
      }
    }
  }, [sessionId])

  // Find the currently selected variant
  const selectedVariant = useMemo(() => {
    if (!product.hasVariants || !product.variants) {
      return null
    }

    // Try to find variant by exact option match
    const variant = product.variants.find(v => {
      const colorMatch = v.options.find(opt => 
        opt.name.toLowerCase().includes('color') && 
        opt.value.toLowerCase() === selectedColor.toLowerCase()
      )
      const sizeMatch = v.options.find(opt => 
        opt.name.toLowerCase().includes('size') && 
        opt.value.toLowerCase() === selectedSize.toLowerCase()
      )
      
      return colorMatch && sizeMatch
    })

    // Fallback to selected variant ID or first available variant
    return variant || 
           product.variants.find(v => v.id === selectedVariantId) ||
           product.variants.find(v => v.available) ||
           product.variants[0]
  }, [product.variants, selectedColor, selectedSize, selectedVariantId, product.hasVariants])

  // Update selected variant when color/size changes
  useEffect(() => {
    if (selectedVariant && selectedVariant.id !== selectedVariantId) {
      setSelectedVariantId(selectedVariant.id)
    }

    // Notify parent component of variant change
    if (onVariantChange) {
      onVariantChange(selectedVariant)
    }
  }, [selectedVariant, selectedVariantId, onVariantChange])

  // Get current price and availability
  const currentPrice = selectedVariant?.price || product.price
  const currentCompareAtPrice = selectedVariant?.compareAtPrice || product.compareAtPrice
  const isAvailable = selectedVariant?.available !== false
  const inventoryQuantity = selectedVariant?.inventoryQuantity || 0
  const currentSku = selectedVariant?.sku

  const discount = currentCompareAtPrice
    ? Math.round(((currentCompareAtPrice - currentPrice) / currentCompareAtPrice) * 100)
    : 0

  // Filter available options based on current selection
  const availableColors = useMemo(() => {
    if (!product.hasVariants || !product.variants) {
      return product.colors
    }

    const availableColorValues = new Set<string>()
    product.variants
      .filter(v => v.available)
      .forEach(variant => {
        const colorOption = variant.options.find(opt => 
          opt.name.toLowerCase().includes('color')
        )
        if (colorOption) {
          availableColorValues.add(colorOption.value.toLowerCase())
        }
      })

    return product.colors.filter(color => 
      availableColorValues.has(color.value.toLowerCase())
    )
  }, [product.colors, product.variants, product.hasVariants])

  const availableSizes = useMemo(() => {
    if (!product.hasVariants || !product.variants) {
      return product.sizes
    }

    const availableSizeValues = new Set<string>()
    product.variants
      .filter(v => {
        // Filter by selected color if any
        if (selectedColor) {
          const colorOption = v.options.find(opt => 
            opt.name.toLowerCase().includes('color')
          )
          return colorOption?.value.toLowerCase() === selectedColor.toLowerCase() && v.available
        }
        return v.available
      })
      .forEach(variant => {
        const sizeOption = variant.options.find(opt => 
          opt.name.toLowerCase().includes('size')
        )
        if (sizeOption) {
          availableSizeValues.add(sizeOption.value)
        }
      })

    return product.sizes.filter(size => 
      availableSizeValues.has(size)
    )
  }, [product.sizes, product.variants, selectedColor, product.hasVariants])

  const handleAddToCart = async () => {
    // Check if item can be added to cart
    if (!isAvailable) {
      toast({
        title: "Cannot add to cart",
        description: "This variant is currently out of stock.",
        variant: "destructive"
      })
      return
    }

    setIsAddingToCart(true)

    try {
      // Use the session ID from the cart store
      const storeSessionId = useCartStore.getState().sessionId
      console.log('Adding to cart: Using session ID from store', storeSessionId)

      const cartInput = {
        productId: product.id,
        variantId: selectedVariant?.id,
        quantity: quantity,
        customAttributes: {
          color: selectedColor,
          size: selectedSize
        }
      }

      console.log('Adding to cart:', cartInput)
      // Add the item to the cart using the cart store
      await addToCart(cartInput)

      toast({
        title: "Added to cart",
        description: `${product.name} has been added to your cart.`,
      })
    } catch (error) {
      console.error('Error adding to cart:', error)

      // Try to extract a more specific error message
      let errorMessage = "Failed to add item to cart. Please try again."

      if (error instanceof Error) {
        if (error.message.includes('not available')) {
          errorMessage = "This product is not available for purchase at this time."
        } else if (error.message.includes('variant is not available')) {
          errorMessage = "The selected variant is not available for purchase."
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setIsAddingToCart(false)
    }
  }

  const handleToggleWishlist = async () => {
    try {
      await toggleWishlist(product.id)

      toast({
        title: isWishlisted ? "Removed from wishlist" : "Added to wishlist",
        description: `${product.name} has been ${isWishlisted ? "removed from" : "added to"} your wishlist.`,
      })
    } catch (error) {
      console.error('Failed to toggle wishlist:', error)
      toast({
        title: "Error",
        description: "Failed to update wishlist. Please try again.",
        variant: "destructive"
      })
    }
  }

  // Use useEffect to set the URL after component mounts to avoid hydration mismatch
  const [productUrl, setProductUrl] = useState("")

  useEffect(() => {
    setProductUrl(window.location.href)
  }, [])

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl md:text-3xl font-light font-montserrat tracking-wide">{product.name}</h1>

        <div className="flex items-center space-x-3 mt-3">
          <span className="text-xl font-light">{formatPrice(currentPrice)}</span>

          {currentCompareAtPrice && (
            <>
              <span className="text-muted-foreground line-through font-light">
                {formatPrice(currentCompareAtPrice)}
              </span>
              <span className="bg-[#6C1411] text-white text-xs font-light px-2 py-1">Save {discount}%</span>
            </>
          )}
        </div>

        {/* Variant Info */}
        {product.hasVariants && selectedVariant && (
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="outline" className="text-xs">
              SKU: {selectedVariant.sku}
            </Badge>
            {selectedVariant.title && (
              <Badge variant="secondary" className="text-xs">
                {selectedVariant.title}
              </Badge>
            )}
          </div>
        )}
      </div>

      <div className="flex items-center space-x-4">
        <InventoryStatus 
          stock={inventoryQuantity} 
          available={isAvailable}
          showQuantity={product.hasVariants}
        />
        <SizeGuide category={product.categoryId} />
      </div>

      {/* Availability Warning */}
      {!isAvailable && (
        <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-md">
          <AlertCircle className="w-4 h-4 text-red-600" />
          <span className="text-sm text-red-600">This variant is currently out of stock</span>
        </div>
      )}

      <div className="space-y-6">
        {/* Color Selection */}
        {availableColors.length > 0 && (
          <div>
            <h3 className="text-sm font-light mb-3 tracking-wide">
              Color
              {product.hasVariants && (
                <span className="text-xs text-muted-foreground ml-2">
                  ({availableColors.length} available)
                </span>
              )}
            </h3>
            <RadioGroup value={selectedColor} onValueChange={setSelectedColor} className="flex flex-wrap gap-3">
              {availableColors.map((color) => (
                <div key={color.value} className="flex items-center space-x-2">
                  <RadioGroupItem value={color.value} id={`color-${color.value}`} className="sr-only" />
                  <Label
                    htmlFor={`color-${color.value}`}
                    className={cn(
                      "flex items-center justify-center w-8 h-8 rounded-full cursor-pointer transition-all duration-300",
                      "border-2 border-transparent hover:border-muted-foreground",
                      selectedColor === color.value && "border-black",
                    )}
                  >
                    <span className="w-6 h-6 rounded-full" style={{ backgroundColor: color.value }} />
                    <span className="sr-only">{color.name}</span>
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        )}

        {/* Size Selection */}
        {availableSizes.length > 0 && (
          <div>
            <h3 className="text-sm font-light mb-3 tracking-wide">
              Size
              {product.hasVariants && (
                <span className="text-xs text-muted-foreground ml-2">
                  ({availableSizes.length} available)
                </span>
              )}
            </h3>
            <RadioGroup value={selectedSize} onValueChange={setSelectedSize} className="flex flex-wrap gap-3">
              {availableSizes.map((size) => (
                <div key={size} className="flex items-center space-x-2">
                  <RadioGroupItem value={size} id={`size-${size}`} className="sr-only" />
                  <Label
                    htmlFor={`size-${size}`}
                    className={cn(
                      "flex items-center justify-center w-10 h-10 cursor-pointer transition-all duration-300",
                      "border hover:bg-muted",
                      selectedSize === size && "bg-black text-white hover:bg-black/90",
                    )}
                  >
                    {size}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>
        )}

        {/* Quantity Selection */}
        <div>
          <h3 className="text-sm font-light mb-3 tracking-wide">Quantity</h3>
          <Select 
            value={quantity.toString()} 
            onValueChange={(value) => setQuantity(Number.parseInt(value))}
            disabled={!isAvailable}
          >
            <SelectTrigger className="w-20 rounded-none font-light">
              <SelectValue placeholder="1" />
            </SelectTrigger>
            <SelectContent>
              {Array.from({ length: Math.min(10, inventoryQuantity || 10) }, (_, i) => i + 1).map((num) => (
                <SelectItem key={num} value={num.toString()} className="font-light">
                  {num}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4">
        <Button
          onClick={handleAddToCart}
          disabled={!isAvailable || isAddingToCart}
          className="flex-1 bg-[#012169] hover:bg-[#012169]/90 rounded-none font-light tracking-widest text-sm py-6 disabled:opacity-50"
        >
          <ShoppingBag className="w-4 h-4 mr-2" />
          {isAddingToCart ? "ADDING..." : (isAvailable ? "ADD TO CART" : "OUT OF STOCK")}
        </Button>
        <Button
          variant="outline"
          className={cn(
            "flex-1 rounded-none font-light tracking-widest text-sm py-6",
            isWishlisted && "bg-pink-50 border-pink-200 text-pink-600",
          )}
          onClick={handleToggleWishlist}
          disabled={wishlistLoading}
        >
          <Heart className={cn("w-4 h-4 mr-2", isWishlisted && "fill-current")} />
          {wishlistLoading ? "UPDATING..." : (isWishlisted ? "IN WISHLIST" : "ADD TO WISHLIST")}
        </Button>
      </div>

      <div className="flex justify-end">
        <SocialShare
          url={productUrl}
          title={`Check out ${product.name} at Coco Milk Kids`}
          description={product.description}
        />
      </div>

      <div className="prose prose-sm max-w-none">
        <h3 className="text-lg font-light tracking-wide">Description</h3>
        <p className="font-light text-muted-foreground">{product.description}</p>
      </div>
    </div>
  )
}
