"use client"

import type React from "react"

import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Eye,
  Scale,
  Heart,
  ShoppingBag,
  Star,
  Zap,
  TrendingUp,
  Clock,
  ImageIcon,
  Loader2
} from "lucide-react"
import { usePriceFormatter } from "@/hooks/use-price-formatter"
import { useCart } from "@/lib/ecommerce/hooks/use-cart"
import { useCartStore2 } from "@/lib/ecommerce/hooks/use-cart-store"
import { useCartStore } from "@/lib/ecommerce/stores/cart-store"
import { useWishlist } from "@/components/wishlist-provider"
import { useToast } from "@/hooks/use-toast"
import { useEnhancedAddToCart } from "@/hooks/use-enhanced-add-to-cart"
import { cn } from "@/lib/utils"
import { Product as EcommerceProduct } from "@/lib/ecommerce/types/product"
import { useState, useEffect } from "react"

// Support both legacy and new product formats
interface LegacyProduct {
  id: string
  name: string
  slug: string
  description: string
  price: number
  compareAtPrice?: number
  images: string[]
  colors: { name: string; value: string }[]
  sizes: string[]
  categoryId: string
  isNew?: boolean
  isSale?: boolean
}

interface ProductCardProps {
  product: EcommerceProduct | LegacyProduct
  className?: string
  showQuickActions?: boolean
  showRating?: boolean
  showCompare?: boolean
  variant?: 'default' | 'compact' | 'featured'
  priority?: boolean // For image loading priority
}

// Type guard to check if product is legacy format
function isLegacyProduct(product: EcommerceProduct | LegacyProduct): product is LegacyProduct {
  return 'name' in product && typeof (product as any).price === 'number'
}

export function ProductCard({
  product,
  className,
  showQuickActions = true,
  showRating = true,
  showCompare = true,
  variant = 'default',
  priority = false
}: ProductCardProps) {
  const { formatPrice } = usePriceFormatter()
  // Use the cart store instead of the useCart hook
  const { addToCart, sessionId } = useCartStore2()
  const { isInWishlist, toggleWishlist } = useWishlist()
  const { toast } = useToast()

  // Make sure we have a session ID
  useEffect(() => {
    if (typeof window !== 'undefined' && !sessionId) {
      const storedSessionId = sessionStorage.getItem('sessionId')
      if (storedSessionId) {
        useCartStore.getState().setSessionId(storedSessionId)
      }
    }
  }, [])

  // State for interactions
  const [isImageLoading, setIsImageLoading] = useState(true)
  const [isAddingToCart, setIsAddingToCart] = useState(false)
  const [imageError, setImageError] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  // Handle both legacy and new product formats
  let price: number
  let compareAtPrice: number | undefined
  let imageUrl: string
  let title: string
  let isOnSale: boolean
  let isNew: boolean
  let rating: number = 0
  let reviewCount: number = 0
  let vendor: string
  let inventoryQuantity: number = 0
  let trackQuantity: boolean = false
  let isAvailable: boolean = true
  let continueSellingWhenOutOfStock: boolean = false
  let status: string = 'active' // Default to active for legacy products

  if (isLegacyProduct(product)) {
    // Legacy product format
    price = product.price
    compareAtPrice = product.compareAtPrice
    imageUrl = product.images?.[0] || ""
    title = product.name
    isOnSale = product.isSale || (!!compareAtPrice && compareAtPrice > price)
    isNew = product.isNew || false
    vendor = 'Coco Milk Kids'
    // Legacy products don't have inventory tracking
    inventoryQuantity = 100 // Assume in stock for legacy products
    trackQuantity = false
    isAvailable = true
    continueSellingWhenOutOfStock = true
  } else {
    // New e-commerce product format
    price = typeof product.price === 'object' ? product.price.amount : product.price
    compareAtPrice = product.compareAtPrice ?
      (typeof product.compareAtPrice === 'object' ? product.compareAtPrice.amount : product.compareAtPrice) :
      undefined
    imageUrl = product.images && product.images.length > 0
      ? product.images[0].url
      : ""
    title = product.title
    isOnSale = !!compareAtPrice && compareAtPrice > price
    isNew = product.createdAt && new Date(product.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days
    vendor = product.vendor || 'Coco Milk Kids'
    // Mock rating for demo - in real app this would come from reviews
    rating = 4.2 + Math.random() * 0.8
    reviewCount = Math.floor(Math.random() * 50) + 5

    // Stock information
    inventoryQuantity = product.inventoryQuantity || 0
    trackQuantity = product.trackQuantity ?? false
    isAvailable = product.isAvailable ?? true
    continueSellingWhenOutOfStock = product.continueSellingWhenOutOfStock ?? false
    status = product.status || 'active' // Get product status
  }

  const discount = compareAtPrice
    ? Math.round(((compareAtPrice - price) / compareAtPrice) * 100)
    : 0

  // Stock status helpers
  const getStockStatus = () => {
    if (!trackQuantity || !isAvailable) {
      return isAvailable ? 'in-stock' : 'out-of-stock'
    }

    if (inventoryQuantity <= 0) {
      return continueSellingWhenOutOfStock ? 'backorder' : 'out-of-stock'
    } else if (inventoryQuantity <= 5) {
      return 'low-stock'
    } else {
      return 'in-stock'
    }
  }

  const getStockDisplay = () => {
    const status = getStockStatus()

    switch (status) {
      case 'in-stock':
        return {
          text: trackQuantity && inventoryQuantity > 0
            ? `${inventoryQuantity} in stock`
            : 'In Stock',
          color: 'bg-green-500',
          textColor: 'text-green-700',
          bgColor: 'bg-green-50'
        }
      case 'low-stock':
        return {
          text: `Only ${inventoryQuantity} left`,
          color: 'bg-orange-500',
          textColor: 'text-orange-700',
          bgColor: 'bg-orange-50'
        }
      case 'out-of-stock':
        return {
          text: 'Out of Stock',
          color: 'bg-red-500',
          textColor: 'text-red-700',
          bgColor: 'bg-red-50'
        }
      case 'backorder':
        return {
          text: 'Available on Backorder',
          color: 'bg-blue-500',
          textColor: 'text-blue-700',
          bgColor: 'bg-blue-50'
        }
      default:
        return {
          text: 'In Stock',
          color: 'bg-green-500',
          textColor: 'text-green-700',
          bgColor: 'bg-green-50'
        }
    }
  }

  const stockInfo = getStockDisplay()
  const stockStatus = getStockStatus()
  // Check both stock status and product availability/status
  const canAddToCart = (stockStatus === 'in-stock' || stockStatus === 'low-stock' || stockStatus === 'backorder')
    && isAvailable && status === 'active'

  const isWishlisted = isInWishlist(product.id)

  // Placeholder image
  const placeholderImage = "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg"
  const finalImageUrl = imageError || !imageUrl ? placeholderImage : imageUrl

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Check if item can be added to cart
    if (!canAddToCart) {
      let errorMessage = "This item is not available for purchase."

      if (stockStatus === 'out-of-stock') {
        errorMessage = "This item is currently out of stock."
      } else if (!isAvailable) {
        errorMessage = "This item is not available at this time."
      } else if (status !== 'active') {
        errorMessage = "This item is not active in our catalog."
      }

      toast({
        title: "Cannot add to cart",
        description: errorMessage,
        variant: "destructive"
      })
      return
    }

    setIsAddingToCart(true)

    try {
      // Get default variant if available (for new e-commerce products)
      let variantId = undefined
      if (!isLegacyProduct(product) && product.variants && product.variants.length > 0) {
        // Use the first variant as default since defaultVariantId doesn't exist on Product type
        variantId = product.variants[0].id
      }

      // Use the session ID from the cart store
      const storeSessionId = useCartStore.getState().sessionId
      console.log('Quick add to cart: Using session ID from store', storeSessionId)

      const cartInput = {
        productId: product.id,
        variantId: variantId,
        quantity: 1,
        customAttributes: {
          color: '', // Default - would be selected in product page
          size: '' // Default - would be selected in product page
        }
      }

      console.log('Quick add to cart: Adding item', cartInput)
      // Add the item to the cart using the cart store
      await addToCart(cartInput)

      toast({
        title: "Added to cart",
        description: stockStatus === 'backorder'
          ? `${title} has been added to your cart (available on backorder).`
          : `${title} has been added to your cart.`,
      })
    } catch (error) {
      console.error('Error adding to cart:', error)

      // Try to extract a more specific error message
      let errorMessage = "Failed to add item to cart. Please try again."

      if (error instanceof Error) {
        if (error.message.includes('not available')) {
          errorMessage = "This product is not available for purchase at this time."
        } else if (error.message.includes('variant is not available')) {
          errorMessage = "The selected variant is not available for purchase."
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setIsAddingToCart(false)
    }
  }

  const handleWishlistToggle = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    toggleWishlist(product.id)

    toast({
      title: isWishlisted ? "Removed from wishlist" : "Added to wishlist",
      description: `${title} has been ${isWishlisted ? "removed from" : "added to"} your wishlist.`,
    })
  }

  const handleAddToCompare = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (typeof window !== "undefined" && (window as any).addToCompare) {
      ; (window as any).addToCompare(product.id)
      toast({
        title: "Added to compare",
        description: `${title} has been added to comparison.`,
      })
    }
  }

  const cardVariants = {
    default: "group block product-card-enhanced",
    compact: "group block product-card-compact",
    featured: "group block product-card-featured border border-gray-200 shadow-lg"
  }

  const imageAspectRatios = {
    default: "aspect-[3/4]",
    compact: "aspect-square",
    featured: "aspect-[4/5]"
  }

  return (
    <div
      className={cn(cardVariants[variant], className)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Enhanced image container */}
      <div className={cn(
        "relative mb-3 overflow-hidden bg-gray-50 rounded-lg group-hover:shadow-md transition-all duration-300",
        imageAspectRatios[variant]
      )}>
        <Link href={`/products/${product.slug}`}>
          {/* Image with loading state */}
          <div className="relative w-full h-full">
            {/* Unavailable overlay */}
            {(!isAvailable || status !== 'active') && (
              <div className="absolute inset-0 bg-gray-200 bg-opacity-50 z-10 flex items-center justify-center">
                <div className="bg-white px-3 py-2 rounded-md shadow-sm">
                  <p className="text-sm font-medium text-gray-700">
                    {!isAvailable ? "Currently Unavailable" : "Product Inactive"}
                  </p>
                </div>
              </div>
            )}
            {isImageLoading && !imageError && (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                <div className="flex flex-col items-center space-y-2">
                  <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
                  <span className="text-xs text-gray-500">Loading...</span>
                </div>
              </div>
            )}

            {imageError ? (
              <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
                <div className="flex flex-col items-center space-y-2">
                  <ImageIcon className="h-8 w-8 text-gray-400" />
                  <span className="text-xs text-gray-500">No image</span>
                </div>
              </div>
            ) : (
              <Image
                src={finalImageUrl}
                alt={title}
                fill
                className={cn(
                  "object-cover transition-all duration-500 group-hover:scale-105",
                  isImageLoading ? "opacity-0" : "opacity-100"
                )}
                priority={priority}
                onLoad={() => setIsImageLoading(false)}
                onError={() => {
                  setImageError(true)
                  setIsImageLoading(false)
                }}
              />
            )}
          </div>
        </Link>

        {/* Enhanced badges */}
        <div className="absolute top-2 left-2 flex flex-col space-y-1">
          {/* Availability badges */}
          {(!isAvailable || status !== 'active') && (
            <Badge variant="outline" className="text-xs font-medium bg-gray-100 text-gray-700 border-gray-300">
              <Clock className="w-3 h-3 mr-1" />
              {!isAvailable ? "Unavailable" : "Inactive"}
            </Badge>
          )}

          {isOnSale && (
            <Badge variant="destructive" className="text-xs font-medium">
              <Zap className="w-3 h-3 mr-1" />
              {discount}% OFF
            </Badge>
          )}

          {isNew && !isOnSale && (
            <Badge className="text-xs font-medium bg-green-500 hover:bg-green-600">
              <TrendingUp className="w-3 h-3 mr-1" />
              New
            </Badge>
          )}

          {variant === 'featured' && (
            <Badge variant="secondary" className="text-xs font-medium">
              <Star className="w-3 h-3 mr-1" />
              Featured
            </Badge>
          )}
        </div>

        {/* Quick action buttons */}
        {showQuickActions && (
          <div className={cn(
            "absolute top-2 right-2 flex flex-col space-y-1 transition-all duration-300",
            isHovered ? "opacity-100 translate-x-0" : "opacity-0 translate-x-2"
          )}>
            <Button
              size="icon"
              variant="secondary"
              className="h-8 w-8 bg-white/90 hover:bg-white shadow-sm"
              onClick={handleWishlistToggle}
            >
              <Heart
                className={cn(
                  "h-4 w-4 transition-colors",
                  isWishlisted ? "fill-red-500 text-red-500" : "text-gray-600"
                )}
              />
            </Button>

            {showCompare && (
              <Button
                size="icon"
                variant="secondary"
                className="h-8 w-8 bg-white/90 hover:bg-white shadow-sm"
                onClick={handleAddToCompare}
              >
                <Scale className="h-4 w-4 text-gray-600" />
              </Button>
            )}

          </div>
        )}

        {/* Stock indicator - only show when not hovered */}
        <div className={cn(
          "absolute bottom-2 left-2 transition-all duration-300",
          isHovered ? "opacity-0 translate-y-2" : "opacity-100 translate-y-0"
        )}>
          <div className="flex items-center space-x-1">
            <div className={cn("w-2 h-2 rounded-full", stockInfo.color)}></div>
            <span className={cn(
              "text-xs px-2 py-1 rounded-full font-medium",
              stockInfo.textColor,
              stockInfo.bgColor
            )}>
              {stockInfo.text}
            </span>
          </div>
        </div>

        {/* Quick add to cart button */}
        <div className={cn(
          "absolute bottom-2 left-2 right-2 transition-all duration-300",
          isHovered ? "opacity-100 translate-y-0" : "opacity-0 translate-y-2"
        )}>
          <Button
            onClick={handleAddToCart}
            disabled={isAddingToCart || !canAddToCart}
            className={cn(
              "w-full h-8 text-xs font-medium",
              !canAddToCart && "opacity-50 cursor-not-allowed"
            )}
            size="sm"
            variant={stockStatus === 'out-of-stock' ? 'outline' : 'default'}
          >
            {isAddingToCart ? (
              <>
                <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                Adding...
              </>
            ) : stockStatus === 'out-of-stock' ? (
              <>
                <ShoppingBag className="w-3 h-3 mr-1" />
                Out of Stock
              </>
            ) : stockStatus === 'backorder' ? (
              <>
                <ShoppingBag className="w-3 h-3 mr-1" />
                Pre-order
              </>
            ) : (
              <>
                <ShoppingBag className="w-3 h-3 mr-1" />
                Quick Add
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Enhanced product info */}
      <div className="space-y-2">
        <Link href={`/products/${product.slug}`}>
          <h3 className={cn(
            "font-medium text-gray-900 hover:text-gray-600 transition-colors duration-200 line-clamp-2",
            variant === 'compact' ? "text-sm" : "text-base"
          )}>
            {title}
          </h3>
        </Link>

        {/* Vendor info */}
        <p className="text-xs text-gray-500 font-normal">
          {vendor}
        </p>

        {/* Rating */}
        {showRating && (
          <div className="flex items-center space-x-1">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={cn(
                    "w-3 h-3",
                    i < Math.floor(rating)
                      ? "fill-yellow-400 text-yellow-400"
                      : "text-gray-300"
                  )}
                />
              ))}
            </div>
            <span className="text-xs text-gray-500">
              {rating.toFixed(1)} ({reviewCount})
            </span>
          </div>
        )}

        {/* Price */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {compareAtPrice ? (
              <>
                <span className={cn(
                  "font-semibold text-gray-900",
                  variant === 'compact' ? "text-sm" : "text-base"
                )}>
                  {formatPrice(price)}
                </span>
                <span className={cn(
                  "text-gray-500 line-through",
                  variant === 'compact' ? "text-xs" : "text-sm"
                )}>
                  {formatPrice(compareAtPrice)}
                </span>
              </>
            ) : (
              <span className={cn(
                "font-semibold text-gray-900",
                variant === 'compact' ? "text-sm" : "text-base"
              )}>
                {formatPrice(price)}
              </span>
            )}
          </div>

          {/* Urgency indicator */}
          {isOnSale && (
            <div className="flex items-center text-xs text-red-600">
              <Clock className="w-3 h-3 mr-1" />
              Limited time
            </div>
          )}
        </div>

        {/* Stock status indicator */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-1">
            <div className={cn("w-1.5 h-1.5 rounded-full", stockInfo.color)}></div>
            <span className={cn(
              "text-xs font-medium",
              stockInfo.textColor
            )}>
              {stockInfo.text}
            </span>
          </div>

          {/* Low stock urgency */}
          {stockStatus === 'low-stock' && (
            <div className="flex items-center text-xs text-orange-600">
              <Clock className="w-3 h-3 mr-1" />
              Hurry!
            </div>
          )}
        </div>

        {/* Additional features for featured variant */}
        {variant === 'featured' && (
          <div className="pt-2 border-t border-gray-100">
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>Free shipping</span>
              <span>Easy returns</span>
            </div>
          </div>
        )}
      </div>

    </div>
  )
}
