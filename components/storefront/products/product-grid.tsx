"use client"

import { ProductCard } from "@/components/storefront/products/product-card"
import { useProducts } from "@/lib/ecommerce/hooks/use-products"
import { useEffect, useMemo, useCallback, useState } from "react"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { RefreshCw, AlertCircle, Filter } from "lucide-react"
import { cn } from "@/lib/utils"

interface ProductGridProps {
  sort?: string
  category?: string
  color?: string
  size?: string
  searchQuery?: string
  className?: string
  showFiltersApplied?: boolean
}

// Constants for better maintainability
const DEFAULT_LIMIT = 24
const SKELETON_COUNT = 12
const GRID_CLASSES = "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8"

// Sort mapping for better performance
const SORT_MAPPING = {
  'price-asc': { sortBy: 'price', sortOrder: 'asc' },
  'price-desc': { sortBy: 'price', sortOrder: 'desc' },
  'name-asc': { sortBy: 'title', sortOrder: 'asc' },
  'name-desc': { sortBy: 'title', sortOrder: 'desc' },
  'newest': { sortBy: 'createdAt', sortOrder: 'desc' },
  'featured': { sortBy: 'featured', sortOrder: 'desc' }
} as const

export function ProductGrid({
  sort,
  category,
  color,
  size,
  searchQuery,
  className,
  showFiltersApplied = true
}: ProductGridProps) {
  const { products, loading, error, searchProducts } = useProducts({ autoFetch: false })
  const [retryCount, setRetryCount] = useState(0)

  // Memoize search parameters to prevent unnecessary re-renders
  const searchParams = useMemo(() => {
    const params: any = {
      status: 'active',
      limit: DEFAULT_LIMIT
    }

    // Apply sort mapping
    if (sort && SORT_MAPPING[sort as keyof typeof SORT_MAPPING]) {
      const sortConfig = SORT_MAPPING[sort as keyof typeof SORT_MAPPING]
      params.sortBy = sortConfig.sortBy
      params.sortOrder = sortConfig.sortOrder
    } else {
      // Default to featured
      params.sortBy = 'featured'
      params.sortOrder = 'desc'
    }

    // Apply filters
    if (category) params.category = category
    if (color) params.color = color
    if (size) params.size = size
    if (searchQuery) params.search = searchQuery

    return params
  }, [sort, category, color, size, searchQuery])

  // Fetch products when search parameters change
  useEffect(() => {
    searchProducts(searchParams)
  }, [searchParams, searchProducts])

  // Retry function for error recovery
  const handleRetry = useCallback(() => {
    setRetryCount(prev => prev + 1)
    searchProducts(searchParams)
  }, [searchParams, searchProducts])

  // Count active filters for display
  const activeFiltersCount = useMemo(() => {
    let count = 0
    if (category) count++
    if (color) count++
    if (size) count++
    if (searchQuery) count++
    return count
  }, [category, color, size, searchQuery])

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className={cn(GRID_CLASSES, className)} role="status" aria-label="Loading products">
      {Array.from({ length: SKELETON_COUNT }).map((_, i) => (
        <div key={i} className="space-y-3">
          <Skeleton className="aspect-[3/4] w-full" />
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-3 w-1/2" />
        </div>
      ))}
    </div>
  )

  // Render loading state
  if (loading) {
    return <LoadingSkeleton />
  }

  // Render error state with retry option
  if (error) {
    return (
      <div className="space-y-6">
        {showFiltersApplied && activeFiltersCount > 0 && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Filter className="h-4 w-4" />
            <span>{activeFiltersCount} filter{activeFiltersCount !== 1 ? 's' : ''} applied</span>
          </div>
        )}

        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Failed to load products: {error.message}</span>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRetry}
              className="ml-4"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  // Render empty state
  if (products.length === 0) {
    return (
      <div className="space-y-6">
        {showFiltersApplied && activeFiltersCount > 0 && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Filter className="h-4 w-4" />
            <span>{activeFiltersCount} filter{activeFiltersCount !== 1 ? 's' : ''} applied</span>
          </div>
        )}

        <div className="text-center py-12">
          <div className="mx-auto w-24 h-24 mb-4 rounded-full bg-muted flex items-center justify-center">
            <Filter className="h-8 w-8 text-muted-foreground" />
          </div>
          <h2 className="text-xl font-medium mb-2">No products found</h2>
          <p className="text-muted-foreground max-w-md mx-auto">
            {activeFiltersCount > 0
              ? "Try adjusting your filters to find what you're looking for."
              : "We couldn't find any products at the moment. Please try again later."
            }
          </p>
          {activeFiltersCount > 0 && (
            <Button
              variant="outline"
              className="mt-4"
              onClick={() => window.location.href = '/products'}
            >
              Clear all filters
            </Button>
          )}
        </div>
      </div>
    )
  }

  // Render products grid
  return (
    <div className="space-y-6">
      {showFiltersApplied && activeFiltersCount > 0 && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Filter className="h-4 w-4" />
          <span>{activeFiltersCount} filter{activeFiltersCount !== 1 ? 's' : ''} applied</span>
          <span className="text-xs">•</span>
          <span>{products.length} product{products.length !== 1 ? 's' : ''} found</span>
        </div>
      )}

      <div
        className={cn(GRID_CLASSES, className)}
        role="grid"
        aria-label={`Product grid showing ${products.length} products`}
      >
        {products.map((product, index) => (
          <ProductCard
            key={product.id}
            product={product}
            priority={index < 8} // Prioritize loading for first 8 products
          />
        ))}
      </div>

      {products.length >= DEFAULT_LIMIT && (
        <div className="text-center py-6">
          <p className="text-sm text-muted-foreground">
            Showing {products.length} products. Use filters to narrow your search.
          </p>
        </div>
      )}
    </div>
  )
}
