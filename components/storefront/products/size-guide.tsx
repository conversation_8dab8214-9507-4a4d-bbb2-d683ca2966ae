"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Ruler } from "lucide-react"

interface SizeGuideProps {
  category: string
}

export function SizeGuide({ category }: SizeGuideProps) {
  const sizeCharts = {
    tops: {
      title: "Tops Size Guide",
      measurements: ["Chest", "Length", "Sleeve Length"],
      sizes: [
        { size: "XS (2-3)", chest: "20-21", length: "14-15", sleeve: "10-11" },
        { size: "S (4-5)", chest: "22-23", length: "16-17", sleeve: "12-13" },
        { size: "M (6-7)", chest: "24-25", length: "18-19", sleeve: "14-15" },
        { size: "L (8-9)", chest: "26-27", length: "20-21", sleeve: "16-17" },
        { size: "XL (10-12)", chest: "28-30", length: "22-24", sleeve: "18-20" },
      ],
    },
    bottoms: {
      title: "Bottoms Size Guide",
      measurements: ["Waist", "Hip", "Inseam", "Length"],
      sizes: [
        { size: "XS (2-3)", waist: "19-20", hip: "21-22", inseam: "12-13", length: "18-19" },
        { size: "S (4-5)", waist: "21-22", hip: "23-24", inseam: "14-15", length: "20-21" },
        { size: "M (6-7)", waist: "23-24", hip: "25-26", inseam: "16-17", length: "22-23" },
        { size: "L (8-9)", waist: "25-26", hip: "27-28", inseam: "18-19", length: "24-25" },
        { size: "XL (10-12)", waist: "27-29", hip: "29-31", inseam: "20-22", length: "26-28" },
      ],
    },
    dresses: {
      title: "Dresses Size Guide",
      measurements: ["Chest", "Waist", "Hip", "Length"],
      sizes: [
        { size: "XS (2-3)", chest: "20-21", waist: "19-20", hip: "21-22", length: "22-24" },
        { size: "S (4-5)", chest: "22-23", waist: "21-22", hip: "23-24", length: "26-28" },
        { size: "M (6-7)", chest: "24-25", waist: "23-24", hip: "25-26", length: "30-32" },
        { size: "L (8-9)", chest: "26-27", waist: "25-26", hip: "27-28", length: "34-36" },
        { size: "XL (10-12)", chest: "28-30", waist: "27-29", hip: "29-31", length: "38-40" },
      ],
    },
  }

  const currentChart = sizeCharts[category as keyof typeof sizeCharts] || sizeCharts.tops

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Ruler className="h-4 w-4 mr-2" />
          Size Guide
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl">
        <DialogHeader>
          <DialogTitle>Size Guide</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="chart" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="chart">Size Chart</TabsTrigger>
            <TabsTrigger value="measure">How to Measure</TabsTrigger>
            <TabsTrigger value="fit">Fit Guide</TabsTrigger>
          </TabsList>

          <TabsContent value="chart" className="space-y-4">
            <h3 className="text-lg font-semibold">{currentChart.title}</h3>
            <p className="text-sm text-muted-foreground">All measurements are in inches</p>

            <div className="overflow-x-auto">
              <table className="w-full border-collapse border border-gray-300">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="border border-gray-300 px-4 py-2 text-left">Size (Age)</th>
                    {currentChart.measurements.map((measurement) => (
                      <th key={measurement} className="border border-gray-300 px-4 py-2 text-left">
                        {measurement}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {currentChart.sizes.map((sizeData, index) => (
                    <tr key={index} className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}>
                      <td className="border border-gray-300 px-4 py-2 font-medium">{sizeData.size}</td>
                      {Object.entries(sizeData)
                        .filter(([key]) => key !== "size")
                        .map(([key, value]) => (
                          <td key={key} className="border border-gray-300 px-4 py-2">
                            {value}"
                          </td>
                        ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>

          <TabsContent value="measure" className="space-y-4">
            <h3 className="text-lg font-semibold">How to Measure Your Child</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">Chest</h4>
                  <p className="text-sm text-muted-foreground">
                    Measure around the fullest part of the chest, keeping the tape measure level.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium">Waist</h4>
                  <p className="text-sm text-muted-foreground">
                    Measure around the natural waistline, which is the narrowest part of the torso.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium">Hip</h4>
                  <p className="text-sm text-muted-foreground">
                    Measure around the fullest part of the hips, about 7-9 inches below the waist.
                  </p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium">Length</h4>
                  <p className="text-sm text-muted-foreground">
                    For tops: measure from the highest point of the shoulder to the desired length. For bottoms: measure
                    from waist to ankle.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium">Sleeve Length</h4>
                  <p className="text-sm text-muted-foreground">
                    Measure from the shoulder point to the desired sleeve length.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium">Inseam</h4>
                  <p className="text-sm text-muted-foreground">
                    Measure from the crotch to the ankle along the inside of the leg.
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="fit" className="space-y-4">
            <h3 className="text-lg font-semibold">Fit Guide</h3>
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900">Oversized Comfort Fit</h4>
                <p className="text-sm text-blue-800 mt-1">
                  Our signature style features a relaxed, oversized fit designed for maximum comfort and movement.
                  Consider sizing down if you prefer a more fitted look.
                </p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-medium text-green-900">Regular Fit</h4>
                <p className="text-sm text-green-800 mt-1">
                  True to size with a comfortable fit that's not too tight or too loose. Perfect for everyday wear.
                </p>
              </div>
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h4 className="font-medium text-yellow-900">Slim Fit</h4>
                <p className="text-sm text-yellow-800 mt-1">
                  A more tailored fit that follows the body's natural shape. Consider sizing up if between sizes.
                </p>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
