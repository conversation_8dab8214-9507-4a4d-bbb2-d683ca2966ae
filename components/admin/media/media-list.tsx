'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  MoreHorizontal,
  Eye,
  Download,
  Edit,
  Trash2,
  Image,
  Video,
  Music,
  FileText,
  File,
  Copy,
  ExternalLink
} from 'lucide-react'
import { MediaFile } from '@/hooks/use-media'
import { formatFileSize, cn } from '@/lib/utils'
import { toast } from 'sonner'
import { ImageWithFallback } from './image-with-fallback'

interface MediaListProps {
  files: MediaFile[]
  loading: boolean
  onPreview: (file: MediaFile) => void
  onDelete: (file: MediaFile) => void
  onDownload: (file: MediaFile) => void
  onEdit?: (file: MediaFile) => void
  selectable?: boolean
  selectedFiles?: string[]
  onSelectionChange?: (fileIds: string[]) => void
}

export function MediaList({
  files,
  loading,
  onPreview,
  onDelete,
  onDownload,
  onEdit,
  selectable = false,
  selectedFiles = [],
  onSelectionChange
}: MediaListProps) {
  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <Image className="h-4 w-4 text-green-600" />
      case 'video':
        return <Video className="h-4 w-4 text-blue-600" />
      case 'audio':
        return <Music className="h-4 w-4 text-purple-600" />
      case 'document':
        return <FileText className="h-4 w-4 text-orange-600" />
      default:
        return <File className="h-4 w-4 text-gray-600" />
    }
  }

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case 'image':
        return 'bg-green-100 text-green-800'
      case 'video':
        return 'bg-blue-100 text-blue-800'
      case 'audio':
        return 'bg-purple-100 text-purple-800'
      case 'document':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleCopyUrl = (file: MediaFile) => {
    navigator.clipboard.writeText(file.url)
    toast.success('File URL copied to clipboard')
  }

  const handleOpenInNewTab = (file: MediaFile) => {
    window.open(file.url, '_blank')
  }

  const handleSelectAll = (checked: boolean) => {
    if (!onSelectionChange) return
    
    if (checked) {
      onSelectionChange(files.map(file => file.id))
    } else {
      onSelectionChange([])
    }
  }

  const handleSelectFile = (fileId: string, checked: boolean) => {
    if (!onSelectionChange) return

    if (checked) {
      onSelectionChange([...selectedFiles, fileId])
    } else {
      onSelectionChange(selectedFiles.filter(id => id !== fileId))
    }
  }

  const isAllSelected = files.length > 0 && selectedFiles.length === files.length
  const isPartiallySelected = selectedFiles.length > 0 && selectedFiles.length < files.length

  if (loading) {
    return (
      <div className="space-y-2">
        {[...Array(10)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-100 rounded animate-pulse" />
        ))}
      </div>
    )
  }

  if (files.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <File className="h-12 w-12 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No files found</h3>
        <p className="text-gray-500">Upload some files to get started</p>
      </div>
    )
  }

  return (
    <div className="border rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            {selectable && (
              <TableHead className="w-12">
                <Checkbox
                  checked={isAllSelected}
                  indeterminate={isPartiallySelected}
                  onCheckedChange={handleSelectAll}
                />
              </TableHead>
            )}
            <TableHead>File</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Size</TableHead>
            <TableHead>Created</TableHead>
            <TableHead>Tags</TableHead>
            <TableHead className="w-12"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {files.map((file) => {
            const isSelected = selectedFiles.includes(file.id)

            return (
              <TableRow
                key={file.id}
                className={cn(
                  "cursor-pointer hover:bg-gray-50",
                  isSelected && "bg-blue-50"
                )}
                onClick={() => onPreview(file)}
              >
                {selectable && (
                  <TableCell onClick={(e) => e.stopPropagation()}>
                    <Checkbox
                      checked={isSelected}
                      onCheckedChange={(checked) => 
                        handleSelectFile(file.id, checked as boolean)
                      }
                    />
                  </TableCell>
                )}
                
                <TableCell>
                  <div className="flex items-center space-x-3">
                    {file.type === 'image' ? (
                      <ImageWithFallback
                        src={file.previewUrl || file.url}
                        fallbackSrc={file.url}
                        alt={file.metadata.alt || file.name}
                        className="w-10 h-10 object-cover rounded"
                        fallbackIcon={getFileIcon(file.type)}
                        fallbackClassName="w-10 h-10 bg-gray-100 rounded flex items-center justify-center"
                      />
                    ) : (
                      <div className="w-10 h-10 bg-gray-100 rounded flex items-center justify-center">
                        {getFileIcon(file.type)}
                      </div>
                    )}
                    <div className="min-w-0 flex-1">
                      <p className="font-medium text-sm truncate" title={file.name}>
                        {file.name}
                      </p>
                      {file.metadata.title && file.metadata.title !== file.name && (
                        <p className="text-xs text-muted-foreground truncate">
                          {file.metadata.title}
                        </p>
                      )}
                    </div>
                  </div>
                </TableCell>

                <TableCell>
                  <Badge
                    variant="secondary"
                    className={cn("text-xs", getFileTypeColor(file.type))}
                  >
                    {file.type.toUpperCase()}
                  </Badge>
                </TableCell>

                <TableCell className="text-sm text-muted-foreground">
                  {formatFileSize(file.size)}
                </TableCell>

                <TableCell className="text-sm text-muted-foreground">
                  {new Date(file.createdAt).toLocaleDateString()}
                </TableCell>

                <TableCell>
                  {file.metadata.tags && file.metadata.tags.length > 0 ? (
                    <div className="flex flex-wrap gap-1">
                      {file.metadata.tags.slice(0, 2).map((tag, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {file.metadata.tags.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{file.metadata.tags.length - 2}
                        </Badge>
                      )}
                    </div>
                  ) : (
                    <span className="text-xs text-muted-foreground">No tags</span>
                  )}
                </TableCell>

                <TableCell onClick={(e) => e.stopPropagation()}>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onPreview(file)}>
                        <Eye className="mr-2 h-4 w-4" />
                        Preview
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onDownload(file)}>
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleCopyUrl(file)}>
                        <Copy className="mr-2 h-4 w-4" />
                        Copy URL
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleOpenInNewTab(file)}>
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Open in New Tab
                      </DropdownMenuItem>
                      {onEdit && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => onEdit(file)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                        </>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => onDelete(file)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            )
          })}
        </TableBody>
      </Table>
    </div>
  )
}
