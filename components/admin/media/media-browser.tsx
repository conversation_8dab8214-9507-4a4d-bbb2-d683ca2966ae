'use client'

import { useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  Search,
  Upload,
  Check,
  Grid3X3,
  List,
  Filter
} from 'lucide-react'
import { toast } from 'sonner'
import { useMedia, MediaFile, MediaFilters } from '@/hooks/use-media'
import { MediaGrid } from './media-grid'
import { MediaList } from './media-list'
import { MediaUploadDialog } from './media-upload-dialog'

interface MediaBrowserProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSelect: (files: MediaFile[]) => void
  multiple?: boolean
  accept?: 'all' | 'image' | 'video' | 'audio' | 'document'
  title?: string
  description?: string
}

export function MediaBrowser({
  open,
  onOpenChange,
  onSelect,
  multiple = false,
  accept = 'all',
  title = 'Select Media',
  description = 'Choose files from your media library'
}: MediaBrowserProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedFiles, setSelectedFiles] = useState<string[]>([])
  const [showUploadDialog, setShowUploadDialog] = useState(false)
  const [activeTab, setActiveTab] = useState('browse')

  const {
    files,
    pagination,
    loading,
    error,
    filters,
    setFilters,
    setPage,
    refetch,
    uploadFiles
  } = useMedia({
    autoFetch: open,
    initialFilters: accept !== 'all' ? { type: accept } : {},
    initialLimit: 20
  })

  const handleFilterChange = (key: keyof MediaFilters, value: string) => {
    setFilters({
      ...filters,
      [key]: value === 'all' ? undefined : value
    })
    setPage(1)
  }

  const handleSearch = (search: string) => {
    setFilters({
      ...filters,
      search: search || undefined
    })
    setPage(1)
  }

  const handleFileSelect = (file: MediaFile) => {
    if (multiple) {
      const isSelected = selectedFiles.includes(file.id)
      if (isSelected) {
        setSelectedFiles(prev => prev.filter(id => id !== file.id))
      } else {
        setSelectedFiles(prev => [...prev, file.id])
      }
    } else {
      setSelectedFiles([file.id])
    }
  }

  const handleSelectionChange = (fileIds: string[]) => {
    setSelectedFiles(fileIds)
  }

  const handleConfirmSelection = () => {
    const selected = files.filter(file => selectedFiles.includes(file.id))
    onSelect(selected)
    onOpenChange(false)
    setSelectedFiles([])
  }

  const handleCancel = () => {
    onOpenChange(false)
    setSelectedFiles([])
  }

  const handleUploadComplete = async (uploadedFiles?: MediaFile[]) => {
    setShowUploadDialog(false)
    await refetch()
    
    // If files were uploaded, automatically select them and switch to browse tab
    if (uploadedFiles && uploadedFiles.length > 0) {
      const uploadedFileIds = uploadedFiles.map(file => file.id)
      setSelectedFiles(uploadedFileIds)
      setActiveTab('browse')
      
      // Show success message
      toast.success(`${uploadedFiles.length} file(s) uploaded and selected`)
      
      // For single file selection, auto-confirm the selection for better UX
      if (!multiple && uploadedFiles.length === 1) {
        setTimeout(() => {
          onSelect(uploadedFiles)
          onOpenChange(false)
          setSelectedFiles([])
        }, 500) // Small delay to show the selection
      }
    }
  }

  // Filter files based on accept prop
  const filteredFiles = accept !== 'all' 
    ? files.filter(file => file.type === accept)
    : files

  const selectedFileObjects = files.filter(file => selectedFiles.includes(file.id))

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[900px] max-h-[85vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            <DialogDescription>{description}</DialogDescription>
          </DialogHeader>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="browse">Browse Library</TabsTrigger>
              <TabsTrigger value="upload">Upload New</TabsTrigger>
            </TabsList>

            <TabsContent value="browse" className="flex-1 flex flex-col space-y-4">
              {/* Search and Filters */}
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search files..."
                      className="pl-10"
                      value={filters.search || ''}
                      onChange={(e) => handleSearch(e.target.value)}
                    />
                  </div>
                </div>
                
                {accept === 'all' && (
                  <Select
                    value={filters.type || 'all'}
                    onValueChange={(value) => handleFilterChange('type', value)}
                  >
                    <SelectTrigger className="w-[140px]">
                      <Filter className="mr-2 h-4 w-4" />
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="image">Images</SelectItem>
                      <SelectItem value="video">Videos</SelectItem>
                      <SelectItem value="audio">Audio</SelectItem>
                      <SelectItem value="document">Documents</SelectItem>
                    </SelectContent>
                  </Select>
                )}

                <div className="flex items-center space-x-1">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Selected Files Info */}
              {selectedFiles.length > 0 && (
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Check className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium">
                      {selectedFiles.length} file{selectedFiles.length !== 1 ? 's' : ''} selected
                    </span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedFiles([])}
                  >
                    Clear Selection
                  </Button>
                </div>
              )}

              {/* File Display */}
              <div className="flex-1 overflow-y-auto min-h-[300px] max-h-[400px]">
                {viewMode === 'grid' ? (
                  <MediaGrid
                    files={filteredFiles}
                    loading={loading}
                    onPreview={handleFileSelect}
                    onDelete={() => {}} // Disable delete in browser mode
                    onDownload={() => {}} // Disable download in browser mode
                    selectable={true}
                    selectedFiles={selectedFiles}
                    onSelectionChange={handleSelectionChange}
                  />
                ) : (
                  <MediaList
                    files={filteredFiles}
                    loading={loading}
                    onPreview={handleFileSelect}
                    onDelete={() => {}} // Disable delete in browser mode
                    onDownload={() => {}} // Disable download in browser mode
                    selectable={true}
                    selectedFiles={selectedFiles}
                    onSelectionChange={handleSelectionChange}
                  />
                )}
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    Page {pagination.page} of {pagination.totalPages}
                  </p>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(pagination.page - 1)}
                      disabled={pagination.page <= 1}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(pagination.page + 1)}
                      disabled={pagination.page >= pagination.totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="upload" className="flex-1">
              <div className="h-full">
                <MediaUploadDialog
                  onUpload={uploadFiles}
                  onClose={handleUploadComplete}
                />
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter>
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center space-x-2">
                {selectedFileObjects.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {selectedFileObjects.slice(0, 3).map((file) => (
                      <Badge key={file.id} variant="secondary" className="text-xs">
                        {file.name}
                      </Badge>
                    ))}
                    {selectedFileObjects.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{selectedFileObjects.length - 3} more
                      </Badge>
                    )}
                  </div>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" onClick={handleCancel}>
                  Cancel
                </Button>
                <Button
                  onClick={handleConfirmSelection}
                  disabled={selectedFiles.length === 0}
                >
                  Select {selectedFiles.length > 0 ? `(${selectedFiles.length})` : ''}
                </Button>
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Upload Dialog */}
      {showUploadDialog && (
        <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
          <MediaUploadDialog
            onUpload={uploadFiles}
            onClose={(uploadedFiles) => {
              setShowUploadDialog(false)
              if (uploadedFiles && uploadedFiles.length > 0) {
                handleUploadComplete(uploadedFiles)
              }
            }}
          />
        </Dialog>
      )}
    </>
  )
}
