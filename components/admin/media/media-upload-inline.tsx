'use client'

import { useState, useRef, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import {
  Upload,
  X,
  File as FileIcon,
  Image,
  Video,
  Music,
  FileText,
  Loader2,
  CheckCircle,
  AlertCircle,
  GripVertical
} from 'lucide-react'
import { cn, formatFileSize } from '@/lib/utils'
import { MediaFile } from '@/hooks/use-media'

interface MediaUploadProps {
  onUpload: (files: File[], metadata?: Partial<MediaFile['metadata']>) => Promise<MediaFile[]>
  onClose: () => void
}

interface FileWithPreview {
  id: string
  file: File
  preview?: string
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
  name: string
  size: number
  type: string
}

export function MediaUploadInline({ onUpload, onClose }: MediaUploadProps) {
  const [files, setFiles] = useState<FileWithPreview[]>([])
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [dragOver, setDragOver] = useState(false)
  const [metadata, setMetadata] = useState({
    folder: 'root',
    alt: '',
    title: '',
    description: '',
    tags: ''
  })
  const [tagInput, setTagInput] = useState('')
  const [lightbox, setLightbox] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Tag parsing and management
  const tags = metadata.tags
    ? metadata.tags.split(',').map(t => t.trim()).filter(Boolean)
    : []
  const addTag = (tag: string) => {
    if (!tag) return
    const newTags = Array.from(new Set([...tags, tag]))
    setMetadata(prev => ({ ...prev, tags: newTags.join(', ') }))
    setTagInput('')
  }
  const removeTag = (tag: string) => {
    const newTags = tags.filter(t => t !== tag)
    setMetadata(prev => ({ ...prev, tags: newTags.join(', ') }))
  }
  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if ((e.key === 'Enter' || e.key === ',') && tagInput.trim()) {
      e.preventDefault()
      addTag(tagInput.trim())
    }
    if (e.key === 'Backspace' && !tagInput && tags.length > 0) {
      removeTag(tags[tags.length - 1])
    }
  }

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <Image className="h-5 w-5 text-green-600" />
    if (fileType.startsWith('video/')) return <Video className="h-5 w-5 text-blue-600" />
    if (fileType.startsWith('audio/')) return <Music className="h-5 w-5 text-purple-600" />
    if (fileType.startsWith('application/') || fileType.startsWith('text/')) {
      return <FileText className="h-5 w-5 text-orange-600" />
    }
    return <FileIcon className="h-5 w-5 text-gray-600" />
  }

  const createFilePreview = (file: File): string | undefined => {
    if (file.type.startsWith('image/')) {
      return URL.createObjectURL(file)
    }
    return undefined
  }

  const addFiles = useCallback((newFiles: FileList | File[]) => {
    const fileArray = Array.from(newFiles)
    const filesWithPreview: FileWithPreview[] = fileArray.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      file: file,
      preview: createFilePreview(file),
      status: 'pending',
      name: file.name,
      size: file.size,
      type: file.type
    }))
    setFiles(prev => [...prev, ...filesWithPreview])
  }, [])

  const removeFile = useCallback((fileId: string) => {
    setFiles(prev => {
      const file = prev.find(f => f.id === fileId)
      if (file?.preview) {
        URL.revokeObjectURL(file.preview)
      }
      return prev.filter(f => f.id !== fileId)
    })
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    const droppedFiles = e.dataTransfer.files
    if (droppedFiles.length > 0) {
      addFiles(droppedFiles)
    }
  }, [addFiles])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }, [])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = e.target.files
    if (selectedFiles) {
      addFiles(selectedFiles)
    }
    e.target.value = ''
  }, [addFiles])

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  const handleDropZoneKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault()
      openFileDialog()
    }
  }

  const handleUpload = async () => {
    if (files.length === 0) return
    setUploading(true)
    setUploadProgress(0)
    setFiles(prev => prev.map(file => ({ ...file, status: 'uploading' as const })))
    // Extract the original File objects from our wrapper objects
    const filesToUpload = files.map(f => f.file)
    const uploadMetadata = {
      folder: metadata.folder || 'root',
      alt: metadata.alt,
      title: metadata.title,
      description: metadata.description,
      tags,
    }
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => Math.min(prev + 10, 95))
    }, 200)
    try {
      await onUpload(filesToUpload, uploadMetadata)
      clearInterval(progressInterval)
      setUploadProgress(100)
      setFiles(prev => prev.map(file => ({ ...file, status: 'success' as const })))
      setTimeout(() => {
        onClose()
      }, 1000)
    } catch (error) {
      clearInterval(progressInterval)
      setUploadProgress(0)
      console.error('Upload error:', error)
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'
      setFiles(prev => prev.map(file => ({ 
        ...file, 
        status: 'error' as const, 
        error: errorMessage
      })))
    } finally {
      setUploading(false)
    }
  }

  const canUpload = files.length > 0 && !uploading

  return (
    <div className="max-w-[600px] mx-auto bg-white rounded-xl shadow-lg p-8 my-12 border border-gray-200">
      <div className="mb-8">
        <h2 className="text-3xl font-extrabold mb-2 tracking-tight">Upload Files</h2>
        <p className="text-muted-foreground mb-4">Upload images, videos, documents and other media files to your library</p>
      </div>
      <div className="space-y-8">
        {/* File Drop Zone */}
        <div
          className={cn(
            'border-2 border-dashed rounded-xl p-10 text-center transition-all cursor-pointer outline-none',
            dragOver ? 'border-blue-500 bg-blue-50 shadow-lg' : 'border-gray-300 hover:border-blue-400',
            uploading && 'pointer-events-none opacity-50',
            'focus:ring-2 focus:ring-blue-400'
          )}
          tabIndex={0}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={openFileDialog}
          onKeyDown={handleDropZoneKeyDown}
          aria-label="File upload drop zone"
        >
          <div className="flex flex-col items-center space-y-4">
            <div className={cn(
              'p-5 rounded-full transition-colors',
              dragOver ? 'bg-blue-100' : 'bg-gray-100'
            )}>
              <Upload className={cn('h-10 w-10', dragOver ? 'text-blue-600' : 'text-gray-600')} />
            </div>
            <div>
              <p className="text-xl font-semibold">Drop files here or click to browse</p>
              <p className="text-sm text-muted-foreground">
                Support for images, videos, audio, and documents up to 50MB
              </p>
            </div>
          </div>
        </div>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          className="hidden"
          onChange={handleFileSelect}
          accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar"
        />
        {/* File List */}
        {files.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="font-semibold text-lg">Selected Files ({files.length})</h4>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setFiles([])}
                disabled={uploading}
                className="hover:bg-red-50 hover:text-red-600"
              >
                Clear All
              </Button>
            </div>
            <div className="space-y-2 max-h-56 overflow-y-auto pr-1">
              {files.map((file) => (
                <div
                  key={file.id}
                  className={cn(
                    'flex items-center space-x-3 p-3 border rounded-lg bg-gray-50 hover:bg-gray-100 transition group',
                    file.status === 'error' && 'border-red-400 bg-red-50',
                    file.status === 'success' && 'border-green-400 bg-green-50'
                  )}
                >
                  <div className="flex items-center gap-2">
                    <GripVertical className="h-4 w-4 text-gray-300 cursor-move" />
                    {file.preview ? (
                      <img
                        src={file.preview}
                        alt={file.name}
                        className="w-12 h-12 object-cover rounded shadow cursor-pointer hover:scale-105 transition"
                        onClick={() => setLightbox(file.preview!)}
                        tabIndex={0}
                        onKeyDown={e => {
                          if (e.key === 'Enter' || e.key === ' ') setLightbox(file.preview!)
                        }}
                        aria-label="Preview image"
                      />
                    ) : (
                      <div className="w-12 h-12 flex items-center justify-center bg-white rounded border">
                        {getFileIcon(file.type)}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-base font-medium truncate">{file.name}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(file.size)}
                    </p>
                    {file.status === 'error' && file.error && (
                      <p className="text-xs text-red-600 mt-1">{file.error}</p>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {file.status === 'pending' && (
                      <Badge variant="outline">Pending</Badge>
                    )}
                    {file.status === 'uploading' && (
                      <Badge variant="secondary">
                        <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                        Uploading
                      </Badge>
                    )}
                    {file.status === 'success' && (
                      <Badge variant="default">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Success
                      </Badge>
                    )}
                    {file.status === 'error' && (
                      <Badge variant="destructive">
                        <AlertCircle className="w-3 h-3 mr-1" />
                        Error
                      </Badge>
                    )}
                    {!uploading && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                        className="hover:bg-red-100"
                        aria-label={`Remove file ${file.name}`}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
        {/* Upload Progress */}
        {uploading && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Uploading files...</span>
              <span>{uploadProgress}%</span>
            </div>
            <Progress value={uploadProgress} />
          </div>
        )}
        {/* Metadata Form */}
        {files.length > 0 && !uploading && (
          <div className="space-y-4 border-t pt-4">
            <h4 className="font-semibold text-lg">File Metadata (Optional)</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="folder">Folder</Label>
                <Input
                  id="folder"
                  value={metadata.folder}
                  onChange={(e) => setMetadata(prev => ({ ...prev, folder: e.target.value }))}
                  placeholder="root"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tags">Tags</Label>
                <div className="flex flex-wrap gap-2 items-center border rounded px-2 py-1 bg-white min-h-[40px]">
                  {tags.map(tag => (
                    <span key={tag} className="inline-flex items-center bg-blue-100 text-blue-700 rounded px-2 py-0.5 text-xs font-medium">
                      {tag}
                      <button
                        type="button"
                        className="ml-1 text-blue-500 hover:text-red-500 focus:outline-none"
                        onClick={() => removeTag(tag)}
                        aria-label={`Remove tag ${tag}`}
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </span>
                  ))}
                  <input
                    id="tags"
                    className="flex-1 min-w-[80px] border-none outline-none bg-transparent text-sm"
                    type="text"
                    value={tagInput}
                    onChange={e => setTagInput(e.target.value)}
                    onKeyDown={handleTagInputKeyDown}
                    placeholder={tags.length === 0 ? 'Add tags...' : ''}
                    disabled={uploading}
                    aria-label="Add tag"
                  />
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={metadata.description}
                onChange={(e) => setMetadata(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Optional description for the files"
                rows={2}
              />
            </div>
          </div>
        )}
      </div>
      <div className="flex justify-end gap-2 mt-10">
        <Button variant="outline" onClick={onClose} disabled={uploading} className="hover:bg-gray-100">
          Cancel
        </Button>
        <Button onClick={handleUpload} disabled={!canUpload} className="hover:bg-blue-600 hover:text-white">
          {uploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Upload {files.length} File{files.length !== 1 ? 's' : ''}
            </>
          )}
        </Button>
      </div>
      {/* Lightbox for image preview */}
      {lightbox && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/70 backdrop-blur-sm"
          onClick={() => setLightbox(null)}
          tabIndex={0}
          aria-modal="true"
          role="dialog"
        >
          <img
            src={lightbox}
            alt="Preview"
            className="max-w-[90vw] max-h-[80vh] rounded-lg shadow-2xl border-4 border-white"
            onClick={e => e.stopPropagation()}
          />
          <button
            className="absolute top-8 right-8 bg-white rounded-full p-2 shadow hover:bg-red-100"
            onClick={() => setLightbox(null)}
            aria-label="Close preview"
          >
            <X className="h-6 w-6 text-red-600" />
          </button>
        </div>
      )}
    </div>
  )
}
