'use client'

import { useState } from 'react'

interface ImageWithFallbackProps {
  src: string
  fallbackSrc?: string
  alt: string
  className?: string
  fallbackIcon: React.ReactNode
  fallbackClassName?: string
}

export function ImageWithFallback({ 
  src, 
  fallbackSrc, 
  alt, 
  className, 
  fallbackIcon,
  fallbackClassName = "w-full h-full bg-gray-50 flex items-center justify-center"
}: ImageWithFallbackProps) {
  const [imgSrc, setImgSrc] = useState(src)
  const [hasError, setHasError] = useState(false)

  const handleError = () => {
    if (fallbackSrc && imgSrc !== fallbackSrc) {
      setImgSrc(fallbackSrc)
    } else {
      setHasError(true)
    }
  }

  if (hasError) {
    return (
      <div className={fallbackClassName}>
        {fallbackIcon}
      </div>
    )
  }

  return (
    <img
      src={imgSrc}
      alt={alt}
      className={className}
      onError={handleError}
    />
  )
}