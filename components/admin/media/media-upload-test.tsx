'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { toast } from 'sonner'

export function MediaUploadTest() {
  const [selectedFiles, setSelectedFiles] = useState<FileList | null>(null)
  const [uploading, setUploading] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedFiles(e.target.files)
  }

  const testConnection = async () => {
    try {
      const response = await fetch('/api/admin/media/test')
      const result = await response.json()
      setTestResult(result)
      
      if (result.success) {
        toast.success('Connection test successful!')
      } else {
        toast.error('Connection test failed: ' + result.message)
      }
    } catch (error) {
      console.error('Test error:', error)
      toast.error('Test failed: ' + (error as Error).message)
    }
  }

  const handleUpload = async () => {
    if (!selectedFiles || selectedFiles.length === 0) {
      toast.error('Please select files to upload')
      return
    }

    setUploading(true)
    
    try {
      const formData = new FormData()
      Array.from(selectedFiles).forEach(file => {
        formData.append('files', file)
      })
      
      formData.append('folder', 'test')
      formData.append('alt', 'Test image')
      formData.append('title', 'Test upload')
      formData.append('description', 'Testing media upload functionality')
      formData.append('tags', 'test,upload')

      console.log('Uploading files:', Array.from(selectedFiles).map(f => f.name))

      const response = await fetch('/api/admin/media', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()
      console.log('Upload response:', result)

      if (response.ok) {
        toast.success('Upload successful!')
        setSelectedFiles(null)
        // Reset file input
        const fileInput = document.getElementById('file-input') as HTMLInputElement
        if (fileInput) fileInput.value = ''
      } else {
        toast.error('Upload failed: ' + result.error)
      }
    } catch (error) {
      console.error('Upload error:', error)
      toast.error('Upload failed: ' + (error as Error).message)
    } finally {
      setUploading(false)
    }
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Media Upload Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <Button onClick={testConnection} variant="outline" className="w-full">
            Test Appwrite Connection
          </Button>
          {testResult && (
            <div className="mt-2 p-3 bg-gray-50 rounded-md">
              <pre className="text-sm overflow-auto">
                {JSON.stringify(testResult, null, 2)}
              </pre>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="file-input">Select Files</Label>
          <Input
            id="file-input"
            type="file"
            multiple
            onChange={handleFileSelect}
            accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
          />
        </div>

        {selectedFiles && (
          <div className="space-y-2">
            <Label>Selected Files:</Label>
            <ul className="text-sm space-y-1">
              {Array.from(selectedFiles).map((file, index) => (
                <li key={index} className="flex justify-between">
                  <span>{file.name}</span>
                  <span className="text-gray-500">
                    {(file.size / 1024 / 1024).toFixed(2)} MB
                  </span>
                </li>
              ))}
            </ul>
          </div>
        )}

        <Button 
          onClick={handleUpload} 
          disabled={!selectedFiles || uploading}
          className="w-full"
        >
          {uploading ? 'Uploading...' : 'Upload Files'}
        </Button>
      </CardContent>
    </Card>
  )
}