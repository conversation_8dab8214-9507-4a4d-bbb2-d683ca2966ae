'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  Download,
  Edit,
  Trash2,
  Copy,
  ExternalLink,
  Image,
  Video,
  Music,
  FileText,
  File,
  Calendar,
  HardDrive,
  Tag,
  Save,
  X
} from 'lucide-react'
import { MediaFile } from '@/hooks/use-media'
import { formatFileSize, cn } from '@/lib/utils'
import { toast } from 'sonner'
import { ImageWithFallback } from './image-with-fallback'

interface MediaPreviewDialogProps {
  file: MediaFile
  open: boolean
  onOpenChange: (open: boolean) => void
  onUpdate: (fileId: string, updates: Partial<MediaFile>) => Promise<MediaFile>
  onDelete: (file: MediaFile) => void
  onDownload: (file: MediaFile) => void
}

export function MediaPreviewDialog({
  file,
  open,
  onOpenChange,
  onUpdate,
  onDelete,
  onDownload
}: MediaPreviewDialogProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    name: file.name,
    metadata: {
      alt: file.metadata.alt || '',
      title: file.metadata.title || '',
      description: file.metadata.description || '',
      tags: file.metadata.tags?.join(', ') || '',
      folder: file.metadata.folder || 'root'
    }
  })
  const [saving, setSaving] = useState(false)

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <Image className="h-6 w-6 text-green-600" />
      case 'video':
        return <Video className="h-6 w-6 text-blue-600" />
      case 'audio':
        return <Music className="h-6 w-6 text-purple-600" />
      case 'document':
        return <FileText className="h-6 w-6 text-orange-600" />
      default:
        return <File className="h-6 w-6 text-gray-600" />
    }
  }

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case 'image':
        return 'bg-green-100 text-green-800'
      case 'video':
        return 'bg-blue-100 text-blue-800'
      case 'audio':
        return 'bg-purple-100 text-purple-800'
      case 'document':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleCopyUrl = () => {
    navigator.clipboard.writeText(file.url)
    toast.success('File URL copied to clipboard')
  }

  const handleOpenInNewTab = () => {
    window.open(file.url, '_blank')
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      
      const updates = {
        name: editForm.name,
        metadata: {
          ...editForm.metadata,
          tags: editForm.metadata.tags
            ? editForm.metadata.tags.split(',').map(tag => tag.trim()).filter(Boolean)
            : []
        }
      }

      await onUpdate(file.id, updates)
      setIsEditing(false)
      toast.success('File updated successfully')
    } catch (error) {
      toast.error('Failed to update file')
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    setEditForm({
      name: file.name,
      metadata: {
        alt: file.metadata.alt || '',
        title: file.metadata.title || '',
        description: file.metadata.description || '',
        tags: file.metadata.tags?.join(', ') || '',
        folder: file.metadata.folder || 'root'
      }
    })
    setIsEditing(false)
  }

  const handleDelete = () => {
    if (confirm(`Are you sure you want to delete "${file.name}"?`)) {
      onDelete(file)
      onOpenChange(false)
    }
  }

  const renderPreview = () => {
    switch (file.type) {
      case 'image':
        return (
          <div className="flex items-center justify-center bg-gray-50 rounded-lg p-4">
            <ImageWithFallback
              src={file.previewUrl || file.url}
              fallbackSrc={file.url}
              alt={file.metadata.alt || file.name}
              className="max-w-full max-h-96 object-contain rounded"
              fallbackIcon={
                <div className="text-center">
                  <Image className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <p className="text-sm text-muted-foreground">
                    Image preview not available
                  </p>
                </div>
              }
              fallbackClassName="flex items-center justify-center bg-gray-50 rounded-lg p-4"
            />
          </div>
        )
      
      case 'video':
        return (
          <div className="flex items-center justify-center bg-gray-50 rounded-lg p-4">
            <video
              src={file.url}
              controls
              className="max-w-full max-h-96 rounded"
            >
              Your browser does not support the video tag.
            </video>
          </div>
        )
      
      case 'audio':
        return (
          <div className="flex items-center justify-center bg-gray-50 rounded-lg p-8">
            <div className="text-center">
              <Music className="h-16 w-16 text-purple-600 mx-auto mb-4" />
              <audio src={file.url} controls className="w-full max-w-md">
                Your browser does not support the audio tag.
              </audio>
            </div>
          </div>
        )
      
      default:
        return (
          <div className="flex items-center justify-center bg-gray-50 rounded-lg p-8">
            <div className="text-center">
              {getFileIcon(file.type)}
              <p className="mt-2 text-sm text-muted-foreground">
                Preview not available for this file type
              </p>
            </div>
          </div>
        )
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center space-x-3">
            {getFileIcon(file.type)}
            <div className="flex-1 min-w-0">
              <DialogTitle className="truncate">{file.name}</DialogTitle>
              <DialogDescription className="flex items-center space-x-2">
                <Badge
                  variant="secondary"
                  className={cn("text-xs", getFileTypeColor(file.type))}
                >
                  {file.type.toUpperCase()}
                </Badge>
                <span>{formatFileSize(file.size)}</span>
                <span>•</span>
                <span>{new Date(file.createdAt).toLocaleDateString()}</span>
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <Tabs defaultValue="preview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="preview">Preview</TabsTrigger>
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="metadata">Metadata</TabsTrigger>
          </TabsList>

          <TabsContent value="preview" className="space-y-4">
            {renderPreview()}
          </TabsContent>

          <TabsContent value="details" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <File className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">File Name</span>
                </div>
                <p className="text-sm text-muted-foreground pl-6">{file.name}</p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <HardDrive className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">File Size</span>
                </div>
                <p className="text-sm text-muted-foreground pl-6">{formatFileSize(file.size)}</p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Created</span>
                </div>
                <p className="text-sm text-muted-foreground pl-6">
                  {new Date(file.createdAt).toLocaleString()}
                </p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">Modified</span>
                </div>
                <p className="text-sm text-muted-foreground pl-6">
                  {new Date(file.updatedAt).toLocaleString()}
                </p>
              </div>
            </div>

            <Separator />

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <ExternalLink className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">File URL</span>
              </div>
              <div className="flex items-center space-x-2 pl-6">
                <Input
                  value={file.url}
                  readOnly
                  className="text-xs"
                />
                <Button size="sm" variant="outline" onClick={handleCopyUrl}>
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="metadata" className="space-y-4">
            {isEditing ? (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-title">Title</Label>
                  <Input
                    id="edit-title"
                    value={editForm.metadata.title}
                    onChange={(e) => setEditForm(prev => ({
                      ...prev,
                      metadata: { ...prev.metadata, title: e.target.value }
                    }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-alt">Alt Text</Label>
                  <Input
                    id="edit-alt"
                    value={editForm.metadata.alt}
                    onChange={(e) => setEditForm(prev => ({
                      ...prev,
                      metadata: { ...prev.metadata, alt: e.target.value }
                    }))}
                    placeholder="Alternative text for accessibility"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea
                    id="edit-description"
                    value={editForm.metadata.description}
                    onChange={(e) => setEditForm(prev => ({
                      ...prev,
                      metadata: { ...prev.metadata, description: e.target.value }
                    }))}
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-tags">Tags</Label>
                  <Input
                    id="edit-tags"
                    value={editForm.metadata.tags}
                    onChange={(e) => setEditForm(prev => ({
                      ...prev,
                      metadata: { ...prev.metadata, tags: e.target.value }
                    }))}
                    placeholder="tag1, tag2, tag3"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Button onClick={handleSave} disabled={saving}>
                    {saving ? (
                      <>
                        <Save className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                      </>
                    )}
                  </Button>
                  <Button variant="outline" onClick={handleCancel}>
                    <X className="mr-2 h-4 w-4" />
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">File Metadata</h4>
                  <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </Button>
                </div>

                <div className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium">Title</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {file.metadata.title || 'No title set'}
                    </p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Alt Text</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {file.metadata.alt || 'No alt text set'}
                    </p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Description</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {file.metadata.description || 'No description set'}
                    </p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Tags</Label>
                    <div className="mt-1">
                      {file.metadata.tags && file.metadata.tags.length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {file.metadata.tags.map((tag, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              <Tag className="mr-1 h-3 w-3" />
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-muted-foreground">No tags set</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={() => onDownload(file)}>
                <Download className="mr-2 h-4 w-4" />
                Download
              </Button>
              <Button variant="outline" onClick={handleOpenInNewTab}>
                <ExternalLink className="mr-2 h-4 w-4" />
                Open
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="destructive" onClick={handleDelete}>
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Close
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
