'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  MoreHorizontal,
  Eye,
  Download,
  Edit,
  Trash2,
  Image,
  Video,
  Music,
  FileText,
  File,
  Copy,
  ExternalLink
} from 'lucide-react'
import { MediaFile } from '@/hooks/use-media'
import { formatFileSize, cn } from '@/lib/utils'
import { toast } from 'sonner'
import { MediaLibraryModal } from './media-library-modal'
import { MediaPreviewDialog } from './media-preview-dialog'
import { ImageWithFallback } from './image-with-fallback'

interface MediaGridProps {
  files: MediaFile[]
  loading: boolean
  onPreview: (file: MediaFile) => void
  onDelete: (file: MediaFile) => void
  onDownload: (file: MediaFile) => void
  onEdit?: (file: MediaFile) => void
  onUpdate?: (fileId: string, updates: Partial<MediaFile>) => Promise<MediaFile>
  selectable?: boolean
  selectedFiles?: string[]
  onSelectionChange?: (fileIds: string[]) => void
  showActions?: boolean
}

export function MediaGrid({
  files,
  loading,
  onPreview,
  onDelete,
  onDownload,
  onEdit,
  onUpdate,
  selectable = false,
  selectedFiles = [],
  onSelectionChange,
  showActions = true
}: MediaGridProps) {
  const [hoveredFile, setHoveredFile] = useState<string | null>(null)
  const [showMediaLibrary, setShowMediaLibrary] = useState(false)
  const [showPreviewDialog, setShowPreviewDialog] = useState(false)
  const [selectedFile, setSelectedFile] = useState<MediaFile | null>(null)

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <Image className="h-8 w-8 text-green-600" />
      case 'video':
        return <Video className="h-8 w-8 text-blue-600" />
      case 'audio':
        return <Music className="h-8 w-8 text-purple-600" />
      case 'document':
        return <FileText className="h-8 w-8 text-orange-600" />
      default:
        return <File className="h-8 w-8 text-gray-600" />
    }
  }

  const getFileTypeColor = (type: string) => {
    switch (type) {
      case 'image':
        return 'bg-green-100 text-green-800'
      case 'video':
        return 'bg-blue-100 text-blue-800'
      case 'audio':
        return 'bg-purple-100 text-purple-800'
      case 'document':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const handleCopyUrl = (file: MediaFile) => {
    navigator.clipboard.writeText(file.url)
    toast.success('File URL copied to clipboard')
  }

  const handleOpenInNewTab = (file: MediaFile) => {
    window.open(file.url, '_blank')
  }

  const handleFileClick = (file: MediaFile) => {
    if (selectable && onSelectionChange) {
      const isSelected = selectedFiles.includes(file.id)
      if (isSelected) {
        onSelectionChange(selectedFiles.filter(id => id !== file.id))
      } else {
        onSelectionChange([...selectedFiles, file.id])
      }
    } else {
      onPreview(file)
    }
  }

  const handleOpenMediaLibrary = (file: MediaFile) => {
    setSelectedFile(file)
    setShowPreviewDialog(true)
  }

  const handleFilePreview = (file: MediaFile) => {
    if (selectable && onSelectionChange) {
      handleFileClick(file)
    } else {
      handleOpenMediaLibrary(file)
    }
  }

  const handleUpdateFile = async (fileId: string, updates: Partial<MediaFile>) => {
    if (onUpdate) {
      return await onUpdate(fileId, updates)
    }
    throw new Error('Update function not provided')
  }

  if (loading) {
    return (
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
        {[...Array(12)].map((_, i) => (
          <Card key={i} className="aspect-square">
            <CardContent className="p-4 h-full">
              <div className="h-full bg-gray-200 rounded animate-pulse"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (files.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <File className="h-12 w-12 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No files found</h3>
        <p className="text-gray-500">Upload some files to get started</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
      {files.map((file) => {
        const isSelected = selectedFiles.includes(file.id)
        const isHovered = hoveredFile === file.id

        return (
          <Card
            key={file.id}
            className={cn(
              "group cursor-pointer transition-all duration-200 hover:shadow-md",
              isSelected && "ring-2 ring-blue-500 shadow-md",
              selectable && "hover:ring-2 hover:ring-blue-300"
            )}
            onMouseEnter={() => setHoveredFile(file.id)}
            onMouseLeave={() => setHoveredFile(null)}
            onClick={() => handleFilePreview(file)}
          >
            <CardContent className="p-0">
              <div className="aspect-square relative overflow-hidden rounded-t-lg">
                {file.type === 'image' ? (
                  <ImageWithFallback
                    src={file.previewUrl || file.url}
                    fallbackSrc={file.url}
                    alt={file.metadata.alt || file.name}
                    className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                    fallbackIcon={getFileIcon(file.type)}
                  />
                ) : (
                  <div className="w-full h-full bg-gray-50 flex items-center justify-center">
                    {getFileIcon(file.type)}
                  </div>
                )}

                {/* Overlay with actions */}
                {showActions && (
                  <div
                    className={cn(
                      "absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center space-x-2 transition-opacity duration-200",
                      isHovered || isSelected ? "opacity-100" : "opacity-0"
                    )}
                  >
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleOpenMediaLibrary(file)
                      }}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        size="sm"
                        variant="secondary"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onPreview(file)}>
                        <Eye className="mr-2 h-4 w-4" />
                        Preview
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => onDownload(file)}>
                        <Download className="mr-2 h-4 w-4" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleCopyUrl(file)}>
                        <Copy className="mr-2 h-4 w-4" />
                        Copy URL
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleOpenInNewTab(file)}>
                        <ExternalLink className="mr-2 h-4 w-4" />
                        Open in New Tab
                      </DropdownMenuItem>
                      {onEdit && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => onEdit(file)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                        </>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => onDelete(file)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                  </div>
                )}

                {/* File type badge */}
                <div className="absolute top-2 left-2">
                  <Badge
                    variant="secondary"
                    className={cn("text-xs", getFileTypeColor(file.type))}
                  >
                    {file.type.toUpperCase()}
                  </Badge>
                </div>

                {/* Selection indicator */}
                {selectable && (
                  <div className="absolute top-2 right-2">
                    <div
                      className={cn(
                        "w-5 h-5 rounded-full border-2 flex items-center justify-center",
                        isSelected
                          ? "bg-blue-500 border-blue-500"
                          : "bg-white border-gray-300"
                      )}
                    >
                      {isSelected && (
                        <div className="w-2 h-2 bg-white rounded-full" />
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* File info */}
              <div className="p-3">
                <h4 className="font-medium text-sm truncate mb-1" title={file.name}>
                  {file.name}
                </h4>
                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>{formatFileSize(file.size)}</span>
                  <span>{new Date(file.createdAt).toLocaleDateString()}</span>
                </div>
                
                {file.metadata.tags && file.metadata.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {file.metadata.tags.slice(0, 2).map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {file.metadata.tags.length > 2 && (
                      <Badge variant="outline" className="text-xs">
                        +{file.metadata.tags.length - 2}
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )
      })}
      
      {/* Preview Dialog */}
      {selectedFile && showPreviewDialog && onUpdate && (
        <MediaPreviewDialog
          file={selectedFile}
          open={showPreviewDialog}
          onOpenChange={setShowPreviewDialog}
          onUpdate={handleUpdateFile}
          onDelete={onDelete}
          onDownload={onDownload}
        />
      )}
    </div>
  )
}
