'use client';

import { NotificationSummary } from '@/lib/notifications/components';
import { useSession } from 'next-auth/react';

/**
 * HeaderNotification Component
 * 
 * This component is designed to be used in the admin header to display notifications
 * It uses the NotificationSummary component from the notifications library
 */
export function HeaderNotification() {
  const { data: session } = useSession();
  const userId = session?.user?.id;

  return (
    <NotificationSummary 
      userId={userId} 
      enableRealTime={true}
      maxNotifications={50}
    />
  );
}