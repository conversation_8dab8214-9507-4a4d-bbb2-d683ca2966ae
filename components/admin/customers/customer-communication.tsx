'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Mail,
  MessageSquare,
  Phone,
  Send,
  Plus,
  Calendar,
  Users,
  Target,
  BarChart3,
  Eye,
  Edit,
  Trash2,
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Filter,
  Search,
  Download,
  Settings
} from 'lucide-react'

interface Campaign {
  id: string
  name: string
  type: 'email' | 'sms' | 'push'
  status: 'draft' | 'scheduled' | 'sent' | 'active'
  subject: string
  content: string
  targetSegment: string
  scheduledDate?: string
  sentDate?: string
  recipients: number
  openRate?: number
  clickRate?: number
  conversionRate?: number
  createdAt: string
}

interface Template {
  id: string
  name: string
  type: 'email' | 'sms' | 'push'
  category: string
  subject: string
  content: string
  isActive: boolean
  usageCount: number
  createdAt: string
}

const mockCampaigns: Campaign[] = [
  {
    id: '1',
    name: 'Summer Sale 2024',
    type: 'email',
    status: 'sent',
    subject: '🌞 Summer Sale - Up to 50% Off!',
    content: 'Don\'t miss our biggest summer sale...',
    targetSegment: 'All Customers',
    sentDate: '2024-06-15',
    recipients: 2847,
    openRate: 24.5,
    clickRate: 3.2,
    conversionRate: 1.8,
    createdAt: '2024-06-10'
  },
  {
    id: '2',
    name: 'VIP Customer Exclusive',
    type: 'email',
    status: 'active',
    subject: '👑 Exclusive VIP Offer Just for You',
    content: 'As a valued VIP customer...',
    targetSegment: 'VIP Customers',
    scheduledDate: '2024-06-25',
    recipients: 89,
    createdAt: '2024-06-20'
  },
  {
    id: '3',
    name: 'Cart Abandonment Reminder',
    type: 'email',
    status: 'active',
    subject: 'You left something behind...',
    content: 'Complete your purchase and save...',
    targetSegment: 'Cart Abandoners',
    recipients: 156,
    openRate: 18.7,
    clickRate: 5.4,
    conversionRate: 12.3,
    createdAt: '2024-06-18'
  }
]

const mockTemplates: Template[] = [
  {
    id: '1',
    name: 'Welcome Email',
    type: 'email',
    category: 'Onboarding',
    subject: 'Welcome to {{store_name}}!',
    content: 'Thank you for joining us...',
    isActive: true,
    usageCount: 234,
    createdAt: '2024-01-15'
  },
  {
    id: '2',
    name: 'Order Confirmation',
    type: 'email',
    category: 'Transactional',
    subject: 'Order Confirmation #{{order_number}}',
    content: 'Your order has been confirmed...',
    isActive: true,
    usageCount: 1456,
    createdAt: '2024-01-20'
  },
  {
    id: '3',
    name: 'Promotional SMS',
    type: 'sms',
    category: 'Marketing',
    subject: '',
    content: 'Flash Sale! 30% off everything. Use code FLASH30. Valid until midnight.',
    isActive: true,
    usageCount: 89,
    createdAt: '2024-02-10'
  }
]

export function CustomerCommunication() {
  const [activeTab, setActiveTab] = useState('campaigns')
  const [campaigns, setCampaigns] = useState<Campaign[]>(mockCampaigns)
  const [templates, setTemplates] = useState<Template[]>(mockTemplates)
  const [isCreateCampaignOpen, setIsCreateCampaignOpen] = useState(false)
  const [isCreateTemplateOpen, setIsCreateTemplateOpen] = useState(false)

  const [newCampaign, setNewCampaign] = useState({
    name: '',
    type: 'email' as const,
    subject: '',
    content: '',
    targetSegment: '',
    scheduledDate: ''
  })

  const [newTemplate, setNewTemplate] = useState({
    name: '',
    type: 'email' as const,
    category: '',
    subject: '',
    content: ''
  })

  const getStatusColor = (status: Campaign['status']) => {
    switch (status) {
      case 'draft': return 'secondary'
      case 'scheduled': return 'outline'
      case 'sent': return 'default'
      case 'active': return 'default'
      default: return 'secondary'
    }
  }

  const getTypeIcon = (type: 'email' | 'sms' | 'push') => {
    switch (type) {
      case 'email': return Mail
      case 'sms': return MessageSquare
      case 'push': return Phone
      default: return Mail
    }
  }

  const createCampaign = () => {
    const campaign: Campaign = {
      id: Date.now().toString(),
      name: newCampaign.name,
      type: newCampaign.type,
      status: newCampaign.scheduledDate ? 'scheduled' : 'draft',
      subject: newCampaign.subject,
      content: newCampaign.content,
      targetSegment: newCampaign.targetSegment,
      scheduledDate: newCampaign.scheduledDate || undefined,
      recipients: Math.floor(Math.random() * 1000) + 100,
      createdAt: new Date().toISOString().split('T')[0]
    }
    
    setCampaigns(prev => [...prev, campaign])
    setNewCampaign({
      name: '',
      type: 'email',
      subject: '',
      content: '',
      targetSegment: '',
      scheduledDate: ''
    })
    setIsCreateCampaignOpen(false)
  }

  const createTemplate = () => {
    const template: Template = {
      id: Date.now().toString(),
      name: newTemplate.name,
      type: newTemplate.type,
      category: newTemplate.category,
      subject: newTemplate.subject,
      content: newTemplate.content,
      isActive: true,
      usageCount: 0,
      createdAt: new Date().toISOString().split('T')[0]
    }
    
    setTemplates(prev => [...prev, template])
    setNewTemplate({
      name: '',
      type: 'email',
      category: '',
      subject: '',
      content: ''
    })
    setIsCreateTemplateOpen(false)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Customer Communication</h2>
          <p className="text-muted-foreground">
            Manage email campaigns, templates, and customer messaging
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <BarChart3 className="mr-2 h-4 w-4" />
            Analytics
          </Button>
          <Button variant="outline" size="sm">
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </div>

      {/* Communication Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{campaigns.length}</div>
            <p className="text-xs text-muted-foreground">
              {campaigns.filter(c => c.status === 'active').length} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Open Rate</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">21.4%</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 text-green-500" /> +2.1% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Click Rate</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.2%</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 text-green-500" /> +0.8% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">5.1%</div>
            <p className="text-xs text-muted-foreground">
              <TrendingUp className="inline h-3 w-3 text-green-500" /> +1.2% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="campaigns" className="flex items-center space-x-2">
            <Mail className="h-4 w-4" />
            <span>Campaigns</span>
          </TabsTrigger>
          <TabsTrigger value="templates" className="flex items-center space-x-2">
            <MessageSquare className="h-4 w-4" />
            <span>Templates</span>
          </TabsTrigger>
          <TabsTrigger value="automation" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>Automation</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="campaigns" className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input placeholder="Search campaigns..." className="pl-8 w-64" />
              </div>
              <Select defaultValue="all">
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="sent">Sent</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Dialog open={isCreateCampaignOpen} onOpenChange={setIsCreateCampaignOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Campaign
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Create New Campaign</DialogTitle>
                  <DialogDescription>
                    Create a new marketing campaign to engage your customers
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="campaign-name">Campaign Name</Label>
                      <Input
                        id="campaign-name"
                        value={newCampaign.name}
                        onChange={(e) => setNewCampaign(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="e.g., Summer Sale 2024"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="campaign-type">Type</Label>
                      <Select 
                        value={newCampaign.type} 
                        onValueChange={(value: 'email' | 'sms' | 'push') => 
                          setNewCampaign(prev => ({ ...prev, type: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="email">Email</SelectItem>
                          <SelectItem value="sms">SMS</SelectItem>
                          <SelectItem value="push">Push Notification</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="target-segment">Target Segment</Label>
                      <Select 
                        value={newCampaign.targetSegment} 
                        onValueChange={(value) => setNewCampaign(prev => ({ ...prev, targetSegment: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select segment" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Customers</SelectItem>
                          <SelectItem value="vip">VIP Customers</SelectItem>
                          <SelectItem value="frequent">Frequent Buyers</SelectItem>
                          <SelectItem value="new">New Customers</SelectItem>
                          <SelectItem value="at-risk">At-Risk Customers</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="scheduled-date">Scheduled Date (Optional)</Label>
                      <Input
                        id="scheduled-date"
                        type="datetime-local"
                        value={newCampaign.scheduledDate}
                        onChange={(e) => setNewCampaign(prev => ({ ...prev, scheduledDate: e.target.value }))}
                      />
                    </div>
                  </div>

                  {newCampaign.type === 'email' && (
                    <div className="space-y-2">
                      <Label htmlFor="campaign-subject">Subject Line</Label>
                      <Input
                        id="campaign-subject"
                        value={newCampaign.subject}
                        onChange={(e) => setNewCampaign(prev => ({ ...prev, subject: e.target.value }))}
                        placeholder="Enter email subject"
                      />
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="campaign-content">Content</Label>
                    <Textarea
                      id="campaign-content"
                      value={newCampaign.content}
                      onChange={(e) => setNewCampaign(prev => ({ ...prev, content: e.target.value }))}
                      placeholder="Enter campaign content..."
                      rows={6}
                    />
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setIsCreateCampaignOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={createCampaign} disabled={!newCampaign.name || !newCampaign.content}>
                      Create Campaign
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div className="grid gap-4">
            {campaigns.map((campaign) => {
              const TypeIcon = getTypeIcon(campaign.type)
              return (
                <Card key={campaign.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <TypeIcon className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <CardTitle className="text-lg">{campaign.name}</CardTitle>
                          <CardDescription>
                            {campaign.subject} • {campaign.targetSegment}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={getStatusColor(campaign.status)}>
                          {campaign.status}
                        </Badge>
                        <div className="flex space-x-1">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-4 md:grid-cols-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold">{campaign.recipients.toLocaleString()}</div>
                        <div className="text-xs text-muted-foreground">Recipients</div>
                      </div>
                      {campaign.openRate && (
                        <div className="text-center">
                          <div className="text-2xl font-bold">{campaign.openRate}%</div>
                          <div className="text-xs text-muted-foreground">Open Rate</div>
                        </div>
                      )}
                      {campaign.clickRate && (
                        <div className="text-center">
                          <div className="text-2xl font-bold">{campaign.clickRate}%</div>
                          <div className="text-xs text-muted-foreground">Click Rate</div>
                        </div>
                      )}
                      {campaign.conversionRate && (
                        <div className="text-center">
                          <div className="text-2xl font-bold">{campaign.conversionRate}%</div>
                          <div className="text-xs text-muted-foreground">Conversion</div>
                        </div>
                      )}
                    </div>
                    <div className="mt-4 flex items-center justify-between text-sm text-muted-foreground">
                      <span>
                        {campaign.sentDate ? `Sent on ${campaign.sentDate}` : 
                         campaign.scheduledDate ? `Scheduled for ${campaign.scheduledDate}` :
                         `Created on ${campaign.createdAt}`}
                      </span>
                      {campaign.status === 'draft' && (
                        <Button size="sm">
                          <Send className="mr-2 h-4 w-4" />
                          Send Now
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input placeholder="Search templates..." className="pl-8 w-64" />
              </div>
              <Select defaultValue="all">
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="email">Email</SelectItem>
                  <SelectItem value="sms">SMS</SelectItem>
                  <SelectItem value="push">Push</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Dialog open={isCreateTemplateOpen} onOpenChange={setIsCreateTemplateOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Template
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Create New Template</DialogTitle>
                  <DialogDescription>
                    Create a reusable template for your campaigns
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="template-name">Template Name</Label>
                      <Input
                        id="template-name"
                        value={newTemplate.name}
                        onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="e.g., Welcome Email"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="template-type">Type</Label>
                      <Select 
                        value={newTemplate.type} 
                        onValueChange={(value: 'email' | 'sms' | 'push') => 
                          setNewTemplate(prev => ({ ...prev, type: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="email">Email</SelectItem>
                          <SelectItem value="sms">SMS</SelectItem>
                          <SelectItem value="push">Push Notification</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="template-category">Category</Label>
                    <Select 
                      value={newTemplate.category} 
                      onValueChange={(value) => setNewTemplate(prev => ({ ...prev, category: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="marketing">Marketing</SelectItem>
                        <SelectItem value="transactional">Transactional</SelectItem>
                        <SelectItem value="onboarding">Onboarding</SelectItem>
                        <SelectItem value="retention">Retention</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {newTemplate.type === 'email' && (
                    <div className="space-y-2">
                      <Label htmlFor="template-subject">Subject Line</Label>
                      <Input
                        id="template-subject"
                        value={newTemplate.subject}
                        onChange={(e) => setNewTemplate(prev => ({ ...prev, subject: e.target.value }))}
                        placeholder="Enter email subject (use {{variables}} for personalization)"
                      />
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="template-content">Content</Label>
                    <Textarea
                      id="template-content"
                      value={newTemplate.content}
                      onChange={(e) => setNewTemplate(prev => ({ ...prev, content: e.target.value }))}
                      placeholder="Enter template content (use {{variables}} for personalization)..."
                      rows={8}
                    />
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setIsCreateTemplateOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={createTemplate} disabled={!newTemplate.name || !newTemplate.content}>
                      Create Template
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {templates.map((template) => {
              const TypeIcon = getTypeIcon(template.type)
              return (
                <Card key={template.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <TypeIcon className="h-4 w-4 text-muted-foreground" />
                        <CardTitle className="text-sm">{template.name}</CardTitle>
                      </div>
                      <Badge variant="outline">{template.category}</Badge>
                    </div>
                    {template.subject && (
                      <CardDescription className="text-xs">
                        {template.subject}
                      </CardDescription>
                    )}
                  </CardHeader>
                  <CardContent>
                    <div className="text-xs text-muted-foreground mb-3 line-clamp-3">
                      {template.content}
                    </div>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>Used {template.usageCount} times</span>
                      <span>{template.createdAt}</span>
                    </div>
                    <div className="flex justify-between mt-3">
                      <Badge variant={template.isActive ? 'default' : 'secondary'}>
                        {template.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                      <div className="flex space-x-1">
                        <Button variant="outline" size="sm">
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </TabsContent>

        <TabsContent value="automation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Email Automation</CardTitle>
              <CardDescription>
                Set up automated email sequences based on customer behavior
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">Automation Coming Soon</h3>
                <p className="text-muted-foreground mb-4">
                  Set up automated workflows to engage customers at the right time
                </p>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Automation
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}