'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  ShoppingCart,
  Calendar,
  Download,
  RefreshCw
} from 'lucide-react'
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts'

const customerGrowthData = [
  { month: 'Jan', customers: 1200, newCustomers: 150, churnedCustomers: 45 },
  { month: 'Feb', customers: 1305, newCustomers: 180, churnedCustomers: 75 },
  { month: 'Mar', customers: 1450, newCustomers: 220, churnedCustomers: 75 },
  { month: 'Apr', customers: 1620, newCustomers: 250, churnedCustomers: 80 },
  { month: 'May', customers: 1850, newCustomers: 280, churnedCustomers: 50 },
  { month: 'Jun', customers: 2100, newCustomers: 300, churnedCustomers: 50 }
]

const customerSegmentData = [
  { name: 'New Customers', value: 35, color: '#8884d8' },
  { name: 'Regular Customers', value: 45, color: '#82ca9d' },
  { name: 'VIP Customers', value: 15, color: '#ffc658' },
  { name: 'Inactive Customers', value: 5, color: '#ff7c7c' }
]

const customerValueData = [
  { range: '0-500', customers: 1200, percentage: 42 },
  { range: '500-1000', customers: 800, percentage: 28 },
  { range: '1000-2500', customers: 600, percentage: 21 },
  { range: '2500-5000', customers: 200, percentage: 7 },
  { range: '5000+', customers: 47, percentage: 2 }
]

const cohortData = [
  { cohort: 'Jan 2024', month1: 100, month2: 85, month3: 72, month4: 65, month5: 58, month6: 52 },
  { cohort: 'Feb 2024', month1: 100, month2: 88, month3: 75, month4: 68, month5: 61 },
  { cohort: 'Mar 2024', month1: 100, month2: 90, month3: 78, month4: 70 },
  { cohort: 'Apr 2024', month1: 100, month2: 92, month3: 80 },
  { cohort: 'May 2024', month1: 100, month2: 94 },
  { cohort: 'Jun 2024', month1: 100 }
]

export function CustomerAnalytics() {
  const [timeRange, setTimeRange] = useState('6m')
  const [activeTab, setActiveTab] = useState('overview')

  const metrics = [
    {
      title: 'Customer Acquisition Cost',
      value: 'R 45.50',
      change: '-12%',
      trend: 'up' as const,
      icon: Users,
      description: 'Cost to acquire new customer'
    },
    {
      title: 'Customer Lifetime Value',
      value: 'R 1,250',
      change: '+8%',
      trend: 'up' as const,
      icon: DollarSign,
      description: 'Average customer lifetime value'
    },
    {
      title: 'Average Order Frequency',
      value: '3.2 orders',
      change: '+5%',
      trend: 'up' as const,
      icon: ShoppingCart,
      description: 'Orders per customer per month'
    },
    {
      title: 'Customer Retention Rate',
      value: '68%',
      change: '+3%',
      trend: 'up' as const,
      icon: TrendingUp,
      description: '6-month retention rate'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Customer Analytics</h2>
          <p className="text-muted-foreground">
            Insights into customer behavior and performance metrics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1m">1 Month</SelectItem>
              <SelectItem value="3m">3 Months</SelectItem>
              <SelectItem value="6m">6 Months</SelectItem>
              <SelectItem value="1y">1 Year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric) => {
          const Icon = metric.icon
          const TrendIcon = metric.trend === 'up' ? TrendingUp : TrendingDown
          return (
            <Card key={metric.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {metric.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{metric.value}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <TrendIcon className={`mr-1 h-3 w-3 ${
                    metric.trend === 'up' ? 'text-green-500' : 'text-red-500'
                  }`} />
                  {metric.change} from last period
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {metric.description}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="segments">Segments</TabsTrigger>
          <TabsTrigger value="cohorts">Cohorts</TabsTrigger>
          <TabsTrigger value="value">Value Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Customer Growth</CardTitle>
                <CardDescription>
                  New customers vs churn over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={customerGrowthData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="customers" 
                      stroke="#8884d8" 
                      strokeWidth={2}
                      name="Total Customers"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="newCustomers" 
                      stroke="#82ca9d" 
                      strokeWidth={2}
                      name="New Customers"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="churnedCustomers" 
                      stroke="#ff7c7c" 
                      strokeWidth={2}
                      name="Churned Customers"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Customer Segments</CardTitle>
                <CardDescription>
                  Distribution of customer types
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={customerSegmentData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percentage }) => `${name}: ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {customerSegmentData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="segments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Segmentation Analysis</CardTitle>
              <CardDescription>
                Detailed breakdown of customer segments and their characteristics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                {customerSegmentData.map((segment) => (
                  <div key={segment.name} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{segment.name}</h3>
                      <Badge style={{ backgroundColor: segment.color, color: 'white' }}>
                        {segment.value}%
                      </Badge>
                    </div>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <p>Avg Order Value: R {Math.floor(Math.random() * 500) + 100}</p>
                      <p>Orders/Month: {Math.floor(Math.random() * 5) + 1}</p>
                      <p>Retention: {Math.floor(Math.random() * 30) + 60}%</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cohorts" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Cohort Analysis</CardTitle>
              <CardDescription>
                Customer retention by acquisition month
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Cohort</th>
                      <th className="text-center p-2">Month 1</th>
                      <th className="text-center p-2">Month 2</th>
                      <th className="text-center p-2">Month 3</th>
                      <th className="text-center p-2">Month 4</th>
                      <th className="text-center p-2">Month 5</th>
                      <th className="text-center p-2">Month 6</th>
                    </tr>
                  </thead>
                  <tbody>
                    {cohortData.map((cohort) => (
                      <tr key={cohort.cohort} className="border-b">
                        <td className="p-2 font-medium">{cohort.cohort}</td>
                        <td className="text-center p-2">
                          <Badge variant="outline">{cohort.month1}%</Badge>
                        </td>
                        <td className="text-center p-2">
                          {cohort.month2 && <Badge variant="outline">{cohort.month2}%</Badge>}
                        </td>
                        <td className="text-center p-2">
                          {cohort.month3 && <Badge variant="outline">{cohort.month3}%</Badge>}
                        </td>
                        <td className="text-center p-2">
                          {cohort.month4 && <Badge variant="outline">{cohort.month4}%</Badge>}
                        </td>
                        <td className="text-center p-2">
                          {cohort.month5 && <Badge variant="outline">{cohort.month5}%</Badge>}
                        </td>
                        <td className="text-center p-2">
                          {cohort.month6 && <Badge variant="outline">{cohort.month6}%</Badge>}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="value" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Value Distribution</CardTitle>
              <CardDescription>
                Customers grouped by lifetime value ranges
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={customerValueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="range" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="customers" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
              <div className="mt-4 grid gap-2">
                {customerValueData.map((item) => (
                  <div key={item.range} className="flex items-center justify-between p-2 border rounded">
                    <span className="font-medium">R {item.range}</span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-muted-foreground">
                        {item.customers} customers
                      </span>
                      <Badge variant="outline">{item.percentage}%</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}