'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Target,
  Plus,
  Edit,
  Trash2,
  Users,
  DollarSign,
  ShoppingCart,
  Calendar,
  MapPin,
  Star,
  Crown,
  Heart,
  AlertTriangle,
  TrendingUp,
  Filter,
  Download,
  Play,
  Pause
} from 'lucide-react'

interface CustomerSegment {
  id: string
  name: string
  description: string
  criteria: SegmentCriteria[]
  customerCount: number
  averageOrderValue: number
  totalRevenue: number
  isActive: boolean
  createdAt: string
  lastUpdated: string
}

interface SegmentCriteria {
  field: string
  operator: string
  value: string | number
  type: 'string' | 'number' | 'date' | 'boolean'
}

const mockSegments: CustomerSegment[] = [
  {
    id: '1',
    name: 'VIP Customers',
    description: 'High-value customers with lifetime value > R5000',
    criteria: [
      { field: 'lifetimeValue', operator: '>', value: 5000, type: 'number' },
      { field: 'orderCount', operator: '>=', value: 10, type: 'number' }
    ],
    customerCount: 89,
    averageOrderValue: 450.75,
    totalRevenue: 125680.50,
    isActive: true,
    createdAt: '2024-01-15',
    lastUpdated: '2024-06-20'
  },
  {
    id: '2',
    name: 'Frequent Buyers',
    description: 'Customers who purchase regularly',
    criteria: [
      { field: 'orderCount', operator: '>=', value: 5, type: 'number' },
      { field: 'daysSinceLastOrder', operator: '<=', value: 30, type: 'number' }
    ],
    customerCount: 456,
    averageOrderValue: 185.25,
    totalRevenue: 84474.00,
    isActive: true,
    createdAt: '2024-02-01',
    lastUpdated: '2024-06-19'
  },
  {
    id: '3',
    name: 'At-Risk Customers',
    description: 'Previously active customers who haven\'t purchased recently',
    criteria: [
      { field: 'daysSinceLastOrder', operator: '>', value: 90, type: 'number' },
      { field: 'orderCount', operator: '>=', value: 3, type: 'number' }
    ],
    customerCount: 234,
    averageOrderValue: 95.50,
    totalRevenue: 22347.00,
    isActive: true,
    createdAt: '2024-03-10',
    lastUpdated: '2024-06-18'
  },
  {
    id: '4',
    name: 'New Customers',
    description: 'Recently registered customers',
    criteria: [
      { field: 'registrationDate', operator: '>=', value: '2024-05-01', type: 'date' },
      { field: 'orderCount', operator: '<=', value: 2, type: 'number' }
    ],
    customerCount: 178,
    averageOrderValue: 125.00,
    totalRevenue: 22250.00,
    isActive: true,
    createdAt: '2024-05-01',
    lastUpdated: '2024-06-20'
  }
]

const segmentFields = [
  { value: 'lifetimeValue', label: 'Lifetime Value', type: 'number' },
  { value: 'orderCount', label: 'Order Count', type: 'number' },
  { value: 'averageOrderValue', label: 'Average Order Value', type: 'number' },
  { value: 'daysSinceLastOrder', label: 'Days Since Last Order', type: 'number' },
  { value: 'registrationDate', label: 'Registration Date', type: 'date' },
  { value: 'location', label: 'Location', type: 'string' },
  { value: 'customerType', label: 'Customer Type', type: 'string' }
]

const operators = {
  number: [
    { value: '>', label: 'Greater than' },
    { value: '>=', label: 'Greater than or equal' },
    { value: '<', label: 'Less than' },
    { value: '<=', label: 'Less than or equal' },
    { value: '=', label: 'Equal to' },
    { value: '!=', label: 'Not equal to' }
  ],
  string: [
    { value: 'contains', label: 'Contains' },
    { value: 'equals', label: 'Equals' },
    { value: 'startsWith', label: 'Starts with' },
    { value: 'endsWith', label: 'Ends with' }
  ],
  date: [
    { value: '>', label: 'After' },
    { value: '<', label: 'Before' },
    { value: '>=', label: 'On or after' },
    { value: '<=', label: 'On or before' }
  ]
}

export function CustomerSegmentation() {
  const [segments, setSegments] = useState<CustomerSegment[]>(mockSegments)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [editingSegment, setEditingSegment] = useState<CustomerSegment | null>(null)
  const [newSegment, setNewSegment] = useState({
    name: '',
    description: '',
    criteria: [] as SegmentCriteria[]
  })

  const getSegmentIcon = (segmentName: string) => {
    if (segmentName.toLowerCase().includes('vip')) return Crown
    if (segmentName.toLowerCase().includes('frequent')) return Heart
    if (segmentName.toLowerCase().includes('risk')) return AlertTriangle
    if (segmentName.toLowerCase().includes('new')) return Users
    return Target
  }

  const getSegmentVariant = (segmentName: string) => {
    if (segmentName.toLowerCase().includes('vip')) return 'default'
    if (segmentName.toLowerCase().includes('frequent')) return 'secondary'
    if (segmentName.toLowerCase().includes('risk')) return 'destructive'
    if (segmentName.toLowerCase().includes('new')) return 'outline'
    return 'outline'
  }

  const addCriteria = () => {
    setNewSegment(prev => ({
      ...prev,
      criteria: [...prev.criteria, { field: '', operator: '', value: '', type: 'string' }]
    }))
  }

  const updateCriteria = (index: number, field: keyof SegmentCriteria, value: any) => {
    setNewSegment(prev => ({
      ...prev,
      criteria: prev.criteria.map((criteria, i) => 
        i === index ? { ...criteria, [field]: value } : criteria
      )
    }))
  }

  const removeCriteria = (index: number) => {
    setNewSegment(prev => ({
      ...prev,
      criteria: prev.criteria.filter((_, i) => i !== index)
    }))
  }

  const toggleSegmentStatus = (segmentId: string) => {
    setSegments(prev => prev.map(segment => 
      segment.id === segmentId 
        ? { ...segment, isActive: !segment.isActive }
        : segment
    ))
  }

  const deleteSegment = (segmentId: string) => {
    setSegments(prev => prev.filter(segment => segment.id !== segmentId))
  }

  const createSegment = () => {
    const segment: CustomerSegment = {
      id: Date.now().toString(),
      name: newSegment.name,
      description: newSegment.description,
      criteria: newSegment.criteria,
      customerCount: Math.floor(Math.random() * 500) + 50,
      averageOrderValue: Math.floor(Math.random() * 300) + 100,
      totalRevenue: Math.floor(Math.random() * 50000) + 10000,
      isActive: true,
      createdAt: new Date().toISOString().split('T')[0],
      lastUpdated: new Date().toISOString().split('T')[0]
    }
    
    setSegments(prev => [...prev, segment])
    setNewSegment({ name: '', description: '', criteria: [] })
    setIsCreateDialogOpen(false)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Customer Segmentation</h2>
          <p className="text-muted-foreground">
            Create and manage customer segments for targeted marketing
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Create Segment
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create Customer Segment</DialogTitle>
                <DialogDescription>
                  Define criteria to automatically group customers
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="name">Segment Name</Label>
                    <Input
                      id="name"
                      value={newSegment.name}
                      onChange={(e) => setNewSegment(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., High Value Customers"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Input
                      id="description"
                      value={newSegment.description}
                      onChange={(e) => setNewSegment(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Brief description of the segment"
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Segment Criteria</Label>
                    <Button type="button" variant="outline" size="sm" onClick={addCriteria}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add Criteria
                    </Button>
                  </div>

                  {newSegment.criteria.map((criteria, index) => (
                    <div key={index} className="grid gap-2 md:grid-cols-4 p-4 border rounded-lg">
                      <Select
                        value={criteria.field}
                        onValueChange={(value) => {
                          const field = segmentFields.find(f => f.value === value)
                          updateCriteria(index, 'field', value)
                          updateCriteria(index, 'type', field?.type || 'string')
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select field" />
                        </SelectTrigger>
                        <SelectContent>
                          {segmentFields.map((field) => (
                            <SelectItem key={field.value} value={field.value}>
                              {field.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Select
                        value={criteria.operator}
                        onValueChange={(value) => updateCriteria(index, 'operator', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Operator" />
                        </SelectTrigger>
                        <SelectContent>
                          {operators[criteria.type as keyof typeof operators]?.map((op) => (
                            <SelectItem key={op.value} value={op.value}>
                              {op.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Input
                        value={criteria.value.toString()}
                        onChange={(e) => updateCriteria(index, 'value', 
                          criteria.type === 'number' ? Number(e.target.value) : e.target.value
                        )}
                        placeholder="Value"
                        type={criteria.type === 'number' ? 'number' : criteria.type === 'date' ? 'date' : 'text'}
                      />

                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeCriteria(index)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>

                <div className="flex justify-end space-x-2">
                  <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button onClick={createSegment} disabled={!newSegment.name || newSegment.criteria.length === 0}>
                    Create Segment
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Segments Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {segments.map((segment) => {
          const Icon = getSegmentIcon(segment.name)
          return (
            <Card key={segment.id} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Icon className="mr-2 h-4 w-4" />
                    {segment.name}
                  </CardTitle>
                  <div className="flex items-center space-x-1">
                    <Badge variant={getSegmentVariant(segment.name)}>
                      {segment.customerCount}
                    </Badge>
                    {segment.isActive ? (
                      <Badge variant="outline" className="text-green-600">
                        Active
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-gray-500">
                        Inactive
                      </Badge>
                    )}
                  </div>
                </div>
                <CardDescription className="text-xs">
                  {segment.description}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Avg Order Value:</span>
                  <span className="font-medium">R {segment.averageOrderValue.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Total Revenue:</span>
                  <span className="font-medium">R {segment.totalRevenue.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Last Updated:</span>
                  <span className="font-medium">{segment.lastUpdated}</span>
                </div>
                <div className="flex justify-between pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => toggleSegmentStatus(segment.id)}
                  >
                    {segment.isActive ? (
                      <>
                        <Pause className="mr-1 h-3 w-3" />
                        Pause
                      </>
                    ) : (
                      <>
                        <Play className="mr-1 h-3 w-3" />
                        Activate
                      </>
                    )}
                  </Button>
                  <div className="flex space-x-1">
                    <Button variant="outline" size="sm">
                      <Edit className="h-3 w-3" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => deleteSegment(segment.id)}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Segment Performance */}
      <Card>
        <CardHeader>
          <CardTitle>Segment Performance</CardTitle>
          <CardDescription>
            Compare performance metrics across customer segments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Segment</th>
                  <th className="text-center p-2">Customers</th>
                  <th className="text-center p-2">Avg Order Value</th>
                  <th className="text-center p-2">Total Revenue</th>
                  <th className="text-center p-2">Revenue per Customer</th>
                  <th className="text-center p-2">Status</th>
                  <th className="text-center p-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {segments.map((segment) => {
                  const revenuePerCustomer = segment.totalRevenue / segment.customerCount
                  return (
                    <tr key={segment.id} className="border-b hover:bg-muted/50">
                      <td className="p-2">
                        <div className="flex items-center">
                          {(() => {
                            const Icon = getSegmentIcon(segment.name)
                            return <Icon className="mr-2 h-4 w-4" />
                          })()}
                          <div>
                            <div className="font-medium">{segment.name}</div>
                            <div className="text-xs text-muted-foreground">
                              {segment.description}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="text-center p-2">
                        <Badge variant="outline">{segment.customerCount}</Badge>
                      </td>
                      <td className="text-center p-2">
                        R {segment.averageOrderValue.toFixed(2)}
                      </td>
                      <td className="text-center p-2">
                        R {segment.totalRevenue.toLocaleString()}
                      </td>
                      <td className="text-center p-2">
                        R {revenuePerCustomer.toFixed(2)}
                      </td>
                      <td className="text-center p-2">
                        <Badge variant={segment.isActive ? 'default' : 'secondary'}>
                          {segment.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </td>
                      <td className="text-center p-2">
                        <div className="flex justify-center space-x-1">
                          <Button variant="outline" size="sm">
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Users className="h-3 w-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}