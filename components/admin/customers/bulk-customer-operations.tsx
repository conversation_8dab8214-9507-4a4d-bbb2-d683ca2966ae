'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  Users,
  Mail,
  Tag,
  Trash2,
  Download,
  Upload,
  FileText,
  Send,
  UserX,
  UserCheck,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Filter,
  X
} from 'lucide-react'
import type { User } from '@/lib/ecommerce/types/user'

interface BulkCustomerOperationsProps {
  selectedCustomers: User[]
  customers: User[]
  onOperationComplete: () => void
  onClearSelection: () => void
}

interface BulkOperation {
  id: string
  type: 'email' | 'tag' | 'status' | 'export' | 'delete'
  name: string
  description: string
  icon: any
  requiresConfirmation: boolean
}

const bulkOperations: BulkOperation[] = [
  {
    id: 'send-email',
    type: 'email',
    name: 'Send Email Campaign',
    description: 'Send marketing email to selected customers',
    icon: Mail,
    requiresConfirmation: false
  },
  {
    id: 'add-tags',
    type: 'tag',
    name: 'Add Tags',
    description: 'Add tags to selected customers',
    icon: Tag,
    requiresConfirmation: false
  },
  {
    id: 'update-status',
    type: 'status',
    name: 'Update Status',
    description: 'Change customer status (active/inactive)',
    icon: UserCheck,
    requiresConfirmation: false
  },
  {
    id: 'export-data',
    type: 'export',
    name: 'Export Data',
    description: 'Export customer data to CSV',
    icon: Download,
    requiresConfirmation: false
  },
  {
    id: 'delete-customers',
    type: 'delete',
    name: 'Delete Customers',
    description: 'Permanently delete selected customers',
    icon: Trash2,
    requiresConfirmation: true
  }
]

export function BulkCustomerOperations({ 
  selectedCustomers, 
  customers, 
  onOperationComplete, 
  onClearSelection 
}: BulkCustomerOperationsProps) {
  const [selectedOperation, setSelectedOperation] = useState<string>('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [operationResult, setOperationResult] = useState<{
    success: boolean
    message: string
    details?: string
  } | null>(null)

  // Email campaign state
  const [emailCampaign, setEmailCampaign] = useState({
    subject: '',
    content: '',
    template: 'marketing'
  })

  // Tag operation state
  const [tagOperation, setTagOperation] = useState({
    action: 'add', // 'add' or 'remove'
    tags: [] as string[],
    newTag: ''
  })

  // Status update state
  const [statusUpdate, setStatusUpdate] = useState({
    status: 'active' // 'active' or 'inactive'
  })

  const handleOperationSelect = (operationId: string) => {
    setSelectedOperation(operationId)
    setOperationResult(null)
  }

  const simulateProgress = () => {
    setProgress(0)
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval)
          return 100
        }
        return prev + 10
      })
    }, 200)
  }

  const executeOperation = async () => {
    if (!selectedOperation || selectedCustomers.length === 0) return

    setIsProcessing(true)
    simulateProgress()

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000))

    const operation = bulkOperations.find(op => op.id === selectedOperation)
    
    try {
      switch (selectedOperation) {
        case 'send-email':
          setOperationResult({
            success: true,
            message: `Email campaign sent successfully to ${selectedCustomers.length} customers`,
            details: `Subject: "${emailCampaign.subject}"`
          })
          break
        
        case 'add-tags':
          setOperationResult({
            success: true,
            message: `Tags added to ${selectedCustomers.length} customers`,
            details: `Tags: ${tagOperation.tags.join(', ')}`
          })
          break
        
        case 'update-status':
          setOperationResult({
            success: true,
            message: `Status updated for ${selectedCustomers.length} customers`,
            details: `New status: ${statusUpdate.status}`
          })
          break
        
        case 'export-data':
          setOperationResult({
            success: true,
            message: `Data exported for ${selectedCustomers.length} customers`,
            details: 'CSV file has been downloaded'
          })
          break
        
        case 'delete-customers':
          setOperationResult({
            success: true,
            message: `${selectedCustomers.length} customers deleted successfully`,
            details: 'This action cannot be undone'
          })
          break
        
        default:
          throw new Error('Unknown operation')
      }
      
      onOperationComplete()
    } catch (error) {
      setOperationResult({
        success: false,
        message: 'Operation failed',
        details: error instanceof Error ? error.message : 'Unknown error occurred'
      })
    } finally {
      setIsProcessing(false)
      setProgress(0)
    }
  }

  const addTag = () => {
    if (tagOperation.newTag && !tagOperation.tags.includes(tagOperation.newTag)) {
      setTagOperation(prev => ({
        ...prev,
        tags: [...prev.tags, prev.newTag],
        newTag: ''
      }))
    }
  }

  const removeTag = (tag: string) => {
    setTagOperation(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }))
  }

  const renderOperationForm = () => {
    switch (selectedOperation) {
      case 'send-email':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email-subject">Email Subject</Label>
              <Input
                id="email-subject"
                value={emailCampaign.subject}
                onChange={(e) => setEmailCampaign(prev => ({ ...prev, subject: e.target.value }))}
                placeholder="Enter email subject"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email-template">Template</Label>
              <Select 
                value={emailCampaign.template} 
                onValueChange={(value) => setEmailCampaign(prev => ({ ...prev, template: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="marketing">Marketing Campaign</SelectItem>
                  <SelectItem value="newsletter">Newsletter</SelectItem>
                  <SelectItem value="promotion">Promotion</SelectItem>
                  <SelectItem value="welcome">Welcome Email</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email-content">Email Content</Label>
              <Textarea
                id="email-content"
                value={emailCampaign.content}
                onChange={(e) => setEmailCampaign(prev => ({ ...prev, content: e.target.value }))}
                placeholder="Enter email content..."
                rows={6}
              />
            </div>
          </div>
        )

      case 'add-tags':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Tag Action</Label>
              <Select 
                value={tagOperation.action} 
                onValueChange={(value) => setTagOperation(prev => ({ ...prev, action: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="add">Add Tags</SelectItem>
                  <SelectItem value="remove">Remove Tags</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Add New Tag</Label>
              <div className="flex space-x-2">
                <Input
                  value={tagOperation.newTag}
                  onChange={(e) => setTagOperation(prev => ({ ...prev, newTag: e.target.value }))}
                  placeholder="Enter tag name"
                  onKeyPress={(e) => e.key === 'Enter' && addTag()}
                />
                <Button type="button" onClick={addTag}>Add</Button>
              </div>
            </div>
            {tagOperation.tags.length > 0 && (
              <div className="space-y-2">
                <Label>Selected Tags</Label>
                <div className="flex flex-wrap gap-2">
                  {tagOperation.tags.map((tag) => (
                    <Badge key={tag} variant="secondary" className="flex items-center space-x-1">
                      <span>{tag}</span>
                      <button onClick={() => removeTag(tag)}>
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )

      case 'update-status':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>New Status</Label>
              <Select 
                value={statusUpdate.status} 
                onValueChange={(value) => setStatusUpdate(prev => ({ ...prev, status: value }))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                This will update the status for all {selectedCustomers.length} selected customers.
              </AlertDescription>
            </Alert>
          </div>
        )

      case 'export-data':
        return (
          <div className="space-y-4">
            <Alert>
              <FileText className="h-4 w-4" />
              <AlertDescription>
                Export data for {selectedCustomers.length} customers to CSV format.
                The file will include customer details, order history, and contact information.
              </AlertDescription>
            </Alert>
          </div>
        )

      case 'delete-customers':
        return (
          <div className="space-y-4">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Warning:</strong> This action will permanently delete {selectedCustomers.length} customers 
                and all their associated data. This cannot be undone.
              </AlertDescription>
            </Alert>
            <div className="flex items-center space-x-2">
              <Checkbox id="confirm-delete" />
              <Label htmlFor="confirm-delete" className="text-sm">
                I understand that this action is permanent and cannot be undone
              </Label>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Selection Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Users className="mr-2 h-5 w-5" />
              Selected Customers
            </span>
            <Badge variant="outline">{selectedCustomers.length}</Badge>
          </CardTitle>
          <CardDescription>
            {selectedCustomers.length === 0 
              ? 'No customers selected. Select customers from the list to perform bulk operations.'
              : `${selectedCustomers.length} customer${selectedCustomers.length === 1 ? '' : 's'} selected for bulk operations.`
            }
          </CardDescription>
        </CardHeader>
        {selectedCustomers.length > 0 && (
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Selected: {selectedCustomers.slice(0, 3).map(c => c.name || c.email).join(', ')}
                {selectedCustomers.length > 3 && ` and ${selectedCustomers.length - 3} more`}
              </div>
              <Button variant="outline" size="sm" onClick={onClearSelection}>
                Clear Selection
              </Button>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Operations */}
      {selectedCustomers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Bulk Operations</CardTitle>
            <CardDescription>
              Choose an operation to perform on selected customers
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Operation Selection */}
            <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
              {bulkOperations.map((operation) => {
                const Icon = operation.icon
                const isSelected = selectedOperation === operation.id
                return (
                  <Card 
                    key={operation.id}
                    className={`cursor-pointer transition-colors ${
                      isSelected ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'
                    }`}
                    onClick={() => handleOperationSelect(operation.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-3">
                        <Icon className={`h-5 w-5 mt-0.5 ${
                          operation.type === 'delete' ? 'text-destructive' : 'text-muted-foreground'
                        }`} />
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-sm">{operation.name}</h3>
                          <p className="text-xs text-muted-foreground mt-1">
                            {operation.description}
                          </p>
                          {operation.requiresConfirmation && (
                            <Badge variant="destructive" className="mt-2 text-xs">
                              Requires Confirmation
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            {/* Operation Form */}
            {selectedOperation && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    {(() => {
                      const operation = bulkOperations.find(op => op.id === selectedOperation)
                      const Icon = operation?.icon
                      return Icon ? <Icon className="mr-2 h-5 w-5" /> : null
                    })()}
                    {bulkOperations.find(op => op.id === selectedOperation)?.name}
                  </CardTitle>
                  <CardDescription>
                    Configure the operation for {selectedCustomers.length} selected customers
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {renderOperationForm()}

                  {/* Progress */}
                  {isProcessing && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Processing...</span>
                        <span>{progress}%</span>
                      </div>
                      <Progress value={progress} />
                    </div>
                  )}

                  {/* Result */}
                  {operationResult && (
                    <Alert variant={operationResult.success ? 'default' : 'destructive'}>
                      {operationResult.success ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <AlertTriangle className="h-4 w-4" />
                      )}
                      <AlertDescription>
                        <div className="font-medium">{operationResult.message}</div>
                        {operationResult.details && (
                          <div className="text-sm mt-1">{operationResult.details}</div>
                        )}
                      </AlertDescription>
                    </Alert>
                  )}

                  {/* Actions */}
                  <div className="flex justify-end space-x-2">
                    <Button 
                      variant="outline" 
                      onClick={() => setSelectedOperation('')}
                      disabled={isProcessing}
                    >
                      Cancel
                    </Button>
                    <Button 
                      onClick={executeOperation}
                      disabled={isProcessing || (selectedOperation === 'send-email' && !emailCampaign.subject)}
                      variant={selectedOperation === 'delete-customers' ? 'destructive' : 'default'}
                    >
                      {isProcessing ? (
                        <>
                          <Clock className="mr-2 h-4 w-4 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <Zap className="mr-2 h-4 w-4" />
                          Execute Operation
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}