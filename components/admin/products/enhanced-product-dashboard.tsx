'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useProductStats } from '@/hooks/useProducts'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Skeleton } from '@/components/ui/skeleton'
import { 
  Package, 
  Plus, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Eye,
  Edit,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Star,
  ShoppingCart,
  Users,
  Image,
  Search,
  Filter,
  Download,
  Upload,
  RefreshCw,
  Settings,
  Sparkles,
  AlertCircle
} from 'lucide-react'
import { EnhancedProductList } from './enhanced-product-list'
import { ProductAnalytics } from './product-analytics'
import { BulkProductOperations } from './bulk-product-operations'
import { ProductImportExport } from './product-import-export'
import { ProductService } from '@/lib/ecommerce/services/product-service'

interface ProductStats {
  total: number
  active: number
  draft: number
  lowStock: number
  outOfStock: number
  totalValue: number
  averagePrice: number
  recentlyUpdated: number
}

interface Product {
  id: string
  status: 'active' | 'draft' | 'archived'
  price: number
  updatedAt: string
  // Add other product properties as needed
}

interface ProductsResponse {
  data: Product[]
  // Add pagination and other response properties as needed
}

interface EnhancedProductDashboardProps {
  initialStats?: ProductStats
}

export function EnhancedProductDashboard({ initialStats }: EnhancedProductDashboardProps) {
  const [activeTab, setActiveTab] = useState('overview')
  const [showBulkOperations, setShowBulkOperations] = useState(false)
  const { stats, isLoading } = useProductStats()
  const error = null // Error is now handled by the hook

  const quickActions = [
    {
      label: 'Add Product',
      href: '/admin/e-commerce/products/new',
      icon: Plus,
      description: 'Create a new product',
      variant: 'default' as const
    },
    {
      label: 'Import Products',
      href: '/admin/e-commerce/products/import',
      icon: Upload,
      description: 'Bulk import products',
      variant: 'outline' as const
    },
    {
      label: 'Analytics',
      href: '/admin/e-commerce/products/analytics',
      icon: BarChart3,
      description: 'Product performance insights',
      variant: 'outline' as const
    }
  ]

  const statCards = [
    {
      title: 'Total Products',
      value: stats.total,
      change: '+12%',
      trend: 'up' as const,
      icon: Package,
      description: 'All products in catalog',
      variant: 'default' as const
    },
    {
      title: 'Active Products',
      value: stats.active,
      change: '+8%',
      trend: 'up' as const,
      icon: CheckCircle,
      description: 'Currently available for sale',
      variant: 'default' as const
    },
    {
      title: 'Total Value',
      value: `R ${stats.totalValue.toLocaleString()}`,
      change: '+15%',
      trend: 'up' as const,
      icon: DollarSign,
      description: 'Total inventory value',
      variant: 'default' as const
    },
    {
      title: 'Average Price',
      value: `R ${stats.averagePrice.toFixed(2)}`,
      change: '+3%',
      trend: 'up' as const,
      icon: TrendingUp,
      description: 'Average product price',
      variant: 'default' as const
    }
  ]

  type ButtonVariant = 'default' | 'secondary' | 'destructive' | 'outline' | 'success'

  const alertCards: Array<{
    title: string
    value: number
    icon: React.ComponentType<{ className?: string }>
    variant: ButtonVariant
    action: string
    href: string
  }> = [
    {
      title: 'Low Stock',
      value: stats.lowStock,
      icon: AlertTriangle,
      variant: 'default',
      action: 'View Products',
      href: '/admin/e-commerce/products?filter=low-stock'
    },
    {
      title: 'Out of Stock',
      value: stats.outOfStock ,
      icon: AlertTriangle,
      variant: 'destructive',
      action: 'Restock Now',
      href: '/admin/e-commerce/products?filter=out-of-stock'
    },
    {
      title: 'Draft Products',
      value: stats.draft,
      icon: Clock,
      variant: 'secondary',
      action: 'Review Drafts',
      href: '/admin/e-commerce/products?status=draft'
    }
  ]

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-9 w-64 mb-2" />
            <Skeleton className="h-5 w-80" />
          </div>
          <div className="flex space-x-2">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-9 w-32" />
            ))}
          </div>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-36" />
          ))}
        </div>
      </div>
    )
  }

  // Error state
  if (error || !stats) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Product Management</h1>
            <p className="text-muted-foreground">
              Manage your product catalog with advanced tools and insights
            </p>
          </div>
        </div>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error || 'Failed to load product statistics. '}
            <Button
              variant="link"
              className="h-auto p-0 ml-1 text-inherit"
              onClick={() => window.location.reload()}
            >
              Try again
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Product Management</h1>
          <p className="text-muted-foreground">
            Manage your product catalog with advanced tools and insights
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {quickActions.map((action) => {
            const Icon = action.icon
            return (
              <Button
                key={action.href}
                variant={action.variant}
                size="sm"
                asChild
              >
                <Link href={action.href}>
                  <Icon className="mr-2 h-4 w-4" />
                  {action.label}
                </Link>
              </Button>
            )
          })}
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => {
          const Icon = stat.icon
                  // For now, we'll show a neutral trend
          const trend = 'up' as const
          const TrendIcon = trend === 'up' ? TrendingUp : TrendingDown
          const variant = stat.variant
          return (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <Icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="flex items-center text-xs text-muted-foreground">
                  <TrendIcon className={`mr-1 h-3 w-3 ${
                    stat.trend === 'up' ? 'text-green-500' : 'text-red-500'
                  }`} />
                  {stat.change} from last month
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Alerts */}
      <div className="grid gap-4 md:grid-cols-3">
        {alertCards.map((alert) => {
          const Icon = alert.icon
          return (
            <Card key={alert.title} className="border-l-4 border-l-orange-500">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium flex items-center">
                    <Icon className="mr-2 h-4 w-4" />
                    {alert.title}
                  </CardTitle>
                  <Badge variant={alert.variant}>{alert.value}</Badge>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <Button variant="outline" size="sm" asChild>
                  <Link href={alert.href}>
                    {alert.action}
                  </Link>
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <Package className="h-4 w-4" />
            <span>Products</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="bulk" className="flex items-center space-x-2">
            <Zap className="h-4 w-4" />
            <span>Bulk Operations</span>
          </TabsTrigger>
          <TabsTrigger value="import" className="flex items-center space-x-2">
            <Upload className="h-4 w-4" />
            <span>Import/Export</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <EnhancedProductList
            onCreateProduct={() => window.location.href = '/admin/e-commerce/products/new'}
            onEditProduct={(product) => window.location.href = `/admin/e-commerce/products/${product.id}/edit`}
            onViewProduct={(product) => window.location.href = `/admin/e-commerce/products/${product.id}`}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <ProductAnalytics />
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Operations</CardTitle>
              <CardDescription>
                Perform actions on multiple products at once
              </CardDescription>
            </CardHeader>
            <CardContent>
              <BulkProductOperations
                selectedProducts={[]}
                products={[]}
                onOperationComplete={() => {}}
                onClearSelection={() => {}}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="import" className="space-y-4">
          <ProductImportExport />
        </TabsContent>
      </Tabs>
    </div>
  )
}
