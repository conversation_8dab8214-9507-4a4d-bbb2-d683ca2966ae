'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useInventoryMutations } from '@/lib/ecommerce/hooks/use-inventory'
import { toast } from 'sonner'

interface InventoryUpdateTestProps {
  productId: string
  variantId?: string
  currentStock: number
}

export function InventoryUpdateTest({ productId, variantId, currentStock }: InventoryUpdateTestProps) {
  const [quantity, setQuantity] = useState(currentStock)
  const { updateInventory, loading, error } = useInventoryMutations()

  const handleUpdateInventory = async () => {
    try {
      const success = await updateInventory(productId, variantId, quantity)
      
      if (success) {
        toast.success('Inventory updated successfully!')
      } else {
        toast.error('Failed to update inventory')
      }
    } catch (err) {
      console.error('Error updating inventory:', err)
      toast.error('An error occurred while updating inventory')
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Update Inventory</CardTitle>
        <CardDescription>
          Test updating the inventory quantity for this product
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-4">
          <Input
            type="number"
            min="0"
            value={quantity}
            onChange={(e) => setQuantity(parseInt(e.target.value, 10))}
            className="w-32"
          />
          <Button 
            onClick={handleUpdateInventory}
            disabled={loading}
          >
            {loading ? 'Updating...' : 'Update Stock'}
          </Button>
        </div>
        
        {error && (
          <p className="text-sm text-red-500">{error}</p>
        )}
        
        <div className="text-sm text-muted-foreground">
          <p>Product ID: {productId}</p>
          {variantId && <p>Variant ID: {variantId}</p>}
          <p>Current Stock: {currentStock}</p>
        </div>
      </CardContent>
    </Card>
  )
}