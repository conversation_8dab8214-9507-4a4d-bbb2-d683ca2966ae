'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Package,
  Edit,
  Trash2,
  Archive,
  Eye,
  EyeOff,
  Tag,
  DollarSign,
  Upload,
  Download,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import { toast } from 'sonner'
import { ZarPriceInput } from '../zar-price-input'

interface BulkOperation {
  type: 'update_status' | 'update_price' | 'update_category' | 'update_tags' | 'delete' | 'export'
  label: string
  icon: React.ReactNode
  description: string
  requiresConfirmation?: boolean
  destructive?: boolean
}

interface BulkOperationResult {
  success: boolean
  processedCount: number
  failedCount: number
  errors?: Array<{
    productId: string
    productTitle: string
    error: string
  }>
}

interface BulkProductOperationsProps {
  selectedProducts: string[]
  products: Array<{
    id: string
    title: string
    status: string
    price: number
  }>
  onOperationComplete: () => void
  onClearSelection: () => void
}

export function BulkProductOperations({ 
  selectedProducts, 
  products, 
  onOperationComplete,
  onClearSelection 
}: BulkProductOperationsProps) {
  const [selectedOperation, setSelectedOperation] = useState<string>('')
  const [showOperationDialog, setShowOperationDialog] = useState(false)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [operationInProgress, setOperationInProgress] = useState(false)
  const [operationProgress, setOperationProgress] = useState(0)
  const [operationResult, setOperationResult] = useState<BulkOperationResult | null>(null)

  // Operation form data
  const [operationData, setOperationData] = useState({
    status: '',
    priceAdjustment: 0,
    priceAdjustmentType: 'percentage', // 'percentage' | 'fixed'
    categoryId: '',
    tags: '',
    newPrice: 0
  })

  const bulkOperations: BulkOperation[] = [
    {
      type: 'update_status',
      label: 'Update Status',
      icon: <Eye className="h-4 w-4" />,
      description: 'Change the status of selected products'
    },
    {
      type: 'update_price',
      label: 'Update Prices',
      icon: <DollarSign className="h-4 w-4" />,
      description: 'Adjust prices for selected products'
    },
    {
      type: 'update_category',
      label: 'Update Category',
      icon: <Tag className="h-4 w-4" />,
      description: 'Move products to a different category'
    },
    {
      type: 'update_tags',
      label: 'Update Tags',
      icon: <Tag className="h-4 w-4" />,
      description: 'Add or update tags for selected products'
    },
    {
      type: 'delete',
      label: 'Delete Products',
      icon: <Trash2 className="h-4 w-4" />,
      description: 'Permanently delete selected products',
      requiresConfirmation: true,
      destructive: true
    },
    {
      type: 'export',
      label: 'Export Products',
      icon: <Download className="h-4 w-4" />,
      description: 'Export selected products to CSV'
    }
  ]

  const selectedProductsData = products.filter(p => selectedProducts.includes(p.id))

  const handleOperationSelect = (operationType: string) => {
    setSelectedOperation(operationType)
    const operation = bulkOperations.find(op => op.type === operationType)
    
    if (operation?.requiresConfirmation) {
      setShowConfirmDialog(true)
    } else {
      setShowOperationDialog(true)
    }
  }

  const executeOperation = async () => {
    setOperationInProgress(true)
    setOperationProgress(0)
    setShowOperationDialog(false)
    setShowConfirmDialog(false)

    try {
      const result = await performBulkOperation(selectedOperation, operationData)
      setOperationResult(result)
      
      if (result.success) {
        toast.success(`Operation completed successfully! ${result.processedCount} products updated.`)
        onOperationComplete()
        onClearSelection()
      } else {
        toast.error(`Operation completed with errors. ${result.failedCount} products failed.`)
      }
    } catch (error) {
      console.error('Bulk operation error:', error)
      toast.error('Operation failed. Please try again.')
    } finally {
      setOperationInProgress(false)
      setOperationProgress(100)
    }
  }

  const performBulkOperation = async (
    operation: string, 
    data: any
  ): Promise<BulkOperationResult> => {
    // Simulate API call with progress updates
    const totalProducts = selectedProducts.length
    let processedCount = 0
    let failedCount = 0
    const errors: Array<{ productId: string; productTitle: string; error: string }> = []

    for (let i = 0; i < totalProducts; i++) {
      const productId = selectedProducts[i]
      const product = selectedProductsData.find(p => p.id === productId)
      
      // Simulate processing time
      await new Promise(resolve => setTimeout(resolve, 100))
      
      try {
        // Simulate API call based on operation type
        switch (operation) {
          case 'update_status':
            await updateProductStatus(productId, data.status)
            break
          case 'update_price':
            await updateProductPrice(productId, data)
            break
          case 'update_category':
            await updateProductCategory(productId, data.categoryId)
            break
          case 'update_tags':
            await updateProductTags(productId, data.tags)
            break
          case 'delete':
            await deleteProduct(productId)
            break
          case 'export':
            // Export doesn't modify products
            break
        }
        
        processedCount++
      } catch (error) {
        failedCount++
        errors.push({
          productId,
          productTitle: product?.title || 'Unknown Product',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
      
      setOperationProgress(((i + 1) / totalProducts) * 100)
    }

    return {
      success: failedCount === 0,
      processedCount,
      failedCount,
      errors: errors.length > 0 ? errors : undefined
    }
  }

  // API functions using our e-commerce endpoints
  const updateProductStatus = async (productId: string, status: string) => {
    const response = await fetch(`/api/e-commerce/products/id/${productId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ status })
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update product status')
    }
  }

  const updateProductPrice = async (productId: string, priceData: any) => {
    let newPrice: number

    if (priceData.priceAdjustmentType === 'percentage') {
      const product = selectedProductsData.find(p => p.id === productId)
      if (!product) throw new Error('Product not found')
      newPrice = product.price * (1 + priceData.priceAdjustment / 100)
    } else if (priceData.priceAdjustmentType === 'fixed') {
      const product = selectedProductsData.find(p => p.id === productId)
      if (!product) throw new Error('Product not found')
      newPrice = product.price + priceData.priceAdjustment
    } else {
      newPrice = priceData.newPrice
    }

    const response = await fetch(`/api/e-commerce/products/id/${productId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        price: { amount: newPrice, currency: 'ZAR' }
      })
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update product price')
    }
  }

  const updateProductCategory = async (productId: string, categoryId: string) => {
    const response = await fetch(`/api/e-commerce/products/id/${productId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ categoryIds: [categoryId] })
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update product category')
    }
  }

  const updateProductTags = async (productId: string, tags: string) => {
    const tagArray = tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)

    const response = await fetch(`/api/e-commerce/products/id/${productId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ tags: tagArray })
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to update product tags')
    }
  }

  const deleteProduct = async (productId: string) => {
    const response = await fetch(`/api/e-commerce/products/id/${productId}`, {
      method: 'DELETE'
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to delete product')
    }
  }

  const renderOperationForm = () => {
    switch (selectedOperation) {
      case 'update_status':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>New Status</Label>
              <Select 
                value={operationData.status} 
                onValueChange={(value) => setOperationData({ ...operationData, status: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )

      case 'update_price':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Price Adjustment Type</Label>
              <Select 
                value={operationData.priceAdjustmentType} 
                onValueChange={(value) => setOperationData({ ...operationData, priceAdjustmentType: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">Percentage Adjustment</SelectItem>
                  <SelectItem value="fixed">Fixed Amount Adjustment</SelectItem>
                  <SelectItem value="set">Set New Price</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {operationData.priceAdjustmentType === 'percentage' && (
              <div className="space-y-2">
                <Label>Percentage Change (%)</Label>
                <Input
                  type="number"
                  value={operationData.priceAdjustment}
                  onChange={(e) => setOperationData({ 
                    ...operationData, 
                    priceAdjustment: parseFloat(e.target.value) || 0 
                  })}
                  placeholder="e.g., 10 for 10% increase, -10 for 10% decrease"
                />
              </div>
            )}

            {operationData.priceAdjustmentType === 'fixed' && (
              <div className="space-y-2">
                <Label>Amount Change (ZAR)</Label>
                <Input
                  type="number"
                  value={operationData.priceAdjustment}
                  onChange={(e) => setOperationData({ 
                    ...operationData, 
                    priceAdjustment: parseFloat(e.target.value) || 0 
                  })}
                  placeholder="e.g., 50 for R50 increase, -50 for R50 decrease"
                />
              </div>
            )}

            {operationData.priceAdjustmentType === 'set' && (
              <ZarPriceInput
                label="New Price"
                value={operationData.newPrice}
                onChange={(value) => setOperationData({ ...operationData, newPrice: value })}
              />
            )}
          </div>
        )

      case 'update_category':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>New Category</Label>
              <Select 
                value={operationData.categoryId} 
                onValueChange={(value) => setOperationData({ ...operationData, categoryId: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">T-Shirts</SelectItem>
                  <SelectItem value="2">Dresses</SelectItem>
                  <SelectItem value="3">Pants</SelectItem>
                  <SelectItem value="4">Shoes</SelectItem>
                  <SelectItem value="5">Accessories</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )

      case 'update_tags':
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Tags</Label>
              <Textarea
                value={operationData.tags}
                onChange={(e) => setOperationData({ ...operationData, tags: e.target.value })}
                placeholder="Enter tags separated by commas (e.g., summer, kids, cotton)"
                rows={3}
              />
              <p className="text-sm text-muted-foreground">
                Separate multiple tags with commas. These will be added to existing tags.
              </p>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  if (selectedProducts.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            <Package className="mx-auto h-8 w-8 mb-2" />
            <p>Select products to perform bulk operations</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Bulk Operations</span>
            <Badge variant="secondary">
              {selectedProducts.length} selected
            </Badge>
          </CardTitle>
          <CardDescription>
            Perform actions on multiple products at once
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2">
            {bulkOperations.map((operation) => (
              <Button
                key={operation.type}
                variant="outline"
                size="sm"
                className={`h-auto p-3 flex flex-col items-center space-y-1 ${
                  operation.destructive ? 'border-red-200 hover:border-red-300' : ''
                }`}
                onClick={() => handleOperationSelect(operation.type)}
                disabled={operationInProgress}
              >
                {operation.icon}
                <span className="text-xs text-center">{operation.label}</span>
              </Button>
            ))}
          </div>

          {operationInProgress && (
            <div className="mt-4 space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Processing operation...</span>
                <span>{Math.round(operationProgress)}%</span>
              </div>
              <Progress value={operationProgress} className="w-full" />
            </div>
          )}

          {operationResult && (
            <div className="mt-4 p-4 border rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                {operationResult.success ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <AlertCircle className="h-5 w-5 text-orange-500" />
                )}
                <span className="font-medium">
                  Operation {operationResult.success ? 'Completed' : 'Completed with Errors'}
                </span>
              </div>
              
              <div className="text-sm text-muted-foreground space-y-1">
                <p>Processed: {operationResult.processedCount} products</p>
                {operationResult.failedCount > 0 && (
                  <p>Failed: {operationResult.failedCount} products</p>
                )}
              </div>

              {operationResult.errors && operationResult.errors.length > 0 && (
                <div className="mt-3 space-y-1">
                  <p className="text-sm font-medium">Errors:</p>
                  <div className="max-h-32 overflow-y-auto space-y-1">
                    {operationResult.errors.map((error, index) => (
                      <div key={index} className="text-xs text-red-600 bg-red-50 p-2 rounded">
                        <span className="font-medium">{error.productTitle}:</span> {error.error}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Operation Dialog */}
      <Dialog open={showOperationDialog} onOpenChange={setShowOperationDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {bulkOperations.find(op => op.type === selectedOperation)?.label}
            </DialogTitle>
            <DialogDescription>
              {bulkOperations.find(op => op.type === selectedOperation)?.description}
              <br />
              This will affect {selectedProducts.length} selected products.
            </DialogDescription>
          </DialogHeader>

          {renderOperationForm()}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowOperationDialog(false)}>
              Cancel
            </Button>
            <Button onClick={executeOperation}>
              Apply Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete {selectedProducts.length} products
              and remove all associated data including variants, images, and reviews.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={executeOperation} className="bg-red-600 hover:bg-red-700">
              Delete Products
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}
