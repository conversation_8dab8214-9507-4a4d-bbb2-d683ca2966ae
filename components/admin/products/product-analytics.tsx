'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  TrendingUp, 
  TrendingDown, 
  Package, 
  DollarSign, 
  ShoppingCart, 
  AlertTriangle,
  Eye,
  Star,
  Users
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { formatDistanceToNow } from 'date-fns'

interface ProductAnalyticsData {
  totalProducts: number
  activeProducts: number
  draftProducts: number
  archivedProducts: number
  totalValue: number
  averagePrice: number
  lowStockProducts: number
  outOfStockProducts: number
  topSellingProducts: Array<{
    id: string
    title: string
    sales: number
    revenue: number
    image?: string
  }>
  categoryBreakdown: Array<{
    name: string
    count: number
    percentage: number
  }>
  recentActivity: Array<{
    id: string
    type: 'created' | 'updated' | 'sold'
    productTitle: string
    timestamp: Date
    details?: string
  }>
  performanceMetrics: {
    totalViews: number
    conversionRate: number
    averageRating: number
    totalReviews: number
  }
}

interface ProductAnalyticsProps {
  timeRange?: '7d' | '30d' | '90d' | '1y'
  onTimeRangeChange?: (range: string) => void
}

interface StatCardProps {
  icon: React.ElementType
  title: string
  value: string | number
  change?: string
  changeType?: 'increase' | 'decrease'
}

export function ProductAnalytics({ 
  timeRange = '30d', 
  onTimeRangeChange 
}: ProductAnalyticsProps) {
  const [data, setData] = useState<ProductAnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchAnalyticsData()
  }, [timeRange])

  const fetchAnalyticsData = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/analytics/products?timeRange=${timeRange}`)
      const result = await response.json()

      if (result.success) {
        setData(result.data)
      } else {
        setError(result.error || 'Failed to fetch analytics data')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const StatCard = ({ icon: Icon, title, value, change, changeType }: StatCardProps) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change && (
          <p className={`text-xs ${changeType === 'increase' ? 'text-green-600' : 'text-red-600'}`}>
            {change}
          </p>
        )}
      </CardContent>
    </Card>
  )

  const StatCardSkeleton = () => (
    <Card>
      <CardContent className="p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-8 bg-gray-200 rounded w-1/2"></div>
        </div>
      </CardContent>
    </Card>
  )

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => <StatCardSkeleton key={i} />)}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <AlertTriangle className="mx-auto h-8 w-8 text-red-500 mb-2" />
            <p className="text-red-600">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!data) return null

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600'
      case 'draft': return 'text-yellow-600'
      case 'archived': return 'text-gray-600'
      default: return 'text-gray-600'
    }
  }

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'created': return <Package className="h-4 w-4 text-green-600" />
      case 'updated': return <Eye className="h-4 w-4 text-blue-600" />
      case 'sold': return <ShoppingCart className="h-4 w-4 text-purple-600" />
      default: return <Package className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Product Analytics</h2>
          <p className="text-muted-foreground">
            An overview of your product performance.
          </p>
        </div>
        <Select value={timeRange} onValueChange={onTimeRangeChange}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
            <SelectItem value="1y">Last year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard 
          icon={Package} 
          title="Total Products" 
          value={data.totalProducts} 
          change={`${data.activeProducts} active`}
        />
        <StatCard 
          icon={DollarSign} 
          title="Total Inventory Value" 
          value={formatCurrency(data.totalValue, 'ZAR')} 
          change={`Avg. price: ${formatCurrency(data.averagePrice, 'ZAR')}`}
        />
        <StatCard 
          icon={AlertTriangle} 
          title="Low Stock"
          value={data.lowStockProducts}
          change={`${data.outOfStockProducts} out of stock`}
        />
        <StatCard 
          icon={Eye} 
          title="Total Views" 
          value={data.performanceMetrics.totalViews} 
        />
        <StatCard 
          icon={ShoppingCart} 
          title="Conversion Rate" 
          value={`${data.performanceMetrics.conversionRate.toFixed(2)}%`} 
        />
        <StatCard 
          icon={Star} 
          title="Average Rating" 
          value={`${data.performanceMetrics.averageRating.toFixed(1)} / 5`} 
        />
        <StatCard 
          icon={Users} 
          title="Total Reviews" 
          value={data.performanceMetrics.totalReviews} 
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Selling Products */}
        <Card>
          <CardHeader>
            <CardTitle>Top Selling Products</CardTitle>
            <CardDescription>Best performing products in the selected period</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {data.topSellingProducts.map((product, index) => (
                <div key={product.id} className="flex items-center gap-4">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted text-muted-foreground">
                    {index + 1}
                  </div>
                  {product.image ? (
                    <img
                      src={product.image}
                      alt={product.title}
                      className="h-10 w-10 rounded-lg object-cover"
                    />
                  ) : (
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-muted">
                      <Package className="h-5 w-5 text-muted-foreground" />
                    </div>
                  )}
                  <div className="flex-1">
                    <p className="font-semibold truncate">{product.title}</p>
                    <p className="text-sm text-muted-foreground">{product.sales} sales</p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">{formatCurrency(product.revenue, 'ZAR')}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Category Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Category Distribution</CardTitle>
            <CardDescription>Products by category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.categoryBreakdown.map((category) => (
                <div key={category.name} className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="font-medium">{category.name}</span>
                    <span className="text-muted-foreground">
                      {category.count} ({category.percentage}%)
                    </span>
                  </div>
                  <Progress value={category.percentage} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Latest product updates and changes</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium">
                    {activity.type === 'created' && 'Created'}
                    {activity.type === 'updated' && 'Updated'}
                    {activity.type === 'sold' && 'Sold'}
                    {' '}
                    <span className="text-primary">{activity.productTitle}</span>
                  </p>
                  {activity.details && (
                    <p className="text-sm text-muted-foreground">{activity.details}</p>
                  )}
                </div>
                <div className="flex-shrink-0 text-sm text-muted-foreground">
                  {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
