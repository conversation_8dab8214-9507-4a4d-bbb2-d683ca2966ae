'use client'

import { useState } from 'react'
import { UseFormReturn } from 'react-hook-form'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Loader2, Tag, Plus, X, Package, AlertTriangle } from 'lucide-react'
import { InventoryUpdateTest } from './inventory-update-test'
import { useCategories } from '@/lib/ecommerce/hooks/use-categories'
import { useCollections } from '@/lib/ecommerce/hooks/use-collections'

// Define the subset of form data that ProductInventory needs
interface ProductInventoryFormData {
  trackQuantity: boolean
  inventoryQuantity: number
  continueSellingWhenOutOfStock: boolean
  categoryIds: string[]
  collectionIds: string[]
  tags: string[]
}

interface ProductInventoryProps {
  form: UseFormReturn<ProductInventoryFormData>
}

// Stock Status Component
function StockStatusIndicator({ quantity, allowOutOfStock }: { quantity: number; allowOutOfStock: boolean }) {
  const getStockStatus = () => {
    if (quantity > 10) return { label: 'In Stock', variant: 'default' as const }
    if (quantity > 0) return { label: 'Low Stock', variant: 'secondary' as const }
    return { label: 'Out of Stock', variant: 'destructive' as const }
  }

  const status = getStockStatus()

  return (
    <div className="p-3 bg-muted rounded-md">
      <div className="text-sm">
        <div className="flex items-center justify-between">
          <span>Stock Status:</span>
          <Badge variant={status.variant}>
            {status.label}
          </Badge>
        </div>
        {quantity === 0 && allowOutOfStock && (
          <div className="flex items-center gap-1 mt-2 text-xs text-amber-600">
            <AlertTriangle className="h-3 w-3" />
            <span>Customers can still purchase this item</span>
          </div>
        )}
        {quantity > 0 && quantity <= 10 && (
          <p className="text-xs text-muted-foreground mt-1">
            Consider restocking soon
          </p>
        )}
      </div>
    </div>
  )
}

// Categories Selector Component
function CategoriesSelector({ form }: { form: UseFormReturn<ProductInventoryFormData> }) {
  const { categories, loading: categoriesLoading, error: categoriesError } = useCategories()
  const { watch, setValue } = form

  const selectedCategoryId = watch('categoryIds')?.[0] || null

  if (categoriesLoading) {
    return (
      <div className="flex items-center space-x-2 p-3 border rounded-md">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-muted-foreground">Loading categories...</span>
      </div>
    )
  }

  if (categoriesError) {
    return (
      <div className="p-3 border border-red-200 rounded-md bg-red-50">
        <p className="text-sm text-red-600">Failed to load categories: {categoriesError}</p>
      </div>
    )
  }

  return (
    <Select
      value={selectedCategoryId || 'none'}
      onValueChange={(value) => {
        const categoryIds = value === 'none' ? [] : [value]
        setValue('categoryIds', categoryIds, { shouldValidate: true })
      }}
    >
      <SelectTrigger>
        <SelectValue placeholder="Select a category (optional)" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="none">No category</SelectItem>
        {categories.map((category) => (
          <SelectItem key={category.id} value={category.id}>
            {category.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

// Collections Selector Component
function CollectionsSelector({ form }: { form: UseFormReturn<ProductInventoryFormData> }) {
  const { collections, loading: collectionsLoading, error: collectionsError } = useCollections()
  const { watch, setValue } = form

  const selectedCollectionId = watch('collectionIds')?.[0] || null

  if (collectionsLoading) {
    return (
      <div className="flex items-center space-x-2 p-3 border rounded-md">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-muted-foreground">Loading collections...</span>
      </div>
    )
  }

  if (collectionsError) {
    return (
      <div className="p-3 border border-red-200 rounded-md bg-red-50">
        <p className="text-sm text-red-600">Failed to load collections: {collectionsError}</p>
      </div>
    )
  }

  return (
    <Select
      value={selectedCollectionId || 'none'}
      onValueChange={(value) => {
        const collectionIds = value === 'none' ? [] : [value]
        setValue('collectionIds', collectionIds, { shouldValidate: true })
      }}
    >
      <SelectTrigger>
        <SelectValue placeholder="Select a collection (optional)" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="none">No collection</SelectItem>
        {collections.map((collection) => (
          <SelectItem key={collection.id} value={collection.id}>
            {collection.title}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
}

// Tags Manager Component
function TagsManager({ form }: { form: UseFormReturn<ProductInventoryFormData> }) {
  const [newTag, setNewTag] = useState('')
  const { watch, setValue } = form
  const watchedTags = watch('tags') || []

  const addTag = () => {
    const trimmedTag = newTag.trim()
    if (trimmedTag && !watchedTags.includes(trimmedTag)) {
      setValue('tags', [...watchedTags, trimmedTag], { shouldValidate: true })
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setValue('tags', watchedTags.filter(tag => tag !== tagToRemove), { shouldValidate: true })
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag()
    }
  }

  return (
    <div className="space-y-2">
      <Label>Tags</Label>
      
      {/* Existing Tags */}
      {watchedTags.length > 0 && (
        <div className="flex flex-wrap gap-2 mb-2">
          {watchedTags.map((tag, index) => (
            <Badge key={index} variant="secondary" className="flex items-center gap-1">
              {tag}
              <X
                className="h-3 w-3 cursor-pointer hover:text-red-500"
                onClick={() => removeTag(tag)}
              />
            </Badge>
          ))}
        </div>
      )}
      
      {/* Add New Tag */}
      <div className="flex gap-2">
        <Input
          value={newTag}
          onChange={(e) => setNewTag(e.target.value)}
          placeholder="Add a tag"
          onKeyPress={handleKeyPress}
          maxLength={50}
        />
        <Button 
          type="button" 
          variant="outline" 
          onClick={addTag}
          disabled={!newTag.trim() || watchedTags.includes(newTag.trim())}
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
      
      <p className="text-sm text-muted-foreground">
        Add tags to help customers find your product (max 20 tags)
      </p>
    </div>
  )
}

// Main Product Inventory Component
export function ProductInventory({ form }: ProductInventoryProps) {
  const { register, watch, setValue, formState: { errors } } = form
  
  const watchedTrackQuantity = watch('trackQuantity')
  const watchedInventoryQuantity = watch('inventoryQuantity') || 0
  const watchedContinueSellingWhenOutOfStock = watch('continueSellingWhenOutOfStock')

  return (
    <div className="space-y-4">
      {/* Inventory Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="mr-2 h-5 w-5" />
            Inventory Management
          </CardTitle>
          <CardDescription>
            Manage stock levels and availability
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Track Quantity Toggle */}
          <div className="flex items-center space-x-2">
            <Switch
              id="trackQuantity"
              checked={watchedTrackQuantity}
              onCheckedChange={(checked) => setValue('trackQuantity', checked, { shouldValidate: true })}
            />
            <Label htmlFor="trackQuantity">Track stock quantity</Label>
          </div>

          {/* Inventory Details (shown when tracking is enabled) */}
          {watchedTrackQuantity && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Stock Quantity Input */}
              <div className="space-y-2">
                <Label htmlFor="inventoryQuantity">Stock Quantity *</Label>
                <Input
                  id="inventoryQuantity"
                  type="number"
                  min="0"
                  step="1"
                  {...register('inventoryQuantity', { 
                    valueAsNumber: true,
                    min: { value: 0, message: 'Quantity cannot be negative' }
                  })}
                  placeholder="0"
                />
                {errors.inventoryQuantity && (
                  <p className="text-sm text-red-500">{errors.inventoryQuantity.message}</p>
                )}
                <p className="text-sm text-muted-foreground">
                  Current stock level for this product
                </p>
              </div>

              {/* Stock Settings and Status */}
              <div className="space-y-4">
                {/* Continue Selling Toggle */}
                <div className="flex items-center space-x-2">
                  <Switch
                    id="continueSellingWhenOutOfStock"
                    checked={watchedContinueSellingWhenOutOfStock}
                    onCheckedChange={(checked) => setValue('continueSellingWhenOutOfStock', checked, { shouldValidate: true })}
                  />
                  <Label htmlFor="continueSellingWhenOutOfStock">
                    Continue selling when out of stock
                  </Label>
                </div>
                
                {/* Stock Status Indicator */}
                <StockStatusIndicator 
                  quantity={watchedInventoryQuantity}
                  allowOutOfStock={watchedContinueSellingWhenOutOfStock}
                />
                
                {/* Inventory Update Test Component */}
                <div className="mt-4">
                  <InventoryUpdateTest 
                    productId="current-product" 
                    currentStock={watchedInventoryQuantity} 
                  />
                </div>
              </div>
            </div>
          )}

          {/* No Tracking Message */}
          {!watchedTrackQuantity && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-700">
                Stock quantity tracking is disabled. This product will always be available for purchase.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Categories and Collections */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Tag className="mr-2 h-5 w-5" />
            Categories & Collections
          </CardTitle>
          <CardDescription>
            Organize your product into relevant categories and collections
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Categories */}
          <div className="space-y-2">
            <Label>Categories</Label>
            <CategoriesSelector form={form} />
            <p className="text-sm text-muted-foreground">
              Organize your product into a category for better discovery
            </p>
          </div>

          {/* Collections */}
          <div className="space-y-2">
            <Label>Collections</Label>
            <CollectionsSelector form={form} />
            <p className="text-sm text-muted-foreground">
              Add to a curated collection for featured displays
            </p>
          </div>

          {/* Tags */}
          <TagsManager form={form} />
        </CardContent>
      </Card>
    </div>
  )
}