'use client'

import { useState, useEffect } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ZarPriceInput } from '../zar-price-input'
import { MediaPicker } from '../media/media-picker'
import { ProductVariantManager } from './product-variant-manager'
import { ProductInventory } from './product-inventory'
import { Save, Loader2, Plus, X, Package, Image as ImageIcon, Search, AlertTriangle, CheckCircle, AlertCircle } from 'lucide-react'
import { toast } from 'sonner'
import { useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import type { Product, ProductVariant } from '@/lib/ecommerce/types/product'

// Form validation schema
const productFormSchema = z.object({
  title: z.string().min(1, 'Product title is required').max(255, 'Title is too long'),
  description: z.string().min(1, 'Product description is required').max(5000, 'Description is too long'),
  slug: z.string().optional(),
  vendor: z.string().optional(),
  productType: z.string().optional(),
  price: z.number().min(0.01, 'Price must be greater than 0'),
  compareAtPrice: z.number().min(0).optional().nullable(),
  costPerItem: z.number().min(0).optional().nullable(),
  status: z.enum(['active', 'draft', 'archived']),
  trackQuantity: z.boolean(),
  inventoryQuantity: z.number().min(0, 'Inventory must be non-negative').int('Inventory must be a whole number'),
  continueSellingWhenOutOfStock: z.boolean(),
  weight: z.number().min(0).optional().nullable(),
  weightUnit: z.string().optional(),
  requiresShipping: z.boolean(),
  isTaxable: z.boolean(),
  seoTitle: z.string().max(60, 'SEO title should be under 60 characters').optional(),
  seoDescription: z.string().max(160, 'SEO description should be under 160 characters').optional(),
  seoKeywords: z.array(z.string()).max(10, 'Maximum 10 keywords allowed').optional(),
  categoryIds: z.array(z.string()).default([]),
  collectionIds: z.array(z.string()).default([]),
  tags: z.array(z.string()).max(20, 'Maximum 20 tags allowed').default([]),
  images: z.array(z.object({
    url: z.string().url('Invalid image URL'),
    altText: z.string(),
    position: z.number()
  })).max(10, 'Maximum 10 images allowed').default([])
})

type ProductFormData = z.infer<typeof productFormSchema>

interface ProductFormProps {
  product?: Product
  onSuccess?: (product: Product) => void
  onCancel: () => void
}

export function ProductForm({ product, onSuccess, onCancel }: ProductFormProps) {
  const [activeTab, setActiveTab] = useState('general')
  const [variants, setVariants] = useState<ProductVariant[]>(product?.variants || [])
  const [newKeyword, setNewKeyword] = useState('')
  const [hasOpenDialogs, setHasOpenDialogs] = useState(false)
  const [hasUnsavedVariantChanges, setHasUnsavedVariantChanges] = useState(false)

  const { 
    createProduct, 
    updateProduct, 
    loading: mutationLoading, 
    error: mutationError,
    clearError 
  } = useProductMutations()

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    clearErrors,
    formState: { errors, isSubmitting, isDirty, isValid }
  } = useForm<ProductFormData>({
    resolver: zodResolver(productFormSchema),
    mode: 'onChange',
    defaultValues: {
      title: product?.title || '',
      description: product?.description || '',
      slug: product?.slug || '',
      vendor: product?.vendor || '',
      productType: product?.productType || '',
      price: product?.price?.amount || 1,
      compareAtPrice: product?.compareAtPrice?.amount || null,
      costPerItem: product?.costPerItem?.amount || null,
      status: product?.status || 'draft',
      trackQuantity: product?.trackQuantity ?? true,
      inventoryQuantity: product?.inventoryQuantity || 0,
      continueSellingWhenOutOfStock: product?.continueSellingWhenOutOfStock ?? false,
      weight: product?.weight || null,
      weightUnit: product?.weightUnit || 'kg',
      requiresShipping: product?.requiresShipping ?? true,
      isTaxable: product?.isTaxable ?? true,
      seoTitle: product?.seo?.title || '',
      seoDescription: product?.seo?.description || '',
      seoKeywords: product?.seo?.keywords || [],
      categoryIds: product?.categories?.map(c => c.id) || [],
      collectionIds: product?.collections?.map(c => c.id) || [],
      tags: product?.tags?.map(t => t.name) || [],
      images: product?.images || []
    },
  })

  const { fields: imageFields } = useFieldArray({
    control,
    name: 'images',
  })

  const watchedTitle = watch('title')
  const watchedKeywords = watch('seoKeywords')

  // Auto-generate slug from title
  useEffect(() => {
    if (watchedTitle && !product) {
      const slug = watchedTitle
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')
      setValue('slug', slug)
    }
  }, [watchedTitle, setValue, product])

  // Clear mutation error when form data changes
  useEffect(() => {
    if (mutationError && isDirty) {
      clearError()
    }
  }, [isDirty, mutationError, clearError])

  // Debug: Log the loading states
  useEffect(() => {
    console.log('Form state changed:', {
      mutationLoading,
      isSubmitting,
      isValid,
      hasErrors: Object.keys(errors).length > 0,
      buttonDisabled: mutationLoading || isSubmitting
    })
  }, [mutationLoading, isSubmitting, isValid, errors])

  const handleFormSubmit = async (data: ProductFormData) => {
    console.log('Form submission started with data:', data)

    try {
      clearError()
      clearErrors()

      // Check for form validation errors first
      if (showValidationErrors()) {
        console.error('Form has validation errors:', errors)
        toast.error('Please fix the validation errors before submitting')
        return
      }

      // Validate required fields
      if (!data.title.trim()) {
        console.error('Validation failed: Product title is required')
        toast.error('Product title is required')
        return
      }

      if (!data.description.trim()) {
        console.error('Validation failed: Product description is required')
        toast.error('Product description is required')
        return
      }

      if (data.price <= 0) {
        console.error('Validation failed: Price must be greater than 0')
        toast.error('Price must be greater than 0')
        return
      }

      console.log('Form validation passed, preparing submit data...')

      // Generate slug if not provided
      const slug = data.slug || data.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')

      const submitData = {
        ...data,
        slug,
        price: data.price, // Send as number, API will construct the object
        compareAtPrice: data.compareAtPrice && data.compareAtPrice > 0
          ? data.compareAtPrice
          : undefined,
        costPerItem: data.costPerItem && data.costPerItem > 0
          ? data.costPerItem
          : undefined,
        currency: 'ZAR', // Send currency separately
        seo: {
          title: data.seoTitle || data.title,
          description: data.seoDescription || data.description.substring(0, 160),
          keywords: data.seoKeywords || []
        },
        variants: variants.length > 0 ? variants : undefined,
        // Clean up null/undefined values
        weight: data.weight || undefined,
        categoryIds: data.categoryIds?.filter(Boolean) || [],
        collectionIds: data.collectionIds?.filter(Boolean) || [],
        tags: data.tags?.filter(Boolean) || [],
        images: data.images || []
      }

      console.log('Submit data prepared:', submitData)

      let result: Product | null = null

      if (product?.id) {
        console.log('Updating existing product with ID:', product.id)
        // Update existing product
        result = await updateProduct(product.id, submitData as any)
      } else {
        console.log('Creating new product...')
        // Create new product
        result = await createProduct(submitData as any)
      }

      console.log('API call result:', result)

      if (result) {
        console.log('Product operation successful:', result)
        toast.success(
          product?.id
            ? `Product "${result.title}" updated successfully!`
            : `Product "${result.title}" created successfully!`
        )

        // Check if there are open dialogs or unsaved changes before closing
        if (hasOpenDialogs || hasUnsavedVariantChanges) {
          toast.success(
            product?.id
              ? `Product "${result.title}" updated successfully! You can continue working on variants.`
              : `Product "${result.title}" created successfully! You can now add variants.`
          )

          // Don't call onSuccess to prevent closing the form
          // User can manually close when they're done with variants
        } else {
          // Safe to close the form
          if (onSuccess) {
            onSuccess(result)
          }
        }

        // Reset form if creating a new product and no open dialogs
        if (!product?.id && !hasOpenDialogs && !hasUnsavedVariantChanges) {
          reset()
          setVariants([])
          setActiveTab('general')
        }
      } else {
        console.error('No result returned from API call')
        // Show error if no result but no exception was thrown
        toast.error(
          product?.id
            ? 'Failed to update product. Please check the form and try again.'
            : 'Failed to create product. Please check the form and try again.'
        )
      }
    } catch (error) {
      console.error('Error submitting form:', error)

      // Show more specific error message if available
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'

      toast.error(
        product?.id
          ? `Failed to update product: ${errorMessage}`
          : `Failed to create product: ${errorMessage}`
      )

      // Also show mutation error if available
      if (mutationError) {
        console.error('Mutation error:', mutationError)
        toast.error(`API Error: ${mutationError.message}`)
      }
    }
  }

  const addKeyword = () => {
    if (newKeyword.trim() && !watchedKeywords?.includes(newKeyword.trim())) {
      setValue('seoKeywords', [...(watchedKeywords || []), newKeyword.trim()])
      setNewKeyword('')
    }
  }

  const handleCancel = () => {
    if (hasOpenDialogs || hasUnsavedVariantChanges) {
      const confirmed = window.confirm(
        'You have unsaved variant changes or open dialogs. Are you sure you want to cancel and lose these changes?'
      )
      if (!confirmed) {
        return
      }
    }
    onCancel()
  }

  const removeKeyword = (keywordToRemove: string) => {
    setValue('seoKeywords', watchedKeywords?.filter(keyword => keyword !== keywordToRemove) || [])
  }

  // Function to show validation errors as toasts
  const showValidationErrors = () => {
    if (Object.keys(errors).length > 0) {
      console.log('Showing validation errors:', errors)

      Object.entries(errors).forEach(([field, error]) => {
        if (error?.message) {
          // Convert camelCase field names to readable format
          const fieldName = field
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
            .replace(/([a-z])([A-Z])/g, '$1 $2')

          toast.error(`${fieldName}: ${error.message}`)
        }
      })

      return true // Has errors
    }
    return false // No errors
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {product ? 'Edit Product' : 'Create New Product'}
            {(hasOpenDialogs || hasUnsavedVariantChanges) && (
              <span className="ml-2 text-sm text-orange-600 font-normal">
                • Working on variants
              </span>
            )}
          </h1>
          <p className="text-muted-foreground">
            {product ? 'Update product information and settings' : 'Add a new product to your store'}
            {(hasOpenDialogs || hasUnsavedVariantChanges) && (
              <span className="block text-orange-600 text-sm mt-1">
                Complete variant work before closing this form
              </span>
            )}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button type="button" variant="outline" onClick={handleCancel} disabled={isSubmitting || mutationLoading}>
            Cancel
          </Button>

          {/* Show Close Form button when there are open dialogs after successful save */}
          {product && (hasOpenDialogs || hasUnsavedVariantChanges) && (
            <Button
              type="button"
              variant="secondary"
              onClick={() => {
                if (hasUnsavedVariantChanges) {
                  const confirmed = window.confirm(
                    'You have unsaved variant changes. Are you sure you want to close the form?'
                  )
                  if (!confirmed) return
                }
                if (onSuccess && product) {
                  onSuccess(product)
                }
              }}
              disabled={isSubmitting || mutationLoading}
            >
              Close Form
            </Button>
          )}
          <Button
            type="button"
            onClick={() => {
              console.log('Debug button clicked')
              console.log('Form data:', watch())
              console.log('Form errors:', errors)
              console.log('Is submitting:', isSubmitting)
              console.log('Is valid:', isValid)
              console.log('Mutation loading:', mutationLoading)
              console.log('Mutation error:', mutationError)
              console.log('Form validation state:', {
                hasErrors: Object.keys(errors).length > 0,
                errorKeys: Object.keys(errors),
                isValid,
                isDirty
              })

              // Show form errors as toasts for better UX
              if (!showValidationErrors()) {
                toast.success('Form validation passed!')
              }
            }}
            variant="secondary"
            className="min-w-[100px]"
          >
            {/*Show Alert or check Icon based on validation*/
              isValid ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />
            }
          </Button>
          <Button
            type="submit"disabled={mutationLoading || isSubmitting}
            className="min-w-[140px]"
            onClick={() => {
              console.log('Submit button clicked!')
              console.log('Form is valid:', isValid)
              console.log('Form errors:', errors)
              console.log('Mutation loading:', mutationLoading)
              console.log('Is submitting:', isSubmitting)
              console.log('Button should be disabled:', mutationLoading || isSubmitting)

              // Show validation errors if form is invalid
              if (!isValid || Object.keys(errors).length > 0) {
                showValidationErrors()
              }
            }}
          >
            {(mutationLoading || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            <Save className="mr-2 h-4 w-4" />
            {product ? 'Update Product' : 'Create Product'}
          </Button>
        </div>
      </div>

      {/* Global Error Alert */}
      {mutationError && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error!</AlertTitle>
          <AlertDescription>{mutationError.message}</AlertDescription>
        </Alert>
      )}

      {/* Form Validation Errors Alert */}
      {Object.keys(errors).length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Form Validation Errors</AlertTitle>
          <AlertDescription>
            <div className="space-y-1">
              <p>Please fix the following errors:</p>
              <ul className="list-disc list-inside space-y-1">
                {Object.entries(errors).map(([field, error]) => {
                  if (error?.message) {
                    const fieldName = field
                      .replace(/([A-Z])/g, ' $1')
                      .replace(/^./, str => str.toUpperCase())
                      .replace(/([a-z])([A-Z])/g, '$1 $2')
                    return (
                      <li key={field} className="text-sm">
                        <strong>{fieldName}:</strong> {error.message}
                      </li>
                    )
                  }
                  return null
                })}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}



      {/* Form Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="pricing">Pricing</TabsTrigger>
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
          <TabsTrigger value="variants">Variants</TabsTrigger>
          <TabsTrigger value="media">Media</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="mr-2 h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Essential product details and description
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Product Title *</Label>
                  <Input
                    id="title"
                    {...register('title')}
                    placeholder="Enter product title"
                  />
                  {errors.title && (
                    <p className="text-sm text-red-500">{errors.title.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="slug">URL Slug</Label>
                  <Input
                    id="slug"
                    {...register('slug')}
                    placeholder="product-url-slug"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  {...register('description')}
                  placeholder="Describe your product..."
                  rows={4}
                />
                {errors.description && (
                  <p className="text-sm text-red-500">{errors.description.message}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="vendor">Vendor</Label>
                  <Input
                    id="vendor"
                    {...register('vendor')}
                    placeholder="Brand or vendor name"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="productType">Product Type</Label>
                  <Input
                    id="productType"
                    {...register('productType')}
                    placeholder="e.g., T-Shirt, Dress, Shoes"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select
                    value={watch('status')}
                    onValueChange={(value: 'active' | 'draft' | 'archived') =>
                      setValue('status', value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="weightUnit">Weight Unit</Label>
                  <Select
                    value={watch('weightUnit')}
                    onValueChange={(value) => setValue('weightUnit', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="kg">Kilograms (kg)</SelectItem>
                      <SelectItem value="g">Grams (g)</SelectItem>
                      <SelectItem value="lb">Pounds (lb)</SelectItem>
                      <SelectItem value="oz">Ounces (oz)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="weight">Weight</Label>
                  <Input
                    id="weight"
                    type="number"
                    step="0.01"
                    {...register('weight', { valueAsNumber: true })}
                    placeholder="0.00"
                  />
                </div>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="requiresShipping"
                      checked={watch('requiresShipping')}
                      onCheckedChange={(checked) => setValue('requiresShipping', checked)}
                    />
                    <Label htmlFor="requiresShipping">Requires shipping</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="isTaxable"
                      checked={watch('isTaxable')}
                      onCheckedChange={(checked) => setValue('isTaxable', checked)}
                    />
                    <Label htmlFor="isTaxable">Taxable</Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pricing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Pricing</CardTitle>
              <CardDescription>
                Set your product prices in South African Rand (ZAR)
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ZarPriceInput
                  label="Regular Price *"
                  value={watch('price')}
                  onChange={(value) => setValue('price', value, { shouldValidate: true })}
                  required
                  description="The standard selling price"
                  error={errors.price?.message}
                />

                <ZarPriceInput
                  label="Compare at Price"
                  value={watch('compareAtPrice') || 0}
                  onChange={(value) => setValue('compareAtPrice', value > 0 ? value : null, { shouldValidate: true })}
                  description="Optional original price for comparison (shows as strikethrough)"
                  error={errors.compareAtPrice?.message}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ZarPriceInput
                  label="Cost per Item"
                  value={watch('costPerItem') || 0}
                  onChange={(value) => setValue('costPerItem', value > 0 ? value : null, { shouldValidate: true })}
                  description="Your cost for this item (for profit calculations)"
                  error={errors.costPerItem?.message}
                />

                {/* Profit Margin Display */}
                {watch('price') > 0 && watch('costPerItem') && watch('costPerItem')! > 0 && (
                  <div className="space-y-2">
                    <Label>Profit Margin</Label>
                    <div className="p-3 bg-muted rounded-md">
                      <div className="text-sm space-y-1">
                        <div className="flex justify-between">
                          <span>Selling Price:</span>
                          <span>R {watch('price').toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Cost:</span>
                          <span>R {watch('costPerItem')!.toFixed(2)}</span>
                        </div>
                        <Separator />
                        <div className="flex justify-between font-medium">
                          <span>Profit:</span>
                          <span className={watch('price') > watch('costPerItem')! ? 'text-green-600' : 'text-red-600'}>
                            R {(watch('price') - watch('costPerItem')!).toFixed(2)}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm text-muted-foreground">
                          <span>Margin:</span>
                          <span>
                            {(((watch('price') - watch('costPerItem')!) / watch('price')) * 100).toFixed(1)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="inventory" className="space-y-4">
          <ProductInventory form={{ register, control, watch, setValue, formState: { errors } } as any} />
        </TabsContent>

        <TabsContent value="variants" className="space-y-4">
          {product && (
            <ProductVariantManager
              product={product}
              variants={variants}
              onVariantsChange={setVariants}
              onDialogStateChange={setHasOpenDialogs}
              onUnsavedChangesChange={setHasUnsavedVariantChanges}
            />
          )}
          {!product && (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-muted-foreground">
                  <Package className="mx-auto h-8 w-8 mb-2" />
                  <p>Save the product first to manage variants</p>
                  <p className="text-xs mt-1">Variants can only be managed for existing products</p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="media" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <ImageIcon className="mr-2 h-5 w-5" />
                  Product Images
                </div>
                {imageFields.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {imageFields.length} / 10 images
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                Upload high-quality images of your product. You can select up to 10 images.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <MediaPicker
                value={imageFields.map(field => ({
                  id: field.id || field.url,
                  name: field.altText || 'Product Image',
                  mimeType: 'image/jpeg',
                  url: field.url,
                  type: 'image' as const,
                  size: 0,
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                  downloadUrl: field.url,
                  previewUrl: field.url,
                  metadata: {
                    folder: 'products',
                    alt: field.altText || '',
                    title: field.altText || '',
                    description: '',
                    tags: []
                  }
                }))}
                onChange={(files) => {
                  const images = files.map((file, index) => ({
                    url: file.url,
                    altText: file.metadata?.alt || file.name,
                    position: index
                  }));
                  setValue('images', images, { shouldDirty: true });
                }}
                multiple={true}
                accept="image"
                maxFiles={10}
                description="Select images from your media library or upload new ones. The first image is the main one."
              />

              {/* Image Preview */}
              {imageFields.length > 0 && (
                <div className="mt-6">
                  <h4 className="text-sm font-medium mb-3">Selected Images Preview</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    {imageFields.map((field, index) => (
                      <div key={field.id} className="relative group">
                        <div className="aspect-square rounded-lg overflow-hidden border">
                          <img
                            src={field.url}
                            alt={field.altText}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="absolute top-2 left-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-black/50 text-white">
                            {index === 0 ? 'Main' : index + 1}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seo" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Search className="mr-2 h-5 w-5" />
                SEO Settings
              </CardTitle>
              <CardDescription>
                Optimize your product for search engines
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="seoTitle">SEO Title</Label>
                <Input
                  id="seoTitle"
                  {...register('seoTitle')}
                  placeholder="SEO optimized title"
                />
                <p className="text-sm text-muted-foreground">
                  Recommended: 50-60 characters
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="seoDescription">SEO Description</Label>
                <Textarea
                  id="seoDescription"
                  {...register('seoDescription')}
                  placeholder="SEO meta description"
                  rows={3}
                />
                <p className="text-sm text-muted-foreground">
                  Recommended: 150-160 characters
                </p>
              </div>

              <div className="space-y-2">
                <Label>SEO Keywords</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {watchedKeywords?.map((keyword, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {keyword}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeKeyword(keyword)}
                      />
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    value={newKeyword}
                    onChange={(e) => setNewKeyword(e.target.value)}
                    placeholder="Add a keyword"
                    onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}
                  />
                  <Button type="button" variant="outline" onClick={addKeyword}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </form>
  )
}
