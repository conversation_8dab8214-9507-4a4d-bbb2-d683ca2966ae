'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Bell, ShoppingCart, Package, AlertTriangle, CreditCard, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useSession } from 'next-auth/react';
import { NotificationChannel, NotificationPriority, NotificationType } from '@/lib/notifications/types';

/**
 * NotificationDemo Component
 * 
 * A demonstration component for testing admin notifications
 */
export function NotificationDemo() {
  const { toast } = useToast();
  const { data: session } = useSession();
  const [sending, setSending] = useState(false);
  
  const [orderId, setOrderId] = useState('ORD-1234');
  const [customerName, setCustomerName] = useState('<PERSON>');
  const [amount, setAmount] = useState('R 299.99');
  const [productName, setProductName] = useState('Organic Cotton Onesie');
  const [quantity, setQuantity] = useState(3);

  // Send notification via API
  const sendNotification = async (notificationData: NotificationData) => {
    if (!session?.user?.id) {
      toast({
        title: 'Error',
        description: 'You must be logged in to send notifications.',
        variant: 'destructive'
      });
      return false;
    }
    
    setSending(true);
    
    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...notificationData,
          recipientId: session.user.id
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to send notification');
      }
      
      return true;
    } catch (error) {
      console.error('Error sending notification:', error);
      toast({
        title: 'Error',
        description: 'Failed to send notification.',
        variant: 'destructive'
      });
      return false;
    } finally {
      setSending(false);
    }
  };

  const handleNewOrder = async () => {
    const success = await sendNotification({
      type: NotificationType.ORDER_CONFIRMATION,
      channel: NotificationChannel.IN_APP,
      priority: NotificationPriority.HIGH,
      title: `New Order: ${orderId}`,
      content: `${customerName} placed a new order for ${amount}`,
      metadata: {
        orderId,
        amount,
        actionUrl: `/admin/e-commerce/orders/${orderId}`
      }
    });
    
    if (success) {
      toast({
        title: 'Notification sent',
        description: 'New order notification has been sent.',
      });
    }
  };

  const handleOrderStatusChange = async () => {
    const success = await sendNotification({
      type: NotificationType.ORDER_STATUS_UPDATE,
      channel: NotificationChannel.IN_APP,
      priority: NotificationPriority.NORMAL,
      title: `Order Status Updated: ${orderId}`,
      content: `Order ${orderId} has been shipped`,
      metadata: {
        orderId,
        status: 'Shipped',
        actionUrl: `/admin/e-commerce/orders/${orderId}`
      }
    });
    
    if (success) {
      toast({
        title: 'Notification sent',
        description: 'Order status notification has been sent.',
      });
    }
  };

  const handleLowStock = async () => {
    const success = await sendNotification({
      type: NotificationType.INVENTORY_ALERT,
      channel: NotificationChannel.IN_APP,
      priority: NotificationPriority.URGENT,
      title: `Low Stock Alert: ${productName}`,
      content: `${productName} is running low on stock (${quantity} remaining)`,
      metadata: {
        productId: 'PROD-123',
        productName,
        quantity,
        actionUrl: `/admin/e-commerce/products/PROD-123`
      }
    });
    
    if (success) {
      toast({
        title: 'Notification sent',
        description: 'Low stock notification has been sent.',
      });
    }
  };

  const handlePaymentSuccess = async () => {
    const success = await sendNotification({
      type: NotificationType.PAYMENT_CONFIRMATION,
      channel: NotificationChannel.IN_APP,
      priority: NotificationPriority.NORMAL,
      title: `Payment Received: ${orderId}`,
      content: `Payment of ${amount} received for order ${orderId}`,
      metadata: {
        orderId,
        amount,
        status: 'success',
        actionUrl: `/admin/e-commerce/orders/${orderId}`
      }
    });
    
    if (success) {
      toast({
        title: 'Notification sent',
        description: 'Payment success notification has been sent.',
      });
    }
  };

  const handlePaymentFailed = async () => {
    const success = await sendNotification({
      type: NotificationType.PAYMENT_FAILED,
      channel: NotificationChannel.IN_APP,
      priority: NotificationPriority.URGENT,
      title: `Payment Failed: ${orderId}`,
      content: `Payment of ${amount} failed for order ${orderId}`,
      metadata: {
        orderId,
        amount,
        status: 'failed',
        actionUrl: `/admin/e-commerce/orders/${orderId}`
      }
    });
    
    if (success) {
      toast({
        title: 'Notification sent',
        description: 'Payment failed notification has been sent.',
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Notification Demo
        </CardTitle>
        <CardDescription>
          Test the notification system with different notification types
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="orderId">Order ID</Label>
            <Input 
              id="orderId" 
              value={orderId} 
              onChange={(e) => setOrderId(e.target.value)} 
              placeholder="ORD-1234" 
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="customerName">Customer Name</Label>
            <Input 
              id="customerName" 
              value={customerName} 
              onChange={(e) => setCustomerName(e.target.value)} 
              placeholder="John Doe" 
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="amount">Amount</Label>
            <Input 
              id="amount" 
              value={amount} 
              onChange={(e) => setAmount(e.target.value)} 
              placeholder="R 299.99" 
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="productName">Product Name</Label>
            <Input 
              id="productName" 
              value={productName} 
              onChange={(e) => setProductName(e.target.value)} 
              placeholder="Organic Cotton Onesie" 
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity</Label>
            <Input 
              id="quantity" 
              type="number" 
              value={quantity.toString()} 
              onChange={(e) => setQuantity(parseInt(e.target.value) || 0)} 
              placeholder="3" 
            />
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="flex flex-wrap gap-2">
        <Button 
          onClick={handleNewOrder} 
          disabled={sending} 
          className="flex items-center gap-2"
        >
          {sending ? <Loader2 className="h-4 w-4 animate-spin" /> : <ShoppingCart className="h-4 w-4" />}
          New Order
        </Button>
        
        <Button 
          onClick={handleOrderStatusChange} 
          disabled={sending} 
          variant="outline" 
          className="flex items-center gap-2"
        >
          {sending ? <Loader2 className="h-4 w-4 animate-spin" /> : <Package className="h-4 w-4" />}
          Order Status
        </Button>
        
        <Button 
          onClick={handleLowStock} 
          disabled={sending} 
          variant="outline" 
          className="flex items-center gap-2"
        >
          {sending ? <Loader2 className="h-4 w-4 animate-spin" /> : <AlertTriangle className="h-4 w-4" />}
          Low Stock
        </Button>
        
        <Button 
          onClick={handlePaymentSuccess} 
          disabled={sending} 
          variant="outline" 
          className="flex items-center gap-2"
        >
          {sending ? <Loader2 className="h-4 w-4 animate-spin" /> : <CreditCard className="h-4 w-4" />}
          Payment Success
        </Button>
        
        <Button 
          onClick={handlePaymentFailed} 
          disabled={sending} 
          variant="destructive" 
          className="flex items-center gap-2"
        >
          {sending ? <Loader2 className="h-4 w-4 animate-spin" /> : <CreditCard className="h-4 w-4" />}
          Payment Failed
        </Button>
      </CardFooter>
    </Card>
  );
}