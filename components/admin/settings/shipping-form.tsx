'use client';

import * as z from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { Switch } from '@/components/ui/switch';
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useShippingSettings } from '@/hooks/use-shipping-settings';

const formSchema = z.object({
  enableFreeShipping: z.boolean(),
  freeShippingThreshold: z.coerce.number().min(0, 'Must be a positive number'),
  flatRateShipping: z.boolean(),
  flatRateAmount: z.coerce.number().min(0, 'Must be a positive number'),
  enableLocalPickup: z.boolean(),
});

type ShippingSettingsFormValues = z.infer<typeof formSchema>;

interface ShippingSettingsFormProps {
  initialData: any;
}

export const ShippingSettingsForm: React.FC<ShippingSettingsFormProps> = ({ initialData }) => {
  const { toast } = useToast();
  const { createOrUpdateSettings } = useShippingSettings();
  const [loading, setLoading] = useState(false);

  const form = useForm<ShippingSettingsFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      enableFreeShipping: false,
      freeShippingThreshold: 0,
      flatRateShipping: false,
      flatRateAmount: 0,
      enableLocalPickup: false,
    },
  });

  const onSubmit = async (data: ShippingSettingsFormValues) => {
    try {
      setLoading(true);
      await createOrUpdateSettings(data);
      toast({ title: 'Success', description: 'Shipping settings saved successfully.' });
    } catch (error) {
      console.error('Error saving shipping settings:', error);
      toast({ title: 'Error', description: 'Failed to save settings.', variant: 'destructive' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <Heading title="Shipping Settings" description="Manage shipping options and rates." />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="enableFreeShipping"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel>Enable Free Shipping</FormLabel>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="freeShippingThreshold"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Free Shipping Threshold</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      disabled={loading || !form.watch('enableFreeShipping')} 
                      placeholder="Minimum order amount for free shipping" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="flatRateShipping"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel>Enable Flat Rate Shipping</FormLabel>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="flatRateAmount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Flat Rate Amount</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      disabled={loading || !form.watch('flatRateShipping')} 
                      placeholder="Flat rate shipping amount" 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={form.control}
            name="enableLocalPickup"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel>Enable Local Pickup</FormLabel>
                </div>
                <FormControl>
                  <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                </FormControl>
              </FormItem>
            )}
          />
          <Button disabled={loading} type="submit">Save Changes</Button>
        </form>
      </Form>
    </div>
  );
};