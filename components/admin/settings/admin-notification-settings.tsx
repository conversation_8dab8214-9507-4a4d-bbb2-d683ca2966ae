'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { But<PERSON> } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Heading } from '@/components/ui/heading';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useToast } from '@/hooks/use-toast';
import { Bell, Mail, MessageSquare, AlertCircle } from 'lucide-react';
import { useBrowserNotificationPermission } from '@/lib/notifications/hooks';

// Define the form schema
const formSchema = z.object({
  emailNotifications: z.object({
    newOrders: z.boolean(),
    orderStatusChanges: z.boolean(),
    lowStock: z.boolean(),
    customerSignups: z.boolean(),
  }),
  adminNotifications: z.object({
    enableBrowserNotifications: z.boolean(),
    enableEmailDigest: z.boolean(),
    digestFrequency: z.enum(['daily', 'weekly', 'monthly']),
  }),
  notificationChannels: z.object({
    email: z.boolean(),
    inApp: z.boolean(),
    push: z.boolean(),
  }),
  notificationTypes: z.object({
    orderConfirmation: z.boolean(),
    paymentConfirmation: z.boolean(),
    shippingUpdate: z.boolean(),
    inventoryAlert: z.boolean(),
    customerActivity: z.boolean(),
  }),
});

type AdminNotificationSettingsFormValues = z.infer<typeof formSchema>;

interface AdminNotificationSettingsProps {
  initialData: any;
}

export const AdminNotificationSettings: React.FC<AdminNotificationSettingsProps> = ({ initialData }) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  
  const {
    permission: browserPermission,
    requesting,
    requestPermission,
    isSupported
  } = useBrowserNotificationPermission();

  // Initialize form with default values or provided data
  const form = useForm<AdminNotificationSettingsFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      emailNotifications: {
        newOrders: true,
        orderStatusChanges: true,
        lowStock: true,
        customerSignups: false,
      },
      adminNotifications: {
        enableBrowserNotifications: true,
        enableEmailDigest: false,
        digestFrequency: 'daily',
      },
      notificationChannels: {
        email: true,
        inApp: true,
        push: false,
      },
      notificationTypes: {
        orderConfirmation: true,
        paymentConfirmation: true,
        shippingUpdate: true,
        inventoryAlert: true,
        customerActivity: false,
      },
    },
  });

  // Request browser notification permission
  const handleRequestPermission = async () => {
    if (!isSupported) {
      toast({
        title: 'Browser notifications not supported',
        description: 'Your browser does not support notifications.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const permission = await requestPermission();
      
      if (permission === 'granted') {
        toast({
          title: 'Notifications enabled',
          description: 'You will now receive browser notifications.',
        });
        
        // Send a test notification
        new Notification('Notifications Enabled', {
          body: 'You will now receive notifications from Coco Milk Store.',
          icon: '/logo.png',
        });
      } else if (permission === 'denied') {
        toast({
          title: 'Notifications blocked',
          description: 'Please enable notifications in your browser settings.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      toast({
        title: 'Error',
        description: 'Failed to request notification permission.',
        variant: 'destructive',
      });
    }
  };

  // Handle form submission
  const onSubmit = async (data: AdminNotificationSettingsFormValues) => {
    try {
      setLoading(true);
      
      // Save settings to API
      const response = await fetch('/api/settings/notifications', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error('Failed to save notification settings');
      }
      
      // Send a test notification to demonstrate the functionality
      if (data.adminNotifications.enableBrowserNotifications && browserPermission === 'granted') {
        // Use browser notification API directly
        new Notification('Settings Updated', {
          body: 'Your notification settings have been updated successfully.',
          icon: '/logo.png',
        });
      }
      
      toast({
        title: 'Success',
        description: 'Notification settings saved successfully.',
      });
    } catch (error) {
      console.error('Error saving notification settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Heading 
        title="Notification Settings" 
        description="Configure how and when you receive notifications about store activity."
      />
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Tabs defaultValue="channels" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="channels">Notification Channels</TabsTrigger>
              <TabsTrigger value="types">Notification Types</TabsTrigger>
              <TabsTrigger value="delivery">Delivery Settings</TabsTrigger>
            </TabsList>
            
            {/* Notification Channels Tab */}
            <TabsContent value="channels" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    Notification Channels
                  </CardTitle>
                  <CardDescription>
                    Configure which channels to use for notifications
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Email Channel */}
                  <FormField
                    control={form.control}
                    name="notificationChannels.email"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center">
                            <Mail className="h-4 w-4 mr-2" />
                            <FormLabel>Email Notifications</FormLabel>
                          </div>
                          <FormDescription>
                            Receive notifications via email
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {/* In-App Channel */}
                  <FormField
                    control={form.control}
                    name="notificationChannels.inApp"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center">
                            <MessageSquare className="h-4 w-4 mr-2" />
                            <FormLabel>In-App Notifications</FormLabel>
                          </div>
                          <FormDescription>
                            Receive notifications within the admin dashboard
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {/* Browser Push Channel */}
                  <FormField
                    control={form.control}
                    name="notificationChannels.push"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center">
                            <Bell className="h-4 w-4 mr-2" />
                            <FormLabel>Browser Push Notifications</FormLabel>
                          </div>
                          <FormDescription>
                            Receive notifications in your browser even when the site is closed
                          </FormDescription>
                          {browserPermission === 'denied' && (
                            <p className="text-sm text-destructive mt-1">
                              Notifications are blocked. Please enable them in your browser settings.
                            </p>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <FormControl>
                            <Switch 
                              checked={field.value} 
                              onCheckedChange={(checked) => {
                                field.onChange(checked);
                                if (checked && browserPermission !== 'granted') {
                                  handleRequestPermission();
                                }
                              }} 
                              disabled={loading || browserPermission === 'denied'} 
                            />
                          </FormControl>
                          {browserPermission !== 'granted' && browserPermission !== 'denied' && (
                            <Button 
                              type="button" 
                              variant="outline" 
                              size="sm"
                              onClick={handleRequestPermission}
                              disabled={loading || !isSupported}
                            >
                              Enable
                            </Button>
                          )}
                        </div>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Notification Types Tab */}
            <TabsContent value="types" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Notification Types</CardTitle>
                  <CardDescription>
                    Choose which types of notifications you want to receive
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Order Confirmation */}
                  <FormField
                    control={form.control}
                    name="notificationTypes.orderConfirmation"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Order Confirmations</FormLabel>
                          <FormDescription>
                            Notifications when new orders are placed
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {/* Payment Confirmation */}
                  <FormField
                    control={form.control}
                    name="notificationTypes.paymentConfirmation"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Payment Confirmations</FormLabel>
                          <FormDescription>
                            Notifications when payments are processed
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {/* Shipping Update */}
                  <FormField
                    control={form.control}
                    name="notificationTypes.shippingUpdate"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Shipping Updates</FormLabel>
                          <FormDescription>
                            Notifications about order shipments and deliveries
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {/* Inventory Alert */}
                  <FormField
                    control={form.control}
                    name="notificationTypes.inventoryAlert"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Inventory Alerts</FormLabel>
                          <FormDescription>
                            Notifications when product stock is low or out
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {/* Customer Activity */}
                  <FormField
                    control={form.control}
                    name="notificationTypes.customerActivity"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Customer Activity</FormLabel>
                          <FormDescription>
                            Notifications about customer registrations and account activities
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Delivery Settings Tab */}
            <TabsContent value="delivery" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Delivery Settings</CardTitle>
                  <CardDescription>
                    Configure how and when notifications are delivered
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Email Digest */}
                  <FormField
                    control={form.control}
                    name="adminNotifications.enableEmailDigest"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Email Digest</FormLabel>
                          <FormDescription>
                            Receive a summary of notifications via email
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {/* Digest Frequency */}
                  <FormField
                    control={form.control}
                    name="adminNotifications.digestFrequency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Digest Frequency</FormLabel>
                        <Select 
                          disabled={loading || !form.watch('adminNotifications.enableEmailDigest')} 
                          onValueChange={field.onChange} 
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select frequency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="daily">Daily</SelectItem>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          How often to send email digests
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {/* Test Notification */}
                  <div className="pt-4 border-t mt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={async () => {
                        try {
                          // Send test notification via API
                          const response = await fetch('/api/notifications/admin', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                              title: 'Test Notification',
                              message: 'This is a test notification to verify your settings.',
                              type: 'info',
                              channels: ['IN_APP'],
                            }),
                          });
                          
                          const result = await response.json();
                          
                          if (!result.success) {
                            throw new Error(result.error || 'Failed to send test notification');
                          }
                          
                          toast({
                            title: 'Test notification sent',
                            description: 'A test notification has been sent to verify your settings.',
                          });
                        } catch (error) {
                          console.error('Failed to send test notification:', error);
                          toast({
                            title: 'Error',
                            description: 'Failed to send test notification. Please try again.',
                            variant: 'destructive',
                          });
                        }
                      }}
                      disabled={loading}
                    >
                      Send Test Notification
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
          
          <Button disabled={loading} type="submit">
            {loading ? 'Saving...' : 'Save Settings'}
          </Button>
        </form>
      </Form>
    </div>
  );
};