'use client';

import * as z from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { useGeneralSettings } from '@/hooks/use-general-settings';
import { useToast } from '@/hooks/use-toast';
import { useState } from 'react';

const formSchema = z.object({
  siteName: z.string().min(1, 'Site name is required'),
  siteDescription: z.string().min(1, 'Site description is required'),
});

type GeneralSettingsFormValues = z.infer<typeof formSchema>;

interface GeneralSettingsFormProps {
  initialData: any;
}

export const GeneralSettingsForm: React.FC<GeneralSettingsFormProps> = ({ initialData }) => {
  const { createOrUpdateSettings } = useGeneralSettings();
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  const form = useForm<GeneralSettingsFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      siteName: '',
      siteDescription: '',
    },
  });

  const onSubmit = async (data: GeneralSettingsFormValues) => {
    try {
      setLoading(true);
      await createOrUpdateSettings(data);
      toast({ title: 'Success', description: 'General settings saved successfully.' });
    } catch (error) {
      console.error('Error saving settings:', error);
      toast({ title: 'Error', description: 'Failed to save settings.', variant: 'destructive' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <Heading title="General Settings" description="Manage general store settings." />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            <FormField
              control={form.control}
              name="siteName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Site Name</FormLabel>
                  <FormControl>
                    <Input disabled={loading} placeholder="Your site name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="siteDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Site Description</FormLabel>
                  <FormControl>
                    <Input disabled={loading} placeholder="Your site description" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <Button disabled={loading} type="submit">Save Changes</Button>
        </form>
      </Form>
    </div>
  );
};
