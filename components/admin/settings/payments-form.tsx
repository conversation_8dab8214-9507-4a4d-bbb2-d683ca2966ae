'use client';

import * as z from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { Switch } from '@/components/ui/switch';
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { usePaymentsSettings } from '@/hooks/use-payments-settings';
import { PaymentSettings } from '@/lib/settings/settings-service';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, CheckCircle, CreditCard, DollarSign, RefreshCw } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';

// Define the form schema for each payment gateway
const payfastSchema = z.object({
  enabled: z.boolean(),
  merchantId: z.string().min(1, 'Merchant ID is required'),
  merchantKey: z.string().min(1, 'Merchant Key is required'),
  passphrase: z.string().optional(),
  testMode: z.boolean(),
  status: z.enum(['connected', 'disconnected', 'testing']),
});

const ozowSchema = z.object({
  enabled: z.boolean(),
  siteCode: z.string().min(1, 'Site Code is required when enabled').or(z.string()),
  privateKey: z.string().min(1, 'Private Key is required when enabled').or(z.string()),
  apiKey: z.string().optional(),
  testMode: z.boolean(),
  bankReference: z.string().optional(),
  status: z.enum(['connected', 'disconnected', 'testing']),
});

const stripeSchema = z.object({
  enabled: z.boolean(),
  publishableKey: z.string().min(1, 'Publishable Key is required when enabled').or(z.string()),
  secretKey: z.string().min(1, 'Secret Key is required when enabled').or(z.string()),
  webhookSecret: z.string().optional(),
  currency: z.string().default('ZAR'),
  testMode: z.boolean(),
  status: z.enum(['connected', 'disconnected', 'testing']),
});

const paypalSchema = z.object({
  enabled: z.boolean(),
  clientId: z.string().min(1, 'Client ID is required when enabled').or(z.string()),
  clientSecret: z.string().min(1, 'Client Secret is required when enabled').or(z.string()),
  mode: z.enum(['sandbox', 'live']),
  testMode: z.boolean(),
  status: z.enum(['connected', 'disconnected', 'testing']),
});

const codSchema = z.object({
  enabled: z.boolean(),
  title: z.string().min(1, 'Title is required'),
  description: z.string().optional(),
  instructions: z.string().optional(),
  enableForShippingMethods: z.array(z.string()),
});

// Combined schema for all payment settings
const formSchema = z.object({
  payfast: payfastSchema,
  ozow: ozowSchema,
  stripe: stripeSchema,
  paypal: paypalSchema,
  cod: codSchema,
});

type PaymentsSettingsFormValues = z.infer<typeof formSchema>;

interface PaymentsSettingsFormProps {
  initialData: PaymentSettings | null;
}

export const PaymentsSettingsForm: React.FC<PaymentsSettingsFormProps> = ({ initialData }) => {
  const { toast } = useToast();
  const { createOrUpdateSettings, updatePaymentGateway, testGatewayConnection } = usePaymentsSettings();
  const [loading, setLoading] = useState(false);
  const [testingGateway, setTestingGateway] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<Record<string, { success: boolean; message: string; details?: any }>>({});

  const form = useForm<PaymentsSettingsFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      payfast: {
        enabled: false,
        merchantId: '',
        merchantKey: '',
        passphrase: '',
        testMode: true,
        status: 'disconnected',
      },
      ozow: {
        enabled: false,
        siteCode: '',
        privateKey: '',
        apiKey: '',
        testMode: true,
        bankReference: '',
        status: 'disconnected',
      },
      stripe: {
        enabled: false,
        publishableKey: '',
        secretKey: '',
        webhookSecret: '',
        currency: 'ZAR',
        testMode: true,
        status: 'disconnected',
      },
      paypal: {
        enabled: false,
        clientId: '',
        clientSecret: '',
        mode: 'sandbox',
        testMode: true,
        status: 'disconnected',
      },
      cod: {
        enabled: true,
        title: 'Cash on Delivery',
        description: 'Pay when you receive your order',
        instructions: 'Please have the exact amount ready when the courier arrives.',
        enableForShippingMethods: ['standard', 'express'],
      },
    },
  });

  const onSubmit = async (data: PaymentsSettingsFormValues) => {
    try {
      setLoading(true);
      await createOrUpdateSettings(data);
      toast({ 
        title: 'Success', 
        description: 'Payment settings saved successfully.' 
      });
    } catch (error) {
      console.error('Error saving payment settings:', error);
      toast({ 
        title: 'Error', 
        description: 'Failed to save settings.', 
        variant: 'destructive' 
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async (gateway: keyof PaymentSettings) => {
    try {
      setTestingGateway(gateway as string);
      
      // Make sure we're using the current form values for testing
      const currentFormValues = form.getValues();
      
      // Update settings before testing to ensure we're using the latest values
      await updatePaymentGateway(gateway, currentFormValues[gateway]);
      
      // Now test the connection with the updated settings
      const result = await testGatewayConnection(gateway);
      
      setTestResults({
        ...testResults,
        [gateway]: result
      });
      
      toast({
        title: result.success ? 'Connection Successful' : 'Connection Failed',
        description: result.message,
        variant: result.success ? 'default' : 'destructive'
      });
    } catch (error: any) {
      console.error(`Error testing ${gateway} connection:`, error);
      
      // Set detailed error information
      setTestResults({
        ...testResults,
        [gateway]: { 
          success: false, 
          message: error.message || 'Connection test failed',
          details: { error: error.toString() }
        }
      });
      
      toast({
        title: 'Connection Test Failed',
        description: error.message || 'An error occurred while testing the connection',
        variant: 'destructive'
      });
    } finally {
      setTestingGateway(null);
    }
  };

  return (
    <div className="space-y-6">
      <Heading title="Payment Settings" description="Configure payment gateways for your store." />
      
      <Tabs defaultValue="payfast" className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="payfast">PayFast</TabsTrigger>
          <TabsTrigger value="ozow">Ozow</TabsTrigger>
          <TabsTrigger value="stripe">Stripe</TabsTrigger>
          <TabsTrigger value="paypal">PayPal</TabsTrigger>
          <TabsTrigger value="cod">Cash on Delivery</TabsTrigger>
        </TabsList>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            {/* PayFast Tab */}
            <TabsContent value="payfast" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>PayFast Settings</CardTitle>
                      <CardDescription>Configure PayFast payment gateway</CardDescription>
                    </div>
                    <Badge variant={form.watch('payfast.enabled') ? 'default' : 'secondary'}>
                      {form.watch('payfast.enabled') ? 'Enabled' : 'Disabled'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="payfast.enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Enable PayFast</FormLabel>
                          <FormDescription>
                            Accept payments via PayFast
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="payfast.merchantId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Merchant ID</FormLabel>
                          <FormControl>
                            <Input 
                              disabled={loading || !form.watch('payfast.enabled')} 
                              placeholder="PayFast Merchant ID" 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Your PayFast merchant ID
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="payfast.merchantKey"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Merchant Key</FormLabel>
                          <FormControl>
                            <Input 
                              disabled={loading || !form.watch('payfast.enabled')} 
                              placeholder="PayFast Merchant Key" 
                              type="password"
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Your PayFast merchant key
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="payfast.passphrase"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Passphrase (Optional)</FormLabel>
                        <FormControl>
                          <Input 
                            disabled={loading || !form.watch('payfast.enabled')} 
                            placeholder="PayFast Passphrase" 
                            type="password"
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          Set in your PayFast account for additional security
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="payfast.testMode"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Test Mode</FormLabel>
                          <FormDescription>
                            Use PayFast sandbox for testing
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch 
                            checked={field.value} 
                            onCheckedChange={field.onChange} 
                            disabled={loading || !form.watch('payfast.enabled')} 
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {testResults.payfast && (
                    <Alert variant={testResults.payfast.success ? "default" : "destructive"}>
                      {testResults.payfast.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                      <AlertTitle>{testResults.payfast.success ? "Connection Successful" : "Connection Failed"}</AlertTitle>
                      <AlertDescription>
                        {testResults.payfast.message}
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => handleTestConnection('payfast')}
                    disabled={loading || testingGateway !== null || !form.watch('payfast.enabled')}
                  >
                    {testingGateway === 'payfast' ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Testing...
                      </>
                    ) : (
                      <>
                        <CreditCard className="mr-2 h-4 w-4" />
                        Test Connection
                      </>
                    )}
                  </Button>
                  <Button type="submit" disabled={loading}>Save Changes</Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            {/* Ozow Tab */}
            <TabsContent value="ozow" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Ozow Settings</CardTitle>
                      <CardDescription>Configure Ozow payment gateway</CardDescription>
                    </div>
                    <Badge variant={form.watch('ozow.enabled') ? 'default' : 'secondary'}>
                      {form.watch('ozow.enabled') ? 'Enabled' : 'Disabled'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="ozow.enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Enable Ozow</FormLabel>
                          <FormDescription>
                            Accept payments via Ozow instant EFT
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="ozow.siteCode"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Site Code</FormLabel>
                          <FormControl>
                            <Input 
                              disabled={loading || !form.watch('ozow.enabled')} 
                              placeholder="Ozow Site Code" 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Your Ozow site code
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="ozow.privateKey"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Private Key</FormLabel>
                          <FormControl>
                            <Input 
                              disabled={loading || !form.watch('ozow.enabled')} 
                              placeholder="Ozow Private Key" 
                              type="password"
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Your Ozow private key
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="ozow.apiKey"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>API Key (Optional)</FormLabel>
                        <FormControl>
                          <Input 
                            disabled={loading || !form.watch('ozow.enabled')} 
                            placeholder="Ozow API Key" 
                            type="password"
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          Required for API integrations
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="ozow.bankReference"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bank Reference</FormLabel>
                        <FormControl>
                          <Input 
                            disabled={loading || !form.watch('ozow.enabled')} 
                            placeholder="Bank Reference" 
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          Default reference shown on bank statements
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="ozow.testMode"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Test Mode</FormLabel>
                          <FormDescription>
                            Use Ozow staging environment for testing
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch 
                            checked={field.value} 
                            onCheckedChange={field.onChange} 
                            disabled={loading || !form.watch('ozow.enabled')} 
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {testResults.ozow && (
                    <Alert variant={testResults.ozow.success ? "default" : "destructive"}>
                      {testResults.ozow.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                      <AlertTitle>{testResults.ozow.success ? "Connection Successful" : "Connection Failed"}</AlertTitle>
                      <AlertDescription>
                        {testResults.ozow.message}
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => handleTestConnection('ozow')}
                    disabled={loading || testingGateway !== null || !form.watch('ozow.enabled')}
                  >
                    {testingGateway === 'ozow' ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Testing...
                      </>
                    ) : (
                      <>
                        <CreditCard className="mr-2 h-4 w-4" />
                        Test Connection
                      </>
                    )}
                  </Button>
                  <Button type="submit" disabled={loading}>Save Changes</Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            {/* Stripe Tab */}
            <TabsContent value="stripe" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Stripe Settings</CardTitle>
                      <CardDescription>Configure Stripe payment gateway</CardDescription>
                    </div>
                    <Badge variant={form.watch('stripe.enabled') ? 'default' : 'secondary'}>
                      {form.watch('stripe.enabled') ? 'Enabled' : 'Disabled'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="stripe.enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Enable Stripe</FormLabel>
                          <FormDescription>
                            Accept credit card payments via Stripe
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="stripe.publishableKey"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Publishable Key</FormLabel>
                          <FormControl>
                            <Input 
                              disabled={loading || !form.watch('stripe.enabled')} 
                              placeholder="Stripe Publishable Key" 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Your Stripe publishable key
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="stripe.secretKey"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Secret Key</FormLabel>
                          <FormControl>
                            <Input 
                              disabled={loading || !form.watch('stripe.enabled')} 
                              placeholder="Stripe Secret Key" 
                              type="password"
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Your Stripe secret key
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="stripe.webhookSecret"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Webhook Secret (Optional)</FormLabel>
                        <FormControl>
                          <Input 
                            disabled={loading || !form.watch('stripe.enabled')} 
                            placeholder="Stripe Webhook Secret" 
                            type="password"
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          Required for handling Stripe webhooks
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="stripe.currency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Currency</FormLabel>
                        <Select 
                          disabled={loading || !form.watch('stripe.enabled')} 
                          onValueChange={field.onChange} 
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select currency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="ZAR">South African Rand (ZAR)</SelectItem>
                            <SelectItem value="USD">US Dollar (USD)</SelectItem>
                            <SelectItem value="EUR">Euro (EUR)</SelectItem>
                            <SelectItem value="GBP">British Pound (GBP)</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Default currency for Stripe payments
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="stripe.testMode"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Test Mode</FormLabel>
                          <FormDescription>
                            Use Stripe test environment
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch 
                            checked={field.value} 
                            onCheckedChange={field.onChange} 
                            disabled={loading || !form.watch('stripe.enabled')} 
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {testResults.stripe && (
                    <Alert variant={testResults.stripe.success ? "default" : "destructive"}>
                      {testResults.stripe.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                      <AlertTitle>{testResults.stripe.success ? "Connection Successful" : "Connection Failed"}</AlertTitle>
                      <AlertDescription>
                        {testResults.stripe.message}
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => handleTestConnection('stripe')}
                    disabled={loading || testingGateway !== null || !form.watch('stripe.enabled')}
                  >
                    {testingGateway === 'stripe' ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Testing...
                      </>
                    ) : (
                      <>
                        <CreditCard className="mr-2 h-4 w-4" />
                        Test Connection
                      </>
                    )}
                  </Button>
                  <Button type="submit" disabled={loading}>Save Changes</Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            {/* PayPal Tab */}
            <TabsContent value="paypal" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>PayPal Settings</CardTitle>
                      <CardDescription>Configure PayPal payment gateway</CardDescription>
                    </div>
                    <Badge variant={form.watch('paypal.enabled') ? 'default' : 'secondary'}>
                      {form.watch('paypal.enabled') ? 'Enabled' : 'Disabled'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="paypal.enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Enable PayPal</FormLabel>
                          <FormDescription>
                            Accept payments via PayPal
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="paypal.clientId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Client ID</FormLabel>
                          <FormControl>
                            <Input 
                              disabled={loading || !form.watch('paypal.enabled')} 
                              placeholder="PayPal Client ID" 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Your PayPal client ID
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="paypal.clientSecret"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Client Secret</FormLabel>
                          <FormControl>
                            <Input 
                              disabled={loading || !form.watch('paypal.enabled')} 
                              placeholder="PayPal Client Secret" 
                              type="password"
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Your PayPal client secret
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="paypal.mode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Mode</FormLabel>
                        <Select 
                          disabled={loading || !form.watch('paypal.enabled')} 
                          onValueChange={field.onChange} 
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select mode" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="sandbox">Sandbox</SelectItem>
                            <SelectItem value="live">Live</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          PayPal environment mode
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="paypal.testMode"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Test Mode</FormLabel>
                          <FormDescription>
                            Use PayPal sandbox for testing
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch 
                            checked={field.value} 
                            onCheckedChange={field.onChange} 
                            disabled={loading || !form.watch('paypal.enabled')} 
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  {testResults.paypal && (
                    <Alert variant={testResults.paypal.success ? "default" : "destructive"}>
                      {testResults.paypal.success ? <CheckCircle className="h-4 w-4" /> : <AlertCircle className="h-4 w-4" />}
                      <AlertTitle>{testResults.paypal.success ? "Connection Successful" : "Connection Failed"}</AlertTitle>
                      <AlertDescription>
                        {testResults.paypal.message}
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => handleTestConnection('paypal')}
                    disabled={loading || testingGateway !== null || !form.watch('paypal.enabled')}
                  >
                    {testingGateway === 'paypal' ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Testing...
                      </>
                    ) : (
                      <>
                        <CreditCard className="mr-2 h-4 w-4" />
                        Test Connection
                      </>
                    )}
                  </Button>
                  <Button type="submit" disabled={loading}>Save Changes</Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            {/* Cash on Delivery Tab */}
            <TabsContent value="cod" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle>Cash on Delivery Settings</CardTitle>
                      <CardDescription>Configure cash on delivery payment option</CardDescription>
                    </div>
                    <Badge variant={form.watch('cod.enabled') ? 'default' : 'secondary'}>
                      {form.watch('cod.enabled') ? 'Enabled' : 'Disabled'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="cod.enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Enable Cash on Delivery</FormLabel>
                          <FormDescription>
                            Allow customers to pay when they receive their order
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="cod.title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input 
                            disabled={loading || !form.watch('cod.enabled')} 
                            placeholder="Payment Method Title" 
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          Title shown to customers during checkout
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="cod.description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Input 
                            disabled={loading || !form.watch('cod.enabled')} 
                            placeholder="Payment Method Description" 
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          Description shown to customers during checkout
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="cod.instructions"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Instructions</FormLabel>
                        <FormControl>
                          <Input 
                            disabled={loading || !form.watch('cod.enabled')} 
                            placeholder="Payment Instructions" 
                            {...field} 
                          />
                        </FormControl>
                        <FormDescription>
                          Instructions shown to customers after checkout
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button type="submit" disabled={loading}>Save Changes</Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </form>
        </Form>
      </Tabs>
    </div>
  );
};
