'use client';

import * as z from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { Switch } from '@/components/ui/switch';
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useSecuritySettings } from '@/hooks/use-security-settings';

const formSchema = z.object({
  enableTwoFactorAuth: z.boolean(),
  passwordPolicy: z.object({
    minLength: z.coerce.number().min(8, 'Minimum length must be at least 8 characters'),
    requireSpecialChar: z.boolean(),
    requireNumber: z.boolean(),
    requireUppercase: z.boolean(),
  }),
  sessionTimeout: z.coerce.number().min(5, 'Session timeout must be at least 5 minutes'),
});

type SecuritySettingsFormValues = z.infer<typeof formSchema>;

interface SecuritySettingsFormProps {
  initialData: any;
}

export const SecuritySettingsForm: React.FC<SecuritySettingsFormProps> = ({ initialData }) => {
  const { toast } = useToast();
  const { createOrUpdateSettings } = useSecuritySettings();
  const [loading, setLoading] = useState(false);

  const form = useForm<SecuritySettingsFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      enableTwoFactorAuth: false,
      passwordPolicy: {
        minLength: 8,
        requireSpecialChar: true,
        requireNumber: true,
        requireUppercase: true,
      },
      sessionTimeout: 30,
    },
  });

  const onSubmit = async (data: SecuritySettingsFormValues) => {
    try {
      setLoading(true);
      await createOrUpdateSettings(data);
      toast({ title: 'Success', description: 'Security settings saved successfully.' });
    } catch (error) {
      console.error('Error saving security settings:', error);
      toast({ title: 'Error', description: 'Failed to save settings.', variant: 'destructive' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <Heading title="Security Settings" description="Manage security and authentication settings." />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="enableTwoFactorAuth"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel>Enable Two-Factor Authentication</FormLabel>
                </div>
                <FormControl>
                  <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                </FormControl>
              </FormItem>
            )}
          />
          
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Password Policy</h3>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="passwordPolicy.minLength"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Minimum Password Length</FormLabel>
                    <FormControl>
                      <Input type="number" disabled={loading} {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="passwordPolicy.requireSpecialChar"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel>Require Special Character</FormLabel>
                    </div>
                    <FormControl>
                      <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="passwordPolicy.requireNumber"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel>Require Number</FormLabel>
                    </div>
                    <FormControl>
                      <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                    </FormControl>
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="passwordPolicy.requireUppercase"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel>Require Uppercase Letter</FormLabel>
                    </div>
                    <FormControl>
                      <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>
          
          <FormField
            control={form.control}
            name="sessionTimeout"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Session Timeout (minutes)</FormLabel>
                <FormControl>
                  <Input type="number" disabled={loading} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <Button disabled={loading} type="submit">Save Changes</Button>
        </form>
      </Form>
    </div>
  );
};