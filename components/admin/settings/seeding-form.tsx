'use client';

import * as z from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { Switch } from '@/components/ui/switch';
import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { SeederRunOptions, SeedingSettings, useSeedingSettings } from '@/hooks/use-seeding-settings';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { AlertCircle, Database, RefreshCw, Trash2, CheckCircle, AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

const formSchema = z.object({
  enableDemoData: z.boolean(),
  seedCategories: z.boolean(),
  seedProducts: z.coerce.number().min(0, 'Must be a positive number'),
  seedCustomers: z.coerce.number().min(0, 'Must be a positive number'),
  seedOrders: z.coerce.number().min(0, 'Must be a positive number'),
  seedReviews: z.coerce.number().min(0, 'Must be a positive number'),
  preserveExistingData: z.boolean(),
  seedingEnvironment: z.enum(['development', 'staging', 'production']),
});

type SeedingSettingsFormValues = z.infer<typeof formSchema>;

interface SeedingSettingsFormProps {
  initialData: SeedingSettings | null;
}

export const SeedingSettingsForm: React.FC<SeedingSettingsFormProps> = ({ initialData }) => {
  const { toast } = useToast();
  const { createOrUpdateSettings, runSeeder, clearData } = useSeedingSettings();
  const [loading, setLoading] = useState(false);
  const [seeding, setSeeding] = useState<string | null>(null);
  const [clearing, setClearing] = useState<string | null>(null);
  const [seedingProgress, setSeedingProgress] = useState(0);
  const [seedingOptions, setSeedingOptions] = useState<{
    withImages: boolean;
    withVariations: boolean;
    randomPrices: boolean;
  }>({
    withImages: true,
    withVariations: true,
    randomPrices: true,
  });

  const form = useForm<SeedingSettingsFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      enableDemoData: false,
      seedCategories: true,
      seedProducts: 20,
      seedCustomers: 10,
      seedOrders: 15,
      seedReviews: 30,
      preserveExistingData: true,
      seedingEnvironment: 'development',
    },
  });

  // Simulate progress when seeding
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (seeding) {
      setSeedingProgress(0);
      interval = setInterval(() => {
        setSeedingProgress(prev => {
          const increment = Math.random() * 15;
          const newValue = Math.min(prev + increment, 95);
          return newValue;
        });
      }, 500);
    } else if (seedingProgress > 0) {
      setSeedingProgress(100);
      const timeout = setTimeout(() => {
        setSeedingProgress(0);
      }, 1000);
      return () => clearTimeout(timeout);
    }
    
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [seeding, seedingProgress]);

  const onSubmit = async (data: SeedingSettingsFormValues) => {
    try {
      setLoading(true);
      await createOrUpdateSettings(data);
      toast({ 
        title: 'Success', 
        description: 'Seeding settings saved successfully.' 
      });
    } catch (error) {
      console.error('Error saving seeding settings:', error);
      toast({ 
        title: 'Error', 
        description: 'Failed to save settings.', 
        variant: 'destructive' 
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRunSeeder = async (type: SeederRunOptions['type']) => {
    try {
      setSeeding(type);
      
      // Get the count based on the type
      let count: number | undefined;
      if (type !== 'all' && type !== 'categories') {
        const fieldName = `seed${type.charAt(0).toUpperCase() + type.slice(1)}` as keyof SeedingSettingsFormValues;
        count = form.getValues(fieldName) as number;
      }
      
      // Run the seeder with options
      await runSeeder({ 
        type, 
        count,
        options: {
          withImages: seedingOptions.withImages,
          withVariations: seedingOptions.withVariations,
          randomPrices: seedingOptions.randomPrices,
        }
      });
      
      toast({ 
        title: 'Success', 
        description: `Successfully seeded ${type === 'all' ? 'all data' : type}.`,
      });
    } catch (error) {
      console.error(`Error seeding ${type}:`, error);
      toast({ 
        title: 'Error', 
        description: `Failed to seed ${type}.`, 
        variant: 'destructive' 
      });
    } finally {
      setSeeding(null);
    }
  };

  const handleClearData = async (type: SeederRunOptions['type']) => {
    if (!confirm(`Are you sure you want to clear all ${type === 'all' ? 'data' : type}? This action cannot be undone.`)) {
      return;
    }
    
    try {
      setClearing(type);
      await clearData(type);
      toast({ 
        title: 'Success', 
        description: `Successfully cleared ${type === 'all' ? 'all data' : type}.` 
      });
    } catch (error) {
      console.error(`Error clearing ${type}:`, error);
      toast({ 
        title: 'Error', 
        description: `Failed to clear ${type}.`, 
        variant: 'destructive' 
      });
    } finally {
      setClearing(null);
    }
  };

  const isEnvironmentWarningVisible = form.watch('seedingEnvironment') === 'production' && form.watch('enableDemoData');

  return (
    <div className="space-y-6">
      <Heading title="Data Seeding Settings" description="Configure and run data seeders for your store." />
      
      <Tabs defaultValue="settings" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="run">Run Seeders</TabsTrigger>
        </TabsList>
        
        <TabsContent value="settings" className="space-y-4 mt-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Important</AlertTitle>
            <AlertDescription>
              Seeding data may affect your existing database. Always backup your data before running seeders in a production environment.
            </AlertDescription>
          </Alert>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle>Seeding Configuration</CardTitle>
                  <CardDescription>Configure how demo data should be generated</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="enableDemoData"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel>Enable Demo Data</FormLabel>
                            <FormDescription>
                              Allow the system to generate demo data for testing
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="seedingEnvironment"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Environment</FormLabel>
                          <Select 
                            disabled={loading} 
                            onValueChange={field.onChange} 
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select environment" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="development">Development</SelectItem>
                              <SelectItem value="staging">Staging</SelectItem>
                              <SelectItem value="production">Production</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Select the environment where seeders will run
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  {isEnvironmentWarningVisible && (
                    <Alert variant="destructive">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertTitle>Warning: Production Environment</AlertTitle>
                      <AlertDescription>
                        You are enabling demo data in a production environment. This is not recommended and could affect real data.
                      </AlertDescription>
                    </Alert>
                  )}
                  
                  <Separator className="my-4" />
                  
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="seedCategories"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel>Seed Categories</FormLabel>
                            <FormDescription>
                              Generate product categories
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch 
                              checked={field.value} 
                              onCheckedChange={field.onChange} 
                              disabled={loading || !form.watch('enableDemoData')} 
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="preserveExistingData"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel>Preserve Existing Data</FormLabel>
                            <FormDescription>
                              Keep existing data when seeding
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch 
                              checked={field.value} 
                              onCheckedChange={field.onChange} 
                              disabled={loading || !form.watch('enableDemoData')} 
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="seedProducts"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Number of Products</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              disabled={loading || !form.watch('enableDemoData')} 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            How many products to generate
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="seedCustomers"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Number of Customers</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              disabled={loading || !form.watch('enableDemoData')} 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            How many customer accounts to generate
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="seedOrders"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Number of Orders</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              disabled={loading || !form.watch('enableDemoData')} 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            How many orders to generate
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="seedReviews"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Number of Reviews</FormLabel>
                          <FormControl>
                            <Input 
                              type="number" 
                              disabled={loading || !form.watch('enableDemoData')} 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            How many product reviews to generate
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
                <CardFooter>
                  <Button disabled={loading} type="submit">Save Settings</Button>
                </CardFooter>
              </Card>
            </form>
          </Form>
        </TabsContent>
        
        <TabsContent value="run" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Seeding Options</CardTitle>
              <CardDescription>Configure additional options for data generation</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="withImages" 
                    checked={seedingOptions.withImages}
                    onCheckedChange={(checked) => 
                      setSeedingOptions(prev => ({ ...prev, withImages: checked === true }))
                    }
                  />
                  <label
                    htmlFor="withImages"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Generate Images
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="withVariations" 
                    checked={seedingOptions.withVariations}
                    onCheckedChange={(checked) => 
                      setSeedingOptions(prev => ({ ...prev, withVariations: checked === true }))
                    }
                  />
                  <label
                    htmlFor="withVariations"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Generate Variations
                  </label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    id="randomPrices" 
                    checked={seedingOptions.randomPrices}
                    onCheckedChange={(checked) => 
                      setSeedingOptions(prev => ({ ...prev, randomPrices: checked === true }))
                    }
                  />
                  <label
                    htmlFor="randomPrices"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Random Prices
                  </label>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {seedingProgress > 0 && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Seeding Progress</span>
                <span>{Math.round(seedingProgress)}%</span>
              </div>
              <Progress value={seedingProgress} className="h-2" />
            </div>
          )}
          
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Generate Data</CardTitle>
                <CardDescription>Create demo data for your store</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <Card className="border shadow-none">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Categories</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Generate product categories and subcategories.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        disabled={seeding !== null || !form.watch('enableDemoData')}
                        onClick={() => handleRunSeeder('categories')}
                      >
                        {seeding === 'categories' ? (
                          <>
                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                            Seeding...
                          </>
                        ) : (
                          <>
                            <Database className="mr-2 h-4 w-4" />
                            Seed Categories
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </Card>
                  
                  <Card className="border shadow-none">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Products</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Generate {form.watch('seedProducts')} product(s) with images and variations.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        disabled={seeding !== null || !form.watch('enableDemoData')}
                        onClick={() => handleRunSeeder('products')}
                      >
                        {seeding === 'products' ? (
                          <>
                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                            Seeding...
                          </>
                        ) : (
                          <>
                            <Database className="mr-2 h-4 w-4" />
                            Seed Products
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </Card>
                  
                  <Card className="border shadow-none">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Customers</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Generate {form.watch('seedCustomers')} customer account(s).
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        disabled={seeding !== null || !form.watch('enableDemoData')}
                        onClick={() => handleRunSeeder('customers')}
                      >
                        {seeding === 'customers' ? (
                          <>
                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                            Seeding...
                          </>
                        ) : (
                          <>
                            <Database className="mr-2 h-4 w-4" />
                            Seed Customers
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </Card>
                  
                  <Card className="border shadow-none">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Orders</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Generate {form.watch('seedOrders')} order(s) with various statuses.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        disabled={seeding !== null || !form.watch('enableDemoData')}
                        onClick={() => handleRunSeeder('orders')}
                      >
                        {seeding === 'orders' ? (
                          <>
                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                            Seeding...
                          </>
                        ) : (
                          <>
                            <Database className="mr-2 h-4 w-4" />
                            Seed Orders
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </Card>
                  
                  <Card className="border shadow-none">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Reviews</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Generate {form.watch('seedReviews')} product review(s).
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        disabled={seeding !== null || !form.watch('enableDemoData')}
                        onClick={() => handleRunSeeder('reviews')}
                      >
                        {seeding === 'reviews' ? (
                          <>
                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                            Seeding...
                          </>
                        ) : (
                          <>
                            <Database className="mr-2 h-4 w-4" />
                            Seed Reviews
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  className="w-full"
                  disabled={seeding !== null || !form.watch('enableDemoData')}
                  onClick={() => handleRunSeeder('all')}
                >
                  {seeding === 'all' ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Seeding All Data...
                    </>
                  ) : (
                    <>
                      <Database className="mr-2 h-4 w-4" />
                      Seed All Data
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Clear Data</CardTitle>
                <CardDescription>Remove existing demo data</CardDescription>
              </CardHeader>
              <CardContent>
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Warning</AlertTitle>
                  <AlertDescription>
                    Clearing data will permanently remove records from your database. This action cannot be undone.
                  </AlertDescription>
                </Alert>
                
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <Card className="border shadow-none">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Categories</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Remove all product categories.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        disabled={clearing !== null}
                        onClick={() => handleClearData('categories')}
                      >
                        {clearing === 'categories' ? (
                          <>
                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                            Clearing...
                          </>
                        ) : (
                          <>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Clear Categories
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </Card>
                  
                  <Card className="border shadow-none">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Products</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Remove all products and their variations.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        disabled={clearing !== null}
                        onClick={() => handleClearData('products')}
                      >
                        {clearing === 'products' ? (
                          <>
                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                            Clearing...
                          </>
                        ) : (
                          <>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Clear Products
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </Card>
                  
                  <Card className="border shadow-none">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Customers</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Remove all customer accounts.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        disabled={clearing !== null}
                        onClick={() => handleClearData('customers')}
                      >
                        {clearing === 'customers' ? (
                          <>
                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                            Clearing...
                          </>
                        ) : (
                          <>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Clear Customers
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </Card>
                  
                  <Card className="border shadow-none">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-lg">Orders</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Remove all orders and order items.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        disabled={clearing !== null}
                        onClick={() => handleClearData('orders')}
                      >
                        {clearing === 'orders' ? (
                          <>
                            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                            Clearing...
                          </>
                        ) : (
                          <>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Clear Orders
                          </>
                        )}
                      </Button>
                    </CardFooter>
                  </Card>
                </div>
              </CardContent>
              <CardFooter>
                <Button 
                  variant="destructive"
                  className="w-full"
                  disabled={clearing !== null}
                  onClick={() => handleClearData('all')}
                >
                  {clearing === 'all' ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Clearing All Data...
                    </>
                  ) : (
                    <>
                      <Trash2 className="mr-2 h-4 w-4" />
                      Clear All Data
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};