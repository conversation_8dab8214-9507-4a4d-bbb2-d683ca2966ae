'use client';

import * as z from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { Switch } from '@/components/ui/switch';
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useNotificationsSettings } from '@/hooks/use-notifications-settings';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const formSchema = z.object({
  emailNotifications: z.object({
    newOrders: z.boolean(),
    orderStatusChanges: z.boolean(),
    lowStock: z.boolean(),
    customerSignups: z.boolean(),
  }),
  adminNotifications: z.object({
    enableBrowserNotifications: z.boolean(),
    enableEmailDigest: z.boolean(),
    digestFrequency: z.enum(['daily', 'weekly', 'monthly']),
  }),
});

type NotificationsSettingsFormValues = z.infer<typeof formSchema>;

interface NotificationsSettingsFormProps {
  initialData: any;
}

export const NotificationsSettingsForm: React.FC<NotificationsSettingsFormProps> = ({ initialData }) => {
  const { toast } = useToast();
  const { createOrUpdateSettings } = useNotificationsSettings();
  const [loading, setLoading] = useState(false);

  const form = useForm<NotificationsSettingsFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      emailNotifications: {
        newOrders: true,
        orderStatusChanges: true,
        lowStock: true,
        customerSignups: false,
      },
      adminNotifications: {
        enableBrowserNotifications: true,
        enableEmailDigest: false,
        digestFrequency: 'daily',
      },
    },
  });

  const onSubmit = async (data: NotificationsSettingsFormValues) => {
    try {
      setLoading(true);
      await createOrUpdateSettings(data);
      toast({ title: 'Success', description: 'Notification settings saved successfully.' });
    } catch (error) {
      console.error('Error saving notification settings:', error);
      toast({ title: 'Error', description: 'Failed to save settings.', variant: 'destructive' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Heading title="Notification Settings" description="Configure how and when notifications are sent." />
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Tabs defaultValue="email" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="email">Email Notifications</TabsTrigger>
              <TabsTrigger value="admin">Admin Notifications</TabsTrigger>
            </TabsList>
            
            <TabsContent value="email" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Email Notifications</CardTitle>
                  <CardDescription>Configure which events trigger email notifications</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="emailNotifications.newOrders"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>New Orders</FormLabel>
                          <FormDescription>
                            Send email notifications when new orders are placed
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="emailNotifications.orderStatusChanges"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Order Status Changes</FormLabel>
                          <FormDescription>
                            Send email notifications when order statuses change
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="emailNotifications.lowStock"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Low Stock Alerts</FormLabel>
                          <FormDescription>
                            Send email notifications when product stock is low
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="emailNotifications.customerSignups"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Customer Signups</FormLabel>
                          <FormDescription>
                            Send email notifications when new customers register
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="admin" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Admin Notifications</CardTitle>
                  <CardDescription>Configure notifications for admin users</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="adminNotifications.enableBrowserNotifications"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Browser Notifications</FormLabel>
                          <FormDescription>
                            Enable browser push notifications for admin users
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="adminNotifications.enableEmailDigest"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel>Email Digest</FormLabel>
                          <FormDescription>
                            Send periodic email digests with store activity
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="adminNotifications.digestFrequency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Digest Frequency</FormLabel>
                        <Select 
                          disabled={loading || !form.watch('adminNotifications.enableEmailDigest')} 
                          onValueChange={field.onChange} 
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select frequency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="daily">Daily</SelectItem>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          How often to send email digests
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
          
          <Button disabled={loading} type="submit">Save Settings</Button>
        </form>
      </Form>
    </div>
  );
};