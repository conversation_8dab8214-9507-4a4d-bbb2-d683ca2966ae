'use client';

import * as z from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { Switch } from '@/components/ui/switch';
import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useContentSettings } from '@/hooks/use-content-settings';

const formSchema = z.object({
  enableComments: z.boolean(),
  postsPerPage: z.coerce.number().min(1, 'Must be at least 1'),
});

type ContentSettingsFormValues = z.infer<typeof formSchema>;

interface ContentSettingsFormProps {
  initialData: any;
}

export const ContentSettingsForm: React.FC<ContentSettingsFormProps> = ({ initialData }) => {
  const { toast } = useToast();
  const { createOrUpdateSettings } = useContentSettings();
  const [loading, setLoading] = useState(false);

  const form = useForm<ContentSettingsFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      enableComments: false,
      postsPerPage: 10,
    },
  });

  const onSubmit = async (data: ContentSettingsFormValues) => {
    try {
      setLoading(true);
      await createOrUpdateSettings(data);
      toast({ title: 'Success', description: 'Content settings saved successfully.' });
    } catch (error) {
      console.error('Error saving content settings:', error);
      toast({ title: 'Error', description: 'Failed to save settings.', variant: 'destructive' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <Heading title="Content Settings" description="Manage content settings." />
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            <FormField
              control={form.control}
              name="enableComments"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel>Enable Comments</FormLabel>
                    <FormMessage />
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} disabled={loading} />
                  </FormControl>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="postsPerPage"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Posts Per Page</FormLabel>
                  <FormControl>
                    <Input type="number" disabled={loading} placeholder="Number of posts per page" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <Button disabled={loading} type="submit">Save Changes</Button>
        </form>
      </Form>
    </div>
  );
};
