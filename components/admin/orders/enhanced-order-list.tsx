'use client'

import { useState, useMemo } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { DataTable, DataTableColumn, DataTableAction } from '@/components/admin/data-table'
import { useOrders } from '@/lib/ecommerce/hooks/use-orders'
import { formatCurrency } from '@/lib/utils'
import { Plus, ShoppingCart, AlertTriangle, Eye, Edit } from 'lucide-react'
import type { Order } from '@/lib/ecommerce/types/order'

interface EnhancedOrderListProps {
  onCreateOrder: () => void
  onEditOrder: (order: Order) => void
  onViewOrder: (order: Order) => void
}

export function EnhancedOrderList({ 
  onCreateOrder, 
  onEditOrder, 
  onViewOrder 
}: EnhancedOrderListProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  const { orders, loading, error, pagination, refetch } = useOrders({
    initialParams: {
      page: currentPage,
      limit: pageSize,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    }
  })

  // Status badge helper
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { variant: 'secondary' as const, label: 'Pending' },
      confirmed: { variant: 'default' as const, label: 'Confirmed' },
      processing: { variant: 'default' as const, label: 'Processing' },
      shipped: { variant: 'default' as const, label: 'Shipped' },
      delivered: { variant: 'default' as const, label: 'Delivered' },
      cancelled: { variant: 'destructive' as const, label: 'Cancelled' },
      refunded: { variant: 'outline' as const, label: 'Refunded' }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || { variant: 'outline' as const, label: status }
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  // Define table columns
  const columns: DataTableColumn<Order>[] = useMemo(() => [
    {
      key: 'orderNumber',
      title: 'Order Number',
      render: (order) => (
        <div>
          <div className="font-medium">#{order.orderNumber}</div>
          <div className="text-sm text-muted-foreground">
            {order.items?.length || 0} items
          </div>
        </div>
      ),
      sortable: true,
      searchable: true
    },
    {
      key: 'customerEmail',
      title: 'Customer',
      render: (order) => (
        <div>
          <div className="font-medium">{order.customer?.email}</div>
          {order.customer?.phone && (
            <div className="text-sm text-muted-foreground">
              {order.customer.phone}
            </div>
          )}
        </div>
      ),
      sortable: true,
      searchable: true
    },
    {
      key: 'total',
      title: 'Total',
      render: (order) => (
        <div className="font-medium">
          {formatCurrency(order.total.amount, order.currency)}
        </div>
      ),
      sortable: true
    },
    {
      key: 'status',
      title: 'Status',
      render: (order) => getStatusBadge(order.status),
      sortable: true
    },
    {
      key: 'createdAt',
      title: 'Date',
      render: (order) => (
        <div className="text-sm">
          {new Date(order.createdAt).toLocaleDateString()}
        </div>
      ),
      sortable: true
    }
  ], [])

  // Define table actions
  const actions: DataTableAction<Order>[] = useMemo(() => [
    {
      label: 'View',
      icon: Eye,
      onClick: onViewOrder
    },
    {
      label: 'Edit',
      icon: Edit,
      onClick: onEditOrder
    }
  ], [onViewOrder, onEditOrder])

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Orders</h2>
          <p className="text-muted-foreground">
            Manage customer orders and fulfillment
          </p>
        </div>
        <Button onClick={onCreateOrder}>
          <Plus className="mr-2 h-4 w-4" />
          Create Order
        </Button>
      </div>

      {/* Error Display */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={refetch}
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ShoppingCart className="mr-2 h-5 w-5" />
            Orders
          </CardTitle>
          <CardDescription>
            {pagination ? `${pagination.total} total orders` : 'Loading orders...'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            data={orders}
            columns={columns}
            actions={actions}
            loading={loading}
            searchPlaceholder="Search orders..."
            emptyMessage="No orders found."
            emptyIcon={ShoppingCart}
            pageSize={pageSize}
          />
        </CardContent>
      </Card>
    </div>
  )
}
