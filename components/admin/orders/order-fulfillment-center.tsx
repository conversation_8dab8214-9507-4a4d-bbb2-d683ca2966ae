'use client'

import { useState, useEffect } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import { Progress } from '@/components/ui/progress'
import {
  Package,
  Truck,
  CheckCircle,
  AlertTriangle,
  Clock,
  Scan,
  Printer,
  MapPin,
  User,
  Loader2,
  Save,
  X,
  Plus,
  Minus,
  BarChart3,
  ShoppingCart
} from 'lucide-react'
import { toast } from 'sonner'
import type { Order, OrderItem } from '@/lib/ecommerce/types'

// Fulfillment item schema
const fulfillmentItemSchema = z.object({
  orderItemId: z.string().min(1, 'Order item ID is required'),
  productId: z.string().min(1, 'Product ID is required'),
  variantId: z.string().optional(),
  sku: z.string().optional(),
  quantityOrdered: z.number().min(1, 'Quantity ordered must be at least 1'),
  quantityToFulfill: z.number().min(0, 'Quantity to fulfill cannot be negative'),
  quantityFulfilled: z.number().min(0, 'Quantity fulfilled cannot be negative').default(0),
  locationId: z.string().min(1, 'Location is required'),
  binLocation: z.string().max(50, 'Bin location is too long').optional(),
  serialNumbers: z.array(z.string()).default([]),
  notes: z.string().max(500, 'Notes are too long').optional(),
})

// Fulfillment validation schema
const fulfillmentSchema = z.object({
  orderId: z.string().min(1, 'Order ID is required'),
  fulfillmentType: z.enum(['full', 'partial']).default('full'),
  items: z.array(fulfillmentItemSchema).min(1, 'At least one item is required'),
  shippingMethod: z.string().min(1, 'Shipping method is required'),
  trackingNumber: z.string().max(100, 'Tracking number is too long').optional(),
  trackingUrl: z.string().url('Invalid tracking URL').optional().or(z.literal('')),
  carrierService: z.string().max(100, 'Carrier service is too long').optional(),
  estimatedDelivery: z.string().optional(), // Date string
  shippingCost: z.number().min(0, 'Shipping cost cannot be negative').optional(),
  packingSlipNotes: z.string().max(1000, 'Packing slip notes are too long').optional(),
  internalNotes: z.string().max(1000, 'Internal notes are too long').optional(),
  notifyCustomer: z.boolean().default(true),
  requireSignature: z.boolean().default(false),
  isGift: z.boolean().default(false),
  giftMessage: z.string().max(500, 'Gift message is too long').optional(),
})

type FulfillmentFormData = z.infer<typeof fulfillmentSchema>

interface OrderFulfillmentCenterProps {
  order: Order
  onFulfillmentComplete?: (fulfillment: any) => void
  onClose?: () => void
}

// Mock inventory locations
const inventoryLocations = [
  { id: 'warehouse-1', name: 'Main Warehouse', address: 'Cape Town, WC' },
  { id: 'warehouse-2', name: 'Johannesburg Hub', address: 'Johannesburg, GP' },
  { id: 'store-1', name: 'V&A Waterfront Store', address: 'Cape Town, WC' },
  { id: 'store-2', name: 'Sandton City Store', address: 'Johannesburg, GP' },
]

// Mock shipping methods
const shippingMethods = [
  { id: 'standard', name: 'Standard Shipping', estimatedDays: '5-7', cost: 99 },
  { id: 'express', name: 'Express Shipping', estimatedDays: '2-3', cost: 199 },
  { id: 'overnight', name: 'Overnight Shipping', estimatedDays: '1', cost: 399 },
  { id: 'pickup', name: 'Store Pickup', estimatedDays: '0', cost: 0 },
]

export function OrderFulfillmentCenter({ order, onFulfillmentComplete, onClose }: OrderFulfillmentCenterProps) {
  const [activeTab, setActiveTab] = useState('items')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [scanMode, setScanMode] = useState(false)
  const [scannedCode, setScannedCode] = useState('')

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    getValues,
    formState: { errors, isSubmitting }
  } = useForm<FulfillmentFormData>({
    resolver: zodResolver(fulfillmentSchema),
    mode: 'onChange',
    defaultValues: {
      orderId: order.id,
      fulfillmentType: 'full',
      items: order.items?.map(item => ({
        orderItemId: item.id || '',
        productId: item.productId,
        variantId: item.variantId,
        sku: item.sku || '',
        quantityOrdered: item.quantity,
        quantityToFulfill: item.quantity,
        quantityFulfilled: 0,
        locationId: 'warehouse-1',
        binLocation: '',
        serialNumbers: [],
        notes: '',
      })) || [],
      shippingMethod: 'standard',
      notifyCustomer: true,
      requireSignature: false,
      isGift: false,
    },
  })

  const { fields: itemFields, update: updateItem } = useFieldArray({
    control,
    name: 'items',
  })

  const watchedItems = watch('items')
  const watchedFulfillmentType = watch('fulfillmentType')
  const watchedShippingMethod = watch('shippingMethod')

  // Calculate fulfillment progress
  const fulfillmentProgress = () => {
    const totalOrdered = watchedItems.reduce((sum, item) => sum + item.quantityOrdered, 0)
    const totalToFulfill = watchedItems.reduce((sum, item) => sum + item.quantityToFulfill, 0)
    return totalOrdered > 0 ? (totalToFulfill / totalOrdered) * 100 : 0
  }

  // Function to show validation errors as toasts
  const showValidationErrors = () => {
    if (Object.keys(errors).length > 0) {
      Object.entries(errors).forEach(([field, error]) => {
        if (error?.message) {
          const fieldName = field
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
          toast.error(`${fieldName}: ${error.message}`)
        }
      })
      return true
    }
    return false
  }

  // Handle barcode scanning
  const handleScan = (code: string) => {
    const items = getValues('items')
    const itemIndex = items.findIndex(item =>
      item.sku === code || item.productId === code
    )

    if (itemIndex >= 0) {
      const item = items[itemIndex]
      if (item.quantityToFulfill < item.quantityOrdered) {
        updateItem(itemIndex, {
          ...item,
          quantityToFulfill: Math.min(item.quantityToFulfill + 1, item.quantityOrdered)
        })
        toast.success(`Scanned: ${item.sku || item.productId}`)
      } else {
        toast.warning('Item already fully scanned')
      }
    } else {
      toast.error('Item not found in this order')
    }
    setScannedCode('')
  }

  // Auto-calculate shipping cost based on method
  useEffect(() => {
    const method = shippingMethods.find(m => m.id === watchedShippingMethod)
    if (method) {
      setValue('shippingCost', method.cost)
    }
  }, [watchedShippingMethod, setValue])

  const handleFulfillmentSubmit = async (data: FulfillmentFormData) => {
    console.log('Fulfillment submission started:', data)

    try {
      setLoading(true)
      setError(null)

      // Check for validation errors
      if (showValidationErrors()) {
        toast.error('Please fix the validation errors before submitting')
        return
      }

      // Validate that at least one item has quantity to fulfill
      const hasItemsToFulfill = data.items.some(item => item.quantityToFulfill > 0)
      if (!hasItemsToFulfill) {
        toast.error('At least one item must have quantity to fulfill')
        return
      }

      // Validate quantities
      const invalidQuantities = data.items.filter(item =>
        item.quantityToFulfill > item.quantityOrdered
      )
      if (invalidQuantities.length > 0) {
        toast.error('Quantity to fulfill cannot exceed quantity ordered')
        return
      }

      console.log('Fulfillment validation passed, processing...')

      // Prepare fulfillment data
      const fulfillmentData = {
        orderId: data.orderId,
        type: data.fulfillmentType,
        items: data.items.filter(item => item.quantityToFulfill > 0),
        shipping: {
          method: data.shippingMethod,
          trackingNumber: data.trackingNumber,
          trackingUrl: data.trackingUrl,
          carrierService: data.carrierService,
          estimatedDelivery: data.estimatedDelivery,
          cost: data.shippingCost,
          requireSignature: data.requireSignature,
        },
        notes: {
          packingSlip: data.packingSlipNotes,
          internal: data.internalNotes,
        },
        gift: {
          isGift: data.isGift,
          message: data.giftMessage,
        },
        notifyCustomer: data.notifyCustomer,
        processedAt: new Date().toISOString(),
        processedBy: 'current-user', // Replace with actual user
      }

      console.log('Submitting fulfillment:', fulfillmentData)

      // Call API to process fulfillment
      const response = await fetch(`/api/e-commerce/orders/${order.id}/fulfillment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-admin-request': 'true',
        },
        body: JSON.stringify(fulfillmentData),
      })

      const result = await response.json()

      if (result.success && result.data) {
        toast.success(`Order #${order.orderNumber} fulfillment processed successfully!`)

        if (onFulfillmentComplete) {
          onFulfillmentComplete(result.data)
        }

        if (onClose) {
          onClose()
        }
      } else {
        setError(result.error || 'Failed to process fulfillment')
        toast.error(result.error || 'Failed to process fulfillment')
      }
    } catch (error) {
      console.error('Error processing fulfillment:', error)
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
      setError(errorMessage)
      toast.error(`Failed to process fulfillment: ${errorMessage}`)
    } finally {
      setLoading(false)
    }
  }

  const updateItemQuantity = (index: number, change: number) => {
    const item = watchedItems[index]
    const newQuantity = Math.max(0, Math.min(item.quantityOrdered, item.quantityToFulfill + change))
    updateItem(index, { ...item, quantityToFulfill: newQuantity })
  }

  const formatCurrency = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return 'R 0.00'
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR',
      minimumFractionDigits: 2
    }).format(amount)
  }

  return (
    <form onSubmit={handleSubmit(handleFulfillmentSubmit)} className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">
            Order Fulfillment Center
          </h2>
          <p className="text-muted-foreground">
            Process fulfillment for Order #{order.orderNumber}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => setScanMode(!scanMode)}
            className={scanMode ? 'bg-blue-50 border-blue-200' : ''}
          >
            <Scan className="mr-2 h-4 w-4" />
            {scanMode ? 'Exit Scan Mode' : 'Scan Mode'}
          </Button>
          {onClose && (
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting || loading}>
              Cancel
            </Button>
          )}
          <Button
            type="submit"disabled={loading || isSubmitting}
            className="min-w-[140px]"
          >
            {(loading || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            <Package className="mr-2 h-4 w-4" />
            Process Fulfillment
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error!</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Scan Mode */}
      {scanMode && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center text-blue-800">
              <Scan className="mr-2 h-5 w-5" />
              Barcode Scanner Mode
            </CardTitle>
            <CardDescription>
              Scan product barcodes or SKUs to automatically update fulfillment quantities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex space-x-2">
              <Input
                value={scannedCode}
                onChange={(e) => setScannedCode(e.target.value)}
                placeholder="Scan or enter SKU/Product ID..."
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault()
                    if (scannedCode.trim()) {
                      handleScan(scannedCode.trim())
                    }
                  }
                }}
                className="flex-1"
                autoFocus
              />
              <Button
                type="button"
                onClick={() => scannedCode.trim() && handleScan(scannedCode.trim())}
                disabled={!scannedCode.trim()}
              >
                Process Scan
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Fulfillment Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="mr-2 h-5 w-5" />
            Fulfillment Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm text-muted-foreground">
                {fulfillmentProgress().toFixed(0)}% Complete
              </span>
            </div>
            <Progress value={fulfillmentProgress()} className="w-full" />

            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-2xl font-bold text-blue-600">
                  {watchedItems.reduce((sum, item) => sum + item.quantityOrdered, 0)}
                </p>
                <p className="text-sm text-muted-foreground">Total Ordered</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-green-600">
                  {watchedItems.reduce((sum, item) => sum + item.quantityToFulfill, 0)}
                </p>
                <p className="text-sm text-muted-foreground">To Fulfill</p>
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-600">
                  {watchedItems.reduce((sum, item) => sum + (item.quantityOrdered - item.quantityToFulfill), 0)}
                </p>
                <p className="text-sm text-muted-foreground">Remaining</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Fulfillment Type Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Fulfillment Type</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="fulfillmentType">Type</Label>
            <Select
              value={watchedFulfillmentType}
              onValueChange={(value: any) => setValue('fulfillmentType', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="full">Full Fulfillment</SelectItem>
                <SelectItem value="partial">Partial Fulfillment</SelectItem>
              </SelectContent>
            </Select>
            {watchedFulfillmentType === 'partial' && (
              <p className="text-sm text-muted-foreground">
                Partial fulfillment allows you to ship some items now and the rest later.
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Fulfillment Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="items">Items</TabsTrigger>
          <TabsTrigger value="shipping">Shipping</TabsTrigger>
          <TabsTrigger value="packaging">Packaging</TabsTrigger>
          <TabsTrigger value="review">Review</TabsTrigger>
        </TabsList>

        <TabsContent value="items" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="mr-2 h-5 w-5" />
                Items to Fulfill
              </CardTitle>
              <CardDescription>
                Select quantities and locations for each item
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {itemFields.map((field, index) => {
                  const item = watchedItems[index]
                  const orderItem = order.items?.find(oi => oi.id === item.orderItemId)

                  return (
                    <div key={field.id} className="p-4 border rounded-lg space-y-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium">{orderItem?.productTitle || 'Unknown Product'}</h4>
                          {orderItem?.variantTitle && (
                            <p className="text-sm text-muted-foreground">{orderItem.variantTitle}</p>
                          )}
                          <p className="text-sm text-muted-foreground">
                            SKU: {item.sku || 'N/A'} | Ordered: {item.quantityOrdered}
                          </p>
                        </div>
                        <Badge variant={item.quantityToFulfill === item.quantityOrdered ? 'default' : 'secondary'}>
                          {item.quantityToFulfill}/{item.quantityOrdered}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="space-y-2">
                          <Label>Quantity to Fulfill</Label>
                          <div className="flex items-center space-x-2">
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => updateItemQuantity(index, -1)}
                              disabled={item.quantityToFulfill <= 0}
                            >
                              <Minus className="h-4 w-4" />
                            </Button>
                            <Input
                              type="number"
                              min="0"
                              max={item.quantityOrdered}
                              value={item.quantityToFulfill}
                              onChange={(e) => {
                                const value = Math.max(0, Math.min(item.quantityOrdered, parseInt(e.target.value) || 0))
                                updateItem(index, { ...item, quantityToFulfill: value })
                              }}
                              className="w-20 text-center"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => updateItemQuantity(index, 1)}
                              disabled={item.quantityToFulfill >= item.quantityOrdered}
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label>Location</Label>
                          <Select
                            value={item.locationId}
                            onValueChange={(value) => updateItem(index, { ...item, locationId: value })}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {inventoryLocations.map((location) => (
                                <SelectItem key={location.id} value={location.id}>
                                  {location.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Bin Location</Label>
                          <Input
                            value={item.binLocation || ''}
                            onChange={(e) => updateItem(index, { ...item, binLocation: e.target.value })}
                            placeholder="A1-B2-C3"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Status</Label>
                          <div className="flex items-center space-x-2">
                            {item.quantityToFulfill === 0 ? (
                              <Badge variant="secondary">Not Selected</Badge>
                            ) : item.quantityToFulfill === item.quantityOrdered ? (
                              <Badge variant="default">Full</Badge>
                            ) : (
                              <Badge variant="outline">Partial</Badge>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label>Item Notes</Label>
                        <Textarea
                          value={item.notes || ''}
                          onChange={(e) => updateItem(index, { ...item, notes: e.target.value })}
                          placeholder="Special handling instructions..."
                          rows={2}
                        />
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="shipping" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Truck className="mr-2 h-5 w-5" />
                Shipping Information
              </CardTitle>
              <CardDescription>
                Configure shipping method and tracking details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="shippingMethod">Shipping Method *</Label>
                  <Select
                    value={watchedShippingMethod}
                    onValueChange={(value) => setValue('shippingMethod', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {shippingMethods.map((method) => (
                        <SelectItem key={method.id} value={method.id}>
                          {method.name} ({method.estimatedDays} days) - {formatCurrency(method.cost)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.shippingMethod && (
                    <p className="text-sm text-red-500">{errors.shippingMethod.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="shippingCost">Shipping Cost</Label>
                  <Input
                    id="shippingCost"
                    type="number"
                    min="0"
                    step="0.01"
                    {...register('shippingCost', { valueAsNumber: true })}
                    placeholder="0.00"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="trackingNumber">Tracking Number</Label>
                  <Input
                    id="trackingNumber"
                    {...register('trackingNumber')}
                    placeholder="Enter tracking number"
                  />
                  {errors.trackingNumber && (
                    <p className="text-sm text-red-500">{errors.trackingNumber.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="carrierService">Carrier Service</Label>
                  <Input
                    id="carrierService"
                    {...register('carrierService')}
                    placeholder="e.g., PostNet, Courier Guy"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="trackingUrl">Tracking URL</Label>
                  <Input
                    id="trackingUrl"
                    type="url"
                    {...register('trackingUrl')}
                    placeholder="https://tracking.example.com/..."
                  />
                  {errors.trackingUrl && (
                    <p className="text-sm text-red-500">{errors.trackingUrl.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="estimatedDelivery">Estimated Delivery</Label>
                  <Input
                    id="estimatedDelivery"
                    type="date"
                    {...register('estimatedDelivery')}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="requireSignature"
                    checked={watch('requireSignature')}
                    onCheckedChange={(checked) => setValue('requireSignature', checked)}
                  />
                  <Label htmlFor="requireSignature">Require Signature</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="notifyCustomer"
                    checked={watch('notifyCustomer')}
                    onCheckedChange={(checked) => setValue('notifyCustomer', checked)}
                  />
                  <Label htmlFor="notifyCustomer">Notify Customer</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="packaging" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="mr-2 h-5 w-5" />
                Packaging & Gift Options
              </CardTitle>
              <CardDescription>
                Configure packaging details and gift options
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="isGift"
                  checked={watch('isGift')}
                  onCheckedChange={(checked) => setValue('isGift', checked)}
                />
                <Label htmlFor="isGift">This is a gift order</Label>
              </div>

              {watch('isGift') && (
                <div className="space-y-2">
                  <Label htmlFor="giftMessage">Gift Message</Label>
                  <Textarea
                    id="giftMessage"
                    {...register('giftMessage')}
                    placeholder="Enter gift message to include with the package..."
                    rows={3}
                  />
                  {errors.giftMessage && (
                    <p className="text-sm text-red-500">{errors.giftMessage.message}</p>
                  )}
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="packingSlipNotes">Packing Slip Notes</Label>
                <Textarea
                  id="packingSlipNotes"
                  {...register('packingSlipNotes')}
                  placeholder="Special instructions for the packing slip..."
                  rows={3}
                />
                {errors.packingSlipNotes && (
                  <p className="text-sm text-red-500">{errors.packingSlipNotes.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="internalNotes">Internal Fulfillment Notes</Label>
                <Textarea
                  id="internalNotes"
                  {...register('internalNotes')}
                  placeholder="Internal notes for warehouse staff..."
                  rows={3}
                />
                {errors.internalNotes && (
                  <p className="text-sm text-red-500">{errors.internalNotes.message}</p>
                )}
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">Packaging Actions</h4>
                <div className="grid grid-cols-2 gap-4">
                  <Button type="button" variant="outline" className="h-auto p-4">
                    <div className="text-center">
                      <Printer className="h-6 w-6 mx-auto mb-2" />
                      <p className="font-medium">Print Packing Slip</p>
                      <p className="text-sm text-muted-foreground">Generate packing slip for this order</p>
                    </div>
                  </Button>
                  <Button type="button" variant="outline" className="h-auto p-4">
                    <div className="text-center">
                      <Package className="h-6 w-6 mx-auto mb-2" />
                      <p className="font-medium">Print Shipping Label</p>
                      <p className="text-sm text-muted-foreground">Generate shipping label</p>
                    </div>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="review" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="mr-2 h-5 w-5" />
                Fulfillment Review
              </CardTitle>
              <CardDescription>
                Review all fulfillment details before processing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Order Summary */}
              <div>
                <h4 className="font-medium mb-3">Order Information</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Order Number:</span>
                    <span className="ml-2 font-medium">#{order.orderNumber}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Customer:</span>
                    <span className="ml-2 font-medium">
                      {order.customer?.firstName} {order.customer?.lastName}
                    </span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Email:</span>
                    <span className="ml-2 font-medium">{order.customer?.email}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Fulfillment Type:</span>
                    <span className="ml-2 font-medium capitalize">{watchedFulfillmentType}</span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Items Summary */}
              <div>
                <h4 className="font-medium mb-3">Items to Fulfill</h4>
                <div className="space-y-3">
                  {watchedItems.filter(item => item.quantityToFulfill > 0).map((item, index) => {
                    const orderItem = order.items?.find(oi => oi.id === item.orderItemId)
                    const location = inventoryLocations.find(loc => loc.id === item.locationId)

                    return (
                      <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                        <div>
                          <p className="font-medium">{orderItem?.productTitle}</p>
                          <p className="text-sm text-muted-foreground">
                            SKU: {item.sku || 'N/A'} | Location: {location?.name}
                            {item.binLocation && ` (${item.binLocation})`}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">Qty: {item.quantityToFulfill}</p>
                          <p className="text-sm text-muted-foreground">
                            of {item.quantityOrdered} ordered
                          </p>
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              <Separator />

              {/* Shipping Summary */}
              <div>
                <h4 className="font-medium mb-3">Shipping Details</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Method:</span>
                    <span className="ml-2 font-medium">
                      {shippingMethods.find(m => m.id === watchedShippingMethod)?.name}
                    </span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Cost:</span>
                    <span className="ml-2 font-medium">{formatCurrency(watch('shippingCost'))}</span>
                  </div>
                  {watch('trackingNumber') && (
                    <div>
                      <span className="text-muted-foreground">Tracking:</span>
                      <span className="ml-2 font-medium font-mono">{watch('trackingNumber')}</span>
                    </div>
                  )}
                  {watch('carrierService') && (
                    <div>
                      <span className="text-muted-foreground">Carrier:</span>
                      <span className="ml-2 font-medium">{watch('carrierService')}</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Shipping Address */}
              <div>
                <h4 className="font-medium mb-3">Shipping Address</h4>
                <div className="p-3 bg-muted rounded-lg text-sm">
                  <p className="font-medium">
                    {order.shippingAddress?.firstName} {order.shippingAddress?.lastName}
                  </p>
                  {order.shippingAddress?.company && (
                    <p>{order.shippingAddress.company}</p>
                  )}
                  <p>{order.shippingAddress?.address1}</p>
                  {order.shippingAddress?.address2 && (
                    <p>{order.shippingAddress.address2}</p>
                  )}
                  <p>
                    {order.shippingAddress?.city}, {order.shippingAddress?.province} {order.shippingAddress?.postalCode}
                  </p>
                  <p>{order.shippingAddress?.country}</p>
                  {order.shippingAddress?.phone && (
                    <p>Phone: {order.shippingAddress.phone}</p>
                  )}
                </div>
              </div>

              <Separator />

              {/* Options Summary */}
              <div>
                <h4 className="font-medium mb-3">Fulfillment Options</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span>Notify Customer:</span>
                    <Badge variant={watch('notifyCustomer') ? 'default' : 'secondary'}>
                      {watch('notifyCustomer') ? 'Yes' : 'No'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Require Signature:</span>
                    <Badge variant={watch('requireSignature') ? 'default' : 'secondary'}>
                      {watch('requireSignature') ? 'Yes' : 'No'}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Gift Order:</span>
                    <Badge variant={watch('isGift') ? 'default' : 'secondary'}>
                      {watch('isGift') ? 'Yes' : 'No'}
                    </Badge>
                  </div>
                </div>
              </div>

              {/* Final Actions */}
              <div className="pt-4">
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertTitle>Ready to Process</AlertTitle>
                  <AlertDescription>
                    Please review all details above. Once you process this fulfillment, the order status will be updated and customers will be notified if enabled.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </form>
  )
}