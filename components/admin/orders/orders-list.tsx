'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { format } from 'date-fns'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Plus, Search, MoreHorizontal, ArrowUpDown } from 'lucide-react'

type OrderStatus = 'PENDING' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED'
type PaymentStatus = 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED' | 'PARTIALLY_REFUNDED'

interface OrderFilters {
  status?: OrderStatus
  paymentStatus?: PaymentStatus
  customerEmail?: string
  orderNumber?: string
  startDate?: string
  endDate?: string
  page?: number
  limit?: number
}

interface AdminOrdersResponse {
  orders: Array<{
    id: string
    orderNumber: string
    status: OrderStatus
    paymentStatus: PaymentStatus
    total: number
    customerEmail: string
    customerName?: string
    createdAt: string
    updatedAt: string
  }>
  total: number
  page: number
  limit: number
  totalPages: number
}

const orderStatuses: Record<OrderStatus, { label: string; color: string }> = {
  PENDING: { label: 'Pending', color: 'secondary' },
  PROCESSING: { label: 'Processing', color: 'default' },
  SHIPPED: { label: 'Shipped', color: 'default' },
  DELIVERED: { label: 'Delivered', color: 'success' },
  CANCELLED: { label: 'Cancelled', color: 'destructive' },
  REFUNDED: { label: 'Refunded', color: 'outline' },
}

const paymentStatuses: Record<PaymentStatus, { label: string; color: string }> = {
  PENDING: { label: 'Pending', color: 'secondary' },
  PAID: { label: 'Paid', color: 'success' },
  FAILED: { label: 'Failed', color: 'destructive' },
  REFUNDED: { label: 'Refunded', color: 'outline' },
  PARTIALLY_REFUNDED: { label: 'Partially Refunded', color: 'outline' },
}

export function OrdersList() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [orders, setOrders] = useState<AdminOrdersResponse['orders']>([])
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<OrderFilters>({
    status: undefined,
    paymentStatus: undefined,
    customerEmail: '',
    orderNumber: '',
    startDate: '',
    endDate: '',
    page: 1,
    limit: 10,
  })
  const [pagination, setPagination] = useState({
    total: 0,
    totalPages: 1,
  })

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setIsLoading(true)
        const params = new URLSearchParams()

        Object.entries(filters).forEach(([key, value]) => {
          if (value) {
            params.set(key, String(value))
          }
        })

        const response = await fetch(`/api/admin/orders?${params.toString()}`)
        const data = await response.json()

        if (data.success) {
          setOrders(data.data.orders)
          setPagination({
            total: data.data.total,
            totalPages: data.data.totalPages,
          })
        } else {
          setError(data.error || 'Failed to fetch orders')
        }
      } catch (error) {
        console.error('Failed to fetch orders:', error)
        setError('Failed to fetch orders')
      } finally {
        setIsLoading(false)
      }
    }

    fetchOrders()
  }, [filters])

  const handleFilterChange = (filterName: keyof OrderFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [filterName]: value,
      page: 1,
    }))
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // The useEffect will trigger the search with the current filters
  }

  const handlePageChange = (newPage: number) => {
    if (newPage > 0 && newPage <= pagination.totalPages) {
      setFilters(prev => ({
        ...prev,
        page: newPage,
      }))
    }
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'secondary'
      case 'processing':
        return 'default'
      case 'shipped':
        return 'default'
      case 'delivered':
        return 'success'
      case 'cancelled':
        return 'destructive'
      case 'refunded':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  const getPaymentStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'pending':
        return 'secondary'
      case 'paid':
        return 'success'
      case 'failed':
        return 'destructive'
      case 'refunded':
        return 'outline'
      default:
        return 'secondary'
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Orders</CardTitle>
            <Button onClick={() => router.push('/admin/orders/new')}>
              <Plus className="mr-2 h-4 w-4" />
              New Order
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-6 space-y-4">
            <form onSubmit={handleSearch} className="flex flex-wrap gap-4">
              <Input
                placeholder="Search by order #"
                className="max-w-sm"
                value={filters.orderNumber || ''}
                onChange={(e) => handleFilterChange('orderNumber', e.target.value)}
              />
              <Input
                placeholder="Search by customer email"
                className="max-w-sm"
                value={filters.customerEmail || ''}
                onChange={(e) => handleFilterChange('customerEmail', e.target.value)}
              />
              <Select
                value={filters.status || 'all'}
                onValueChange={(value) => handleFilterChange('status', value === 'all' ? undefined : value)}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {Object.keys(orderStatuses).map((status) => (
                    <SelectItem key={status} value={status}>
                      {orderStatuses[status as OrderStatus].label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={filters.paymentStatus || 'all'}
                onValueChange={(value) => handleFilterChange('paymentStatus', value === 'all' ? undefined : value)}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Payment Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Payments</SelectItem>
                  {Object.keys(paymentStatuses).map((status) => (
                    <SelectItem key={status} value={status}>
                      {paymentStatuses[status as PaymentStatus].label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button type="submit">
                <Search className="mr-2 h-4 w-4" />
                Search
              </Button>
            </form>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Order</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Payment</TableHead>
                  <TableHead className="text-right">Total</TableHead>
                  <TableHead></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      Loading...
                    </TableCell>
                  </TableRow>
                ) : error ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center text-red-500">
                      {error}
                    </TableCell>
                  </TableRow>
                ) : orders.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      No orders found.
                    </TableCell>
                  </TableRow>
                ) : (
                  orders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell className="font-medium">
                        {order.orderNumber}
                      </TableCell>
                      <TableCell>
                        {format(new Date(order.createdAt), 'MMM d, yyyy')}
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{order.customerEmail}</div>
                        {order.customerName && (
                          <div className="text-sm text-muted-foreground">
                            {order.customerName}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadgeVariant(order.status)}>
                          {order.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getPaymentStatusBadgeVariant(order.paymentStatus)}>
                          {order.paymentStatus}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        R{order.total.toFixed(2)}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            router.push(`/admin/orders/${order.id}`)
                          }
                        >
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {!isLoading && pagination.totalPages > 1 && (
            <div className="mt-4 flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Showing{' '}
                <span className="font-medium">
                  {((filters.page || 1) - 1) * (filters.limit || 10) + 1}
                </span>{' '}
                to{' '}
                <span className="font-medium">
                  {Math.min(((filters.page || 1) * (filters.limit || 10)), pagination.total)}
                </span>{' '}
                of <span className="font-medium">{pagination.total}</span> orders
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange((filters.page || 1) - 1)}
                  disabled={(filters.page || 1) <= 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange((filters.page || 1) + 1)}
                  disabled={(filters.page || 1) >= pagination.totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
