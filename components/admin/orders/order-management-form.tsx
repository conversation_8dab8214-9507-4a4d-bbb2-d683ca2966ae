'use client'

import { useState, useEffect } from 'react'
import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Save, Loader2, Plus, X, ShoppingCart, User, MapPin, Package, CreditCard, AlertTriangle } from 'lucide-react'
import { toast } from 'sonner'
import { useOrderMutations } from '@/lib/ecommerce/hooks/use-orders'
import type { Order, CreateOrderInput, UpdateOrderInput } from '@/lib/ecommerce/types'

// Address validation schema
const addressSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50, 'First name is too long'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name is too long'),
  company: z.string().max(100, 'Company name is too long').optional(),
  address1: z.string().min(1, 'Address is required').max(255, 'Address is too long'),
  address2: z.string().max(255, 'Address line 2 is too long').optional(),
  city: z.string().min(1, 'City is required').max(100, 'City name is too long'),
  province: z.string().min(1, 'Province is required').max(100, 'Province name is too long'),
  country: z.string().min(1, 'Country is required').max(100, 'Country name is too long'),
  postalCode: z.string().min(1, 'Postal code is required').max(20, 'Postal code is too long'),
  phone: z.string().max(20, 'Phone number is too long').optional(),
})

// Order item validation schema
const orderItemSchema = z.object({
  productId: z.string().min(1, 'Product is required'),
  variantId: z.string().optional(),
  quantity: z.number().min(1, 'Quantity must be at least 1').int('Quantity must be a whole number'),
  unitPrice: z.number().min(0, 'Unit price cannot be negative'),
  customAttributes: z.record(z.any()).optional(),
  personalizedMessage: z.string().max(500, 'Message is too long').optional(),
  giftWrap: z.boolean().default(false),
})

// Main order form validation schema
const orderFormSchema = z.object({
  // Customer information
  customer: z.object({
    id: z.string().optional(),
    email: z.string().email('Invalid email address'),
    firstName: z.string().min(1, 'First name is required').max(50, 'First name is too long'),
    lastName: z.string().min(1, 'Last name is required').max(50, 'Last name is too long'),
    phone: z.string().max(20, 'Phone number is too long').optional(),
  }),
  
  // Addresses
  billingAddress: addressSchema,
  shippingAddress: addressSchema,
  sameAsShipping: z.boolean().default(true),
  
  // Items
  items: z.array(orderItemSchema).min(1, 'At least one item is required'),
  
  // Shipping
  shippingMethodId: z.string().min(1, 'Shipping method is required'),
  
  // Payment
  paymentMethodId: z.string().optional(),
  
  // Order details
  paymentStatus: z.enum(['pending', 'authorized', 'partially_paid', 'paid', 'partially_refunded', 'refunded', 'voided']).default('pending'),
  fulfillmentStatus: z.enum(['unfulfilled', 'partially_fulfilled', 'fulfilled', 'shipped', 'delivered', 'returned', 'cancelled']).default('unfulfilled'),
  
  // Discounts
  discountCodes: z.array(z.string()).default([]),
  
  // Notes
  customerNote: z.string().max(1000, 'Customer note is too long').optional(),
  internalNotes: z.array(z.string()).default([]),
  
  // Metadata
  source: z.enum(['web', 'mobile', 'pos', 'api', 'admin']).default('admin'),
  sourceIdentifier: z.string().optional(),
  attributes: z.record(z.any()).default({}),
  tags: z.array(z.string()).max(20, 'Maximum 20 tags allowed').default([]),
})

type OrderFormData = z.infer<typeof orderFormSchema>

interface OrderManagementFormProps {
  order?: Order
  onSuccess?: (order: Order) => void
  onCancel: () => void
}

export function OrderManagementForm({ order, onSuccess, onCancel }: OrderManagementFormProps) {
  const [activeTab, setActiveTab] = useState('customer')
  const [newTag, setNewTag] = useState('')
  const [newInternalNote, setNewInternalNote] = useState('')

  const { 
    createOrder, 
    updateOrder, 
    loading: mutationLoading, 
    error: mutationError,
    clearError 
  } = useOrderMutations()

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    clearErrors,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<OrderFormData>({
    resolver: zodResolver(orderFormSchema),
    mode: 'onChange',
    defaultValues: {
      customer: {
        id: order?.customer?.id || '',
        email: order?.customer?.email || '',
        firstName: order?.customer?.firstName || '',
        lastName: order?.customer?.lastName || '',
        phone: order?.customer?.phone || '',
      },
      billingAddress: order?.billingAddress || {
        firstName: '',
        lastName: '',
        company: '',
        address1: '',
        address2: '',
        city: '',
        province: '',
        country: 'South Africa',
        postalCode: '',
        phone: '',
      },
      shippingAddress: order?.shippingAddress || {
        firstName: '',
        lastName: '',
        company: '',
        address1: '',
        address2: '',
        city: '',
        province: '',
        country: 'South Africa',
        postalCode: '',
        phone: '',
      },
      sameAsShipping: true,
      items: order?.items?.map(item => ({
        productId: item.productId,
        variantId: item.variantId,
        quantity: item.quantity,
        unitPrice: item.unitPrice?.amount || 0,
        customAttributes: {},
        personalizedMessage: '',
        giftWrap: false,
      })) || [{
        productId: '',
        variantId: '',
        quantity: 1,
        unitPrice: 0,
        customAttributes: {},
        personalizedMessage: '',
        giftWrap: false,
      }],
      shippingMethodId: order?.shippingMethod?.id || '',
      paymentMethodId: '',
      paymentStatus: order?.paymentStatus || 'pending',
      fulfillmentStatus: order?.fulfillmentStatus || 'unfulfilled',
      discountCodes: [],
      customerNote: order?.customerNote || '',
      internalNotes: order?.internalNotes || [],
      source: 'admin',
      sourceIdentifier: '',
      attributes: order?.attributes || {},
      tags: order?.tags || [],
    },
  })

  const { fields: itemFields, append: appendItem, remove: removeItem } = useFieldArray({
    control,
    name: 'items',
  })

  const watchedSameAsShipping = watch('sameAsShipping')
  const watchedTags = watch('tags')
  const watchedInternalNotes = watch('internalNotes')

  // Copy shipping address to billing when sameAsShipping changes
  useEffect(() => {
    if (watchedSameAsShipping) {
      const shippingAddress = watch('shippingAddress')
      setValue('billingAddress', shippingAddress)
    }
  }, [watchedSameAsShipping, setValue, watch])

  // Clear mutation error when form data changes
  useEffect(() => {
    if (mutationError && isDirty) {
      clearError()
    }
  }, [isDirty, mutationError, clearError])

  // Function to show validation errors as toasts
  const showValidationErrors = () => {
    if (Object.keys(errors).length > 0) {
      console.log('Showing validation errors:', errors)
      
      Object.entries(errors).forEach(([field, error]) => {
        if (error?.message) {
          // Convert camelCase field names to readable format
          const fieldName = field
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, str => str.toUpperCase())
            .replace(/([a-z])([A-Z])/g, '$1 $2')
          
          toast.error(`${fieldName}: ${error.message}`)
        }
      })
      
      return true // Has errors
    }
    return false // No errors
  }

  const handleFormSubmit = async (data: OrderFormData) => {
    console.log('Order form submission started with data:', data)
    
    try {
      clearError()
      clearErrors()

      // Check for form validation errors first
      if (showValidationErrors()) {
        console.error('Form has validation errors:', errors)
        toast.error('Please fix the validation errors before submitting')
        return
      }

      console.log('Form validation passed, preparing submit data...')

      // Prepare submit data
      const createPayload: CreateOrderInput = {
        customerEmail: data.customer.email,
        customerPhone: data.customer.phone,
        items: data.items.map(item => ({
          productId: item.productId,
          variantId: item.variantId,
          quantity: item.quantity,
          price: item.unitPrice,
          name: 'Product Name', // This should be fetched or passed in
          image: 'product-image-url' // This should be fetched or passed in
        })),
        shippingAddress: {
          ...data.shippingAddress
        },
        billingAddress: data.sameAsShipping ? undefined : {
          ...data.billingAddress
        },
        shippingMethod: data.shippingMethodId,
        paymentMethod: data.paymentMethodId,
        couponCode: data.discountCodes.join(','),
        notes: data.customerNote,
        tags: data.tags,
        source: data.source,
      }

      const submitData: CreateOrderInput | UpdateOrderInput = {
        ...createPayload,
        ...(order?.id && { id: order.id }),
      }

      console.log('Submit data prepared:', submitData)

      let result: Order | null = null

      if (order?.id) {
        console.log('Updating existing order with ID:', order.id)
        result = await updateOrder(submitData as UpdateOrderInput)
      } else {
        console.log('Creating new order...')
        result = await createOrder(submitData as CreateOrderInput)
      }

      console.log('API call result:', result)

      if (result) {
        console.log('Order operation successful:', result)
        toast.success(
          order?.id
            ? `Order #${result.orderNumber} updated successfully!`
            : `Order #${result.orderNumber} created successfully!`
        )

        if (onSuccess) {
          onSuccess(result)
        }

        // Reset form if creating a new order
        if (!order?.id) {
          reset()
          setActiveTab('customer')
        }
      } else {
        console.error('No result returned from API call')
        toast.error(
          order?.id
            ? 'Failed to update order. Please check the form and try again.'
            : 'Failed to create order. Please check the form and try again.'
        )
      }
    } catch (error) {
      console.error('Error submitting form:', error)
      
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
      
      toast.error(
        order?.id
          ? `Failed to update order: ${errorMessage}`
          : `Failed to create order: ${errorMessage}`
      )
      
      if (mutationError) {
        console.error('Mutation error:', mutationError)
        toast.error(`API Error: ${mutationError}`)
      }
    }
  }

  const addTag = () => {
    if (newTag.trim() && !watchedTags?.includes(newTag.trim())) {
      setValue('tags', [...(watchedTags || []), newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setValue('tags', watchedTags?.filter(tag => tag !== tagToRemove) || [])
  }

  const addInternalNote = () => {
    if (newInternalNote.trim()) {
      setValue('internalNotes', [...(watchedInternalNotes || []), newInternalNote.trim()])
      setNewInternalNote('')
    }
  }

  const removeInternalNote = (index: number) => {
    const notes = [...(watchedInternalNotes || [])]
    notes.splice(index, 1)
    setValue('internalNotes', notes)
  }

  const addItem = () => {
    appendItem({
      productId: '',
      variantId: '',
      quantity: 1,
      unitPrice: 0,
      customAttributes: {},
      personalizedMessage: '',
      giftWrap: false,
    })
  }

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            {order ? `Edit Order #${order.orderNumber}` : 'Create New Order'}
          </h1>
          <p className="text-muted-foreground">
            {order ? 'Update order information and status' : 'Create a new order for a customer'}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting || mutationLoading}>
            Cancel
          </Button>
          <Button
            type="button"
            onClick={() => {
              console.log('Debug button clicked')
              console.log('Form data:', watch())
              console.log('Form errors:', errors)
              console.log('Is submitting:', isSubmitting)
              console.log('Mutation loading:', mutationLoading)
              console.log('Mutation error:', mutationError)
              
              if (!showValidationErrors()) {
                toast.success('Form validation passed!')
              }
            }}
            variant="secondary"
            className="min-w-[100px]"
          >
            Debug
          </Button>
          <Button
            type="submit"disabled={mutationLoading || isSubmitting}
            className="min-w-[140px]"
            onClick={() => {
              console.log('Submit button clicked!')
              console.log('Form errors:', errors)
              
              if (Object.keys(errors).length > 0) {
                showValidationErrors()
              }
            }}
          >
            {(mutationLoading || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            <Save className="mr-2 h-4 w-4" />
            {order ? 'Update Order' : 'Create Order'}
          </Button>
        </div>
      </div>

      {/* Global Error Alert */}
      {mutationError && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error!</AlertTitle>
          <AlertDescription>{mutationError}</AlertDescription>
        </Alert>
      )}

      {/* Form Validation Errors Alert */}
      {Object.keys(errors).length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Form Validation Errors</AlertTitle>
          <AlertDescription>
            <div className="space-y-1">
              <p>Please fix the following errors:</p>
              <ul className="list-disc list-inside space-y-1">
                {Object.entries(errors).map(([field, error]) => {
                  if (error?.message) {
                    const fieldName = field
                      .replace(/([A-Z])/g, ' $1')
                      .replace(/^./, str => str.toUpperCase())
                      .replace(/([a-z])([A-Z])/g, '$1 $2')
                    return (
                      <li key={field} className="text-sm">
                        <strong>{fieldName}:</strong> {error.message}
                      </li>
                    )
                  }
                  return null
                })}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Form Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="customer">Customer</TabsTrigger>
          <TabsTrigger value="items">Items</TabsTrigger>
          <TabsTrigger value="shipping">Shipping</TabsTrigger>
          <TabsTrigger value="payment">Payment</TabsTrigger>
          <TabsTrigger value="status">Status</TabsTrigger>
          <TabsTrigger value="notes">Notes</TabsTrigger>
        </TabsList>

        <TabsContent value="customer" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="mr-2 h-5 w-5" />
                Customer Information
              </CardTitle>
              <CardDescription>
                Customer details and contact information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customer.email">Email Address *</Label>
                  <Input
                    id="customer.email"
                    type="email"
                    {...register('customer.email')}
                    placeholder="<EMAIL>"
                  />
                  {errors.customer?.email && (
                    <p className="text-sm text-red-500">{errors.customer.email.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="customer.phone">Phone Number</Label>
                  <Input
                    id="customer.phone"
                    {...register('customer.phone')}
                    placeholder="+27 12 345 6789"
                  />
                  {errors.customer?.phone && (
                    <p className="text-sm text-red-500">{errors.customer.phone.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customer.firstName">First Name *</Label>
                  <Input
                    id="customer.firstName"
                    {...register('customer.firstName')}
                    placeholder="John"
                  />
                  {errors.customer?.firstName && (
                    <p className="text-sm text-red-500">{errors.customer.firstName.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="customer.lastName">Last Name *</Label>
                  <Input
                    id="customer.lastName"
                    {...register('customer.lastName')}
                    placeholder="Doe"
                  />
                  {errors.customer?.lastName && (
                    <p className="text-sm text-red-500">{errors.customer.lastName.message}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Address */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="mr-2 h-5 w-5" />
                Shipping Address
              </CardTitle>
              <CardDescription>
                Where the order will be delivered
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="shippingAddress.firstName">First Name *</Label>
                  <Input
                    id="shippingAddress.firstName"
                    {...register('shippingAddress.firstName')}
                    placeholder="John"
                  />
                  {errors.shippingAddress?.firstName && (
                    <p className="text-sm text-red-500">{errors.shippingAddress.firstName.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="shippingAddress.lastName">Last Name *</Label>
                  <Input
                    id="shippingAddress.lastName"
                    {...register('shippingAddress.lastName')}
                    placeholder="Doe"
                  />
                  {errors.shippingAddress?.lastName && (
                    <p className="text-sm text-red-500">{errors.shippingAddress.lastName.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="shippingAddress.company">Company (Optional)</Label>
                <Input
                  id="shippingAddress.company"
                  {...register('shippingAddress.company')}
                  placeholder="Company Name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="shippingAddress.address1">Address Line 1 *</Label>
                <Input
                  id="shippingAddress.address1"
                  {...register('shippingAddress.address1')}
                  placeholder="123 Main Street"
                />
                {errors.shippingAddress?.address1 && (
                  <p className="text-sm text-red-500">{errors.shippingAddress.address1.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="shippingAddress.address2">Address Line 2 (Optional)</Label>
                <Input
                  id="shippingAddress.address2"
                  {...register('shippingAddress.address2')}
                  placeholder="Apartment, suite, etc."
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="shippingAddress.city">City *</Label>
                  <Input
                    id="shippingAddress.city"
                    {...register('shippingAddress.city')}
                    placeholder="Cape Town"
                  />
                  {errors.shippingAddress?.city && (
                    <p className="text-sm text-red-500">{errors.shippingAddress.city.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="shippingAddress.province">Province *</Label>
                  <Select
                    value={watch('shippingAddress.province')}
                    onValueChange={(value) => setValue('shippingAddress.province', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select province" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Western Cape">Western Cape</SelectItem>
                      <SelectItem value="Eastern Cape">Eastern Cape</SelectItem>
                      <SelectItem value="Northern Cape">Northern Cape</SelectItem>
                      <SelectItem value="Free State">Free State</SelectItem>
                      <SelectItem value="KwaZulu-Natal">KwaZulu-Natal</SelectItem>
                      <SelectItem value="North West">North West</SelectItem>
                      <SelectItem value="Gauteng">Gauteng</SelectItem>
                      <SelectItem value="Mpumalanga">Mpumalanga</SelectItem>
                      <SelectItem value="Limpopo">Limpopo</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.shippingAddress?.province && (
                    <p className="text-sm text-red-500">{errors.shippingAddress.province.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="shippingAddress.postalCode">Postal Code *</Label>
                  <Input
                    id="shippingAddress.postalCode"
                    {...register('shippingAddress.postalCode')}
                    placeholder="8001"
                  />
                  {errors.shippingAddress?.postalCode && (
                    <p className="text-sm text-red-500">{errors.shippingAddress.postalCode.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="shippingAddress.country">Country *</Label>
                  <Select
                    value={watch('shippingAddress.country')}
                    onValueChange={(value) => setValue('shippingAddress.country', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="South Africa">South Africa</SelectItem>
                      <SelectItem value="Botswana">Botswana</SelectItem>
                      <SelectItem value="Namibia">Namibia</SelectItem>
                      <SelectItem value="Lesotho">Lesotho</SelectItem>
                      <SelectItem value="Swaziland">Swaziland</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.shippingAddress?.country && (
                    <p className="text-sm text-red-500">{errors.shippingAddress.country.message}</p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="shippingAddress.phone">Phone Number</Label>
                  <Input
                    id="shippingAddress.phone"
                    {...register('shippingAddress.phone')}
                    placeholder="+27 12 345 6789"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="items" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="mr-2 h-5 w-5" />
                Order Items
              </CardTitle>
              <CardDescription>
                Products and quantities for this order
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {itemFields.map((field, index) => (
                <div key={field.id} className="p-4 border rounded-lg space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">Item {index + 1}</h4>
                    {itemFields.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeItem(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`items.${index}.productId`}>Product ID *</Label>
                      <Input
                        id={`items.${index}.productId`}
                        {...register(`items.${index}.productId`)}
                        placeholder="Enter product ID"
                      />
                      {errors.items?.[index]?.productId && (
                        <p className="text-sm text-red-500">{errors.items[index]?.productId?.message}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`items.${index}.variantId`}>Variant ID</Label>
                      <Input
                        id={`items.${index}.variantId`}
                        {...register(`items.${index}.variantId`)}
                        placeholder="Enter variant ID (optional)"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor={`items.${index}.quantity`}>Quantity *</Label>
                      <Input
                        id={`items.${index}.quantity`}
                        type="number"
                        min="1"
                        step="1"
                        {...register(`items.${index}.quantity`, { valueAsNumber: true })}
                        placeholder="1"
                      />
                      {errors.items?.[index]?.quantity && (
                        <p className="text-sm text-red-500">{errors.items[index]?.quantity?.message}</p>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor={`items.${index}.unitPrice`}>Unit Price (ZAR) *</Label>
                      <Input
                        id={`items.${index}.unitPrice`}
                        type="number"
                        min="0"
                        step="0.01"
                        {...register(`items.${index}.unitPrice`, { valueAsNumber: true })}
                        placeholder="0.00"
                      />
                      {errors.items?.[index]?.unitPrice && (
                        <p className="text-sm text-red-500">{errors.items[index]?.unitPrice?.message}</p>
                      )}
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor={`items.${index}.personalizedMessage`}>Personalized Message</Label>
                    <Textarea
                      id={`items.${index}.personalizedMessage`}
                      {...register(`items.${index}.personalizedMessage`)}
                      placeholder="Optional personalized message for this item"
                      rows={2}
                    />
                  </div>
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={addItem}
                className="w-full"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add Item
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="shipping" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="mr-2 h-5 w-5" />
                Shipping Information
              </CardTitle>
              <CardDescription>
                Shipping method and billing address
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="shippingMethodId">Shipping Method *</Label>
                <Select
                  value={watch('shippingMethodId')}
                  onValueChange={(value) => setValue('shippingMethodId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select shipping method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="standard">Standard Shipping (5-7 days)</SelectItem>
                    <SelectItem value="express">Express Shipping (2-3 days)</SelectItem>
                    <SelectItem value="overnight">Overnight Shipping (1 day)</SelectItem>
                    <SelectItem value="pickup">Store Pickup</SelectItem>
                  </SelectContent>
                </Select>
                {errors.shippingMethodId && (
                  <p className="text-sm text-red-500">{errors.shippingMethodId.message}</p>
                )}
              </div>

              <Separator />

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="sameAsShipping"
                  {...register('sameAsShipping')}
                  className="rounded"
                />
                <Label htmlFor="sameAsShipping">Billing address same as shipping address</Label>
              </div>

              {!watchedSameAsShipping && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Billing Address</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="billingAddress.firstName">First Name *</Label>
                        <Input
                          id="billingAddress.firstName"
                          {...register('billingAddress.firstName')}
                          placeholder="John"
                        />
                        {errors.billingAddress?.firstName && (
                          <p className="text-sm text-red-500">{errors.billingAddress.firstName.message}</p>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="billingAddress.lastName">Last Name *</Label>
                        <Input
                          id="billingAddress.lastName"
                          {...register('billingAddress.lastName')}
                          placeholder="Doe"
                        />
                        {errors.billingAddress?.lastName && (
                          <p className="text-sm text-red-500">{errors.billingAddress.lastName.message}</p>
                        )}
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="billingAddress.address1">Address Line 1 *</Label>
                      <Input
                        id="billingAddress.address1"
                        {...register('billingAddress.address1')}
                        placeholder="123 Main Street"
                      />
                      {errors.billingAddress?.address1 && (
                        <p className="text-sm text-red-500">{errors.billingAddress.address1.message}</p>
                      )}
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="billingAddress.city">City *</Label>
                        <Input
                          id="billingAddress.city"
                          {...register('billingAddress.city')}
                          placeholder="Cape Town"
                        />
                        {errors.billingAddress?.city && (
                          <p className="text-sm text-red-500">{errors.billingAddress.city.message}</p>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="billingAddress.province">Province *</Label>
                        <Select
                          value={watch('billingAddress.province')}
                          onValueChange={(value) => setValue('billingAddress.province', value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select province" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Western Cape">Western Cape</SelectItem>
                            <SelectItem value="Eastern Cape">Eastern Cape</SelectItem>
                            <SelectItem value="Northern Cape">Northern Cape</SelectItem>
                            <SelectItem value="Free State">Free State</SelectItem>
                            <SelectItem value="KwaZulu-Natal">KwaZulu-Natal</SelectItem>
                            <SelectItem value="North West">North West</SelectItem>
                            <SelectItem value="Gauteng">Gauteng</SelectItem>
                            <SelectItem value="Mpumalanga">Mpumalanga</SelectItem>
                            <SelectItem value="Limpopo">Limpopo</SelectItem>
                          </SelectContent>
                        </Select>
                        {errors.billingAddress?.province && (
                          <p className="text-sm text-red-500">{errors.billingAddress.province.message}</p>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="billingAddress.postalCode">Postal Code *</Label>
                        <Input
                          id="billingAddress.postalCode"
                          {...register('billingAddress.postalCode')}
                          placeholder="8001"
                        />
                        {errors.billingAddress?.postalCode && (
                          <p className="text-sm text-red-500">{errors.billingAddress.postalCode.message}</p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="mr-2 h-5 w-5" />
                Payment Information
              </CardTitle>
              <CardDescription>
                Payment method and financial details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="paymentMethodId">Payment Method</Label>
                <Select
                  value={watch('paymentMethodId')}
                  onValueChange={(value) => setValue('paymentMethodId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="card">Credit/Debit Card</SelectItem>
                    <SelectItem value="paypal">PayPal</SelectItem>
                    <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                    <SelectItem value="cash_on_delivery">Cash on Delivery</SelectItem>
                    <SelectItem value="store_credit">Store Credit</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="paymentStatus">Payment Status</Label>
                <Select
                  value={watch('paymentStatus')}
                  onValueChange={(value: any) => setValue('paymentStatus', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="authorized">Authorized</SelectItem>
                    <SelectItem value="partially_paid">Partially Paid</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="partially_refunded">Partially Refunded</SelectItem>
                    <SelectItem value="refunded">Refunded</SelectItem>
                    <SelectItem value="voided">Voided</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="status" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ShoppingCart className="mr-2 h-5 w-5" />
                Order Status
              </CardTitle>
              <CardDescription>
                Current order and fulfillment status
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Order Status</Label>
                  <Select
                    value={watch('status')}
                    onValueChange={(value: any) => setValue('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="confirmed">Confirmed</SelectItem>
                      <SelectItem value="processing">Processing</SelectItem>
                      <SelectItem value="shipped">Shipped</SelectItem>
                      <SelectItem value="delivered">Delivered</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                      <SelectItem value="refunded">Refunded</SelectItem>
                      <SelectItem value="returned">Returned</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="fulfillmentStatus">Fulfillment Status</Label>
                  <Select
                    value={watch('fulfillmentStatus')}
                    onValueChange={(value: any) => setValue('fulfillmentStatus', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unfulfilled">Unfulfilled</SelectItem>
                      <SelectItem value="partially_fulfilled">Partially Fulfilled</SelectItem>
                      <SelectItem value="fulfilled">Fulfilled</SelectItem>
                      <SelectItem value="shipped">Shipped</SelectItem>
                      <SelectItem value="delivered">Delivered</SelectItem>
                      <SelectItem value="returned">Returned</SelectItem>
                      <SelectItem value="cancelled">Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="source">Order Source</Label>
                <Select
                  value={watch('source')}
                  onValueChange={(value: any) => setValue('source', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="web">Web</SelectItem>
                    <SelectItem value="mobile">Mobile App</SelectItem>
                    <SelectItem value="pos">Point of Sale</SelectItem>
                    <SelectItem value="api">API</SelectItem>
                    <SelectItem value="admin">Admin Panel</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="sourceIdentifier">Source Identifier</Label>
                <Input
                  id="sourceIdentifier"
                  {...register('sourceIdentifier')}
                  placeholder="Optional source identifier"
                />
              </div>

              {/* Tags */}
              <div className="space-y-2">
                <Label>Tags</Label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {watchedTags?.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                      {tag}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => removeTag(tag)}
                      />
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Add a tag"
                    onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  />
                  <Button type="button" variant="outline" onClick={addTag}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Note</CardTitle>
              <CardDescription>
                Note from the customer about this order
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="customerNote">Customer Note</Label>
                <Textarea
                  id="customerNote"
                  {...register('customerNote')}
                  placeholder="Customer's note about the order..."
                  rows={4}
                />
                {errors.customerNote && (
                  <p className="text-sm text-red-500">{errors.customerNote.message}</p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Internal Notes</CardTitle>
              <CardDescription>
                Private notes for staff use only
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {watchedInternalNotes?.map((note, index) => (
                <div key={index} className="flex items-start gap-2 p-3 bg-muted rounded-md">
                  <div className="flex-1 text-sm">{note}</div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeInternalNote(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}

              <div className="flex gap-2">
                <Textarea
                  value={newInternalNote}
                  onChange={(e) => setNewInternalNote(e.target.value)}
                  placeholder="Add an internal note..."
                  rows={2}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={addInternalNote}
                  disabled={!newInternalNote.trim()}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </form>
  )
}
