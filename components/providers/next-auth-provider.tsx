"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { SessionProvider, useSession } from "next-auth/react"
import { useUser } from "@/lib/hooks/use-user"
import { useAddresses } from "@/lib/hooks/use-addresses"

interface AuthContextType {
  user: any
  addresses: any[]
  isLoading: boolean
  isAuthenticated: boolean
  defaultShippingAddress: any
  defaultBillingAddress: any
  refreshUserData: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function NextAuthProvider({ children }: { children: React.ReactNode }) {
  return (
    <SessionProvider>
      <AuthContextProvider>
        {children}
      </AuthContextProvider>
    </SessionProvider>
  )
}

function AuthContextProvider({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession()
  const { 
    user, 
    isLoading: userLoading, 
    refreshUser 
  } = useUser()
  
  const { 
    addresses, 
    isLoading: addressesLoading,
    getDefaultAddress,
    refreshAddresses
  } = useAddresses()

  const isAuthenticated = status === "authenticated"
  const isLoading = userLoading || addressesLoading || status === "loading"

  const defaultShippingAddress = getDefaultAddress("shipping")
  const defaultBillingAddress = getDefaultAddress("billing")

  const refreshUserData = async () => {
    await Promise.all([refreshUser(), refreshAddresses()])
  }

  const value: AuthContextType = {
    user,
    addresses,
    isLoading,
    isAuthenticated,
    defaultShippingAddress,
    defaultBillingAddress,
    refreshUserData,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useNextAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useNextAuth must be used within a NextAuthProvider')
  }
  return context
}