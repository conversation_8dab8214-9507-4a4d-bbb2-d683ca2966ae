"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState, useEffect } from "react"
import { useAuth } from "@/components/auth-provider"
import {
  LayoutDashboard,
  ShoppingCart,
  User,
  Settings,
  LogOut,
  ChevronsUpDown,
  Heart,
  CreditCard,
  MapPin,
  Package,
  Clock,
  Receipt,
  ChevronRight,
  Home,
  Search,
  Bell,
  HelpCircle,
  Shield
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from "@/components/ui/sidebar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"

// Hook to fetch order counts for sidebar badges
function useOrderCounts() {
  const { token, isAuthenticated } = useAuth()
  const [counts, setCounts] = useState({
    total: 0,
    pending: 0,
    processing: 0,
    shipped: 0,
    delivered: 0
  })

  useEffect(() => {
    if (!isAuthenticated) return

    const fetchCounts = async () => {
      try {
        const response = await fetch('/api/woocommerce/orders/stats', {
          headers: { 
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
        const data = await response.json()

        if (data.success) {
          const stats = data.data
          setCounts({
            total: stats.overview?.totalOrders || 0,
            pending: stats.ordersByStatus?.pending || 0,
            processing: stats.ordersByStatus?.processing || 0,
            shipped: stats.ordersByStatus?.shipped || 0,
            delivered: stats.ordersByStatus?.delivered || 0
          })
        }
      } catch (error) {
        console.error('Failed to fetch order counts:', error)
      }
    }

    fetchCounts()
    // Refresh every 5 minutes
    const interval = setInterval(fetchCounts, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [isAuthenticated, token])

  return counts
}

// Navigation data structure
interface NavigationItem {
  title: string
  url: string
  icon: any
  badge?: string
  badgeVariant?: "default" | "secondary" | "destructive" | "outline"
  description?: string
  items?: {
    title: string
    url: string
    icon?: any
    badge?: string
    description?: string
  }[]
}

// Generate orders navigation with dynamic counts
function getOrdersNavigation(orderCounts: ReturnType<typeof useOrderCounts>): NavigationItem {
  return {
    title: "My Orders",
    url: "/account/orders",
    icon: ShoppingCart,
    badge: orderCounts.total > 0 ? orderCounts.total.toString() : undefined,
    badgeVariant: orderCounts.pending > 0 ? "secondary" as const : "default" as const,
    description: "View and track your orders",
    items: [
      {
        title: "All Orders",
        url: "/account/orders",
        icon: ShoppingCart,
        description: "View all your orders"
      },
      {
        title: "Pending",
        url: "/account/orders?status=pending",
        icon: Clock,
        badge: orderCounts.pending > 0 ? orderCounts.pending.toString() : undefined,
        description: "Orders awaiting processing"
      },
      {
        title: "Shipped",
        url: "/account/orders?status=shipped",
        icon: Package,
        badge: orderCounts.shipped > 0 ? orderCounts.shipped.toString() : undefined,
        description: "Orders in transit"
      },
      {
        title: "Order History",
        url: "/account/orders/history",
        icon: Receipt,
        description: "View your order history"
      },
    ],
  }
}

const navigationData = {
  main: [
    {
      title: "Dashboard",
      url: "/account",
      icon: LayoutDashboard,
      description: "Account overview and summary",
    },
    {
      title: "My Profile",
      url: "/account/profile",
      icon: User,
      description: "Manage your personal information",
    },
  ] as NavigationItem[],
  shopping: [
    {
      title: "Favorites",
      url: "/account/favorites",
      icon: Heart,
      description: "Your favorite products",
    },
  ] as NavigationItem[],
  settings: [
    {
      title: "Addresses",
      url: "/account/addresses",
      icon: MapPin,
      description: "Manage your shipping and billing addresses",
      items: [
        {
          title: "Shipping Addresses",
          url: "/account/addresses/shipping",
          icon: MapPin,
          description: "Manage shipping addresses"
        },
        {
          title: "Billing Addresses",
          url: "/account/addresses/billing",
          icon: Receipt,
          description: "Manage billing addresses"
        },
      ],
    },
    {
      title: "Payment Methods",
      url: "/account/payment-methods",
      icon: CreditCard,
      description: "Manage your payment methods",
    },
    {
      title: "Notifications",
      url: "/account/notifications",
      icon: Bell,
      description: "Manage your notification preferences",
    },
    {
      title: "Security",
      url: "/account/security",
      icon: Shield,
      description: "Manage your account security",
    },
  ] as NavigationItem[],
  help: [
    {
      title: "Help Center",
      url: "/help",
      icon: HelpCircle,
      description: "Get help with your account",
    },
  ] as NavigationItem[],
}

export const CustomerSidebar = ({ ...props }: React.ComponentProps<typeof Sidebar>) => {
  const { user, logout } = useAuth()
  
  const handleSignOut = async () => {
    try {
      await logout()
    } catch (error) {
      console.error("Error signing out:", error)
    }
  }
  const pathname = usePathname()
  const orderCounts = useOrderCounts()
  // Simplified - no complex state management needed
  const [openItems, setOpenItems] = React.useState<Record<string, boolean>>({
    // Default open states for main sections
    'orders': false,
    'addresses': false,
    'payment-methods': false,
    'notifications': false,
    'security': false,
  })

  const toggleItem = (itemKey: string) => {
    setOpenItems(prev => ({
      ...prev,
      [itemKey]: !prev[itemKey]
    }))
  }

  // Auto-open parent menu if child is active
  React.useEffect(() => {
    const newOpenItems = { ...openItems }

    // Check each navigation section for active items
    Object.values(navigationData).flat().forEach(item => {
      if (item.items) {
        const hasActiveChild = item.items.some(subItem => pathname === subItem.url)
        if (hasActiveChild || pathname.startsWith(item.url)) {
          const itemKey = item.url.split('/').pop() || item.title.toLowerCase().replace(/\s+/g, '-')
          newOpenItems[itemKey] = true
        }
      }
    })

    setOpenItems(newOpenItems)
  }, [pathname])

  // Helper function to render navigation items with collapsible support
  const renderNavigationItem = (item: NavigationItem) => {
    const itemKey = item.url.split('/').pop() || item.title.toLowerCase().replace(/\s+/g, '-')
    const isOpen = openItems[itemKey]
    const isActive = pathname === item.url
    const hasActiveChild = item.items?.some(subItem => pathname === subItem.url)

    if (item.items && item.items.length > 0) {
      return (
        <Collapsible
          key={item.title}
          open={isOpen}
          onOpenChange={() => toggleItem(itemKey)}
        >
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton
                tooltip={item.title}
                className={`w-full justify-between ${
                  isActive || hasActiveChild ? 'bg-sidebar-accent text-sidebar-accent-foreground' : ''
                }`}
              >
                <div className="flex items-center gap-2">
                  <item.icon className="h-4 w-4" />
                  <span className="truncate">{item.title}</span>
                </div>
                <div className="flex items-center gap-1">
                  {item.badge && (
                    <Badge
                      variant={item.badgeVariant || "secondary"}
                      className="text-xs px-1.5 py-0.5"
                    >
                      {item.badge}
                    </Badge>
                  )}
                  <ChevronRight
                    className={`h-3 w-3 transition-transform duration-200 ${
                      isOpen ? 'rotate-90' : ''
                    }`}
                  />
                </div>
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                {item.items.map((subItem) => (
                  <SidebarMenuSubItem key={subItem.title}>
                    <SidebarMenuSubButton
                      asChild
                      className={pathname === subItem.url ? 'bg-sidebar-accent text-sidebar-accent-foreground' : ''}
                    >
                      <Link href={subItem.url} className="flex items-center gap-2">
                        {subItem.icon && <subItem.icon className="h-3 w-3" />}
                        <span className="truncate">{subItem.title}</span>
                        {subItem.badge && (
                          <Badge variant="secondary" className="text-xs px-1 py-0">
                            {subItem.badge}
                          </Badge>
                        )}
                      </Link>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                ))}
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
      )
    }

    return (
      <SidebarMenuItem key={item.title}>
        <SidebarMenuButton
          asChild
          tooltip={item.title}
          className={isActive ? 'bg-sidebar-accent text-sidebar-accent-foreground' : ''}
        >
          <Link href={item.url} className="flex items-center gap-2">
            <item.icon className="h-4 w-4" />
            <span className="truncate">{item.title}</span>
            {item.badge && (
              <Badge
                variant={item.badgeVariant || "secondary"}
                className="text-xs px-1.5 py-0.5 ml-auto"
              >
                {item.badge}
              </Badge>
            )}
          </Link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    )
  }

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-pink-600 text-white">
                  <Home className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">Coco Milk Store</span>
                  <span className="truncate text-xs">My Account</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton size="sm" asChild className="w-full justify-start text-muted-foreground">
              <Link href="/search" className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                <span className="truncate">Search products...</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Account</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationData.main.map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Orders</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {/* Dynamic Orders Navigation */}
              {renderNavigationItem(getOrdersNavigation(orderCounts))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Shopping</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationData.shopping.map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Settings</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationData.settings.map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Help & Support</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationData.help.map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={user?.avatar_url || "/avatars/user.png"} alt={user?.display_name || "User"} />
                    <AvatarFallback className="rounded-lg">
                      {user?.display_name?.substring(0, 2).toUpperCase() || "U"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">{user?.display_name || 'Guest'}</span>
                    <span className="truncate text-xs">{user?.email || 'Not signed in'}</span>
                  </div>
                  <ChevronsUpDown className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage src={user?.avatar_url || "/avatars/user.png"} alt={user?.display_name || "User"} />
                      <AvatarFallback className="rounded-lg">
                        {user?.display_name?.substring(0, 2).toUpperCase() || "U"}
                      </AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">{user?.display_name || 'Guest'}</span>
                      <span className="truncate text-xs">{user?.email || 'Not signed in'}</span>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/account/profile">
                    <User className="mr-2 h-4 w-4" />
                    My Profile
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/account/orders">
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    My Orders
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href="/account/notifications">
                    <Bell className="mr-2 h-4 w-4" />
                    Notifications
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut}>
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}