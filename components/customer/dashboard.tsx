'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { ShoppingBag, User, CreditCard, Package, Heart } from "lucide-react"

export function Dashboard() {
  return (
    <div className="space-y-6">
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="wishlist">Wishlist</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  <ShoppingBag className="h-4 w-4 mr-2" />
                  Total Orders
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">5</div>
                <p className="text-xs text-muted-foreground">+2 from last month</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  <Package className="h-4 w-4 mr-2" />
                  Active Orders
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1</div>
                <p className="text-xs text-muted-foreground">Currently in progress</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Total Spent
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">R 1,250.00</div>
                <p className="text-xs text-muted-foreground">Lifetime value</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">
                  <Heart className="h-4 w-4 mr-2" />
                  Wishlist Items
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">3</div>
                <p className="text-xs text-muted-foreground">Saved for later</p>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Recent Orders</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 border rounded-md">
                    <div>
                      <p className="font-medium">#CM-1005</p>
                      <p className="text-sm text-muted-foreground">Coco Milk 500ml x 2</p>
                    </div>
                    <Badge variant="outline">Delivered</Badge>
                  </div>
                  <div className="flex justify-between items-center p-3 border rounded-md">
                    <div>
                      <p className="font-medium">#CM-1004</p>
                      <p className="text-sm text-muted-foreground">Coco Milk 1L</p>
                    </div>
                    <Badge variant="outline">Delivered</Badge>
                  </div>
                  <Button variant="link" className="w-full">View All Orders</Button>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Shipping Address</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <p className="font-medium">Default Address</p>
                  <p className="text-sm text-muted-foreground">
                    123 Coco Lane<br />
                    Coconutville, CN 4567<br />
                    South Africa
                  </p>
                  <Button variant="outline" size="sm">Edit Address</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="orders" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Order History</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Order ID</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Items</TableHead>
                    <TableHead>Total</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">#CM-1005</TableCell>
                    <TableCell>2023-06-15</TableCell>
                    <TableCell>Coco Milk 500ml x 2</TableCell>
                    <TableCell>R 120.00</TableCell>
                    <TableCell><Badge variant="outline">Delivered</Badge></TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm">View Details</Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">#CM-1004</TableCell>
                    <TableCell>2023-05-28</TableCell>
                    <TableCell>Coco Milk 1L</TableCell>
                    <TableCell>R 75.00</TableCell>
                    <TableCell><Badge variant="outline">Delivered</Badge></TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm">View Details</Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">#CM-1003</TableCell>
                    <TableCell>2023-05-10</TableCell>
                    <TableCell>Coco Milk 500ml, Coco Snack Pack</TableCell>
                    <TableCell>R 95.00</TableCell>
                    <TableCell><Badge variant="outline">Delivered</Badge></TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm">View Details</Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">#CM-1002</TableCell>
                    <TableCell>2023-04-03</TableCell>
                    <TableCell>Coco Milk 1L x 2</TableCell>
                    <TableCell>R 140.00</TableCell>
                    <TableCell><Badge variant="outline">Delivered</Badge></TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm">View Details</Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">#CM-1001</TableCell>
                    <TableCell>2023-03-15</TableCell>
                    <TableCell>Coco Milk 500ml</TableCell>
                    <TableCell>R 60.00</TableCell>
                    <TableCell><Badge>Processing</Badge></TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm">Track Order</Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="profile" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Personal Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input id="name" defaultValue="John Doe" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" defaultValue="<EMAIL>" disabled />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input id="phone" defaultValue="+27 12 345 6789" />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline">Cancel</Button>
                  <Button>Save Changes</Button>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Change Password</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Input id="currentPassword" type="password" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input id="newPassword" type="password" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input id="confirmPassword" type="password" />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline">Cancel</Button>
                  <Button>Update Password</Button>
                </div>
              </CardContent>
            </Card>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Shipping Addresses</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="p-4 border rounded-md relative">
                    <Badge className="absolute top-2 right-2">Default</Badge>
                    <p className="font-medium">Home Address</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      John Doe<br />
                      123 Coco Lane<br />
                      Coconutville, CN 4567<br />
                      South Africa
                    </p>
                    <div className="mt-3 flex space-x-2">
                      <Button variant="outline" size="sm">Edit</Button>
                      <Button variant="destructive" size="sm">Delete</Button>
                    </div>
                  </div>
                  <div className="p-4 border rounded-md">
                    <p className="font-medium">Work Address</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      John Doe<br />
                      456 Milk Avenue<br />
                      Dairy Town, DT 7890<br />
                      South Africa
                    </p>
                    <div className="mt-3 flex space-x-2">
                      <Button variant="outline" size="sm">Edit</Button>
                      <Button variant="destructive" size="sm">Delete</Button>
                      <Button variant="outline" size="sm">Set as Default</Button>
                    </div>
                  </div>
                </div>
                <Button>Add New Address</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="p-4 border rounded-md relative">
                    <Badge className="absolute top-2 right-2">Default</Badge>
                    <p className="font-medium">Visa **** 1234</p>
                    <p className="text-sm text-muted-foreground mt-1">
                      Expires 12/24<br />
                      John Doe
                    </p>
                    <div className="mt-3 flex space-x-2">
                      <Button variant="destructive" size="sm">Remove</Button>
                    </div>
                  </div>
                  <div className="p-4 border rounded-md flex items-center justify-center dashed-border opacity-70">
                    <div className="text-center">
                      <CreditCard className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                      <p className="text-sm text-muted-foreground">Add New Card</p>
                    </div>
                  </div>
                </div>
                <Button>Add Payment Method</Button>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Billing History</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Invoice ID</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Amount</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">#INV-1005</TableCell>
                    <TableCell>2023-06-15</TableCell>
                    <TableCell>R 120.00</TableCell>
                    <TableCell><Badge variant="outline">Paid</Badge></TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm">Download PDF</Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">#INV-1004</TableCell>
                    <TableCell>2023-05-28</TableCell>
                    <TableCell>R 75.00</TableCell>
                    <TableCell><Badge variant="outline">Paid</Badge></TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm">Download PDF</Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">#INV-1003</TableCell>
                    <TableCell>2023-05-10</TableCell>
                    <TableCell>R 95.00</TableCell>
                    <TableCell><Badge variant="outline">Paid</Badge></TableCell>
                    <TableCell>
                      <Button variant="outline" size="sm">Download PDF</Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="wishlist" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>My Wishlist</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <div className="border rounded-md overflow-hidden">
                  <div className="bg-muted h-48 flex items-center justify-center">
                    <img 
                      src="/images/coco-milk-500.jpg" 
                      alt="Coco Milk 500ml" 
                      className="object-cover h-full w-full" 
                    />
                  </div>
                  <div className="p-3">
                    <h3 className="font-medium">Coco Milk 500ml</h3>
                    <p className="text-sm text-muted-foreground">R 60.00</p>
                    <div className="mt-2 flex space-x-2">
                      <Button size="sm">Add to Cart</Button>
                      <Button variant="destructive" size="sm">Remove</Button>
                    </div>
                  </div>
                </div>
                <div className="border rounded-md overflow-hidden">
                  <div className="bg-muted h-48 flex items-center justify-center">
                    <img 
                      src="/images/coco-snack-pack.jpg" 
                      alt="Coco Snack Pack" 
                      className="object-cover h-full w-full" 
                    />
                  </div>
                  <div className="p-3">
                    <h3 className="font-medium">Coco Snack Pack</h3>
                    <p className="text-sm text-muted-foreground">R 35.00</p>
                    <div className="mt-2 flex space-x-2">
                      <Button size="sm">Add to Cart</Button>
                      <Button variant="destructive" size="sm">Remove</Button>
                    </div>
                  </div>
                </div>
                <div className="border rounded-md overflow-hidden">
                  <div className="bg-muted h-48 flex items-center justify-center">
                    <img 
                      src="/images/coco-milk-1l.jpg" 
                      alt="Coco Milk 1L" 
                      className="object-cover h-full w-full" 
                    />
                  </div>
                  <div className="p-3">
                    <h3 className="font-medium">Coco Milk 1L</h3>
                    <p className="text-sm text-muted-foreground">R 75.00</p>
                    <div className="mt-2 flex space-x-2">
                      <Button size="sm">Add to Cart</Button>
                      <Button variant="destructive" size="sm">Remove</Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
