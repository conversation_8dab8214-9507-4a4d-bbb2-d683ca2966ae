# Coco Milk Kids E-commerce Platform Information

## Summary
A comprehensive e-commerce platform built with Next.js 15, featuring advanced admin tools, AI integration, and South African payment gateways. The project includes a complete product catalog, shopping cart, checkout, order management, visual page builder, and AI-powered features.

## Structure
- **app/**: Next.js App Router with admin dashboard and customer-facing pages
- **components/**: UI components, admin components, and page builder components
- **lib/**: Core utilities, services, and business logic modules
- **prisma/**: Database schema and migrations for PostgreSQL
- **scripts/**: Setup and utility scripts, including seeders
- **tests/**: API test scripts for product and order management

## Language & Runtime
**Language**: TypeScript/JavaScript
**Version**: Node.js 18+ (as specified in README)
**Framework**: Next.js 15.2.4
**Build System**: Next.js build system
**Package Manager**: pnpm (recommended in README)

## Dependencies
**Main Dependencies**:
- Next.js 15.2.4 - App Router, Server Components
- React 19 - Latest React features
- Prisma ORM - Database management
- Appwrite 17.0.0 - Backend services
- next-auth 5.0.0-beta.28 - Authentication
- @ai-sdk packages - AI integration
- Tailwind CSS - Styling

**Development Dependencies**:
- TypeScript 5 - Type safety
- Prisma Client - Database ORM
- tsx - TypeScript execution
- ESLint - Code linting

## Database
**Type**: PostgreSQL
**ORM**: Prisma
**Schema**: Comprehensive e-commerce data model including:
- Users, roles, and permissions
- Products, variants, and inventory
- Orders and cart management
- Media and content management

## Build & Installation
```bash
# Install dependencies
pnpm install

# Setup environment
cp .env.example .env
# Edit .env with your database and API credentials

# Setup database
pnpm db:push

# Seed database
pnpm seed

# Create admin user
pnpm create-admin

# Start development server
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start
```

## Testing
**Framework**: Custom Node.js test scripts
**Test Location**: `/tests/api/`
**Test Types**: API endpoint tests for products and orders
**Run Command**:
```bash
# Run all tests
node tests/api/run-all.js

# Run specific test
node tests/api/health-check.js
```

## Key Features

### E-commerce Core
- Product catalog with variants and inventory management
- Shopping cart and checkout process
- Order management and processing
- Customer accounts and profiles
- Payment gateway integration (PayFast & Ozow)

### Admin Dashboard
- Product and inventory management
- Order processing and fulfillment
- Customer management
- Analytics and reporting
- Content management

### Page Builder
- Visual drag-and-drop editor
- AI-powered block generation
- Template system
- Custom components

### AI Integration
- Product recommendations
- Shopping assistant chatbot
- Content generation
- Smart search functionality

## Payment Integration
**Gateways**: PayFast and Ozow (South African payment providers)
**Configuration**: Payment settings in environment variables
**Implementation**: Custom payment service with gateway factory pattern

## Authentication
**System**: NextAuth.js with JWT
**User Management**: Custom user model with roles and permissions
**Security**: Password hashing, session management, and rate limiting