import type { NextAuthConfig } from "next-auth"
import Credentials from "next-auth/providers/credentials"
import Google from "next-auth/providers/google"
import Facebook from "next-auth/providers/facebook"
import { LoginSchema } from "@/schemas/auth"
import { getUserByEmail, getUserRoles } from "@/lib/auth/user"
import { compare } from "bcryptjs"

export default {
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    Facebook({
      clientId: process.env.FACEBOOK_CLIENT_ID,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
    }),
    Credentials({
      async authorize(credentials) {
        console.log("Credentials authorize called with:", credentials ? "credentials provided" : "no credentials")
        
        if (!credentials) {
          console.error("No credentials provided")
          return null
        }
        
        const validatedFields = LoginSchema.safeParse(credentials)

        if (!validatedFields.success) {
          console.error("Validation failed:", validatedFields.error)
          return null
        }
        
        const { email, password } = validatedFields.data
        console.log("Attempting login for email:", email)
        
        try {
          // For development/demo purposes, allow a test user
          if (email === "<EMAIL>" && password === "password") {
            console.log("Using demo user account")
            return {
              id: "1",
              name: "Demo User",
              email: "<EMAIL>",
              role: "CUSTOMER",
              firstName: "Demo",
              lastName: "User",
              displayName: "Demo User",
              avatar: null,
              emailVerified: true
            }
          }
          
          // Get user from database
          const user = await getUserByEmail(email)
          
          if (!user) {
            console.log("User not found:", email)
            return null
          }
          
          if (!user.password) {
            console.log("User has no password (possibly OAuth only):", email)
            return null
          }
          
          // Verify password
          const passwordMatch = await compare(password, user.password)
          
          if (!passwordMatch) {
            console.log("Password doesn't match for user:", email)
            return null
          }
          
          console.log("Password verified for user:", email)
          
          // Get user roles
          const roles = await getUserRoles(user.id)
          const roleNames = roles.map(role => role.slug)
          console.log("User roles:", roleNames)
          
          // Return user with role information
          return {
            id: user.id,
            name: user.displayName || `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email,
            email: user.email,
            role: roleNames.includes("admin") ? "ADMIN" : roleNames.includes("customer") ? "CUSTOMER" : "USER",
            firstName: user.firstName,
            lastName: user.lastName,
            displayName: user.displayName,
            avatar: user.avatar,
            emailVerified: user.emailVerified
          }
        } catch (error) {
          console.error("Error in authorize callback:", error)
          return null
        }
      }
    })
  ],
  pages: {
    signIn: "/login",
    error: "/login",
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
        token.firstName = user.firstName
        token.lastName = user.lastName
        token.displayName = user.displayName
        token.avatar = user.avatar
        token.emailVerified = user.emailVerified
      }
      
      return token
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.id = token.sub as string
        session.user.role = token.role as string
        session.user.firstName = token.firstName as string || null
        session.user.lastName = token.lastName as string || null
        session.user.displayName = token.displayName as string || null
        session.user.avatar = token.avatar as string || null
        session.user.emailVerified = token.emailVerified as boolean || false
      }
      
      return session
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url
      return baseUrl
    }
  },
} satisfies NextAuthConfig