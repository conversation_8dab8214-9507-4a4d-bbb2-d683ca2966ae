'use client'

import React, { useState } from 'react'
import { PageBlock, CheckoutBlockConfig } from '../../types'
import { BaseBlock } from '../base-block'
import { useCart } from '@/lib/ecommerce/hooks/use-cart'
import { CheckoutForm } from '@/components/storefront/checkout/checkout-form'
import { CheckoutSummary } from '@/components/storefront/checkout/checkout-summary'
import { OrderConfirmation } from '@/components/storefront/orders/order-confirmation'
import { Button } from '@/components/ui/button'
import { ShoppingBag } from 'lucide-react'
import Link from 'next/link'

interface CheckoutBlockProps {
  block: PageBlock
  isEditing?: boolean
}

export function CheckoutBlock({ block, isEditing = false }: CheckoutBlockProps) {
  const { cart } = useCart()
  const items = cart?.items || []
  const isEmpty = !cart || cart.items.length === 0
  const [orderComplete, setOrderComplete] = useState(false)
  const [orderData, setOrderData] = useState<any>(null)

  // Handle order completion
  const handleOrderComplete = (order: any) => {
    setOrderData(order)
    setOrderComplete(true)
  }

  // Use real cart data - no mock data needed
  const isCartEmpty = isEmpty

  // Empty cart state - exactly like hardcoded
  if (isCartEmpty) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <div className="container px-4 md:px-6 py-16">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <ShoppingBag className="h-16 w-16 text-muted-foreground" />
            <h1 className="text-2xl font-bold font-montserrat">Your cart is empty</h1>
            <p className="text-muted-foreground">Add some items to your cart before proceeding to checkout.</p>
            <Button asChild>
              <Link href="/products">Continue Shopping</Link>
            </Button>
          </div>
        </div>
      </BaseBlock>
    )
  }

  // Order confirmation state - exactly like hardcoded
  if (orderComplete && orderData) {
    return (
      <BaseBlock block={block} isEditing={isEditing}>
        <OrderConfirmation order={orderData} />
      </BaseBlock>
    )
  }

  return (
    <BaseBlock block={block} isEditing={isEditing}>
      {/* Exact copy of hardcoded checkout page structure */}
      <div className="container px-4 md:px-6 py-6 md:py-10">
        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <CheckoutForm onOrderComplete={handleOrderComplete} />
          </div>
          <div>
            <CheckoutSummary />
          </div>
        </div>
      </div>
    </BaseBlock>
  )
}
