import { useState, useEffect } from "react"
import { toast } from "sonner"
import { UserAddress } from "@/components/user/address-list"

// This hook manages user addresses
export function useUserAddresses(userId?: string) {
  const [addresses, setAddresses] = useState<UserAddress[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch addresses when the component mounts or userId changes
  useEffect(() => {
    if (!userId) {
      setAddresses([])
      setIsLoading(false)
      return
    }

    const fetchAddresses = async () => {
      setIsLoading(true)
      setError(null)
      
      try {
        // In a real implementation, this would be an API call
        // For now, we'll simulate fetching from localStorage for demo purposes
        const storedAddresses = localStorage.getItem(`user_addresses_${userId}`)
        const parsedAddresses = storedAddresses ? JSON.parse(storedAddresses) : []
        
        // Add a small delay to simulate network request
        await new Promise(resolve => setTimeout(resolve, 500))
        
        setAddresses(parsedAddresses)
      } catch (err) {
        console.error("Error fetching addresses:", err)
        setError("Failed to load addresses. Please try again.")
        toast.error("Failed to load addresses")
      } finally {
        setIsLoading(false)
      }
    }

    fetchAddresses()
  }, [userId])

  // Save addresses to storage (simulating API)
  const saveAddresses = async (newAddresses: UserAddress[]) => {
    if (!userId) return
    
    try {
      // In a real implementation, this would be an API call
      localStorage.setItem(`user_addresses_${userId}`, JSON.stringify(newAddresses))
      setAddresses(newAddresses)
    } catch (err) {
      console.error("Error saving addresses:", err)
      throw new Error("Failed to save addresses")
    }
  }

  // Add a new address
  const addAddress = async (address: Omit<UserAddress, "id">) => {
    // Generate a unique ID (in a real app, this would come from the backend)
    const newAddress: UserAddress = {
      ...address,
      id: `addr_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    }

    // If this is the first address or marked as default, ensure it's set as default
    let updatedAddresses: UserAddress[]
    
    if (addresses.length === 0 || address.isDefault) {
      // If it's the first address or explicitly set as default
      updatedAddresses = [
        ...addresses.map(addr => ({
          ...addr,
          isDefault: address.type === "both" 
            ? false 
            : addr.type !== address.type && addr.isDefault,
        })),
        newAddress,
      ]
    } else {
      // Just add the new address
      updatedAddresses = [...addresses, newAddress]
    }

    await saveAddresses(updatedAddresses)
    return newAddress
  }

  // Update an existing address
  const updateAddress = async (id: string, updatedAddress: Omit<UserAddress, "id">) => {
    const addressIndex = addresses.findIndex(addr => addr.id === id)
    if (addressIndex === -1) {
      throw new Error("Address not found")
    }

    let updatedAddresses = [...addresses]
    
    // If setting as default, update other addresses of the same type
    if (updatedAddress.isDefault) {
      updatedAddresses = updatedAddresses.map(addr => ({
        ...addr,
        isDefault: addr.id === id 
          ? true 
          : updatedAddress.type === "both" 
            ? false 
            : addr.type !== updatedAddress.type && addr.isDefault,
      }))
    }

    // Update the specific address
    updatedAddresses[addressIndex] = {
      ...updatedAddress,
      id,
    }

    await saveAddresses(updatedAddresses)
    return updatedAddresses[addressIndex]
  }

  // Delete an address
  const deleteAddress = async (id: string) => {
    const addressToDelete = addresses.find(addr => addr.id === id)
    if (!addressToDelete) {
      throw new Error("Address not found")
    }

    const updatedAddresses = addresses.filter(addr => addr.id !== id)
    
    // If we deleted a default address, set a new default if possible
    if (addressToDelete.isDefault) {
      const sameTypeAddresses = updatedAddresses.filter(
        addr => addr.type === addressToDelete.type || addr.type === "both"
      )
      
      if (sameTypeAddresses.length > 0) {
        // Set the first address of the same type as default
        const newDefaultId = sameTypeAddresses[0].id
        return setDefaultAddress(newDefaultId, addressToDelete.type)
      }
    }

    await saveAddresses(updatedAddresses)
  }

  // Set an address as default
  const setDefaultAddress = async (id: string, type: "shipping" | "billing" | "both") => {
    const updatedAddresses = addresses.map(addr => ({
      ...addr,
      isDefault: addr.id === id 
        ? true 
        : type === "both" 
          ? false 
          : (addr.type !== type && addr.type !== "both") && addr.isDefault,
    }))

    await saveAddresses(updatedAddresses)
  }

  return {
    addresses,
    isLoading,
    error,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
  }
}