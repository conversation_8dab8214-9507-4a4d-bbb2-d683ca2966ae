import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { toast } from "sonner"
import { UserAddress } from "@/components/user/address-list"

interface UseAddressesResult {
  addresses: UserAddress[]
  isLoading: boolean
  error: string | null
  addAddress: (address: Omit<UserAddress, "id">) => Promise<UserAddress | null>
  updateAddress: (id: string, address: Omit<UserAddress, "id">) => Promise<UserAddress | null>
  deleteAddress: (id: string) => Promise<boolean>
  setDefaultAddress: (id: string, type: "shipping" | "billing" | "both") => Promise<boolean>
  getAddressById: (id: string) => UserAddress | undefined
  getDefaultAddress: (type: "shipping" | "billing") => UserAddress | undefined
  refreshAddresses: () => Promise<void>
}

export function useAddresses(): UseAddressesResult {
  const { data: session, status } = useSession()
  const [addresses, setAddresses] = useState<UserAddress[]>([])
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch addresses when session changes
  useEffect(() => {
    const fetchAddresses = async () => {
      if (status === "loading") return
      
      if (!session?.user?.id) {
        setAddresses([])
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/users/${session.user.id}/addresses`)
        
        if (!response.ok) {
          throw new Error("Failed to fetch addresses")
        }
        
        const data = await response.json()
        setAddresses(data.addresses)
      } catch (err) {
        console.error("Error fetching addresses:", err)
        setError("Failed to load addresses. Please try again.")
        toast.error("Failed to load addresses")
      } finally {
        setIsLoading(false)
      }
    }

    fetchAddresses()
  }, [session, status])

  // Add a new address
  const addAddress = async (address: Omit<UserAddress, "id">): Promise<UserAddress | null> => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to add an address")
      return null
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/users/${session.user.id}/addresses`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(address),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to add address")
      }

      const data = await response.json()
      const newAddress = data.address

      setAddresses((prev) => [...prev, newAddress])
      toast.success("Address added successfully")
      return newAddress
    } catch (err: any) {
      console.error("Error adding address:", err)
      setError(err.message || "Failed to add address. Please try again.")
      toast.error(err.message || "Failed to add address")
      return null
    } finally {
      setIsLoading(false)
    }
  }

  // Update an existing address
  const updateAddress = async (
    id: string,
    address: Omit<UserAddress, "id">
  ): Promise<UserAddress | null> => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to update an address")
      return null
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/users/${session.user.id}/addresses/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(address),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to update address")
      }

      const data = await response.json()
      const updatedAddress = data.address

      setAddresses((prev) =>
        prev.map((addr) => (addr.id === id ? updatedAddress : addr))
      )
      toast.success("Address updated successfully")
      return updatedAddress
    } catch (err: any) {
      console.error("Error updating address:", err)
      setError(err.message || "Failed to update address. Please try again.")
      toast.error(err.message || "Failed to update address")
      return null
    } finally {
      setIsLoading(false)
    }
  }

  // Delete an address
  const deleteAddress = async (id: string): Promise<boolean> => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to delete an address")
      return false
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/users/${session.user.id}/addresses/${id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to delete address")
      }

      setAddresses((prev) => prev.filter((addr) => addr.id !== id))
      toast.success("Address deleted successfully")
      return true
    } catch (err: any) {
      console.error("Error deleting address:", err)
      setError(err.message || "Failed to delete address. Please try again.")
      toast.error(err.message || "Failed to delete address")
      return false
    } finally {
      setIsLoading(false)
    }
  }

  // Set an address as default
  const setDefaultAddress = async (
    id: string,
    type: "shipping" | "billing" | "both"
  ): Promise<boolean> => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to set a default address")
      return false
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/users/${session.user.id}/addresses/${id}/default`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ type }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to set default address")
      }

      const data = await response.json()
      setAddresses(data.addresses)
      toast.success(`Default ${type === "both" ? "" : type + " "}address updated`)
      return true
    } catch (err: any) {
      console.error("Error setting default address:", err)
      setError(err.message || "Failed to set default address. Please try again.")
      toast.error(err.message || "Failed to set default address")
      return false
    } finally {
      setIsLoading(false)
    }
  }

  // Get address by ID
  const getAddressById = (id: string): UserAddress | undefined => {
    return addresses.find((addr) => addr.id === id)
  }

  // Get default address by type
  const getDefaultAddress = (type: "shipping" | "billing"): UserAddress | undefined => {
    return addresses.find(
      (addr) => 
        addr.isDefault && 
        (addr.type === type || addr.type === "both")
    )
  }

  // Refresh addresses
  const refreshAddresses = async (): Promise<void> => {
    if (!session?.user?.id) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/users/${session.user.id}/addresses`)
      
      if (!response.ok) {
        throw new Error("Failed to refresh addresses")
      }
      
      const data = await response.json()
      setAddresses(data.addresses)
    } catch (err) {
      console.error("Error refreshing addresses:", err)
      setError("Failed to refresh addresses")
    } finally {
      setIsLoading(false)
    }
  }

  return {
    addresses,
    isLoading,
    error,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    getAddressById,
    getDefaultAddress,
    refreshAddresses,
  }
}