import { useSession, signIn as nextAuthSignIn, signOut as nextAuthSignOut } from "next-auth/react"
import { useState } from "react"
import { toast } from "sonner"
import { z } from "zod"

// Schema for login validation
const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
})

// Schema for registration validation
const registerSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
})

interface UseAuthResult {
  user: any
  isLoading: boolean
  isAuthenticated: boolean
  signIn: (email: string, password: string) => Promise<boolean>
  signUp: (data: {
    email: string
    password: string
    firstName: string
    lastName: string
  }) => Promise<boolean>
  signOut: () => Promise<void>
}

export function useAuth(): UseAuthResult {
  const { data: session, status } = useSession()
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const isAuthenticated = status === "authenticated"
  const user = session?.user || null

  // Sign in with email and password
  const signIn = async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true)

      // Validate input
      const validationResult = loginSchema.safeParse({ email, password })
      if (!validationResult.success) {
        const errorMessage = validationResult.error.errors[0]?.message || "Invalid email or password"
        toast.error(errorMessage)
        return false
      }

      // Sign in with NextAuth
      const result = await nextAuthSignIn("credentials", {
        email,
        password,
        redirect: false,
      })

      if (!result?.ok) {
        toast.error(result?.error || "Invalid email or password")
        return false
      }

      toast.success("Signed in successfully")
      return true
    } catch (error) {
      console.error("Sign in error:", error)
      toast.error("An error occurred during sign in")
      return false
    } finally {
      setIsLoading(false)
    }
  }

  // Sign up with email and password
  const signUp = async (data: {
    email: string
    password: string
    firstName: string
    lastName: string
  }): Promise<boolean> => {
    try {
      setIsLoading(true)

      // Validate input
      const validationResult = registerSchema.safeParse(data)
      if (!validationResult.success) {
        const errorMessage = validationResult.error.errors[0]?.message || "Invalid registration data"
        toast.error(errorMessage)
        return false
      }

      // Register user via API
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        toast.error(errorData.error || "Registration failed")
        return false
      }

      // Sign in with the new credentials
      const signInResult = await signIn(data.email, data.password)
      if (signInResult) {
        toast.success("Account created successfully")
      }
      return signInResult
    } catch (error) {
      console.error("Sign up error:", error)
      toast.error("An error occurred during registration")
      return false
    } finally {
      setIsLoading(false)
    }
  }

  // Sign out
  const signOut = async (): Promise<void> => {
    try {
      setIsLoading(true)
      await nextAuthSignOut({ redirect: false })
      toast.success("Signed out successfully")
    } catch (error) {
      console.error("Sign out error:", error)
      toast.error("An error occurred during sign out")
    } finally {
      setIsLoading(false)
    }
  }

  return {
    user,
    isLoading: status === "loading" || isLoading,
    isAuthenticated,
    signIn,
    signUp,
    signOut,
  }
}