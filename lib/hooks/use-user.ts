import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { toast } from "sonner"
import { User } from "@prisma/client"

type UserData = Omit<User, "password" | "createdAt" | "updatedAt"> & {
  addresses?: any[]
}

interface UseUserResult {
  user: UserData | null
  isLoading: boolean
  error: string | null
  updateUser: (data: Partial<UserData>) => Promise<boolean>
  refreshUser: () => Promise<void>
}

export function useUser(): UseUserResult {
  const { data: session, status } = useSession()
  const [user, setUser] = useState<UserData | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch user data when session changes
  useEffect(() => {
    const fetchUserData = async () => {
      if (status === "loading") return
      
      if (!session?.user?.id) {
        setUser(null)
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/users/${session.user.id}`)
        
        if (!response.ok) {
          throw new Error("Failed to fetch user data")
        }
        
        const data = await response.json()
        setUser(data.user)
      } catch (err) {
        console.error("Error fetching user data:", err)
        setError("Failed to load user data. Please try again.")
        toast.error("Failed to load user data")
      } finally {
        setIsLoading(false)
      }
    }

    fetchUserData()
  }, [session, status])

  // Update user data
  const updateUser = async (data: Partial<UserData>): Promise<boolean> => {
    if (!session?.user?.id) {
      toast.error("You must be logged in to update your profile")
      return false
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/users/${session.user.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || "Failed to update user data")
      }

      const updatedData = await response.json()
      setUser((prev) => prev ? { ...prev, ...updatedData.user } : updatedData.user)
      toast.success("Profile updated successfully")
      return true
    } catch (err: any) {
      console.error("Error updating user data:", err)
      setError(err.message || "Failed to update profile. Please try again.")
      toast.error(err.message || "Failed to update profile")
      return false
    } finally {
      setIsLoading(false)
    }
  }

  // Refresh user data
  const refreshUser = async (): Promise<void> => {
    if (!session?.user?.id) return

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/users/${session.user.id}`)
      
      if (!response.ok) {
        throw new Error("Failed to refresh user data")
      }
      
      const data = await response.json()
      setUser(data.user)
    } catch (err) {
      console.error("Error refreshing user data:", err)
      setError("Failed to refresh user data")
    } finally {
      setIsLoading(false)
    }
  }

  return {
    user,
    isLoading,
    error,
    updateUser,
    refreshUser,
  }
}