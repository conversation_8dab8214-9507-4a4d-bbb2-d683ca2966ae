// Appwrite client setup and initialization
import { Client as WebClient, Account, Databases as WebDatabases, Storage as WebStorage, Functions as WebFunctions } from 'appwrite'
import { Client as NodeClient, Databases as NodeDatabases, Storage as NodeStorage, Functions as NodeFunctions } from 'node-appwrite'
import { appwriteConfig, appwriteServerConfig, validateAppwriteConfig } from './config'

// Client-side Appwrite client (for browser)
let clientInstance: WebClient | null = null

export function getAppwriteClient(): WebClient {
  if (!clientInstance) {
    if (!validateAppwriteConfig(appwriteConfig)) {
      throw new Error('Invalid Appwrite configuration. Please check your environment variables.')
    }

    try {
      clientInstance = new WebClient()
      // Initialize client with required configuration using separate calls
      clientInstance.setEndpoint(appwriteConfig.endpoint)
      clientInstance.setProject(appwriteConfig.projectId)

      // Set locale for South African users
      clientInstance.setLocale('en-ZA')
      
      console.log('✅ Appwrite client initialized successfully')
    } catch (error) {
      console.error('Failed to initialize Appwrite client:', error)
      throw new Error('Failed to initialize Appwrite client. Please check your configuration.')
    }
  }

  return clientInstance
}

// Server-side Appwrite client (for API routes)
let serverClientInstance: NodeClient | null = null

export function getAppwriteServerClient(): NodeClient {
  if (!serverClientInstance) {
    // Strict validation for server-side config
    if (!appwriteServerConfig.endpoint || !appwriteServerConfig.projectId || !appwriteServerConfig.apiKey) {
      console.warn('Missing required Appwrite server configuration. Appwrite features will be disabled.')
      console.warn('Please set APPWRITE_ENDPOINT, APPWRITE_PROJECT_ID, and APPWRITE_API_KEY environment variables.')
      throw new Error(
        'Missing required Appwrite server configuration. Please set APPWRITE_ENDPOINT, APPWRITE_PROJECT_ID, and APPWRITE_API_KEY environment variables.'
      )
    }

    try {
      serverClientInstance = new NodeClient()
      // Initialize server client with required configuration using separate calls
      serverClientInstance.setEndpoint(appwriteServerConfig.endpoint)
      serverClientInstance.setProject(appwriteServerConfig.projectId)
      serverClientInstance.setKey(appwriteServerConfig.apiKey)
      
      console.log('✅ Appwrite server client (from client.ts) initialized successfully')
    } catch (error) {
      console.error('Failed to initialize Appwrite server client:', error)
      throw new Error('Failed to initialize Appwrite server client. Please check your configuration.')
    }
  }

  return serverClientInstance
}

// Safe version that returns null instead of throwing
export function getAppwriteServerClientSafe(): NodeClient | null {
  try {
    return getAppwriteServerClient()
  } catch (error) {
    console.warn('Appwrite server client not available:', error)
    return null
  }
}

// Service instances for client-side
export class AppwriteServices {
  private static instance: AppwriteServices
  public readonly client: WebClient
  public readonly account: Account
  public readonly databases: WebDatabases
  public readonly storage: WebStorage
  public readonly functions: WebFunctions

  private constructor() {
    this.client = getAppwriteClient()
    this.account = new Account(this.client)
    this.databases = new WebDatabases(this.client)
    this.storage = new WebStorage(this.client)
    this.functions = new WebFunctions(this.client)
  }

  static getInstance(): AppwriteServices {
    if (!AppwriteServices.instance) {
      AppwriteServices.instance = new AppwriteServices()
    }
    return AppwriteServices.instance
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      // Try to get account info - this is a simple way to check if we're connected
      await this.account.get()
      return true
    } catch (error) {
      // If account.get() fails, try a simpler check
      try {
        // For anonymous users, this will still work to test connectivity
        await this.account.createAnonymousSession()
        await this.account.deleteSession('current')
        return true
      } catch (secondError) {
        console.warn('Appwrite health check failed:', error)
        return false
      }
    }
  }
}

// Service instances for server-side
export class AppwriteServerServices {
  private static instance: AppwriteServerServices | null = null
  public readonly client: NodeClient | null
  public readonly databases: NodeDatabases | null
  public readonly storage: NodeStorage | null
  public readonly functions: NodeFunctions | null
  public readonly isAvailable: boolean

  private constructor() {
    const client = getAppwriteServerClientSafe()
    this.client = client
    this.isAvailable = client !== null
    
    if (client) {
      this.databases = new NodeDatabases(client)
      this.storage = new NodeStorage(client)
      this.functions = new NodeFunctions(client)
    } else {
      this.databases = null
      this.storage = null
      this.functions = null
    }
  }

  static getInstance(): AppwriteServerServices {
    if (!AppwriteServerServices.instance) {
      AppwriteServerServices.instance = new AppwriteServerServices()
    }
    return AppwriteServerServices.instance
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    if (!this.isAvailable || !this.storage) {
      return false
    }
    
    try {
      // Try to list files in the configured bucket to test connectivity
      await this.storage.listFiles(appwriteConfig.storageBucketId)
      return true
    } catch (error) {
      console.warn('Appwrite server health check failed:', error)
      return false
    }
  }
}

// Utility functions
export function isClientSide(): boolean {
  return typeof window !== 'undefined'
}

export function getServices() {
  if (isClientSide()) {
    return AppwriteServices.getInstance()
  } else {
    return AppwriteServerServices.getInstance()
  }
}

// Error handling wrapper
export async function withAppwriteErrorHandling<T>(
  operation: () => Promise<T>,
  errorMessage: string = 'Appwrite operation failed'
): Promise<T> {
  try {
    return await operation()
  } catch (error: any) {
    console.error(`${errorMessage}:`, error)
    
    // Handle specific Appwrite errors
    if (error.code) {
      switch (error.code) {
        case 401:
          throw new Error('Authentication required')
        case 403:
          throw new Error('Permission denied')
        case 404:
          throw new Error('Resource not found')
        case 429:
          throw new Error('Rate limit exceeded')
        case 500:
          throw new Error('Server error')
        default:
          throw new Error(error.message || errorMessage)
      }
    }
    
    throw new Error(errorMessage)
  }
}

// Connection status
export class AppwriteConnectionManager {
  private static instance: AppwriteConnectionManager
  private isConnected: boolean = false
  private connectionListeners: Array<(connected: boolean) => void> = []

  private constructor() {
    this.checkConnection()
  }

  static getInstance(): AppwriteConnectionManager {
    if (!AppwriteConnectionManager.instance) {
      AppwriteConnectionManager.instance = new AppwriteConnectionManager()
    }
    return AppwriteConnectionManager.instance
  }

  async checkConnection(): Promise<boolean> {
    try {
      const services = getServices()
      this.isConnected = await services.healthCheck()
      this.notifyListeners()
      return this.isConnected
    } catch (error) {
      this.isConnected = false
      this.notifyListeners()
      return false
    }
  }

  onConnectionChange(listener: (connected: boolean) => void): () => void {
    this.connectionListeners.push(listener)
    
    // Return unsubscribe function
    return () => {
      const index = this.connectionListeners.indexOf(listener)
      if (index > -1) {
        this.connectionListeners.splice(index, 1)
      }
    }
  }

  private notifyListeners(): void {
    this.connectionListeners.forEach(listener => listener(this.isConnected))
  }

  get connected(): boolean {
    return this.isConnected
  }
}

// Export default instances
export const appwrite = AppwriteServices.getInstance()
// Don't auto-initialize server services to avoid early errors
export const getAppwriteServer = () => AppwriteServerServices.getInstance()
export const connectionManager = AppwriteConnectionManager.getInstance()
