// Appwrite Storage service for file management

import { ID, Models, Query } from 'appwrite'
import { getServices, withAppwriteErrorHandling } from './client'
import { 
  appwriteConfig, 
  STORAGE_BUCKETS,
  APPWRITE_COLLECTIONS, 
  APPWRITE_ERRORS,
  isValidFileType,
  isValidFileSize,
  getFileUrl,
  getFileDownloadUrl,
  generateFileId
} from './config'

export interface UploadResult {
  fileId: string
  url: string
  downloadUrl: string
  file: Models.File
}

export interface UploadOptions {
  bucketId?: string
  fileId?: string
  permissions?: string[]
  onProgress?: (progress: number) => void
}

export interface FileValidation {
  maxSize: number
  allowedTypes: string[]
}

export class AppwriteStorageService {
  private static instance: AppwriteStorageService

  static getInstance(): AppwriteStorageService {
    if (!AppwriteStorageService.instance) {
      AppwriteStorageService.instance = new AppwriteStorageService()
    }
    return AppwriteStorageService.instance
  }

  /**
   * Upload a file to Appwrite storage
   */
  async uploadFile(
    file: File,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    return withAppwriteErrorHandling(async () => {
      const {
        bucketId = appwriteConfig.storageBucketId,
        fileId = generateFileId(),
        permissions = [],
        onProgress
      } = options

      // Validate inputs
      if (!file) {
        throw new Error('File is required')
      }
      if (!bucketId) {
        throw new Error('Bucket ID is required')
      }

      // Validate file
      this.validateFile(file, bucketId)

      const services = getServices()
      if (!services.storage) {
        throw new Error('Storage service is not available')
      }

      // Upload file with progress tracking
      const uploadedFile = await services.storage.createFile(
        bucketId,
        fileId,
        file,
        permissions
      )

      if (!uploadedFile || !uploadedFile.$id) {
        throw new Error('File upload failed - no file ID returned')
      }

      // Generate URLs
      const url = getFileUrl(bucketId, uploadedFile.$id)
      const downloadUrl = getFileDownloadUrl(bucketId, uploadedFile.$id)

      return {
        fileId: uploadedFile.$id,
        url,
        downloadUrl,
        file: uploadedFile
      }
    }, APPWRITE_ERRORS.UPLOAD_FAILED)
  }

  /**
   * Upload multiple files
   */
  async uploadFiles(
    files: File[],
    options: UploadOptions = {}
  ): Promise<UploadResult[]> {
    if (!files || files.length === 0) {
      throw new Error('No files provided for upload')
    }

    const results: UploadResult[] = []
    const errors: Array<{ file: string; error: string }> = []
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const fileOptions = {
        ...options,
        onProgress: options.onProgress 
          ? (progress: number) => {
              const totalProgress = ((i * 100) + progress) / files.length
              options.onProgress!(totalProgress)
            }
          : undefined
      }
      
      try {
        const result = await this.uploadFile(file, fileOptions)
        results.push(result)
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed'
        console.error(`Failed to upload file ${file.name}:`, errorMessage)
        errors.push({ file: file.name, error: errorMessage })
        
        // Continue with other files instead of throwing immediately
        // This allows partial success in batch uploads
      }
    }

    // If all files failed, throw an error
    if (results.length === 0 && errors.length > 0) {
      throw new Error(`All file uploads failed. First error: ${errors[0].error}`)
    }

    // If some files failed, log a warning but return successful uploads
    if (errors.length > 0) {
      console.warn(`${errors.length} out of ${files.length} files failed to upload:`, errors)
    }

    return results
  }

  /**
   * Get file information
   */
  async getFile(bucketId: string, fileId: string): Promise<Models.File> {
    return withAppwriteErrorHandling(async () => {
      if (!bucketId) {
        throw new Error('Bucket ID is required')
      }
      if (!fileId) {
        throw new Error('File ID is required')
      }

      const services = getServices()
      if (!services.storage) {
        throw new Error('Storage service is not available')
      }

      return await services.storage.getFile(bucketId, fileId)
    }, APPWRITE_ERRORS.DOWNLOAD_FAILED)
  }

  /**
   * Delete a file
   */
  async deleteFile(bucketId: string, fileId: string): Promise<void> {
    return withAppwriteErrorHandling(async () => {
      if (!bucketId) {
        throw new Error('Bucket ID is required')
      }
      if (!fileId) {
        throw new Error('File ID is required')
      }

      const services = getServices()
      if (!services.storage) {
        throw new Error('Storage service is not available')
      }

      await services.storage.deleteFile(bucketId, fileId)
    }, 'Failed to delete file')
  }

  /**
   * Delete multiple files
   */
  async deleteFiles(bucketId: string, fileIds: string[]): Promise<Array<{id: string, success: boolean, error?: string}>> {
    if (!bucketId) {
      throw new Error('Bucket ID is required')
    }
    if (!fileIds || fileIds.length === 0) {
      throw new Error('No file IDs provided for deletion')
    }

    const results: Array<{id: string, success: boolean, error?: string}> = []

    for (const fileId of fileIds) {
      try {
        await this.deleteFile(bucketId, fileId)
        results.push({ id: fileId, success: true })
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Delete failed'
        console.error(`Failed to delete file ${fileId}:`, errorMessage)
        results.push({ id: fileId, success: false, error: errorMessage })
      }
    }

    return results
  }

  /**
   * Get file preview URL
   */
  getFilePreview(
    bucketId: string,
    fileId: string,
    width?: number,
    height?: number,
    gravity?: string,
    quality?: number,
    borderWidth?: number,
    borderColor?: string,
    borderRadius?: number,
    opacity?: number,
    rotation?: number,
    background?: string,
    output?: string
  ): string {
    if (!bucketId) {
      throw new Error('Bucket ID is required')
    }
    if (!fileId) {
      throw new Error('File ID is required')
    }

    const services = getServices()
    if (!services.storage) {
      throw new Error('Storage service is not available')
    }

    // Call getFilePreview with explicit parameters to avoid TypeScript issues
    const previewUrl = services.storage.getFilePreview(
      bucketId,
      fileId,
      width,
      height,
      gravity,
      quality,
      borderWidth,
      borderColor,
      borderRadius,
      opacity,
      rotation,
      background,
      output
    )

    // Appwrite SDK returns a URL string directly
    return previewUrl.toString()
  }

  /**
   * List files in a bucket
   */
  async listFiles(
    bucketId: string,
    queries?: string[]
  ): Promise<Models.FileList> {
    return withAppwriteErrorHandling(async () => {
      if (!bucketId) {
        throw new Error('Bucket ID is required')
      }

      const services = getServices()
      if (!services.storage) {
        throw new Error('Storage service is not available')
      }

      return await services.storage.listFiles(bucketId, queries)
    }, 'Failed to list files')
  }

  /**
   * Get file URL for viewing
   */
  getFileUrl(bucketId: string, fileId: string): string {
    return getFileUrl(bucketId, fileId)
  }

  /**
   * Get file download URL
   */
  getFileDownloadUrl(bucketId: string, fileId: string): string {
    return getFileDownloadUrl(bucketId, fileId)
  }

  /**
   * Validate file before upload
   */
  private validateFile(file: File, bucketId: string): void {
    if (!file) {
      throw new Error('File is required for validation')
    }
    if (!bucketId) {
      throw new Error('Bucket ID is required for validation')
    }

    // Get bucket configuration
    const bucketConfig = Object.values(STORAGE_BUCKETS).find(
      bucket => bucket.id === bucketId
    )

    if (!bucketConfig) {
      throw new Error(`Unknown bucket: ${bucketId}`)
    }

    // Validate file type
    if (!isValidFileType(file, bucketConfig.allowedFileTypes)) {
      throw new Error(`${APPWRITE_ERRORS.INVALID_FILE_TYPE}. Allowed types: ${bucketConfig.allowedFileTypes.join(', ')}`)
    }

    // Validate file size
    if (!isValidFileSize(file, bucketConfig.maxFileSize)) {
      throw new Error(`${APPWRITE_ERRORS.FILE_TOO_LARGE}. Maximum size: ${Math.round(bucketConfig.maxFileSize / 1024 / 1024)}MB`)
    }

    // Additional validations
    if (!file.name || file.name.trim().length === 0) {
      throw new Error('File must have a valid name')
    }

    if (file.name.length > 255) {
      throw new Error('File name is too long (maximum 255 characters)')
    }
  }

  /**
   * Get optimized image URL for product images
   */
  getOptimizedImageUrl(
    fileId: string,
    width: number = 800,
    height: number = 600,
    quality: number = 80
  ): string {
    return this.getFilePreview(
      appwriteConfig.storageBucketId,
      fileId,
      width,
      height,
      'center',
      quality,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      'webp'
    )
  }

  /**
   * Get thumbnail URL for product images
   */
  getThumbnailUrl(fileId: string, size: number = 200): string {
    return this.getFilePreview(
      appwriteConfig.storageBucketId,
      fileId,
      size,
      size,
      'center',
      80,
      undefined,
      undefined,
      8, // border radius for rounded corners
      undefined,
      undefined,
      undefined,
      'webp'
    )
  }
}

// Export singleton instance
export const storageService = AppwriteStorageService.getInstance()

// Utility functions for common operations
export async function uploadProductImage(file: File): Promise<UploadResult> {
  if (!file) {
    throw new Error('File is required for product image upload')
  }
  
  return storageService.uploadFile(file, {
    bucketId: STORAGE_BUCKETS.PRODUCT_IMAGES.id
  })
}

export async function uploadProductImages(files: File[]): Promise<UploadResult[]> {
  if (!files || files.length === 0) {
    throw new Error('Files are required for product images upload')
  }
  
  return storageService.uploadFiles(files, {
    bucketId: STORAGE_BUCKETS.PRODUCT_IMAGES.id
  })
}

export async function uploadUserAvatar(file: File): Promise<UploadResult> {
  if (!file) {
    throw new Error('File is required for user avatar upload')
  }
  
  return storageService.uploadFile(file, {
    bucketId: STORAGE_BUCKETS.USER_AVATARS.id
  })
}

export function getProductImageUrl(fileId: string, optimized: boolean = true): string {
  if (!fileId) {
    throw new Error('File ID is required to get product image URL')
  }
  
  if (optimized) {
    return storageService.getOptimizedImageUrl(fileId)
  }
  return getFileUrl(appwriteConfig.storageBucketId, fileId)
}

export function getProductThumbnailUrl(fileId: string): string {
  if (!fileId) {
    throw new Error('File ID is required to get product thumbnail URL')
  }
  
  return storageService.getThumbnailUrl(fileId)
}

// Enhanced Media Management Helper Functions

// Types for media management
export interface MediaFile {
  id: string
  name: string
  mimeType: string
  size: number
  createdAt: string
  updatedAt: string
  url: string
  previewUrl: string | null
  downloadUrl: string
  type: 'image' | 'video' | 'audio' | 'document' | 'other'
  metadata: {
    folder: string
    alt: string
    title: string
    description: string
    tags: string[]
  }
}

export interface MediaUploadResult {
  id?: string
  name: string
  mimeType?: string
  size?: number
  url?: string
  previewUrl?: string | null
  downloadUrl?: string
  type?: string
  metadata?: MediaFile['metadata']
  error?: string
}

export interface PaginationParams {
  page: number
  limit: number
  search: string
  type: 'all' | 'image' | 'video' | 'audio' | 'document'
  folder: string
  sortBy: 'createdAt' | 'name' | 'size'
  sortOrder: 'asc' | 'desc'
}

// Constants for media management
export const MAX_FILES_PER_UPLOAD = 10
export const SUPPORTED_PREVIEW_FORMATS = [
  'image/jpeg', 
  'image/jpg', 
  'image/png', 
  'image/webp', 
  'image/gif', 
  'image/bmp', 
  'image/tiff', 
  'image/svg+xml'
]

/**
 * Get file type from MIME type
 */
export function getFileType(mimeType: string): 'image' | 'video' | 'audio' | 'document' | 'other' {
  if (!mimeType || typeof mimeType !== 'string') {
    return 'other'
  }
  
  const normalizedMimeType = mimeType.toLowerCase().trim()
  
  if (normalizedMimeType.startsWith('image/')) return 'image'
  if (normalizedMimeType.startsWith('video/')) return 'video'
  if (normalizedMimeType.startsWith('audio/')) return 'audio'
  if (normalizedMimeType.startsWith('application/') || normalizedMimeType.startsWith('text/')) return 'document'
  return 'other'
}

/**
 * Parse and validate GET request parameters
 */
export function parseAndValidateGetParams(url: string): { success: true; data: PaginationParams } | { success: false; error: string } {
  try {
    const { searchParams } = new URL(url)
    
    const page = Math.max(1, parseInt(searchParams.get('page') || '1'))
    const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '20'))) // Cap at 100
    const search = searchParams.get('search')?.trim() || ''
    const type = searchParams.get('type') || 'all'
    const folder = searchParams.get('folder')?.trim() || ''
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    // Validate type parameter
    if (!['all', 'image', 'video', 'audio', 'document'].includes(type)) {
      return { success: false, error: 'Invalid type parameter' }
    }

    // Validate sortBy parameter
    if (!['createdAt', 'name', 'size'].includes(sortBy)) {
      return { success: false, error: 'Invalid sortBy parameter' }
    }

    // Validate sortOrder parameter
    if (!['asc', 'desc'].includes(sortOrder)) {
      return { success: false, error: 'Invalid sortOrder parameter' }
    }

    return {
      success: true,
      data: {
        page,
        limit,
        search,
        type: type as PaginationParams['type'],
        folder,
        sortBy: sortBy as PaginationParams['sortBy'],
        sortOrder: sortOrder as PaginationParams['sortOrder']
      }
    }
  } catch (error) {
    return { success: false, error: 'Invalid URL parameters' }
  }
}

/**
 * Build Appwrite queries for file listing
 */
export function buildQueries({ limit, offset, search, sortBy, sortOrder }: {
  limit: number
  offset: number
  search: string
  sortBy: string
  sortOrder: string
}) {
  const queries = [
    Query.limit(limit),
    Query.offset(offset)
  ]

  // Add search filter
  if (search) {
    queries.push(Query.search('name', search))
  }

  // Add sorting
  if (sortOrder === 'desc') {
    queries.push(Query.orderDesc(sortBy === 'createdAt' ? '$createdAt' : sortBy))
  } else {
    queries.push(Query.orderAsc(sortBy === 'createdAt' ? '$createdAt' : sortBy))
  }

  return queries
}

/**
 * Filter files by type and folder
 */
export function filterFilesByType(files: any[], type: string, folder: string) {
  if (!files || !Array.isArray(files)) {
    return []
  }

  let filtered = files

  // Filter by type
  if (type && type !== 'all') {
    filtered = filtered.filter(file => {
      if (!file || !file.mimeType) {
        return false
      }
      
      const mimeType = file.mimeType.toLowerCase().trim()
      switch (type) {
        case 'image':
          return mimeType.startsWith('image/')
        case 'video':
          return mimeType.startsWith('video/')
        case 'audio':
          return mimeType.startsWith('audio/')
        case 'document':
          return mimeType.startsWith('application/') || mimeType.startsWith('text/')
        default:
          return true
      }
    })
  }

  // Filter by folder (if implemented in metadata)
  if (folder && folder !== 'root') {
    // This would require storing folder info in file metadata
    // For now, we'll skip this filter as Appwrite doesn't natively support folders
    // In the future, this could be implemented by checking file metadata
  }

  return filtered
}

/**
 * Transform Appwrite file to MediaFile format
 */
export async function transformFileResponse(file: any, bucketId: string, requestedFolder: string): Promise<MediaFile> {
  if (!file) {
    throw new Error('File object is required for transformation')
  }
  if (!bucketId) {
    throw new Error('Bucket ID is required for transformation')
  }

  // Validate required file properties
  if (!file.$id || !file.name || !file.mimeType) {
    throw new Error('File object is missing required properties ($id, name, mimeType)')
  }

  try {
    return {
      id: file.$id,
      name: file.name,
      mimeType: file.mimeType,
      size: file.sizeOriginal || file.size || 0,
      createdAt: file.$createdAt || new Date().toISOString(),
      updatedAt: file.$updatedAt || new Date().toISOString(),
      url: storageService.getFileUrl(bucketId, file.$id),
      previewUrl: shouldGeneratePreview(file.mimeType)
        ? (() => {
            try {
              return storageService.getFilePreview(bucketId, file.$id, 300, 300, 'center', 85)
            } catch (error) {
              console.warn(`Failed to generate preview URL for file ${file.$id}:`, error)
              return null
            }
          })()
        : null,
      downloadUrl: storageService.getFileDownloadUrl(bucketId, file.$id),
      type: getFileType(file.mimeType),
      metadata: {
        folder: requestedFolder || 'root',
        alt: generateAltText(file.name),
        title: formatFileName(file.name),
        description: '',
        tags: []
      }
    }
  } catch (error) {
    console.error('Error transforming file response:', error)
    throw new Error(`Failed to transform file response: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

/**
 * Validate upload request
 */
export function validateUploadRequest(files: File[], folder: string): { success: true } | { success: false; error: string } {
  if (!files || files.length === 0) {
    return { success: false, error: 'No files provided' }
  }

  if (files.length > MAX_FILES_PER_UPLOAD) {
    return { success: false, error: `Maximum ${MAX_FILES_PER_UPLOAD} files allowed per upload` }
  }

  if (folder.length > 100) {
    return { success: false, error: 'Folder name too long (max 100 characters)' }
  }

  return { success: true }
}

/**
 * Validate single file for upload
 */
export function validateSingleFile(file: File, bucketConfig: typeof STORAGE_BUCKETS.MEDIA_LIBRARY, index: number): { success: true } | { success: false; error: string } {
  // Check file type
  if (!isValidFileType(file, bucketConfig.allowedFileTypes)) {
    return { success: false, error: `File ${index}: ${APPWRITE_ERRORS.INVALID_FILE_TYPE} (${file.type})` }
  }

  // Check file size
  if (!isValidFileSize(file, bucketConfig.maxFileSize)) {
    return { success: false, error: `File ${index}: ${APPWRITE_ERRORS.FILE_TOO_LARGE} (${formatFileSize(file.size)} > ${formatFileSize(bucketConfig.maxFileSize)})` }
  }

  // Check file name
  if (!file.name || file.name.trim().length === 0) {
    return { success: false, error: `File ${index}: Invalid file name` }
  }

  if (file.name.length > 255) {
    return { success: false, error: `File ${index}: File name too long (max 255 characters)` }
  }

  return { success: true }
}

/**
 * Check if file type supports preview generation
 */
export function shouldGeneratePreview(mimeType: string): boolean {
  if (!mimeType || typeof mimeType !== 'string') {
    return false
  }
  
  const normalizedMimeType = mimeType.toLowerCase().trim()
  
  // Check exact matches first
  if (SUPPORTED_PREVIEW_FORMATS.includes(normalizedMimeType)) {
    return true
  }
  
  // Check if it's any image type as fallback
  return normalizedMimeType.startsWith('image/')
}

/**
 * Generate unique file ID for Appwrite (max 36 chars, no periods)
 * The actual filename with extension will be stored in metadata
 */
export function generateUniqueFileName(originalName: string): string {
  if (!originalName || typeof originalName !== 'string') {
    throw new Error('Original filename is required and must be a string')
  }

  const timestamp = Date.now().toString(36) // Convert to base36 for shorter string
  const random = Math.random().toString(36).substring(2, 8)
  const nameWithoutExtension = originalName.replace(/\.[^/.]+$/, '')
  const sanitizedName = nameWithoutExtension.replace(/[^a-zA-Z0-9-_]/g, '_')
  
  // Create a short prefix from the original name (max 10 chars)
  const shortName = sanitizedName.substring(0, 10) || 'file' // fallback if name is empty after sanitization
  
  // Combine: shortName + timestamp + random (ensure total length <= 36)
  const fileId = `${shortName}_${timestamp}_${random}`
  
  // Ensure it doesn't exceed 36 characters and starts with alphanumeric
  let finalId = fileId.length > 36 ? fileId.substring(0, 36) : fileId
  
  // Ensure it starts with alphanumeric character (Appwrite requirement)
  if (!/^[a-zA-Z0-9]/.test(finalId)) {
    finalId = 'f' + finalId.substring(1)
  }
  
  return finalId
}

/**
 * Sanitize folder name
 */
export function sanitizeFolder(folder: string): string {
  return folder.replace(/[^a-zA-Z0-9-_/]/g, '_').replace(/\/+/g, '/').trim() || 'root'
}

/**
 * Generate alt text from filename
 */
export function generateAltText(filename: string): string {
  return filename
    .replace(/\.[^/.]+$/, '') // Remove extension
    .replace(/[_-]/g, ' ') // Replace underscores and dashes with spaces
    .replace(/\b\w/g, l => l.toUpperCase()) // Capitalize first letter of each word
}

/**
 * Format filename for display
 */
export function formatFileName(filename: string): string {
  return filename
    .replace(/\.[^/.]+$/, '') // Remove extension
    .replace(/[_-]/g, ' ') // Replace underscores and dashes with spaces
}

/**
 * Parse tags from string
 */
export function parseTags(tagsString: string): string[] {
  if (!tagsString) return []
  
  return tagsString
    .split(',')
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0)
    .slice(0, 10) // Limit to 10 tags
}

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * Upload files to media library with enhanced metadata
 */
export async function uploadMediaFiles(
  files: File[],
  options: {
    folder?: string
    alt?: string
    title?: string
    description?: string
    tags?: string
  } = {}
): Promise<MediaUploadResult[]> {
  const {
    folder = 'root',
    alt = '',
    title = '',
    description = '',
    tags = ''
  } = options

  console.log('uploadMediaFiles called with:', {
    fileCount: files.length,
    folder,
    alt,
    title,
    description,
    tags
  })

  // Validate upload request
  const validation = validateUploadRequest(files, folder)
  if (!validation.success) {
    console.error('Upload validation failed:', validation.error)
    throw new Error(validation.error)
  }

  const bucketId = STORAGE_BUCKETS.MEDIA_LIBRARY.id
  const uploadResults: MediaUploadResult[] = []
  const bucketConfig = STORAGE_BUCKETS.MEDIA_LIBRARY

  console.log('Using bucket configuration:', {
    bucketId,
    maxFileSize: bucketConfig.maxFileSize,
    allowedFileTypes: bucketConfig.allowedFileTypes.length
  })

  // Process files with better error handling
  for (let i = 0; i < files.length; i++) {
    const file = files[i]
    console.log(`Processing file ${i + 1}/${files.length}:`, {
      name: file.name,
      type: file.type,
      size: file.size
    })
    
    try {
      // Comprehensive file validation
      const fileValidation = validateSingleFile(file, bucketConfig, i + 1)
      if (!fileValidation.success) {
        console.error(`Validation failed for ${file.name}:`, fileValidation.error)
        throw new Error(fileValidation.error)
      }

      // Upload file with enhanced metadata
      const uniqueFileName = generateUniqueFileName(file.name)
      console.log(`Uploading ${file.name} as ${uniqueFileName} (length: ${uniqueFileName.length})`)
      
      // Validate the generated file ID (Appwrite allows: a-z, A-Z, 0-9, period, hyphen, underscore, can't start with special char)
      if (uniqueFileName.length > 36) {
        throw new Error(`Generated file ID is too long: ${uniqueFileName.length} chars (max 36)`)
      }
      if (!/^[a-zA-Z0-9][a-zA-Z0-9._-]*$/.test(uniqueFileName)) {
        throw new Error(`Generated file ID contains invalid characters: ${uniqueFileName}`)
      }
      
      const result = await storageService.uploadFile(file, {
        bucketId,
        fileId: uniqueFileName,
        permissions: ['read("any")'] // Adjust permissions as needed
      })

      console.log(`Successfully uploaded file to storage:`, {
        fileId: result.fileId,
        url: result.url
      })

      // Try to save metadata to the database (optional, don't fail upload if this fails)
      try {
        const services = getServices()
        await services.databases.createDocument(
          appwriteConfig.databaseId,
          APPWRITE_COLLECTIONS.MEDIA_METADATA,
          result.fileId,
          {
            name: file.name,
            mimeType: file.type,
            size: file.size,
            folder: sanitizeFolder(folder),
            alt: alt || generateAltText(file.name),
            title: title || formatFileName(file.name),
            description: description || '',
            tags: parseTags(tags)
          }
        )
        console.log(`Successfully saved metadata for ${file.name}`)
      } catch (metadataError) {
        console.warn(`Failed to save metadata for ${file.name}:`, metadataError)
        // Continue without failing the upload
      }

      // Create enhanced result object
      const transformedResult: MediaUploadResult = {
        id: result.fileId,
        name: file.name,
        mimeType: file.type,
        size: file.size,
        url: result.url,
        previewUrl: shouldGeneratePreview(file.type) 
          ? storageService.getFilePreview(bucketId, result.fileId, 300, 300, 'center', 85)
          : null,
        downloadUrl: result.downloadUrl,
        type: getFileType(file.type),
        metadata: {
          folder: sanitizeFolder(folder),
          alt: alt || generateAltText(file.name),
          title: title || formatFileName(file.name),
          description: description || '',
          tags: parseTags(tags)
        }
      }

      uploadResults.push(transformedResult)
      console.log(`Successfully processed ${file.name}`)

    } catch (fileError) {
      const errorMessage = fileError instanceof Error ? fileError.message : 'Upload failed'
      console.error(`Failed to upload ${file.name}:`, errorMessage)
      
      uploadResults.push({
        name: file.name,
        error: errorMessage
      })
    }
  }

  console.log('Upload process completed:', {
    total: files.length,
    successful: uploadResults.filter(r => !r.error).length,
    failed: uploadResults.filter(r => r.error).length
  })

  return uploadResults
}

/**
 * Delete multiple files from storage
 */
export async function deleteMediaFiles(fileIds: string[]): Promise<Array<{id: string, success: boolean, error?: string}>> {
  if (!fileIds || fileIds.length === 0) {
    throw new Error('No file IDs provided for deletion')
  }

  const bucketId = STORAGE_BUCKETS.MEDIA_LIBRARY.id
  if (!bucketId) {
    throw new Error('Media library bucket ID is not configured')
  }

  const results: Array<{id: string, success: boolean, error?: string}> = []

  for (const id of fileIds) {
    const trimmedId = id?.trim()
    if (!trimmedId) {
      results.push({ 
        id: id || 'undefined', 
        success: false, 
        error: 'Invalid file ID' 
      })
      continue
    }

    try {
      await storageService.deleteFile(bucketId, trimmedId)
      results.push({ id: trimmedId, success: true })
    } catch (error) {
      results.push({ 
        id: trimmedId, 
        success: false, 
        error: error instanceof Error ? error.message : 'Delete failed' 
      })
    }
  }

  return results
}

/**
 * Get media files with filtering and pagination
 */
export async function getMediaFiles(params: {
  page?: number
  limit?: number
  search?: string
  type?: 'all' | 'image' | 'video' | 'audio' | 'document'
  folder?: string
  sortBy?: 'createdAt' | 'name' | 'size'
  sortOrder?: 'asc' | 'desc'
}): Promise<{
  files: MediaFile[]
  pagination: {
    page: number
    limit: number
    total: number
    totalFiltered: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  filters: {
    type: string
    folder: string
    search: string | null
  }
}> {
  const {
    page = 1,
    limit = 20,
    search = '',
    type = 'all',
    folder = '',
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = params

  // Validate parameters
  if (page < 1) {
    throw new Error('Page must be greater than 0')
  }
  if (limit < 1 || limit > 100) {
    throw new Error('Limit must be between 1 and 100')
  }

  const bucketId = STORAGE_BUCKETS.MEDIA_LIBRARY.id
  if (!bucketId) {
    throw new Error('Media library bucket ID is not configured')
  }

  const offset = (page - 1) * limit

  // Build queries
  const queries = buildQueries({
    limit,
    offset,
    search,
    sortBy,
    sortOrder
  })

  const files = await storageService.listFiles(bucketId, queries)

  if (!files || !files.files) {
    throw new Error('Failed to retrieve files from storage')
  }

  // Apply client-side filtering
  const filteredFiles = filterFilesByType(files.files, type, folder)

  // Transform files with enhanced metadata
  const transformedFiles = await Promise.all(
    filteredFiles.map(file => transformFileResponse(file, bucketId, folder))
  )

  // Calculate pagination correctly
  const totalFiltered = filteredFiles.length
  const totalPages = Math.ceil(totalFiltered / limit)

  return {
    files: transformedFiles,
    pagination: {
      page,
      limit,
      total: files.total,
      totalFiltered,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    },
    filters: {
      type,
      folder,
      search: search || null
    }
  }
}
