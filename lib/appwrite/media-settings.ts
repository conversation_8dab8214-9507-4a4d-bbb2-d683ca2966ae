import { Client, Databases, ID, Query, Models } from 'node-appwrite'
import { getAppwriteServer, DATABASE_ID } from './server'

// Collection ID for media settings
export const MEDIA_SETTINGS_COLLECTION_ID = 'media-settings'

export interface MediaSettings {
  storageProvider: 'local' | 's3' | 'cloudinary' | 'google' | 'appwrite';
  localSettings?: {
    uploadDir: string;
    maxFileSize: number;
    allowedFileTypes: string;
  };
  s3Settings?: {
    accessKeyId: string;
    secretAccessKey: string;
    region: string;
    bucket: string;
    endpoint?: string;
  };
  cloudinarySettings?: {
    cloudName: string;
    apiKey: string;
    apiSecret: string;
    uploadPreset?: string;
  };
  googleSettings?: {
    projectId: string;
    bucketName: string;
    keyFilePath: string;
  };
  appwriteSettings?: {
    bucketId: string;
    maximumFileSize: number;
    allowedFileExtensions: string[];
    compression: boolean;
    encryption: boolean;
    antivirus: boolean;
  };
  imageOptimization: {
    enabled: boolean;
    quality?: number;
    maxWidth?: number;
    maxHeight?: number;
    formats?: string[];
  };
  cacheControl: {
    enabled: boolean;
    maxAge?: number;
    staleWhileRevalidate?: number;
  };
}

// Default media settings
export const defaultMediaSettings: MediaSettings = {
  storageProvider: 'appwrite',
  localSettings: {
    uploadDir: '/uploads',
    maxFileSize: 10,
    allowedFileTypes: 'jpg,jpeg,png,gif,webp,svg,pdf',
  },
  s3Settings: {
    accessKeyId: '',
    secretAccessKey: '',
    region: 'us-east-1',
    bucket: '',
    endpoint: '',
  },
  cloudinarySettings: {
    cloudName: '',
    apiKey: '',
    apiSecret: '',
    uploadPreset: '',
  },
  googleSettings: {
    projectId: '',
    bucketName: '',
    keyFilePath: '',
  },
  appwriteSettings: {
    bucketId: 'media',
    maximumFileSize: 52428800, // 50MB
    allowedFileExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'pdf'],
    compression: true,
    encryption: true,
    antivirus: true,
  },
  imageOptimization: {
    enabled: true,
    quality: 80,
    maxWidth: 1920,
    maxHeight: 1080,
    formats: ['webp', 'avif', 'jpg'],
  },
  cacheControl: {
    enabled: true,
    maxAge: 86400,
    staleWhileRevalidate: 43200,
  },
}

export class MediaSettingsService {
  private static instance: MediaSettingsService;
  private databases: Databases;
  private collectionId: string;

  private constructor() {
    const appwriteServer = getAppwriteServer();
    this.databases = new Databases(appwriteServer['client']);
    this.collectionId = MEDIA_SETTINGS_COLLECTION_ID;
    this.initializeCollection();
  }

  public static getInstance(): MediaSettingsService {
    if (!MediaSettingsService.instance) {
      MediaSettingsService.instance = new MediaSettingsService();
    }
    return MediaSettingsService.instance;
  }

  /**
   * Initialize the media settings collection if it doesn't exist
   */
  private async initializeCollection(): Promise<void> {
    try {
      try {
        // Check if collection exists
        await this.databases.getCollection(DATABASE_ID, this.collectionId);
        console.log(`✅ Media settings collection already exists`);
      } catch (error) {
        // Create collection if it doesn't exist
        console.log(`🚀 Creating media settings collection...`);
        await this.databases.createCollection(
          DATABASE_ID,
          this.collectionId,
          'Media Settings',
          ['read("any")', 'write("team")']
        );

        // Create attributes
        await this.databases.createStringAttribute(
          DATABASE_ID,
          this.collectionId,
          'storageProvider',
          50,
          true,
          'appwrite'
        );

        // Create string attributes for JSON data with large size
        await this.databases.createStringAttribute(
          DATABASE_ID,
          this.collectionId,
          'localSettings',
          16384, // Large size for JSON data
          false
        );

        await this.databases.createStringAttribute(
          DATABASE_ID,
          this.collectionId,
          's3Settings',
          16384, // Large size for JSON data
          false
        );

        await this.databases.createStringAttribute(
          DATABASE_ID,
          this.collectionId,
          'cloudinarySettings',
          16384, // Large size for JSON data
          false
        );

        await this.databases.createStringAttribute(
          DATABASE_ID,
          this.collectionId,
          'googleSettings',
          16384, // Large size for JSON data
          false
        );

        await this.databases.createStringAttribute(
          DATABASE_ID,
          this.collectionId,
          'appwriteSettings',
          16384, // Large size for JSON data
          false
        );

        await this.databases.createStringAttribute(
          DATABASE_ID,
          this.collectionId,
          'imageOptimization',
          16384, // Large size for JSON data
          false
        );

        await this.databases.createStringAttribute(
          DATABASE_ID,
          this.collectionId,
          'cacheControl',
          16384, // Large size for JSON data
          false
        );

        await this.databases.createBooleanAttribute(
          DATABASE_ID,
          this.collectionId,
          'active',
          true,
          true
        );

        console.log(`✅ Media settings collection created successfully`);
      }
    } catch (error) {
      console.error(`❌ Error initializing media settings collection:`, error);
    }
  }

  /**
   * Get the active media settings
   */
  async getMediaSettings(): Promise<MediaSettings> {
    try {
      // Query for active settings
      const response = await this.databases.listDocuments(
        DATABASE_ID,
        this.collectionId,
        [Query.equal('active', true), Query.limit(1)]
      );

      if (response.documents.length === 0) {
        // Create default settings if none exist
        return this.createDefaultSettings();
      }

      const settings = response.documents[0];
      return this.formatSettings(settings);
    } catch (error) {
      console.error('Error fetching media settings:', error);
      // Return default settings if there's an error
      return defaultMediaSettings;
    }
  }

  /**
   * Create default media settings
   */
  private async createDefaultSettings(): Promise<MediaSettings> {
    try {
      const document = await this.databases.createDocument(
        DATABASE_ID,
        this.collectionId,
        ID.unique(),
        {
          storageProvider: defaultMediaSettings.storageProvider,
          localSettings: JSON.stringify(defaultMediaSettings.localSettings),
          s3Settings: JSON.stringify(defaultMediaSettings.s3Settings),
          cloudinarySettings: JSON.stringify(defaultMediaSettings.cloudinarySettings),
          googleSettings: JSON.stringify(defaultMediaSettings.googleSettings),
          appwriteSettings: JSON.stringify(defaultMediaSettings.appwriteSettings),
          imageOptimization: JSON.stringify(defaultMediaSettings.imageOptimization),
          cacheControl: JSON.stringify(defaultMediaSettings.cacheControl),
          active: true
        }
      );

      return this.formatSettings(document);
    } catch (error) {
      console.error('Error creating default media settings:', error);
      return defaultMediaSettings;
    }
  }

  /**
   * Update media settings
   */
  async updateMediaSettings(settings: MediaSettings): Promise<MediaSettings> {
    try {
      // Find active settings
      const response = await this.databases.listDocuments(
        DATABASE_ID,
        this.collectionId,
        [Query.equal('active', true), Query.limit(1)]
      );

      let document;
      if (response.documents.length === 0) {
        // Create new settings if none exist
        document = await this.databases.createDocument(
          DATABASE_ID,
          this.collectionId,
          ID.unique(),
          {
            storageProvider: settings.storageProvider,
            localSettings: settings.localSettings ? JSON.stringify(settings.localSettings) : null,
            s3Settings: settings.s3Settings ? JSON.stringify(settings.s3Settings) : null,
            cloudinarySettings: settings.cloudinarySettings ? JSON.stringify(settings.cloudinarySettings) : null,
            googleSettings: settings.googleSettings ? JSON.stringify(settings.googleSettings) : null,
            appwriteSettings: settings.appwriteSettings ? JSON.stringify(settings.appwriteSettings) : null,
            imageOptimization: settings.imageOptimization ? JSON.stringify(settings.imageOptimization) : null,
            cacheControl: settings.cacheControl ? JSON.stringify(settings.cacheControl) : null,
            active: true
          }
        );
      } else {
        // Update existing settings
        const existingSettings = response.documents[0];
        document = await this.databases.updateDocument(
          DATABASE_ID,
          this.collectionId,
          existingSettings.$id,
          {
            storageProvider: settings.storageProvider,
            localSettings: settings.localSettings ? JSON.stringify(settings.localSettings) : null,
            s3Settings: settings.s3Settings ? JSON.stringify(settings.s3Settings) : null,
            cloudinarySettings: settings.cloudinarySettings ? JSON.stringify(settings.cloudinarySettings) : null,
            googleSettings: settings.googleSettings ? JSON.stringify(settings.googleSettings) : null,
            appwriteSettings: settings.appwriteSettings ? JSON.stringify(settings.appwriteSettings) : null,
            imageOptimization: settings.imageOptimization ? JSON.stringify(settings.imageOptimization) : null,
            cacheControl: settings.cacheControl ? JSON.stringify(settings.cacheControl) : null,
          }
        );
      }

      return this.formatSettings(document);
    } catch (error) {
      console.error('Error updating media settings:', error);
      throw new Error('Failed to update media settings');
    }
  }

  /**
   * Format settings from Appwrite document to MediaSettings interface
   */
  private formatSettings(document: Models.Document): MediaSettings {
    return {
      storageProvider: document.storageProvider as MediaSettings['storageProvider'],
      localSettings: document.localSettings ? JSON.parse(document.localSettings) : undefined,
      s3Settings: document.s3Settings ? JSON.parse(document.s3Settings) : undefined,
      cloudinarySettings: document.cloudinarySettings ? JSON.parse(document.cloudinarySettings) : undefined,
      googleSettings: document.googleSettings ? JSON.parse(document.googleSettings) : undefined,
      appwriteSettings: document.appwriteSettings ? JSON.parse(document.appwriteSettings) : undefined,
      imageOptimization: document.imageOptimization ? JSON.parse(document.imageOptimization) : undefined,
      cacheControl: document.cacheControl ? JSON.parse(document.cacheControl) : undefined,
    };
  }
}

// Export singleton instance getter
export const getMediaSettingsService = () => MediaSettingsService.getInstance();