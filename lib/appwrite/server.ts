import { Client, Storage, Databases, Permission, Role, Compression, AppwriteException, Models } from 'node-appwrite'

// Server-side Appwrite client is initialized in the AppwriteServerService class
// instead of here at the module level to ensure proper initialization order

export const MEDIA_BUCKET_ID = process.env.NEXT_PUBLIC_APPWRITE_MEDIA_BUCKET_ID || 'media'
export const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || 'main'
export const MEDIA_COLLECTION_ID = process.env.NEXT_PUBLIC_APPWRITE_MEDIA_COLLECTION_ID || 'media-metadata'

export interface BucketConfig {
  bucketId: string
  name: string
  permissions: string[]
  fileSecurity: boolean
  enabled: boolean
  maximumFileSize: number
  allowedFileExtensions: string[]
  compression: Compression
  encryption: boolean
  antivirus: boolean
}

export interface CollectionConfig {
  collectionId: string
  name: string
  permissions: string[]
  documentSecurity: boolean
  enabled: boolean
}

interface StringAttributeDef {
  key: string
  type: 'string'
  size: number
  required: boolean
  array?: boolean
}

interface IntegerAttributeDef {
  key: string
  type: 'integer'
  required: boolean
  array?: boolean
}

type AttributeDef = StringAttributeDef | IntegerAttributeDef

export class AppwriteServerService {
  private static instance: AppwriteServerService;
  private client: Client;
  private databases: Databases;
  private storage: Storage;

  private constructor() {
    try {
      // Validate required environment variables
      const endpoint = process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT
      const projectId = process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID
      
      // Try to get API key from multiple possible sources
      let apiKey = process.env.APPWRITE_API_KEY
      
      // Debug logging
      console.log('Appwrite Server Initialization:')
      console.log('- NEXT_PUBLIC_APPWRITE_ENDPOINT:', endpoint ? '✅ Set' : '❌ Missing')
      console.log('- NEXT_PUBLIC_APPWRITE_PROJECT_ID:', projectId ? '✅ Set' : '❌ Missing')
      console.log('- APPWRITE_API_KEY:', apiKey ? '✅ Set' : '❌ Missing')
      
      // Fallback to the value from .env if not found in process.env
      if (!apiKey) {
        apiKey = 'standard_d032b243642b680994d29f92b10b4b92040fa1ae431ad873a76087b4aee6e37e8ea033843dccd26564e35b387b1a15adf21ce28d9a2ddfd2011249cd5b44b7988656d312c3dc61b4e1a79993490410c3f2ffc6ce35aada1a8ac06882c834b998a631a04bdbfa7cf203438e24e9f67130fc6039ff03ee3aedccef135489b90e7d'
        console.log('- Using hardcoded API key as fallback')
      }

      if (!endpoint) {
        throw new Error('NEXT_PUBLIC_APPWRITE_ENDPOINT is required')
      }
      if (!projectId) {
        throw new Error('NEXT_PUBLIC_APPWRITE_PROJECT_ID is required')
      }
      if (!apiKey) {
        throw new Error('APPWRITE_API_KEY is required')
      }

      // Initialize the client with proper configuration
      this.client = new Client()
      this.client.setEndpoint(endpoint)
      this.client.setProject(projectId)
      this.client.setKey(apiKey)

      // Initialize services
      this.storage = new Storage(this.client);
      this.databases = new Databases(this.client);
      
      console.log('✅ Appwrite server client initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize Appwrite server client:', error)
      throw new Error(`Failed to initialize Appwrite server client. Please check your configuration.`)
    }
  }

  public static getInstance(): AppwriteServerService {
    if (!AppwriteServerService.instance) {
      AppwriteServerService.instance = new AppwriteServerService();
    }
    return AppwriteServerService.instance;
  }
  
  /**
   * Get the Appwrite databases service
   */
  public getDatabases(): Databases {
    return this.databases;
  }

  /**
   * List all storage buckets
   */
  async listBuckets(): Promise<any> {
    try {
      return await this.storage.listBuckets()
    } catch (error) {
      console.error(`❌ Error listing buckets:`, error)
      throw error
    }
  }

  /**
   * Create a new storage bucket with the given configuration
   */
  async createBucket(config: BucketConfig): Promise<any> {
    try {
      const bucket = await this.storage.createBucket(
        config.bucketId,
        config.name,
        config.permissions,
        config.fileSecurity,
        config.enabled,
        config.maximumFileSize,
        config.allowedFileExtensions,
        config.compression,
        config.encryption,
        config.antivirus
      )
      console.log(`✅ Bucket '${config.bucketId}' created successfully`)
      return bucket
    } catch (error) {
      console.error(`❌ Error creating bucket '${config.bucketId}':`, error)
      throw new Error(`Failed to create bucket: ${error}`)
    }
  }

  /**
   * Get a bucket by its ID, or create it if it doesn't exist
   */
  async getOrCreateBucket(config: BucketConfig): Promise<any> {
    try {
      const existingBucket = await this.storage.getBucket(config.bucketId)
      console.log(`✅ Bucket '${config.bucketId}' already exists.`)
      return existingBucket
    } catch (error) {
      if (error instanceof AppwriteException && error.code === 404) {
        console.log(`🚀 Bucket '${config.bucketId}' not found. Creating...`)
        return this.createBucket(config)
      }
      console.error(`❌ Error getting bucket '${config.bucketId}':`, error)
      throw error
    }
  }

  /**
   * Create or get the media storage bucket with optimal configuration
   */
  async createMediaBucket(config?: Partial<BucketConfig>): Promise<Models.Bucket> {
    const defaultConfig: BucketConfig = {
      bucketId: MEDIA_BUCKET_ID,
      name: 'Media Library',
      permissions: [
        Permission.read(Role.any()),
        Permission.create(Role.users()),
        Permission.update(Role.users()),
        Permission.delete(Role.users())
      ],
      fileSecurity: true,
      enabled: true,
      maximumFileSize: 52428800, // 50MB
      allowedFileExtensions: [
        // Images
        'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp', 'tiff', 'ico',
        // Videos
        'mp4', 'webm', 'ogg', 'avi', 'mov', 'wmv', 'flv', 'mkv', '3gp',
        // Audio
        'mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a', 'wma',
        // Documents
        'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf', 'odt', 'ods', 'odp',
        // Archives
        'zip', 'rar', '7z', 'tar', 'gz',
        // Code
        'js', 'ts', 'jsx', 'tsx', 'css', 'scss', 'html', 'json', 'xml', 'yaml', 'yml'
      ],
      compression: Compression.Gzip,
      encryption: true,
      antivirus: true
    }

    const finalConfig = { ...defaultConfig, ...config }
    return this.getOrCreateBucket(finalConfig)
  }

  /**
   * Create database for metadata storage
   */
  async createDatabase(databaseId: string = DATABASE_ID, name: string = 'Main Database'): Promise<Models.Database> {
    try {
      try {
        const existingDatabase = await this.databases.get(databaseId)
        console.log(`✅ Database '${databaseId}' already exists`)
        return existingDatabase
      } catch (error) {
        if (error instanceof AppwriteException && error.code === 404) {
          console.log(`🚀 Creating database '${databaseId}'...`)
          const database = await this.databases.create(databaseId, name)
          console.log(`✅ Database '${databaseId}' created successfully`)
          return database
        }
        throw error
      }
    } catch (error) {
      console.error(`❌ Error creating database:`, error)
      throw new Error(`Failed to create database: ${error}`)
    }
  }

  /**
   * Create media metadata collection
   */
  async createMediaMetadataCollection(config?: Partial<CollectionConfig>): Promise<Models.Collection> {
    const defaultConfig: CollectionConfig = {
      collectionId: MEDIA_COLLECTION_ID,
      name: 'Media Metadata',
      permissions: [
        Permission.read(Role.any()),
        Permission.create(Role.users()),
        Permission.update(Role.users()),
        Permission.delete(Role.users())
      ],
      documentSecurity: true,
      enabled: true
    }

    const finalConfig = { ...defaultConfig, ...config }

    try {
      try {
        const existingCollection = await this.databases.getCollection(DATABASE_ID, finalConfig.collectionId)
        console.log(`✅ Collection '${finalConfig.collectionId}' already exists`)
        return existingCollection
      } catch (error) {
        if (error instanceof AppwriteException && error.code === 404) {
          console.log(`🚀 Creating collection '${finalConfig.collectionId}'...`)
          const collection = await this.databases.createCollection(
            DATABASE_ID,
            finalConfig.collectionId,
            finalConfig.name,
            finalConfig.permissions,
            finalConfig.documentSecurity,
            finalConfig.enabled
          )
          console.log(`✅ Collection '${finalConfig.collectionId}' created successfully`)
          await this.createMediaMetadataAttributes(finalConfig.collectionId)
          return collection
        }
        throw error
      }
    } catch (error) {
      console.error(`❌ Error creating media metadata collection:`, error)
      throw new Error(`Failed to create media metadata collection: ${error}`)
    }
  }

    private async createMediaMetadataAttributes(collectionId: string): Promise<void> {
    const attributes: AttributeDef[] = [
      { key: 'fileId', type: 'string', size: 255, required: true },
      { key: 'name', type: 'string', size: 255, required: true },
      { key: 'mimeType', type: 'string', size: 128, required: true },
      { key: 'size', type: 'integer', required: true },
      { key: 'width', type: 'integer', required: false },
      { key: 'height', type: 'integer', required: false },
      { key: 'alt', type: 'string', size: 512, required: false },
      { key: 'title', type: 'string', size: 512, required: false },
      { key: 'description', type: 'string', size: 1024, required: false },
      { key: 'tags', type: 'string', size: 255, required: false, array: true },
      { key: 'folder', type: 'string', size: 255, required: false },
      { key: 'uploadedBy', type: 'string', size: 255, required: true },
    ]

    console.log(`🚀 Creating attributes for collection '${collectionId}'...`)
    for (const attr of attributes) {
      try {
        if (attr.type === 'string') {
          await this.databases.createStringAttribute(DATABASE_ID, collectionId, attr.key, attr.size, attr.required, undefined, attr.array)
        } else if (attr.type === 'integer') {
          await this.databases.createIntegerAttribute(DATABASE_ID, collectionId, attr.key, attr.required, undefined, undefined, undefined, attr.array)
        }
        console.log(`  ✅ Created attribute: ${attr.key}`)
      } catch (error) {
        if (!(error instanceof AppwriteException && error.code === 409)) {
          console.error(`  ❌ Error creating attribute '${attr.key}':`, error)
        }
      }
    }
    await this.createMediaMetadataIndexes(collectionId)
  }

  private async createMediaMetadataIndexes(collectionId: string): Promise<void> {
    const indexes = [
      { key: 'fileId_index', type: 'key', attributes: ['fileId'] },
      { key: 'folder_index', type: 'key', attributes: ['folder'] },
      { key: 'mimeType_index', type: 'key', attributes: ['mimeType'] },
      // Skip fulltext index on array attributes (not supported in Appwrite 1.6.0)
      // { key: 'tags_search', type: 'fulltext', attributes: ['tags'] },
      { key: 'title_search', type: 'fulltext', attributes: ['title', 'description'] }
    ]

    console.log(`🚀 Creating indexes for collection '${collectionId}'...`)
    for (const index of indexes) {
      try {
        await this.databases.createIndex(DATABASE_ID, collectionId, index.key, index.type as any, index.attributes)
        console.log(`  ✅ Created index: ${index.key}`)
      } catch (error) {
        if (!(error instanceof AppwriteException && error.code === 409)) {
          console.error(`  ❌ Error creating index '${index.key}':`, error)
        }
      }
    }
  }

  async setupMediaLibrary(): Promise<{ bucket: Models.Bucket; database: Models.Database; collection: Models.Collection; }> {
    console.log('🚀 Setting up complete media library infrastructure...')
    try {
      const bucket = await this.createMediaBucket()
      const database = await this.createDatabase()
      const collection = await this.createMediaMetadataCollection()
      console.log('✅ Media library infrastructure setup completed successfully!')
      return { bucket, database, collection }
    } catch (error) {
      console.error('❌ Failed to setup media library infrastructure:', error)
      throw error
    }
  }

  async getBucketInfo(bucketId: string = MEDIA_BUCKET_ID): Promise<Models.Bucket> {
    try {
      return await this.storage.getBucket(bucketId)
    } catch (error) {
      console.error(`❌ Error getting bucket info:`, error)
      throw error
    }
  }

  async updateBucketPermissions(bucketId: string = MEDIA_BUCKET_ID, permissions: string[]): Promise<Models.Bucket> {
    try {
      const bucket = await this.storage.getBucket(bucketId)
      return await this.storage.updateBucket(
        bucketId,
        bucket.name,
        permissions,
        bucket.fileSecurity,
        bucket.enabled,
        bucket.maximumFileSize,
        bucket.allowedFileExtensions,
        bucket.compression as Compression,
        bucket.encryption,
        bucket.antivirus
      )
    } catch (error) {
      console.error(`❌ Error updating bucket permissions:`, error)
      throw error
    }
  }

  async deleteBucket(bucketId: string): Promise<void> {
    try {
      await this.storage.deleteBucket(bucketId)
      console.log(`✅ Bucket '${bucketId}' deleted successfully`)
    } catch (error) {
      console.error(`❌ Error deleting bucket:`, error)
      throw error
    }
  }
}

// Export singleton instance getter (lazy loading)
export const getAppwriteServer = () => AppwriteServerService.getInstance()
