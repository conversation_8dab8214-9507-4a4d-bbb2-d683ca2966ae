// Product data transformers for storefront compatibility
import type { Product as EcommerceProduct } from '../types'

// Enhanced product interface with variant support
export interface StorefrontProduct {
  id: string
  name: string
  slug: string
  description: string
  price: number
  compareAtPrice?: number
  images: string[]
  colors: { name: string; value: string }[]
  sizes: string[]
  categoryId: string
  isNew?: boolean
  isSale?: boolean
  // Enhanced variant support
  variants?: StorefrontVariant[]
  hasVariants: boolean
  defaultVariantId?: string
}

// Storefront variant interface
export interface StorefrontVariant {
  id: string
  title: string
  sku: string
  price: number
  compareAtPrice?: number
  available: boolean
  inventoryQuantity: number
  options: { name: string; value: string }[]
  image?: string
}

// ProductInfo component expected interface
export interface ProductInfoProduct {
  id: string
  name: string
  description: string
  price: number
  compareAtPrice?: number
  colors: { name: string; value: string }[]
  sizes: string[]
  slug: string
  categoryId: string
  // Enhanced variant support
  variants?: StorefrontVariant[]
  hasVariants: boolean
  defaultVariantId?: string
}

/**
 * Transform e-commerce Product to StorefrontProduct format
 */
export function transformToStorefrontProduct(product: EcommerceProduct): StorefrontProduct {
  const variants = transformVariantsToStorefront(product.variants || [])
  const hasVariants = variants.length > 0
  const defaultVariant = variants.find(v => v.available) || variants[0]

  return {
    id: product.id,
    name: product.title, // Map title to name
    slug: product.slug,
    description: product.description,
    price: hasVariants && defaultVariant ? defaultVariant.price :
      (typeof product.price === 'object' ? product.price.amount : product.price),
    compareAtPrice: hasVariants && defaultVariant ? defaultVariant.compareAtPrice :
      (product.compareAtPrice ?
        (typeof product.compareAtPrice === 'object' ? product.compareAtPrice.amount : product.compareAtPrice) :
        undefined),
    images: product.images?.map(img => img.url) || [],
    colors: extractColorsFromProduct(product),
    sizes: extractSizesFromProduct(product),
    categoryId: product.categories?.[0]?.id || '',
    isNew: isProductNew(product),
    isSale: isProductOnSale(product),
    variants,
    hasVariants,
    defaultVariantId: defaultVariant?.id
  }
}

/**
 * Transform e-commerce Product to ProductInfo expected format
 */
export function transformToProductInfoProduct(product: EcommerceProduct): ProductInfoProduct {
  const variants = transformVariantsToStorefront(product.variants || [])
  const hasVariants = variants.length > 0
  const defaultVariant = variants.find(v => v.available) || variants[0]

  return {
    id: product.id,
    name: product.title, // Map title to name
    description: product.description,
    price: hasVariants && defaultVariant ? defaultVariant.price :
      (typeof product.price === 'object' ? product.price.amount : product.price),
    compareAtPrice: hasVariants && defaultVariant ? defaultVariant.compareAtPrice :
      (product.compareAtPrice ?
        (typeof product.compareAtPrice === 'object' ? product.compareAtPrice.amount : product.compareAtPrice) :
        undefined),
    colors: extractColorsFromProduct(product),
    sizes: extractSizesFromProduct(product),
    slug: product.slug,
    categoryId: product.categories?.[0]?.id || '',
    variants,
    hasVariants,
    defaultVariantId: defaultVariant?.id
  }
}

/**
 * Transform product variants to storefront format
 */
function transformVariantsToStorefront(variants: any[]): StorefrontVariant[] {
  return variants.map(variant => ({
    id: variant.id,
    title: variant.title,
    sku: variant.sku,
    price: typeof variant.price === 'object' ? variant.price.amount : variant.price,
    compareAtPrice: variant.compareAtPrice ?
      (typeof variant.compareAtPrice === 'object' ? variant.compareAtPrice.amount : variant.compareAtPrice) :
      undefined,
    available: variant.available && variant.inventoryQuantity > 0,
    inventoryQuantity: variant.inventoryQuantity || 0,
    options: variant.options?.map((opt: any) => ({
      name: opt.name,
      value: opt.value
    })) || [],
    image: variant.image?.url
  }))
}

/**
 * Extract colors from product variants and options
 */
function extractColorsFromProduct(product: EcommerceProduct): { name: string; value: string }[] {
  const colors: { name: string; value: string }[] = []
  
  // Try to get colors from product options
  const colorOption = product.options?.find(opt => 
    opt.name.toLowerCase().includes('color') || 
    opt.name.toLowerCase().includes('colour')
  )
  
  if (colorOption && colorOption.values) {
    colorOption.values.forEach(value => {
      colors.push({
        name: value,
        value: value.toLowerCase().replace(/\s+/g, '-')
      })
    })
  }
  
  // If no colors found in options, try variants
  if (colors.length === 0 && product.variants) {
    const uniqueColors = new Set<string>()

    product.variants.forEach(variant => {
      // Look for color options in variant options
      const colorOption = variant.options?.find((opt: any) =>
        opt.name.toLowerCase().includes('color') ||
        opt.name.toLowerCase().includes('colour')
      )

      if (colorOption) {
        uniqueColors.add(colorOption.value)
      }
      // Fallback: check variant title
      else if (variant.title && variant.title.toLowerCase().includes('color')) {
        uniqueColors.add(variant.title)
      }
    })

    uniqueColors.forEach(color => {
      colors.push({
        name: color,
        value: color.toLowerCase().replace(/\s+/g, '-')
      })
    })
  }
  
  // Default colors if none found
  if (colors.length === 0) {
    colors.push({ name: 'Default', value: 'default' })
  }
  
  return colors
}

/**
 * Extract sizes from product variants and options
 */
function extractSizesFromProduct(product: EcommerceProduct): string[] {
  const sizes: string[] = []
  
  // Try to get sizes from product options
  const sizeOption = product.options?.find(opt => 
    opt.name.toLowerCase().includes('size')
  )
  
  if (sizeOption && sizeOption.values) {
    sizes.push(...sizeOption.values)
  }
  
  // If no sizes found in options, try variants
  if (sizes.length === 0 && product.variants) {
    const uniqueSizes = new Set<string>()

    product.variants.forEach(variant => {
      // Look for size options in variant options
      const sizeOption = variant.options?.find((opt: any) =>
        opt.name.toLowerCase().includes('size')
      )

      if (sizeOption) {
        uniqueSizes.add(sizeOption.value)
      }
      // Fallback: check variant title
      else if (variant.title && variant.title.toLowerCase().includes('size')) {
        uniqueSizes.add(variant.title)
      }
    })

    sizes.push(...Array.from(uniqueSizes))
  }
  
  // Default size if none found
  if (sizes.length === 0) {
    sizes.push('One Size')
  }
  
  return sizes
}

/**
 * Determine if product is new (created within last 30 days)
 */
function isProductNew(product: EcommerceProduct): boolean {
  if (!product.createdAt) return false
  
  const thirtyDaysAgo = new Date()
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
  
  return new Date(product.createdAt) > thirtyDaysAgo
}

/**
 * Determine if product is on sale
 */
function isProductOnSale(product: EcommerceProduct): boolean {
  const price = typeof product.price === 'object' ? product.price.amount : product.price
  const compareAtPrice = product.compareAtPrice ? 
    (typeof product.compareAtPrice === 'object' ? product.compareAtPrice.amount : product.compareAtPrice) : 
    null
  
  return !!(compareAtPrice && compareAtPrice > price)
}

/**
 * Transform array of e-commerce products to storefront format
 */
export function transformToStorefrontProducts(products: EcommerceProduct[]): StorefrontProduct[] {
  return products.map(transformToStorefrontProduct)
}

/**
 * Get product filters for collections
 */
export function getCollectionFilters(collectionType: string) {
  switch (collectionType) {
    case 'best-sellers':
      return {
        // In a real implementation, you'd have analytics data
        // For now, we'll use products with high inventory turnover or featured status
        featured: true,
        status: ['active'] as const
      }
    
    case 'new-arrivals':
      return {
        status: ['active'] as const,
        // Filter for products created in the last 30 days
        createdAfter: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
      }
    
    case 'sale':
      return {
        status: ['active'] as const,
        onSale: true
      }
    
    case 'heritage-day':
      return {
        status: ['active'] as const,
        // You could filter by tags or collections
        tags: ['heritage', 'south-african', 'traditional']
      }
    
    case 'school-uniforms':
      return {
        status: ['active'] as const,
        categoryIds: [], // You'd need to get the school uniform category ID
        tags: ['school', 'uniform', 'education']
      }
    
    case 'summer':
      return {
        status: ['active'] as const,
        tags: ['summer', 'lightweight', 'breathable']
      }
    
    default:
      return {
        status: ['active'] as const
      }
  }
}

/**
 * Build search parameters for collection products
 */
export function buildCollectionSearchParams(
  collectionType: string,
  sortBy: string = 'featured',
  filters: any = {},
  page: number = 1,
  limit: number = 20
) {
  const baseFilters = getCollectionFilters(collectionType)
  
  // Map sort options
  let sort = { field: 'createdAt' as const, direction: 'desc' as const }
  
  switch (sortBy) {
    case 'price-asc':
      sort = { field: 'price', direction: 'asc' }
      break
    case 'price-desc':
      sort = { field: 'price', direction: 'desc' }
      break
    case 'name-asc':
      sort = { field: 'title', direction: 'asc' }
      break
    case 'name-desc':
      sort = { field: 'title', direction: 'desc' }
      break
    case 'newest':
      sort = { field: 'createdAt', direction: 'desc' }
      break
    case 'featured':
    default:
      sort = { field: 'createdAt', direction: 'desc' }
      break
  }
  
  return {
    filters: {
      ...baseFilters,
      ...filters
    },
    sort,
    page,
    limit
  }
}
export function transformProductFromDb(product: any): StorefrontProduct {
  return {
    id: product.id,
    name: product.title,
    slug: product.slug,
    description: product.description,
    price: product.price.amount,
    compareAtPrice: product.compareAtPrice?.amount,
    images: product.images.map((img: any) => img.url),
    colors: extractColorsFromProduct(product),
    sizes: extractSizesFromProduct(product),
    categoryId: product.categories[0]?.category.id || '',
    isNew: isProductNew(product),
    isSale: isProductOnSale(product),
    variants: transformVariantsToStorefront(product.variants || []),
    hasVariants: (product.variants || []).length > 0,
    defaultVariantId: (product.variants || []).find((v: any) => v.available)?.id
  }
}

export function transformProductsFromDb(products: any[]): StorefrontProduct[] {
  return products.map(transformProductFromDb)
}