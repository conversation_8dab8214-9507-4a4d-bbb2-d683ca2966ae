// Enhanced error handling utilities for cart operations
import { toast } from 'sonner'

export interface CartError {
  code: string
  message: string
  retryable?: boolean
  actionRequired?: string
}

export interface CartErrorHandlerOptions {
  showToast?: boolean
  logError?: boolean
  onRetry?: () => void
  onRefresh?: () => void
}

export class CartErrorHandler {
  static handle(error: CartError, options: CartErrorHandlerOptions = {}) {
    const {
      showToast = true,
      logError = true,
      onRetry,
      onRefresh
    } = options

    if (logError) {
      console.error('Cart Error:', error)
    }

    if (showToast) {
      this.showErrorToast(error, { onRetry, onRefresh })
    }

    return error
  }

  private static showErrorToast(error: CartError, actions: { onRetry?: () => void, onRefresh?: () => void }) {
    const { code, message } = error
    const { onRetry, onRefresh } = actions

    switch (code) {
      case 'INSUFFICIENT_STOCK':
        toast.error('Out of Stock', {
          description: message,
          action: onRefresh ? {
            label: 'Refresh',
            onClick: onRefresh
          } : undefined
        })
        break

      case 'NETWORK_ERROR':
      case 'TIMEOUT_ERROR':
        toast.error('Connection Issue', {
          description: message,
          action: onRetry ? {
            label: 'Retry',
            onClick: onRetry
          } : undefined
        })
        break

      case 'SERVER_ERROR':
        toast.error('Server Issue', {
          description: message,
          action: onRetry ? {
            label: 'Try Again',
            onClick: onRetry
          } : undefined
        })
        break

      case 'NOT_FOUND':
        toast.error('Product Not Found', {
          description: message,
          action: onRefresh ? {
            label: 'Refresh Page',
            onClick: onRefresh
          } : undefined
        })
        break

      case 'VALIDATION_ERROR':
        toast.error('Invalid Selection', {
          description: message
        })
        break

      case 'PRICE_CHANGED':
        toast.warning('Price Updated', {
          description: message,
          action: onRefresh ? {
            label: 'Refresh',
            onClick: onRefresh
          } : undefined
        })
        break

      case 'SESSION_EXPIRED':
        toast.error('Session Expired', {
          description: message,
          action: onRefresh ? {
            label: 'Refresh Page',
            onClick: onRefresh
          } : undefined
        })
        break

      default:
        toast.error('Error', {
          description: message,
          action: onRetry ? {
            label: 'Try Again',
            onClick: onRetry
          } : undefined
        })
    }
  }

  static createError(code: string, message?: string, retryable = false): CartError {
    return {
      code,
      message: message || this.getDefaultMessage(code),
      retryable
    }
  }

  private static getDefaultMessage(code: string): string {
    const messages: Record<string, string> = {
      'INSUFFICIENT_STOCK': 'Not enough stock available',
      'NOT_FOUND': 'Product not found',
      'VALIDATION_ERROR': 'Invalid input provided',
      'NETWORK_ERROR': 'Network connection failed',
      'TIMEOUT_ERROR': 'Request timed out',
      'SERVER_ERROR': 'Server error occurred',
      'PRODUCT_UNAVAILABLE': 'Product unavailable',
      'CART_LIMIT_EXCEEDED': 'Cart limit exceeded',
      'PRICE_CHANGED': 'Price has changed',
      'SESSION_EXPIRED': 'Session expired',
      'ADD_TO_CART_ERROR': 'Failed to add to cart'
    }

    return messages[code] || 'An error occurred'
  }

  static isRetryable(error: CartError): boolean {
    const retryableCodes = [
      'NETWORK_ERROR',
      'TIMEOUT_ERROR',
      'SERVER_ERROR'
    ]
    return retryableCodes.includes(error.code) || error.retryable === true
  }

  static requiresRefresh(error: CartError): boolean {
    const refreshCodes = [
      'NOT_FOUND',
      'PRICE_CHANGED',
      'SESSION_EXPIRED',
      'INSUFFICIENT_STOCK'
    ]
    return refreshCodes.includes(error.code)
  }
}

// Success handler for positive feedback
export class CartSuccessHandler {
  static handle(action: string, productName: string, options: { quantity?: number, variant?: string } = {}) {
    const { quantity = 1, variant } = options
    
    let description = `${productName} has been ${action}.`
    
    if (quantity > 1) {
      description = `${quantity}x ${productName} ${action === 'added to cart' ? 'have been added to your cart' : `have been ${action}`}.`
    }
    
    if (variant) {
      description = description.replace(productName, `${productName} (${variant})`)
    }

    toast.success(this.getSuccessTitle(action), {
      description
    })
  }

  private static getSuccessTitle(action: string): string {
    const titles: Record<string, string> = {
      'added to cart': 'Added to Cart',
      'updated in cart': 'Cart Updated',
      'removed from cart': 'Removed from Cart'
    }
    
    return titles[action] || 'Success'
  }
}