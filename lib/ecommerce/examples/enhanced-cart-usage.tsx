// Example usage of the enhanced useCart hook with product integration
'use client'

import React, { useEffect, useState } from 'react'
import { useCart } from '../hooks/use-cart'
import { Product, ProductVariant } from '../types/product'
import { EnhancedAddToCartInput, ProductAvailabilityCheck } from '../types/cart'

export function EnhancedCartExample() {
  const {
    cart,
    enhancedCart,
    loading,
    error,
    
    // Basic operations
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    
    // Enhanced operations
    bulkAddToCart,
    validateCart,
    checkProductAvailability,
    getRecommendations,
    getAlternativeProducts,
    
    // Product helpers
    getCartItemProduct,
    getCartItemVariant,
    isProductInCart,
    getProductQuantityInCart,
    
    // Analytics
    getCartCategories,
    getCartValue,
    getCartWeight,
    hasOutOfStockItems,
    
    // Bundle operations
    addBundleToCart,
    getAvailableBundles
  } = useCart({
    userId: 'user-123',
    sessionId: 'session-456',
    autoFetch: true
  })

  const [recommendations, setRecommendations] = useState<Product[]>([])
  const [availabilityCheck, setAvailabilityCheck] = useState<ProductAvailabilityCheck | null>(null)

  // Example: Add product with enhanced validation
  const handleEnhancedAddToCart = async (productId: string, variantId?: string) => {
    try {
      // First check availability
      const availability = await checkProductAvailability(productId, variantId, 1)
      setAvailabilityCheck(availability)

      if (!availability.isAvailable) {
        alert(`Product is not available. Available quantity: ${availability.availableQuantity}`)
        return
      }

      // Enhanced add to cart with validation
      const enhancedInput: EnhancedAddToCartInput = {
        productId,
        variantId,
        quantity: 1,
        validateInventory: true,
        validatePrice: true,
        customization: [
          {
            attributeId: 'color',
            value: 'blue'
          }
        ]
      }

      await addToCart(enhancedInput)
      console.log('Product added successfully with validation!')
    } catch (error) {
      console.error('Failed to add product:', error)
    }
  }

  // Example: Bulk add multiple products
  const handleBulkAdd = async () => {
    try {
      const result = await bulkAddToCart({
        items: [
          { productId: 'prod-1', variantId: 'var-1', quantity: 2 },
          { productId: 'prod-2', quantity: 1 },
          { productId: 'prod-3', variantId: 'var-3', quantity: 3 }
        ]
      })

      console.log('Bulk add result:', result)
      if (result.failedItems.length > 0) {
        console.warn('Some items failed to add:', result.failedItems)
      }
    } catch (error) {
      console.error('Bulk add failed:', error)
    }
  }

  // Example: Validate cart and handle issues
  const handleValidateCart = async () => {
    try {
      const validation = await validateCart()
      
      if (!validation.isValid) {
        console.log('Cart validation issues:')
        validation.errors.forEach(error => {
          console.log(`- ${error.type}: ${error.message}`)
        })

        // Handle pricing changes
        validation.pricingChanges.forEach(change => {
          console.log(`Price changed for product ${change.productId}: ${change.oldPrice.amount} -> ${change.newPrice.amount}`)
        })

        // Handle inventory issues
        validation.inventoryIssues.forEach(issue => {
          console.log(`Inventory issue for product ${issue.productId}: requested ${issue.requestedQuantity}, available ${issue.availableQuantity}`)
        })
      }
    } catch (error) {
      console.error('Cart validation failed:', error)
    }
  }

  // Example: Get recommendations based on cart contents
  const loadRecommendations = async () => {
    try {
      const recs = await getRecommendations()
      setRecommendations(recs)
    } catch (error) {
      console.error('Failed to load recommendations:', error)
    }
  }

  // Example: Handle out of stock items
  const handleOutOfStockItems = async () => {
    if (!enhancedCart) return

    for (const item of enhancedCart.items) {
      if (!item.isAvailable) {
        console.log(`Item ${item.productTitle} is out of stock`)
        
        // Get alternative products
        try {
          const alternatives = await getAlternativeProducts(item.productId)
          console.log(`Found ${alternatives.length} alternatives for ${item.productTitle}`)
        } catch (error) {
          console.error('Failed to get alternatives:', error)
        }
      }
    }
  }

  // Load recommendations when cart changes
  useEffect(() => {
    if (cart && cart.items.length > 0) {
      loadRecommendations()
    }
  }, [cart])

  // Check for out of stock items
  useEffect(() => {
    if (hasOutOfStockItems()) {
      handleOutOfStockItems()
    }
  }, [enhancedCart])

  if (loading) {
    return <div>Loading cart...</div>
  }

  if (error) {
    return <div>Error: {error.message}</div>
  }

  return (
    <div className="enhanced-cart-example">
      <h2>Enhanced Cart Example</h2>
      
      {/* Cart Summary */}
      <div className="cart-summary">
        <h3>Cart Summary</h3>
        <p>Items: {cart?.itemCount || 0}</p>
        <p>Total: ${getCartValue()}</p>
        <p>Weight: {getCartWeight()}kg</p>
        {hasOutOfStockItems() && (
          <p className="warning">⚠️ Some items are out of stock</p>
        )}
      </div>

      {/* Cart Categories */}
      <div className="cart-categories">
        <h3>Categories in Cart</h3>
        {getCartCategories().map(category => (
          <div key={category.categoryId}>
            {category.categoryName}: {category.itemCount} items (${category.totalValue})
          </div>
        ))}
      </div>

      {/* Cart Items with Enhanced Information */}
      <div className="cart-items">
        <h3>Cart Items</h3>
        {enhancedCart?.items.map(item => {
          const product = getCartItemProduct(item.id)
          const variant = getCartItemVariant(item.id)
          
          return (
            <div key={item.id} className="cart-item">
              <h4>{item.productTitle}</h4>
              {variant && <p>Variant: {variant.title}</p>}
              <p>Quantity: {item.quantity}</p>
              <p>Price: ${item.unitPrice.amount}</p>
              <p>Available: {item.isAvailable ? 'Yes' : 'No'}</p>
              
              {/* Enhanced product information */}
              {product && (
                <div className="product-details">
                  <p>Categories: {product.categories.map(c => c.name).join(', ')}</p>
                  <p>Tags: {product.tags.map(t => t.name).join(', ')}</p>
                  <p>Weight: {product.weight}kg</p>
                </div>
              )}
              
              <button onClick={() => removeFromCart({ itemId: item.id })}>
                Remove
              </button>
            </div>
          )
        })}
      </div>

      {/* Available Bundles */}
      <div className="available-bundles">
        <h3>Available Bundles</h3>
        {getAvailableBundles().map(bundle => (
          <div key={bundle.bundleId} className="bundle">
            <h4>{bundle.title}</h4>
            <p>Savings: ${bundle.savings}</p>
            <button onClick={() => addBundleToCart(bundle.bundleId)}>
              Add Bundle
            </button>
          </div>
        ))}
      </div>

      {/* Recommendations */}
      <div className="recommendations">
        <h3>Recommended Products</h3>
        {recommendations.map(product => (
          <div key={product.id} className="recommendation">
            <h4>{product.title}</h4>
            <p>${product.price.amount}</p>
            <p>In cart: {isProductInCart(product.id) ? 'Yes' : 'No'}</p>
            {isProductInCart(product.id) && (
              <p>Quantity in cart: {getProductQuantityInCart(product.id)}</p>
            )}
            <button onClick={() => handleEnhancedAddToCart(product.id)}>
              Add to Cart
            </button>
          </div>
        ))}
      </div>

      {/* Actions */}
      <div className="cart-actions">
        <button onClick={() => handleEnhancedAddToCart('sample-product-id')}>
          Add Sample Product (Enhanced)
        </button>
        <button onClick={handleBulkAdd}>
          Bulk Add Products
        </button>
        <button onClick={handleValidateCart}>
          Validate Cart
        </button>
        <button onClick={clearCart}>
          Clear Cart
        </button>
      </div>

      {/* Availability Check Result */}
      {availabilityCheck && (
        <div className="availability-check">
          <h3>Last Availability Check</h3>
          <p>Product: {availabilityCheck.productId}</p>
          <p>Available: {availabilityCheck.isAvailable ? 'Yes' : 'No'}</p>
          <p>Available Quantity: {availabilityCheck.availableQuantity}</p>
          {availabilityCheck.estimatedRestockDate && (
            <p>Estimated Restock: {availabilityCheck.estimatedRestockDate.toLocaleDateString()}</p>
          )}
        </div>
      )}
    </div>
  )
}

// Example of using the cart in a product listing component
export function ProductListWithCart({ products }: { products: Product[] }) {
  const { 
    addToCart, 
    isProductInCart, 
    getProductQuantityInCart,
    checkProductAvailability 
  } = useCart()

  const handleAddToCart = async (product: Product, variant?: ProductVariant) => {
    try {
      // Check availability first
      const availability = await checkProductAvailability(
        product.id, 
        variant?.id, 
        1
      )

      if (!availability.isAvailable) {
        alert(`Sorry, ${product.title} is not available`)
        return
      }

      await addToCart({
        productId: product.id,
        variantId: variant?.id,
        quantity: 1
      })
    } catch (error) {
      console.error('Failed to add to cart:', error)
    }
  }

  return (
    <div className="product-list">
      {products.map(product => (
        <div key={product.id} className="product-card">
          <h3>{product.title}</h3>
          <p>${product.price.amount}</p>
          <p>Available: {product.isAvailable ? 'Yes' : 'No'}</p>
          
          {isProductInCart(product.id) && (
            <p>In cart: {getProductQuantityInCart(product.id)}</p>
          )}
          
          {product.hasVariants ? (
            <div className="variants">
              {product.variants.map(variant => (
                <button
                  key={variant.id}
                  onClick={() => handleAddToCart(product, variant)}
                  disabled={!variant.available}
                >
                  Add {variant.title} to Cart
                </button>
              ))}
            </div>
          ) : (
            <button
              onClick={() => handleAddToCart(product)}
              disabled={!product.isAvailable}
            >
              Add to Cart
            </button>
          )}
        </div>
      ))}
    </div>
  )
}

export default EnhancedCartExample