import { db } from '@/lib/db'
import type { Product, ProductVariant, ProductCategory } from '../types/product'
import type { Money, ApiResponse } from '../types/base'
import type { Prisma } from '@prisma/client'

/**
 * Get a product by its slug
 */
export async function getProductBySlug(slug: string): Promise<ApiResponse<Product>> {
  try {
    const product = await db.product.findFirst({
      where: {
        slug,
        isVisible: true,
        status: 'active'
      },
      include: {
        images: {
          select: {
            id: true,
            url: true,
            altText: true,
            position: true,
            width: true,
            height: true
          }
        },
        categories: {
          select: {
            category: {
              select: {
                id: true,
                name: true,
                slug: true,
                description: true,
                image: true,
                position: true,
                isVisible: true,
                seoTitle: true,
                seoDescription: true,
                createdAt: true,
                updatedAt: true
              }
            }
          }
        },
        variants: {
          where: {
            available: true
          },
          select: {
            id: true,
            sku: true,
            title: true,
            price: true,
            compareAtPrice: true,
            inventoryQuantity: true,
            inventoryPolicy: true,
            fulfillmentService: true,
            inventoryManagement: true,
            available: true,
            position: true,
            barcode: true,
            taxable: true,
            requiresShipping: true,
            trackQuantity: true,
            continueSellingWhenOutOfStock: true,
            options: true
          }
        },
        options: {
          select: {
            id: true,
            name: true,
            position: true,
            values: true
          }
        }
      }
    })

    if (!product) {
      return {
        success: false,
        error: {
          code: 'PRODUCT_NOT_FOUND',
          message: `Product with slug ${slug} not found`
        }
      }
    }

    // Transform product data to match our domain types
    const transformedProduct: Product = {
      id: product.id,
      title: product.title,
      slug: product.slug,
      description: product.description,
      descriptionHtml: product.descriptionHtml || undefined,
      vendor: product.vendor || undefined,
      productType: product.productType || undefined,
      handle: product.handle,
      status: product.status as 'active' | 'draft' | 'archived',
      publishedAt: product.publishedAt || undefined,
      price: transformMoney(product.price, product.currency),
      compareAtPrice: product.compareAtPrice ? 
        transformMoney(product.compareAtPrice, product.currency) : 
        undefined,
      trackQuantity: product.trackQuantity,
      continueSellingWhenOutOfStock: product.continueSellingWhenOutOfStock,
      inventoryQuantity: product.inventoryQuantity,
      images: product.images.map(img => ({
        id: img.id,
        url: img.url,
        altText: img.altText || undefined,
        position: img.position
      })),
      hasVariants: product.hasVariants,
      variants: product.variants.map(variant => ({
        id: variant.id,
        productId: product.id,
        sku: variant.sku,
        title: variant.title,
        price: transformMoney(variant.price, product.currency),
        compareAtPrice: variant.compareAtPrice ? 
          transformMoney(variant.compareAtPrice, product.currency) : 
          undefined,
        inventoryQuantity: variant.inventoryQuantity,
        inventoryPolicy: variant.inventoryPolicy as 'deny' | 'continue',
        fulfillmentService: variant.fulfillmentService,
        inventoryManagement: variant.inventoryManagement,
        available: variant.available,
        options: variant.options,
        taxable: variant.taxable,
        requiresShipping: variant.requiresShipping,
        trackQuantity: variant.trackQuantity,
        continueSellingWhenOutOfStock: variant.continueSellingWhenOutOfStock,
        createdAt: new Date(),
        updatedAt: new Date()
      })),
      options: product.options.map(opt => ({
        id: opt.id,
        name: opt.name,
        position: opt.position,
        values: opt.values
      })),
      categories: product.categories.map(c => ({
        id: c.category.id,
        name: c.category.name,
        slug: c.category.slug,
        description: c.category.description || undefined,
        image: c.category.image || undefined,
        position: c.category.position,
        isVisible: c.category.isVisible,
        seoTitle: c.category.seoTitle || undefined,
        seoDescription: c.category.seoDescription || undefined,
        createdAt: c.category.createdAt,
        updatedAt: c.category.updatedAt,
        children: [] // Categories might have children but we're not fetching them here
      })),
      tags: [],
      collections: [],
      seo: {
        title: product.seoTitle || product.title,
        description: product.seoDescription || product.description
      },
      isGiftCard: product.isGiftCard,
      requiresShipping: product.requiresShipping,
      isTaxable: product.isTaxable,
      isVisible: product.isVisible,
      isAvailable: product.isAvailable,
      availableForSale: product.availableForSale,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt
    }

    return {
      success: true,
      data: transformedProduct
    }

  } catch (error) {
    console.error('Error fetching product by slug:', error)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An error occurred while fetching the product'
      }
    }
  }
}

function transformMoney(amount: number | Prisma.Decimal, currency: string): Money {
  return {
    amount: Number(amount),
    currency: currency || 'ZAR'
  }
}
