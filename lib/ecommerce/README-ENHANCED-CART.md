# Enhanced Cart System with Product Integration

This document describes the enhanced cart system that provides deep integration with the comprehensive product type system.

## Overview

The enhanced cart system extends the basic cart functionality with:

- **Product-aware operations**: Full product and variant information in cart items
- **Inventory validation**: Real-time availability checking
- **Smart recommendations**: AI-powered product suggestions based on cart contents
- **Bundle support**: Product bundle handling and recommendations
- **Advanced analytics**: Category analysis, weight calculations, and more
- **Bulk operations**: Efficient handling of multiple cart operations

## Key Features

### 1. Enhanced Cart Items

Cart items now include full product information:

```typescript
interface EnhancedCartItem extends CartItem {
  product?: Product
  variant?: ProductVariant
  availability?: ProductAvailability
  
  // Enhanced product information
  productImages?: ProductImage[]
  productCategories?: string[]
  productTags?: string[]
  
  // Inventory and availability
  inventoryQuantity?: number
  inventoryPolicy?: 'deny' | 'continue'
  trackQuantity?: boolean
  
  // Pricing enhancements
  originalPrice?: Money
  salePrice?: Money
  bulkPricing?: Array<{
    minQuantity: number
    price: Money
  }>
}
```

### 2. Product-Aware Cart Operations

#### Enhanced Add to Cart
```typescript
const enhancedInput: EnhancedAddToCartInput = {
  productId: 'prod-123',
  variantId: 'var-456',
  quantity: 2,
  validateInventory: true,
  validatePrice: true,
  customization: [
    {
      attributeId: 'color',
      value: 'blue'
    }
  ],
  subscription: {
    frequency: 'monthly',
    discount: 10
  }
}

await addToCart(enhancedInput)
```

#### Bulk Operations
```typescript
const result = await bulkAddToCart({
  items: [
    { productId: 'prod-1', variantId: 'var-1', quantity: 2 },
    { productId: 'prod-2', quantity: 1 },
    { productId: 'prod-3', variantId: 'var-3', quantity: 3 }
  ]
})
```

### 3. Product Information Helpers

```typescript
// Check if product is in cart
const inCart = isProductInCart('prod-123', 'var-456')

// Get quantity of product in cart
const quantity = getProductQuantityInCart('prod-123')

// Get full product information for cart item
const product = getCartItemProduct('cart-item-id')
const variant = getCartItemVariant('cart-item-id')
```

### 4. Cart Validation

```typescript
const validation = await validateCart()

if (!validation.isValid) {
  // Handle validation errors
  validation.errors.forEach(error => {
    console.log(`${error.type}: ${error.message}`)
  })
  
  // Handle pricing changes
  validation.pricingChanges.forEach(change => {
    console.log(`Price changed: ${change.oldPrice.amount} -> ${change.newPrice.amount}`)
  })
  
  // Handle inventory issues
  validation.inventoryIssues.forEach(issue => {
    console.log(`Inventory issue: requested ${issue.requestedQuantity}, available ${issue.availableQuantity}`)
  })
}
```

### 5. Product Availability Checking

```typescript
const availability = await checkProductAvailability('prod-123', 'var-456', 2)

if (!availability.isAvailable) {
  console.log(`Only ${availability.availableQuantity} available`)
  
  // Show alternative variants
  availability.alternativeVariants?.forEach(alt => {
    console.log(`Alternative: ${alt.title} - ${alt.availableQuantity} available`)
  })
}
```

### 6. Smart Recommendations

```typescript
// Get recommendations based on cart contents
const recommendations = await getRecommendations()

// Get alternative products for out-of-stock items
const alternatives = await getAlternativeProducts('prod-123')
```

### 7. Bundle Operations

```typescript
// Add a product bundle to cart
await addBundleToCart('bundle-123', 2)

// Get available bundles based on cart contents
const bundles = getAvailableBundles()
bundles.forEach(bundle => {
  console.log(`${bundle.title}: Save $${bundle.savings}`)
})
```

### 8. Cart Analytics

```typescript
// Get cart categories breakdown
const categories = getCartCategories()
categories.forEach(cat => {
  console.log(`${cat.categoryName}: ${cat.itemCount} items, $${cat.totalValue}`)
})

// Get cart metrics
const totalValue = getCartValue()
const totalWeight = getCartWeight()
const hasOutOfStock = hasOutOfStockItems()
```

## Enhanced Cart Types

### EnhancedCart
Extends the basic Cart with product-aware features:
- Product category analytics
- Smart recommendations
- Bundle opportunities
- Inventory warnings

### ProductCartValidation
Comprehensive validation including:
- Product-specific validations
- Pricing change detection
- Inventory issue identification

### CartRecommendationEngine
AI-powered recommendation system:
- Frequently bought together
- Similar products
- Complementary items
- Upsell opportunities

## Usage Examples

### Basic Enhanced Cart Usage

```typescript
import { useCart } from '../hooks/use-cart'

function MyCartComponent() {
  const {
    cart,
    enhancedCart,
    addToCart,
    validateCart,
    getRecommendations,
    isProductInCart,
    getCartCategories
  } = useCart({
    userId: 'user-123',
    autoFetch: true
  })

  // Enhanced add to cart with validation
  const handleAddToCart = async (productId: string) => {
    await addToCart({
      productId,
      quantity: 1,
      validateInventory: true,
      validatePrice: true
    })
  }

  return (
    <div>
      {/* Cart items with full product info */}
      {enhancedCart?.items.map(item => (
        <div key={item.id}>
          <h3>{item.productTitle}</h3>
          <p>Categories: {item.productCategories?.join(', ')}</p>
          <p>Weight: {item.variantWeight}kg</p>
          <p>Available: {item.isAvailable ? 'Yes' : 'No'}</p>
        </div>
      ))}
      
      {/* Category breakdown */}
      <div>
        <h3>Categories</h3>
        {getCartCategories().map(cat => (
          <div key={cat.categoryId}>
            {cat.categoryName}: {cat.itemCount} items
          </div>
        ))}
      </div>
    </div>
  )
}
```

### Product Listing with Cart Integration

```typescript
function ProductList({ products }: { products: Product[] }) {
  const { 
    addToCart, 
    isProductInCart, 
    checkProductAvailability 
  } = useCart()

  const handleAddToCart = async (product: Product) => {
    // Check availability first
    const availability = await checkProductAvailability(product.id, undefined, 1)
    
    if (!availability.isAvailable) {
      alert('Product not available')
      return
    }

    await addToCart({
      productId: product.id,
      quantity: 1,
      validateInventory: true
    })
  }

  return (
    <div>
      {products.map(product => (
        <div key={product.id}>
          <h3>{product.title}</h3>
          <p>${product.price.amount}</p>
          {isProductInCart(product.id) && <span>✓ In Cart</span>}
          <button onClick={() => handleAddToCart(product)}>
            Add to Cart
          </button>
        </div>
      ))}
    </div>
  )
}
```

## API Endpoints

The enhanced cart system expects these API endpoints:

- `GET /api/e-commerce/cart?includeProducts=true` - Fetch enhanced cart
- `POST /api/e-commerce/cart/add` - Add item with validation
- `POST /api/e-commerce/cart/bulk-add` - Bulk add items
- `POST /api/e-commerce/cart/validate` - Validate cart
- `GET /api/e-commerce/products/availability` - Check product availability
- `POST /api/e-commerce/cart/recommendations` - Get recommendations
- `GET /api/e-commerce/products/{id}/alternatives` - Get alternative products
- `POST /api/e-commerce/cart/add-bundle` - Add product bundle

## Benefits

1. **Better User Experience**: Real-time inventory checking and smart recommendations
2. **Reduced Cart Abandonment**: Proactive handling of out-of-stock items
3. **Increased Sales**: Bundle recommendations and upselling opportunities
4. **Data-Driven Insights**: Comprehensive cart analytics and category breakdown
5. **Robust Validation**: Prevent pricing and inventory issues at checkout
6. **Performance**: Efficient bulk operations and optimized data fetching

## Migration from Basic Cart

To migrate from the basic cart to the enhanced cart:

1. Update imports to include new types
2. Use `enhancedCart` instead of `cart` for full product information
3. Add product validation to cart operations
4. Implement recommendation displays
5. Add bundle handling
6. Update API endpoints to support enhanced features

The enhanced cart is backward compatible with the basic cart API, so migration can be gradual.