// Reports Service for E-commerce Analytics
import { PrismaClient } from '@prisma/client'
import { ApiResponse } from '../../types/base'
// If you need to use custom errors like ValidationError, NotFoundError, etc., import them from '../../types/base' as well:
// import { ValidationError, NotFoundError, UnauthorizedError, InsufficientStockError } from '../../types/base'

const prisma = new PrismaClient()

export interface SalesReport {
  period: string
  totalSales: number
  totalOrders: number
  averageOrderValue: number
  topProducts: Array<{
    id: string
    title: string
    sales: number
    revenue: number
  }>
  salesByDay: Array<{
    date: string
    sales: number
    orders: number
  }>
}

export interface CustomerReport {
  totalCustomers: number
  newCustomers: number
  returningCustomers: number
  topCustomers: Array<{
    id: string
    name: string
    email: string
    totalSpent: number
    orderCount: number
  }>
  customersByRegion: Array<{
    region: string
    count: number
  }>
}

export interface ProductReport {
  totalProducts: number
  activeProducts: number
  lowStockProducts: number
  topSellingProducts: Array<{
    id: string
    title: string
    sales: number
    revenue: number
    stock: number
  }>
  categoryPerformance: Array<{
    category: string
    sales: number
    revenue: number
  }>
}

export interface ReportFilters {
  startDate?: Date
  endDate?: Date
  category?: string
  region?: string
  limit?: number
}

export class ReportsService {
  async getSalesReport(filters: ReportFilters = {}): Promise<ApiResponse<SalesReport>> {
    try {
      const { startDate, endDate, limit = 10 } = filters
      const dateFilter = this.buildDateFilter(startDate, endDate)

      // Get total sales and orders
      const orderStats = await prisma.order.aggregate({
        where: {
          status: 'completed',
          ...dateFilter
        },
        _count: true,
        _sum: {
          total: true
        }
      })

      const totalOrders = orderStats._count || 0
      const totalSales = Number(orderStats._sum?.total || 0)
      const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0

      // Get top products
      const topProducts = await this.getTopSellingProducts(limit, startDate, endDate)

      // Get sales by day
      const salesByDay = await this.getSalesByDay(startDate, endDate)

      const report: SalesReport = {
        period: this.formatPeriod(startDate, endDate),
        totalSales,
        totalOrders,
        averageOrderValue,
        topProducts,
        salesByDay
      }

      return {
        success: true,
        data: report
      }
    } catch (error) {
      console.error('Sales report error:', error)
      return {
        success: false,
        error: {
          code: 'REPORT_ERROR',
          message: 'Failed to generate sales report'
        }
      }
    }
  }

  async getCustomerReport(filters: ReportFilters = {}): Promise<ApiResponse<CustomerReport>> {
    try {
      const { startDate, endDate, limit = 10 } = filters
      const dateFilter = this.buildDateFilter(startDate, endDate)

      // Get customer counts
      const totalCustomers = await prisma.user.count({
        where: dateFilter
      })

      // Calculate new vs returning (simplified)
      const newCustomers = Math.floor(totalCustomers * 0.3)
      const returningCustomers = totalCustomers - newCustomers

      // Get top customers
      const topCustomers = await prisma.user.findMany({
        where: {
          ...dateFilter,
          totalSpent: {
            gt: 0
          }
        },
        select: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          totalSpent: true,
          orderCount: true
        },
        orderBy: {
          totalSpent: 'desc'
        },
        take: limit
      })

      // Get customers by region (simplified)
      const customersByRegion = await prisma.userAddress.groupBy({
        by: ['province'],
        _count: {
          userId: true
        },
        orderBy: {
          _count: {
            userId: 'desc'
          }
        },
        take: 10
      })

      const report: CustomerReport = {
        totalCustomers,
        newCustomers,
        returningCustomers,
        topCustomers: topCustomers.map(customer => ({
          id: customer.id,
          name: `${customer.firstName || ''} ${customer.lastName || ''}`.trim() || 'Unknown',
          email: customer.email,
          totalSpent: Number(customer.totalSpent),
          orderCount: customer.orderCount
        })),
        customersByRegion: customersByRegion.map(region => ({
          region: region.province,
          count: region._count.userId
        }))
      }

      return {
        success: true,
        data: report
      }
    } catch (error) {
      console.error('Customer report error:', error)
      return {
        success: false,
        error: {
          code: 'REPORT_ERROR',
          message: 'Failed to generate customer report'
        }
      }
    }
  }

  async getProductReport(filters: ReportFilters = {}): Promise<ApiResponse<ProductReport>> {
    try {
      const { startDate, endDate, limit = 10 } = filters

      // Get product counts
      const totalProducts = await prisma.product.count()
      const activeProducts = await prisma.product.count({
        where: { status: 'active' }
      })
      const lowStockProducts = await prisma.product.count({
        where: {
          inventoryQuantity: {
            lt: 10
          }
        }
      })

      // Get top selling products
      const topSellingProducts = await this.getTopSellingProducts(limit, startDate, endDate)

      // Get category performance (simplified)
      const categoryPerformance = await prisma.productCategory.findMany({
        take: 10,
        select: {
          name: true,
          products: {
            select: {
              product: {
                select: {
                  orderItems: {
                    where: {
                      order: {
                        status: 'completed',
                        ...(startDate && endDate ? {
                          createdAt: {
                            gte: startDate,
                            lte: endDate
                          }
                        } : {})
                      }
                    },
                    select: {
                      quantity: true,
                      unitPrice: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      const report: ProductReport = {
        totalProducts,
        activeProducts,
        lowStockProducts,
        topSellingProducts: topSellingProducts.map(product => ({
          ...product,
          stock: 0 // Would need to calculate from inventory
        })),
        categoryPerformance: categoryPerformance.map(category => {
          const sales = category.products.reduce((total, productRel) => {
            return total + productRel.product.orderItems.reduce((sum, item) => sum + item.quantity, 0)
          }, 0)
          
          const revenue = category.products.reduce((total, productRel) => {
            return total + productRel.product.orderItems.reduce((sum, item) => sum + Number(item.unitPrice), 0)
          }, 0)

          return {
            category: category.name,
            sales,
            revenue
          }
        })
      }

      return {
        success: true,
        data: report
      }
    } catch (error) {
      console.error('Product report error:', error)
      return {
        success: false,
        error: {
          code: 'REPORT_ERROR',
          message: 'Failed to generate product report'
        }
      }
    }
  }

  private async getTopSellingProducts(limit: number, startDate?: Date, endDate?: Date) {
    const dateFilter = startDate && endDate ? {
      order: {
        createdAt: {
          gte: startDate,
          lte: endDate
        }
      }
    } : {}

    const topProducts = await prisma.orderItem.groupBy({
      by: ['productId'],
      where: {
        order: {
          status: 'completed',
          ...dateFilter
        }
      },
      _count: {
        productId: true
      },
      _sum: {
        unitPrice: true
      },
      orderBy: {
        _sum: {
          unitPrice: 'desc'
        }
      },
      take: limit
    })

    // Get product details
    const productIds = topProducts.map(p => p.productId)
    const products = await prisma.product.findMany({
      where: {
        id: {
          in: productIds
        }
      },
      select: {
        id: true,
        title: true
      }
    })

    return topProducts.map(item => {
      const product = products.find(p => p.id === item.productId)
      return {
        id: item.productId,
        title: product?.title || 'Unknown Product',
        sales: item._count.productId,
        revenue: Number(item._sum.unitPrice || 0)
      }
    })
  }

  private async getSalesByDay(startDate?: Date, endDate?: Date) {
    const dateFilter = this.buildDateFilter(startDate, endDate)

    const orders = await prisma.order.findMany({
      where: {
        status: 'completed',
        ...dateFilter
      },
      select: {
        createdAt: true,
        total: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    // Group by date
    const salesByDate = new Map<string, { sales: number; orders: number }>()

    orders.forEach(order => {
      const date = order.createdAt.toISOString().split('T')[0]
      const existing = salesByDate.get(date) || { sales: 0, orders: 0 }
      salesByDate.set(date, {
        sales: existing.sales + Number(order.total),
        orders: existing.orders + 1
      })
    })

    return Array.from(salesByDate.entries()).map(([date, data]) => ({
      date,
      ...data
    }))
  }

  private buildDateFilter(startDate?: Date, endDate?: Date) {
    const filter: any = {}

    if (startDate || endDate) {
      filter.createdAt = {}
      if (startDate) {
        filter.createdAt.gte = startDate
      }
      if (endDate) {
        filter.createdAt.lte = endDate
      }
    }

    return filter
  }

  private formatPeriod(startDate?: Date, endDate?: Date): string {
    if (!startDate && !endDate) {
      return 'All time'
    }
    
    if (startDate && endDate) {
      return `${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`
    }
    
    if (startDate) {
      return `From ${startDate.toISOString().split('T')[0]}`
    }
    
    if (endDate) {
      return `Until ${endDate.toISOString().split('T')[0]}`
    }
    
    return 'Custom period'
  }
}

export const reportsService = new ReportsService()
