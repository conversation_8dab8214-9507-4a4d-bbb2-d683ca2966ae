'use client'

import { useState, useEffect, useCallback } from 'react'

// Inventory management hooks

export interface InventoryItem {
  id: string
  productId: string
  variantId?: string
  sku: string
  productTitle: string
  variantTitle?: string
  locationId: string
  locationName: string
  quantityAvailable: number
  quantityReserved: number
  quantityOnHand: number
  reorderPoint: number
  maxStock: number
  lastUpdated: Date
  binLocation?: string
  price: number
}

export interface InventorySearchParams {
  query?: string
  page?: number
  limit?: number
  filters?: {
    location?: string
    stockLevel?: string
    productId?: string
    variantId?: string
  }
  sort?: {
    field: string
    direction: 'asc' | 'desc'
  }
}

export interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface UseInventoryOptions {
  initialParams?: InventorySearchParams
  autoFetch?: boolean
}

export interface UseInventoryReturn {
  inventory: InventoryItem[]
  loading: boolean
  error: string | null
  pagination: PaginationInfo | null
  searchInventory: (params?: InventorySearchParams) => Promise<void>
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing inventory search and listing
 */
export function useInventory(options: UseInventoryOptions = {}): UseInventoryReturn {
  const { initialParams = {}, autoFetch = true } = options

  const [inventory, setInventory] = useState<InventoryItem[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<PaginationInfo | null>(null)
  const [currentParams, setCurrentParams] = useState<InventorySearchParams>(initialParams)

  const searchInventory = useCallback(async (params?: InventorySearchParams) => {
    setLoading(true)
    setError(null)

    const searchParams = params || currentParams
    setCurrentParams(searchParams)

    try {
      // Build query parameters
      const queryParams = new URLSearchParams()
      
      if (searchParams.page) queryParams.append('page', searchParams.page.toString())
      if (searchParams.limit) queryParams.append('limit', searchParams.limit.toString())
      if (searchParams.query) queryParams.append('query', searchParams.query)
      
      if (searchParams.filters) {
        if (searchParams.filters.location) queryParams.append('location', searchParams.filters.location)
        if (searchParams.filters.stockLevel) queryParams.append('stockLevel', searchParams.filters.stockLevel)
        if (searchParams.filters.productId) queryParams.append('productId', searchParams.filters.productId)
        if (searchParams.filters.variantId) queryParams.append('variantId', searchParams.filters.variantId)
      }
      
      if (searchParams.sort) {
        queryParams.append('sortField', searchParams.sort.field)
        queryParams.append('sortDirection', searchParams.sort.direction)
      }

      // Make API call
      const response = await fetch(`/api/inventory?${queryParams.toString()}`)
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status}`)
      }
      
      const data = await response.json()
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch inventory data')
      }
      
      // Transform API response to match our interface
      const inventoryItems: InventoryItem[] = data.data.data.map((item: any) => ({
        id: item.id,
        productId: item.productId,
        variantId: item.variantId,
        sku: item.productSku || item.sku,
        productTitle: item.productTitle,
        variantTitle: item.variantTitle,
        locationId: item.locationId,
        locationName: item.location || 'Main Warehouse',
        quantityAvailable: item.availableStock || 0,
        quantityReserved: item.reservedStock || 0,
        quantityOnHand: item.currentStock || 0,
        reorderPoint: item.reorderPoint || 10,
        maxStock: item.maxStock || 100,
        lastUpdated: new Date(item.lastUpdated),
        binLocation: item.binLocation,
        price: item.costPrice || 0
      }))
      
      setInventory(inventoryItems)
      setPagination(data.data.pagination)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      setInventory([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [currentParams])

  const refetch = useCallback(() => {
    return searchInventory(currentParams)
  }, [searchInventory, currentParams])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch) {
      searchInventory(initialParams)
    }
  }, []) // Only run on mount

  return {
    inventory,
    loading,
    error,
    pagination,
    searchInventory,
    refetch,
    clearError
  }
}

export interface UseInventoryMutationsReturn {
  adjustInventory: (productId: string, variantId: string | undefined, adjustment: number, reason: string) => Promise<boolean>
  updateInventory: (productId: string, variantId: string | undefined, quantity: number, cost?: number) => Promise<boolean>
  reserveInventory: (productId: string, variantId: string | undefined, quantity: number, orderId: string) => Promise<boolean>
  releaseReservation: (productId: string, variantId: string | undefined, quantity: number, orderId: string) => Promise<boolean>
  loading: boolean
  error: string | null
  clearError: () => void
}

/**
 * Hook for inventory mutations
 */
export function useInventoryMutations(): UseInventoryMutationsReturn {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Function to fetch a single inventory item
  const fetchInventoryItem = useCallback(async (
    productId: string,
    variantId?: string
  ) => {
    try {
      const params = new URLSearchParams();
      params.append('productId', productId);
      if (variantId) {
        params.append('variantId', variantId);
      }
      
      const response = await fetch(`/api/inventory?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch inventory item');
      }
      
      return await response.json();
    } catch (err) {
      console.error('Error fetching inventory item:', err);
      return null;
    }
  }, [])

  const adjustInventory = useCallback(async (
    productId: string, 
    variantId: string | undefined, 
    adjustment: number, 
    reason: string
  ): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      // Make actual API call to adjust inventory
      const response = await fetch('/api/inventory', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
          variantId,
          quantity: adjustment > 0 ? adjustment : Math.abs(adjustment),
          adjustment: true,
          reason
        }),
      });

      const data = await response.json();
      
      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to adjust inventory');
      }
      
      // Refresh inventory data after adjustment
      if (productId) {
        await fetchInventoryItem(productId, variantId);
      }
      
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return false;
    } finally {
      setLoading(false)
    }
  }, [fetchInventoryItem])

  const updateInventory = useCallback(async (
    productId: string, 
    variantId: string | undefined, 
    quantity: number, 
    cost?: number
  ): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      // Make actual API call to update inventory
      const response = await fetch('/api/inventory', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
          variantId,
          quantity,
          cost
        }),
      });

      const data = await response.json();
      
      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to update inventory');
      }
      
      // Refresh inventory data after update
      if (productId) {
        await fetchInventoryItem(productId, variantId);
      }
      
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return false;
    } finally {
      setLoading(false)
    }
  }, [fetchInventoryItem])

  const reserveInventory = useCallback(async (
    productId: string, 
    variantId: string | undefined, 
    quantity: number, 
    orderId: string
  ): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      // Make actual API call to reserve inventory
      const response = await fetch('/api/inventory/reserve', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
          variantId,
          quantity,
          orderId
        }),
      });

      const data = await response.json();
      
      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to reserve inventory');
      }
      
      // Refresh inventory data after reservation
      if (productId) {
        await fetchInventoryItem(productId, variantId);
      }
      
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return false;
    } finally {
      setLoading(false)
    }
  }, [fetchInventoryItem])

  const releaseReservation = useCallback(async (
    productId: string, 
    variantId: string | undefined, 
    quantity: number, 
    orderId: string
  ): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      // Make actual API call to release reservation
      const response = await fetch('/api/inventory/release', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productId,
          variantId,
          quantity,
          orderId
        }),
      });

      const data = await response.json();
      
      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to release reservation');
      }
      
      // Refresh inventory data after releasing reservation
      if (productId) {
        await fetchInventoryItem(productId, variantId);
      }
      
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred')
      return false;
    } finally {
      setLoading(false)
    }
  }, [fetchInventoryItem])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    adjustInventory,
    updateInventory,
    reserveInventory,
    releaseReservation,
    loading,
    error,
    clearError
  }
}