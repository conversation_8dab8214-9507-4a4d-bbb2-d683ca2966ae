// Hook for using the checkout store with additional utilities
'use client'

import { useEffect, useMemo } from 'react'
import { useCheckoutStore } from '../stores/checkout-store'
import { useAuth } from '@/components/auth-provider'

export function useCheckout() {
  // Use a stable reference to the store
  const store = useCheckoutStore(
    useMemo(() => (state) => state, [])
  );
  
  const { user, customer } = useAuth()
  
  // Auto-prefill user data when available
  useEffect(() => {
    if (user && customer && !store.formData.email) {
      store.prefillFromUser({
        email: user.email,
        firstName: customer.firstName,
        lastName: customer.lastName,
        phone: customer.phone,
        defaultShippingAddress: customer.defaultShippingAddress
      })
    }
  }, [user, customer, store])
  
  return store
}

// Selector hooks for specific parts of the store
export function useCheckoutStep() {
  return useCheckoutStore(
    useMemo(
      () => (state) => ({
        currentStep: state.currentStep,
        steps: state.steps,
        setCurrentStep: state.setCurrentStep,
        canProceedToNextStep: state.canProceedToNextStep
        // Removed getProgressPercentage to avoid potential circular dependencies
      }),
      []
    )
  );
}

export function useCheckoutForm() {
  return useCheckoutStore(
    useMemo(
      () => (state) => ({
        formData: state.formData,
        validation: state.validation,
        updateFormData: state.updateFormData,
        validateStep: state.validateStep,
        isValidating: state.isValidating
      }),
      []
    )
  );
}

export function useCheckoutShipping() {
  return useCheckoutStore(
    useMemo(
      () => (state) => ({
        selectedShippingMethod: state.selectedShippingMethod,
        setShippingMethod: state.setShippingMethod,
        formData: state.formData,
        updateFormData: state.updateFormData
      }),
      []
    )
  );
}

export function useCheckoutPayment() {
  return useCheckoutStore(
    useMemo(
      () => (state) => ({
        selectedPaymentMethod: state.selectedPaymentMethod,
        selectedPaymentGateway: state.selectedPaymentGateway,
        setPaymentMethod: state.setPaymentMethod,
        setPaymentGateway: state.setPaymentGateway,
        isProcessing: state.isProcessing,
        setProcessing: state.setProcessing
      }),
      []
    )
  );
}

export function useCheckoutPromo() {
  return useCheckoutStore(
    useMemo(
      () => (state) => ({
        appliedPromoCodes: state.appliedPromoCodes,
        promoCodeInput: state.promoCodeInput,
        isApplyingPromo: state.isApplyingPromo,
        promoError: state.promoError,
        applyPromoCode: state.applyPromoCode,
        removePromoCode: state.removePromoCode,
        setPromoCodeInput: state.setPromoCodeInput
      }),
      []
    )
  );
}

export function useCheckoutGift() {
  return useCheckoutStore(
    useMemo(
      () => (state) => ({
        giftWrap: state.giftWrap,
        giftWrapMessage: state.giftWrapMessage,
        setGiftWrap: state.setGiftWrap,
        isGift: state.formData.isGift,
        giftMessage: state.formData.giftMessage,
        updateFormData: state.updateFormData
      }),
      []
    )
  );
}

export function useCheckoutSummary() {
  return useCheckoutStore(
    useMemo(
      () => (state) => ({
        showOrderSummary: state.showOrderSummary,
        toggleOrderSummary: state.toggleOrderSummary,
        giftWrap: state.giftWrap,
        appliedPromoCodes: state.appliedPromoCodes,
        selectedShippingMethod: state.selectedShippingMethod,
        selectedPaymentMethod: state.selectedPaymentMethod
      }),
      []
    )
  );
}