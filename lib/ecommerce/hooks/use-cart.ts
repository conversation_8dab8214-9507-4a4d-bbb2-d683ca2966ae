// React hooks for cart management
'use client'

import { useState, useEffect, useCallback } from 'react'
import { 
  Cart, 
  CartItem,
  AddToCartInput,
  UpdateCartItemInput,
  RemoveFromCartInput,
  ApplyDiscountInput,
  RemoveDiscountInput,
  BulkAddToCartInput,
  BulkAddResult,
  CartValidationResult,
  EnhancedCartItem,
  ProductAvailabilityCheck,
  CartItemWithProduct,
  EnhancedCart,
  EnhancedAddToCartInput,
  ProductCartValidation,
} from '../types/cart'

import { ApiResponse } from '../types/base'
import { Product, ProductVariant, ProductAvailability } from '../types/product'

// Error handling utilities
function getErrorCode(error: unknown): string {
  if (error instanceof Error) {
    if (error.message.includes('NetworkError') || error.message.includes('fetch')) {
      return 'NETWORK_ERROR'
    }
    if (error.message.includes('timeout')) {
      return 'TIMEOUT_ERROR'
    }
    if (error.message.includes('HTTP 400')) {
      return 'VALIDATION_ERROR'
    }
    if (error.message.includes('HTTP 404')) {
      return 'NOT_FOUND'
    }
    if (error.message.includes('HTTP 409')) {
      return 'INSUFFICIENT_STOCK'
    }
    if (error.message.includes('HTTP 5')) {
      return 'SERVER_ERROR'
    }
  }
  return 'UNKNOWN_ERROR'
}

function isRetryableError(error: unknown): boolean {
  if (error instanceof Error) {
    const message = error.message.toLowerCase()
    return (
      message.includes('network') ||
      message.includes('timeout') ||
      message.includes('http 5') ||
      message.includes('fetch')
    )
  }
  return false
}

function getDetailedErrorMessage(code: string, originalMessage?: string): string {
  const errorMessages: Record<string, string> = {
    'INSUFFICIENT_STOCK': 'Sorry, there isn\'t enough stock available for this item. Please reduce the quantity or try again later.',
    'NOT_FOUND': 'This product is no longer available. Please refresh the page and try again.',
    'VALIDATION_ERROR': 'Please check your selection and try again. Make sure all required options are selected.',
    'NETWORK_ERROR': 'Connection issue detected. Please check your internet connection and try again.',
    'TIMEOUT_ERROR': 'The request took too long to complete. Please try again.',
    'SERVER_ERROR': 'Our servers are experiencing issues. Please try again in a few moments.',
    'PRODUCT_UNAVAILABLE': 'This product is currently unavailable for purchase.',
    'CART_LIMIT_EXCEEDED': 'You\'ve reached the maximum quantity allowed for this item.',
    'PRICE_CHANGED': 'The price of this item has changed. Please refresh the page to see the updated price.',
    'SESSION_EXPIRED': 'Your session has expired. Please refresh the page and try again.',
    'ADD_TO_CART_ERROR': 'Unable to add item to cart. Please try again.',
    'UNKNOWN_ERROR': 'An unexpected error occurred. Please try again.'
  }

  return errorMessages[code] || originalMessage || errorMessages['UNKNOWN_ERROR']
}

export interface UseCartOptions {
  userId?: string
  sessionId?: string
  autoFetch?: boolean
}

export interface UseCartReturn {
  cart: Cart | null
  enhancedCart: EnhancedCart | null
  loading: boolean
  error: { code: string; message: string } | null
  
  // Basic cart operations
  addToCart: (input: AddToCartInput | EnhancedAddToCartInput) => Promise<void>
  updateCartItem: (input: UpdateCartItemInput) => Promise<void>
  removeFromCart: (input: RemoveFromCartInput) => Promise<void>
  applyDiscount: (input: ApplyDiscountInput) => Promise<void>
  removeDiscount: (input: RemoveDiscountInput) => Promise<void>
  clearCart: () => Promise<void>
  refetch: () => Promise<void>
  clearError: () => void
  
  // Enhanced product-aware operations
  bulkAddToCart: (input: BulkAddToCartInput) => Promise<BulkAddResult>
  validateCart: () => Promise<ProductCartValidation>
  checkProductAvailability: (productId: string, variantId?: string, quantity?: number) => Promise<ProductAvailabilityCheck>
  getRecommendations: () => Promise<Product[]>
  getAlternativeProducts: (productId: string) => Promise<Product[]>
  
  // Product information helpers
  getCartItemProduct: (itemId: string) => Product | null
  getCartItemVariant: (itemId: string) => ProductVariant | null
  isProductInCart: (productId: string, variantId?: string) => boolean
  getProductQuantityInCart: (productId: string, variantId?: string) => number
  
  // Cart analytics
  getCartCategories: () => Array<{ categoryId: string; categoryName: string; itemCount: number; totalValue: number }>
  getCartValue: () => number
  getCartWeight: () => number
  hasOutOfStockItems: () => boolean
  
  // Bundle operations
  addBundleToCart: (bundleId: string, quantity?: number) => Promise<void>
  getAvailableBundles: () => Array<{ bundleId: string; title: string; savings: number }>
}

/**
 * Hook for managing shopping cart
 */
export function useCart(options: UseCartOptions = {}): UseCartReturn {
  const { userId, sessionId, autoFetch = true } = options

  const [cart, setCart] = useState<Cart | null>(null)
  const [enhancedCart, setEnhancedCart] = useState<EnhancedCart | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)

  const fetchCart = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      if (userId) params.append('userId', userId)
      if (sessionId) params.append('sessionId', sessionId)
      params.append('includeProducts', 'true') // Request enhanced cart data

      const response = await fetch(`/api/e-commerce/cart?${params.toString()}`)
      const result: ApiResponse<EnhancedCart> = await response.json()

      if (result.success && result.data) {
        setCart(result.data)
        setEnhancedCart(result.data)
      } else {
        setError(result.error || { code: 'FETCH_ERROR', message: 'Failed to fetch cart' })
        setCart(null)
        setEnhancedCart(null)
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      setCart(null)
      setEnhancedCart(null)
    } finally {
      setLoading(false)
    }
  }, [userId, sessionId])

  const addToCart = useCallback(async (input: AddToCartInput | EnhancedAddToCartInput) => {
    console.log('useCart: addToCart called', { input, userId, sessionId })
    setLoading(true)
    setError(null)

    // Retry configuration
    const maxRetries = 3
    const retryDelay = 1000 // 1 second

    const attemptAddToCart = async (attempt: number): Promise<void> => {
      try {
        const requestBody = {
          ...input,
          userId,
          sessionId
        }

        console.log(`useCart: Making request to /api/e-commerce/cart/add (attempt ${attempt})`, requestBody)

        const response = await fetch('/api/e-commerce/cart/add', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        })

        console.log('useCart: Response status', response.status)
        
        // Handle different HTTP status codes
        if (!response.ok) {
          if (response.status >= 500 && attempt < maxRetries) {
            // Server error - retry
            console.log(`useCart: Server error, retrying in ${retryDelay}ms...`)
            await new Promise(resolve => setTimeout(resolve, retryDelay))
            return attemptAddToCart(attempt + 1)
          }
          
          // Client error or max retries reached
          const errorText = await response.text()
          throw new Error(`HTTP ${response.status}: ${errorText}`)
        }

        const result: ApiResponse<EnhancedCart> = await response.json()
        console.log('useCart: Response data', result)

        if (result.success && result.data) {
          setCart(result.data)
          setEnhancedCart(result.data)
          console.log('useCart: Cart updated successfully')
        } else {
          console.error('useCart: Add to cart failed', result.error)
          
          // Create detailed error based on error code
          const errorCode = result.error?.code || 'ADD_TO_CART_ERROR'
          const errorMessage = getDetailedErrorMessage(errorCode, result.error?.message)
          
          setError({ 
            code: errorCode, 
            message: errorMessage 
          })
          throw new Error(errorMessage)
        }
      } catch (err) {
        if (attempt < maxRetries && isRetryableError(err)) {
          console.log(`useCart: Retryable error, retrying in ${retryDelay}ms...`, err)
          await new Promise(resolve => setTimeout(resolve, retryDelay))
          return attemptAddToCart(attempt + 1)
        }
        
        // Final error handling
        console.error('useCart: Final error', err)
        const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred'
        const errorCode = getErrorCode(err)
        
        setError({
          code: errorCode,
          message: getDetailedErrorMessage(errorCode, errorMessage)
        })
        throw err
      }
    }

    try {
      await attemptAddToCart(1)
    } finally {
      setLoading(false)
    }
  }, [userId, sessionId])

  const updateCartItem = useCallback(async (input: UpdateCartItemInput) => {
    setLoading(true)
    setError(null)

    try {
      const requestBody = {
        ...input,
        userId,
        sessionId
      }

      const response = await fetch('/api/e-commerce/cart/update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      const result: ApiResponse<EnhancedCart> = await response.json()

      if (result.success && result.data) {
        setCart(result.data)
        setEnhancedCart(result.data)
      } else {
        setError(result.error || { code: 'UPDATE_CART_ERROR', message: 'Failed to update cart item' })
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    } finally {
      setLoading(false)
    }
  }, [userId, sessionId])

  const removeFromCart = useCallback(async (input: RemoveFromCartInput) => {
    setLoading(true)
    setError(null)

    try {
      const requestBody = {
        ...input,
        userId,
        sessionId
      }

      const response = await fetch('/api/e-commerce/cart/remove', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      const result: ApiResponse<EnhancedCart> = await response.json()

      if (result.success && result.data) {
        setCart(result.data)
        setEnhancedCart(result.data)
      } else {
        setError(result.error || { code: 'REMOVE_FROM_CART_ERROR', message: 'Failed to remove item from cart' })
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    } finally {
      setLoading(false)
    }
  }, [userId, sessionId])

  const applyDiscount = useCallback(async (input: ApplyDiscountInput) => {
    setLoading(true)
    setError(null)

    try {
      const requestBody = {
        ...input,
        userId,
        sessionId
      }

      const response = await fetch('/api/e-commerce/cart/discount/apply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      const result: ApiResponse<EnhancedCart> = await response.json()

      if (result.success && result.data) {
        setCart(result.data)
        setEnhancedCart(result.data)
      } else {
        setError(result.error || { code: 'APPLY_DISCOUNT_ERROR', message: 'Failed to apply discount' })
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    } finally {
      setLoading(false)
    }
  }, [userId, sessionId])

  const removeDiscount = useCallback(async (input: RemoveDiscountInput) => {
    setLoading(true)
    setError(null)

    try {
      const requestBody = {
        ...input,
        userId,
        sessionId
      }

      const response = await fetch('/api/e-commerce/cart/discount/remove', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      const result: ApiResponse<EnhancedCart> = await response.json()

      if (result.success && result.data) {
        setCart(result.data)
        setEnhancedCart(result.data)
      } else {
        setError(result.error || { code: 'REMOVE_DISCOUNT_ERROR', message: 'Failed to remove discount' })
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    } finally {
      setLoading(false)
    }
  }, [userId, sessionId])

  const clearCart = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/cart/clear', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId, sessionId }),
      })

      const result: ApiResponse<void> = await response.json()

      if (result.success) {
        setCart(null)
        setEnhancedCart(null)
      } else {
        setError(result.error || { code: 'CLEAR_CART_ERROR', message: 'Failed to clear cart' })
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
    } finally {
      setLoading(false)
    }
  }, [userId, sessionId])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Enhanced product-aware operations
  const bulkAddToCart = useCallback(async (input: BulkAddToCartInput): Promise<BulkAddResult> => {
    setLoading(true)
    setError(null)

    try {
      const requestBody = {
        ...input,
        userId,
        sessionId
      }

      const response = await fetch('/api/e-commerce/cart/bulk-add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      const result: ApiResponse<BulkAddResult> = await response.json()

      if (result.success && result.data) {
        // Refresh cart after bulk add
        await fetchCart()
        return result.data
      } else {
        setError(result.error || { code: 'BULK_ADD_ERROR', message: 'Failed to bulk add items to cart' })
        throw new Error(result.error?.message || 'Failed to bulk add items to cart')
      }
    } catch (err) {
      setError({
        code: 'NETWORK_ERROR',
        message: err instanceof Error ? err.message : 'An unexpected error occurred'
      })
      throw err
    } finally {
      setLoading(false)
    }
  }, [userId, sessionId, fetchCart])

  const validateCart = useCallback(async (): Promise<ProductCartValidation> => {
    if (!cart) {
      throw new Error('No cart to validate')
    }

    try {
      const response = await fetch('/api/e-commerce/cart/validate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cartId: cart.id, userId, sessionId }),
      })

      const result: ApiResponse<ProductCartValidation> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        throw new Error(result.error?.message || 'Failed to validate cart')
      }
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'An unexpected error occurred')
    }
  }, [cart, userId, sessionId])

  const checkProductAvailability = useCallback(async (
    productId: string, 
    variantId?: string, 
    quantity: number = 1
  ): Promise<ProductAvailabilityCheck> => {
    try {
      const params = new URLSearchParams({
        productId,
        quantity: quantity.toString()
      })
      if (variantId) params.append('variantId', variantId)

      const response = await fetch(`/api/e-commerce/products/availability?${params.toString()}`)
      const result: ApiResponse<ProductAvailabilityCheck> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        throw new Error(result.error?.message || 'Failed to check product availability')
      }
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'An unexpected error occurred')
    }
  }, [])

  const getRecommendations = useCallback(async (): Promise<Product[]> => {
    if (!cart) return []

    try {
      const response = await fetch('/api/e-commerce/cart/recommendations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ cartId: cart.id, userId, sessionId }),
      })

      const result: ApiResponse<Product[]> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        return []
      }
    } catch (err) {
      console.error('Failed to get recommendations:', err)
      return []
    }
  }, [cart, userId, sessionId])

  const getAlternativeProducts = useCallback(async (productId: string): Promise<Product[]> => {
    try {
      const response = await fetch(`/api/e-commerce/products/${productId}/alternatives`)
      const result: ApiResponse<Product[]> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        return []
      }
    } catch (err) {
      console.error('Failed to get alternative products:', err)
      return []
    }
  }, [])

  // Product information helpers
  const getCartItemProduct = useCallback((itemId: string): Product | null => {
    if (!enhancedCart) return null
    const item = enhancedCart.items.find(item => item.id === itemId) as EnhancedCartItem
    return item?.product || null
  }, [enhancedCart])

  const getCartItemVariant = useCallback((itemId: string): ProductVariant | null => {
    if (!enhancedCart) return null
    const item = enhancedCart.items.find(item => item.id === itemId) as EnhancedCartItem
    return item?.variant || null
  }, [enhancedCart])

  const isProductInCart = useCallback((productId: string, variantId?: string): boolean => {
    if (!cart) return false
    return cart.items.some(item => 
      item.productId === productId && 
      (variantId ? item.variantId === variantId : true)
    )
  }, [cart])

  const getProductQuantityInCart = useCallback((productId: string, variantId?: string): number => {
    if (!cart) return 0
    return cart.items
      .filter(item => 
        item.productId === productId && 
        (variantId ? item.variantId === variantId : true)
      )
      .reduce((total, item) => total + item.quantity, 0)
  }, [cart])

  // Cart analytics
  const getCartCategories = useCallback(() => {
    if (!enhancedCart?.productCategories) return []
    return enhancedCart.productCategories.map(cat => ({
      categoryId: cat.categoryId,
      categoryName: cat.categoryName,
      itemCount: cat.itemCount,
      totalValue: cat.totalValue.amount
    }))
  }, [enhancedCart])

  const getCartValue = useCallback((): number => {
    return cart?.total.amount || 0
  }, [cart])

  const getCartWeight = useCallback((): number => {
    if (!enhancedCart) return 0
    return enhancedCart.items.reduce((total, item) => {
      const weight = (item as EnhancedCartItem).variantWeight || 0
      return total + (weight * item.quantity)
    }, 0)
  }, [enhancedCart])

  const hasOutOfStockItems = useCallback((): boolean => {
    if (!cart) return false
    return cart.items.some(item => !item.isAvailable)
  }, [cart])

  // Bundle operations
  const addBundleToCart = useCallback(async (bundleId: string, quantity: number = 1): Promise<void> => {
    setLoading(true)
    setError(null)

    try {
      const requestBody = {
        bundleId,
        quantity,
        userId,
        sessionId
      }

      const response = await fetch('/api/e-commerce/cart/add-bundle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      const result: ApiResponse<EnhancedCart> = await response.json()

      if (result.success && result.data) {
        setCart(result.data)
        setEnhancedCart(result.data)
      } else {
        setError(result.error || { code: 'ADD_BUNDLE_ERROR', message: 'Failed to add bundle to cart' })
      }
    } catch (err) {
      setError({
        code: 'NETWORK_ERROR',
        message: err instanceof Error ? err.message : 'An unexpected error occurred'
      })
    } finally {
      setLoading(false)
    }
  }, [userId, sessionId])

  const getAvailableBundles = useCallback(() => {
    if (!enhancedCart?.availableBundles) return []
    return enhancedCart.availableBundles.map(bundle => ({
      bundleId: bundle.bundleId,
      title: bundle.title,
      savings: bundle.potentialSavings.amount
    }))
  }, [enhancedCart])

  useEffect(() => {
    if (autoFetch) {
      fetchCart()
    }
  }, [autoFetch, userId, sessionId]) // Use primitive dependencies instead of fetchCart function

  return {
    cart,
    enhancedCart,
    loading,
    error,
    
    // Basic cart operations
    addToCart,
    updateCartItem,
    removeFromCart,
    applyDiscount,
    removeDiscount,
    clearCart,
    refetch: fetchCart,
    clearError,
    
    // Enhanced product-aware operations
    bulkAddToCart,
    validateCart,
    checkProductAvailability,
    getRecommendations,
    getAlternativeProducts,
    
    // Product information helpers
    getCartItemProduct,
    getCartItemVariant,
    isProductInCart,
    getProductQuantityInCart,
    
    // Cart analytics
    getCartCategories,
    getCartValue,
    getCartWeight,
    hasOutOfStockItems,
    
    // Bundle operations
    addBundleToCart,
    getAvailableBundles
  }
}
