// Hook for using the cart store
import { useEffect, useRef } from 'react'
import { useCartStore } from '../stores/cart-store'

interface UseCartStoreOptions {
  autoFetch?: boolean
  sessionId?: string | null
}

export function useCartStore2(options: UseCartStoreOptions = {}) {
  const { autoFetch = true, sessionId } = options
  
  const store = useCartStore()
  const initializedRef = useRef(false)
  const previousSessionIdRef = useRef<string | null>(null)
  
  // Set session ID if provided and only if it's different from the previous one
  useEffect(() => {
    if (sessionId && sessionId !== previousSessionIdRef.current) {
      previousSessionIdRef.current = sessionId
      store.setSessionId(sessionId)
    }
  }, [sessionId, store])
  
  // Fetch cart data if autoFetch is true, but only once on mount or when sessionId changes
  useEffect(() => {
    if (autoFetch && sessionId && (!initializedRef.current || sessionId !== previousSessionIdRef.current)) {
      initializedRef.current = true
      previousSessionIdRef.current = sessionId
      store.refetch()
    }
  }, [autoFetch, sessionId, store]) // Depend on sessionId and store, but store should be stable
  
  return store
}

// Export the original function name for backward compatibility
export const useCartStore1 = useCartStore2;