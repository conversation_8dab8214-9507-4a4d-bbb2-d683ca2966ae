// React hooks for payment management
'use client'

import { useState, useCallback, useEffect } from 'react'
import { ApiResponse } from '../types/base'
import { PaymentMethod, PaymentGateway } from '@/lib/payments/types'

// Simple cache to prevent multiple API calls
let paymentGatewaysCache: PaymentGatewaysResponse | null = null
let paymentGatewaysCachePromise: Promise<PaymentGatewaysResponse> | null = null

export interface PaymentMethodOption {
  id: string
  name: string
  description: string
  icon: string
  processingTime: string
  fees: {
    percentage: number
    fixed: number
  }
  supportedGateways: string[]
  enabled: boolean
}

export interface PaymentGatewayOption {
  id: string
  name: string
  supportedMethods: string[]
  supportedCurrencies: string[]
  enabled: boolean
}

export interface PaymentGatewaysResponse {
  paymentMethods: PaymentMethodOption[]
  gateways: PaymentGatewayOption[]
  defaultGateway: string | null
  defaultMethod: string
}

export interface PaymentRequest {
  orderId: string
  amount: number
  currency: 'ZAR'
  customerEmail: string
  customerName: string
  description: string
  paymentMethod?: string
  preferredGateway?: string
  returnUrl?: string
  cancelUrl?: string
  notifyUrl?: string
  metadata?: Record<string, any>
}

export interface PaymentResponse {
  success: boolean
  transactionId?: string
  paymentUrl?: string
  qrCode?: string
  reference?: string
  status?: string
  message?: string
  error?: string
  errorCode?: string
}

export interface UsePaymentGatewaysReturn {
  paymentMethods: PaymentMethodOption[]
  gateways: PaymentGatewayOption[]
  defaultGateway: string | null
  defaultMethod: string
  loading: boolean
  error: { code: string; message: string } | null
  getPaymentGateways: () => Promise<void>
  createPayment: (request: PaymentRequest) => Promise<PaymentResponse | null>
  calculateFees: (amount: number, method: string) => number
  clearError: () => void
}

/**
 * Hook for managing payment gateways and methods
 */
export function usePaymentGateways(): UsePaymentGatewaysReturn {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethodOption[]>([])
  const [gateways, setGateways] = useState<PaymentGatewayOption[]>([])
  const [defaultGateway, setDefaultGateway] = useState<string | null>(null)
  const [defaultMethod, setDefaultMethod] = useState<string>(PaymentMethod.CARD)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)

  const getPaymentGateways = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      // Check if we have cached data
      if (paymentGatewaysCache) {
        setPaymentMethods(paymentGatewaysCache.paymentMethods)
        setGateways(paymentGatewaysCache.gateways)
        setDefaultGateway(paymentGatewaysCache.defaultGateway)
        setDefaultMethod(paymentGatewaysCache.defaultMethod)
        setLoading(false)
        return
      }

      // Check if there's already a request in progress
      if (paymentGatewaysCachePromise) {
        const cachedData = await paymentGatewaysCachePromise
        setPaymentMethods(cachedData.paymentMethods)
        setGateways(cachedData.gateways)
        setDefaultGateway(cachedData.defaultGateway)
        setDefaultMethod(cachedData.defaultMethod)
        setLoading(false)
        return
      }

      // Make the API call and cache the promise
      paymentGatewaysCachePromise = (async () => {
        const response = await fetch('/api/e-commerce/payments/gateways')
        const result: ApiResponse<PaymentGatewaysResponse> = await response.json()

        if (result.success && result.data) {
          paymentGatewaysCache = result.data
          return result.data
        } else {
          throw new Error(result.error?.message || 'Failed to fetch payment gateways')
        }
      })()

      const data = await paymentGatewaysCachePromise
      setPaymentMethods(data.paymentMethods)
      setGateways(data.gateways)
      setDefaultGateway(data.defaultGateway)
      setDefaultMethod(data.defaultMethod)
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      setPaymentMethods([])
      setGateways([])
      // Clear the cache promise on error so it can be retried
      paymentGatewaysCachePromise = null
    } finally {
      setLoading(false)
    }
  }, [])

  const createPayment = useCallback(async (request: PaymentRequest): Promise<PaymentResponse | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/payments/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      })

      const result: ApiResponse<PaymentResponse> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || { code: 'PAYMENT_ERROR', message: 'Failed to create payment' })
        return null
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const calculateFees = useCallback((amount: number, method: string): number => {
    const paymentMethod = paymentMethods.find(m => m.id === method)
    if (!paymentMethod) return 0

    const percentageFee = (amount * paymentMethod.fees.percentage) / 100
    return percentageFee + paymentMethod.fees.fixed
  }, [paymentMethods])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Load payment gateways on mount
  useEffect(() => {
    getPaymentGateways()
  }, [getPaymentGateways])

  return {
    paymentMethods,
    gateways,
    defaultGateway,
    defaultMethod,
    loading,
    error,
    getPaymentGateways,
    createPayment,
    calculateFees,
    clearError
  }
}

export interface UseCheckoutReturn {
  processCheckout: (checkoutData: CheckoutData) => Promise<CheckoutResult | null>
  loading: boolean
  error: { code: string; message: string } | null
  clearError: () => void
}

export interface CheckoutData {
  // Customer information
  customerEmail: string
  customerPhone?: string
  customerName: string
  
  // Addresses
  shippingAddress: {
    firstName: string
    lastName: string
    address1: string
    address2?: string
    city: string
    province: string
    postalCode: string
    country: string
    phone?: string
  }
  billingAddress?: {
    firstName: string
    lastName: string
    address1: string
    address2?: string
    city: string
    province: string
    postalCode: string
    country: string
    phone?: string
  }
  
  // Shipping and payment
  shippingMethod: string
  paymentMethod: string
  preferredGateway?: string
  
  // Cart items
  items: Array<{
    productId: string
    variantId?: string
    quantity: number
    price: number
    name: string
    image?: string
    color?: string
    size?: string
  }>
  
  // Optional
  couponCode?: string
  notes?: string
}

export interface CheckoutResult {
  success: boolean
  orderId: string
  paymentUrl?: string
  error?: string
}

/**
 * Enhanced checkout hook with payment gateway integration
 */
export function useCheckout(): UseCheckoutReturn {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)

  const processCheckout = useCallback(async (checkoutData: CheckoutData): Promise<CheckoutResult | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...checkoutData,
          // Add gateway preference
          preferredGateway: checkoutData.preferredGateway
        }),
      })

      const result: ApiResponse<CheckoutResult> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || { code: 'CHECKOUT_ERROR', message: 'Failed to process checkout' })
        return null
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    processCheckout,
    loading,
    error,
    clearError
  }
}