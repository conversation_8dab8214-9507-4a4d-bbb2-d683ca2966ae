'use client'

import { useState, useEffect, useCallback } from 'react'
import {
  Product,
  ProductSearchParams,
  CreateProductInput,
  UpdateProductInput,
  ProductVariant,
  CreateVariantInput,
  UpdateVariantInput,
} from '../types/product'
import { PaginationInfo, PaginatedResponse, ApiResponse } from '../types/base'

// Note: Using API routes instead of direct service calls to avoid Prisma in browser

export interface UseProductsOptions {
  initialParams?: ProductSearchParams
  autoFetch?: boolean
}

export interface UseProductsReturn {
  products: Product[]
  loading: boolean
  error: { code: string; message: string } | null
  pagination: PaginatedResponse<Product>['pagination'] | null
  searchProducts: (params?: ProductSearchParams) => Promise<void>
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing product search and listing
 */
export function useProducts(options: UseProductsOptions = {}): UseProductsReturn {
  const { initialParams = {}, autoFetch = true } = options

  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)
  const [pagination, setPagination] = useState<PaginatedResponse<Product>['pagination'] | null>(null)
  const [currentParams, setCurrentParams] = useState<ProductSearchParams>(initialParams)

  const searchProducts = useCallback(async (params?: ProductSearchParams) => {
    setLoading(true)
    setError(null)

    try {
      const searchParams = params || currentParams
      // Only update currentParams if params were provided to avoid infinite loops
      if (params) {
        setCurrentParams(searchParams)
      }

      // Build query string from search parameters
      const queryParams = new URLSearchParams()

      if (searchParams.query) queryParams.set('query', searchParams.query)
      if (searchParams.page) queryParams.set('page', searchParams.page.toString())
      if (searchParams.limit) queryParams.set('limit', searchParams.limit.toString())
      if (searchParams.sort?.field) queryParams.set('sortBy', searchParams.sort.field)
      if (searchParams.sort?.direction) queryParams.set('sortOrder', searchParams.sort.direction)

      // Add filters
      if (searchParams.filters?.categoryIds?.length) queryParams.set('categoryIds', searchParams.filters.categoryIds.join(','))
      if (searchParams.filters?.tagIds?.length) queryParams.set('tagIds', searchParams.filters.tagIds.join(','))
      if (searchParams.filters?.collectionIds?.length) queryParams.set('collectionIds', searchParams.filters.collectionIds.join(','))
      if (searchParams.filters?.vendor) queryParams.set('vendor', searchParams.filters.vendor)
      if (searchParams.filters?.productType) queryParams.set('productType', searchParams.filters.productType)
      if (searchParams.filters?.status?.length) queryParams.set('status', searchParams.filters.status.join(','))
      if (searchParams.filters?.inStock !== undefined) queryParams.set('inStock', searchParams.filters.inStock.toString())
      if (searchParams.filters?.onSale !== undefined) queryParams.set('onSale', searchParams.filters.onSale.toString())
      if (searchParams.filters?.isVisible !== undefined) queryParams.set('isVisible', searchParams.filters.isVisible.toString())
      if (searchParams.filters?.priceRange?.min) queryParams.set('minPrice', searchParams.filters.priceRange.min.toString())
      if (searchParams.filters?.priceRange?.max) queryParams.set('maxPrice', searchParams.filters.priceRange.max.toString())

      const response = await fetch(`/api/e-commerce/products?${queryParams.toString()}`)
      const result: ApiResponse<PaginatedResponse<Product>> = await response.json()

      if (result.success && result.data) {
        setProducts(result.data.data || [])
        setPagination(result.data.pagination || null)
      } else {
        setError(result.error || { code: 'FETCH_ERROR', message: 'Failed to fetch products' })
        setProducts([])
        setPagination(null)
      }
    } catch (err) {
      console.error('Error fetching products:', err)
      setError({
        code: 'NETWORK_ERROR',
        message: err instanceof Error ? err.message : 'An unexpected error occurred'
      })
      setProducts([])
      setPagination(null)
    } finally {
      setLoading(false)
    }
  }, [currentParams])

  const refetch = useCallback(() => {
    return searchProducts(currentParams)
  }, [searchProducts, currentParams])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch) {
      searchProducts(initialParams)
    }
  }, [autoFetch, searchProducts])

  return {
    products,
    loading,
    error,
    pagination,
    searchProducts,
    refetch,
    clearError
  }
}

export interface UseProductOptions {
  productId?: string
  slug?: string
  autoFetch?: boolean
}

export interface UseProductReturn {
  product: Product | null
  loading: boolean
  error: { code: string; message: string } | null
  refetch: () => Promise<void>
  clearError: () => void
}

/**
 * Hook for managing a single product
 */
export function useProduct(options: UseProductOptions = {}): UseProductReturn {
  const { productId, slug, autoFetch = true } = options

  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)

  const fetchProduct = useCallback(async () => {
    if (!productId && !slug) {
      setProduct(null)
      return
    }

    setLoading(true)
    setError(null)

    try {
      const endpoint = slug
        ? `/api/e-commerce/products/slug/${slug}`
        : `/api/e-commerce/products/id/${productId}`

      const response = await fetch(endpoint)
      const result: ApiResponse<Product> = await response.json()

      if (result.success && result.data) {
        setProduct(result.data)
      } else {
        setError(result.error || { code: 'FETCH_ERROR', message: 'Failed to fetch product' })
        setProduct(null)
      }
    } catch (err) {
      setError({ 
        code: 'NETWORK_ERROR', 
        message: err instanceof Error ? err.message : 'An unexpected error occurred' 
      })
      setProduct(null)
    } finally {
      setLoading(false)
    }
  }, [productId, slug])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  useEffect(() => {
    if (autoFetch && (productId || slug)) {
      fetchProduct()
    }
  }, [autoFetch, productId, slug])

  return {
    product,
    loading,
    error,
    refetch: fetchProduct,
    clearError
  }
}

export interface UseProductMutationsReturn {
  createProduct: (data: CreateProductInput) => Promise<Product | null>
  updateProduct: (id: string, data: UpdateProductInput) => Promise<Product | null>
  deleteProduct: (id: string) => Promise<boolean>
  duplicateProduct: (id: string) => Promise<Product | null>
  createVariant: (productId: string, data: CreateVariantInput) => Promise<ProductVariant | null>
  updateVariant: (productId: string, variantId: string, data: UpdateVariantInput) => Promise<ProductVariant | null>
  deleteVariant: (productId: string, variantId: string) => Promise<boolean>
  loading: boolean
  error: { code: string; message: string } | null
  clearError: () => void
}

/**
 * Hook for product mutation operations (create, update, delete)
 */
export interface UseProductMutationsReturn {
  createProduct: (data: CreateProductInput) => Promise<Product | null>
  updateProduct: (id: string, data: UpdateProductInput) => Promise<Product | null>
  deleteProduct: (id: string) => Promise<boolean>
  duplicateProduct: (id: string) => Promise<Product | null>
  createVariant: (productId: string, data: CreateVariantInput) => Promise<ProductVariant | null>
  updateVariant: (productId: string, variantId: string, data: UpdateVariantInput) => Promise<ProductVariant | null>
  deleteVariant: (productId: string, variantId: string) => Promise<boolean>
  loading: boolean
  error: { code: string; message: string } | null
  clearError: () => void
}

/**
 * Hook for product mutation operations (create, update, delete)
 */
export function useProductMutations(): UseProductMutationsReturn {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<{ code: string; message: string } | null>(null)

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const createProduct = useCallback(async (data: CreateProductInput): Promise<Product | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/e-commerce/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result: ApiResponse<Product> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || { code: 'CREATE_ERROR', message: 'Failed to create product' })
        return null
      }
    } catch (err) {
      console.error('Error creating product:', err)
      setError({
        code: 'NETWORK_ERROR',
        message: err instanceof Error ? err.message : 'An unexpected error occurred'
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const updateProduct = useCallback(async (id: string, data: UpdateProductInput): Promise<Product | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/id/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result: ApiResponse<Product> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || { code: 'UPDATE_ERROR', message: 'Failed to update product' })
        return null
      }
    } catch (err) {
      console.error('Error updating product:', err)
      setError({
        code: 'NETWORK_ERROR',
        message: err instanceof Error ? err.message : 'An unexpected error occurred'
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const deleteProduct = useCallback(async (id: string): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/id/${id}`, {
        method: 'DELETE',
      })

      const result: ApiResponse<{ success: boolean }> = await response.json()

      if (result.success) {
        return true
      } else {
        setError(result.error || { code: 'DELETE_ERROR', message: 'Failed to delete product' })
        return false
      }
    } catch (err) {
      console.error('Error deleting product:', err)
      setError({
        code: 'NETWORK_ERROR',
        message: err instanceof Error ? err.message : 'An unexpected error occurred'
      })
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  const duplicateProduct = useCallback(async (id: string): Promise<Product | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/id/${id}/duplicate`, {
        method: 'POST',
      })

      const result: ApiResponse<Product> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || { code: 'DUPLICATE_ERROR', message: 'Failed to duplicate product' })
        return null
      }
    } catch (err) {
      console.error('Error duplicating product:', err)
      setError({
        code: 'NETWORK_ERROR',
        message: err instanceof Error ? err.message : 'An unexpected error occurred'
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const createVariant = useCallback(async (productId: string, data: CreateVariantInput): Promise<ProductVariant | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}/variants`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result: ApiResponse<ProductVariant> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || { code: 'CREATE_VARIANT_ERROR', message: 'Failed to create variant' })
        return null
      }
    } catch (err) {
      console.error('Error creating variant:', err)
      setError({
        code: 'NETWORK_ERROR',
        message: err instanceof Error ? err.message : 'An unexpected error occurred'
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const updateVariant = useCallback(async (productId: string, variantId: string, data: UpdateVariantInput): Promise<ProductVariant | null> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}/variants/${variantId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      const result: ApiResponse<ProductVariant> = await response.json()

      if (result.success && result.data) {
        return result.data
      } else {
        setError(result.error || { code: 'UPDATE_VARIANT_ERROR', message: 'Failed to update variant' })
        return null
      }
    } catch (err) {
      console.error('Error updating variant:', err)
      setError({
        code: 'NETWORK_ERROR',
        message: err instanceof Error ? err.message : 'An unexpected error occurred'
      })
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  const deleteVariant = useCallback(async (productId: string, variantId: string): Promise<boolean> => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/e-commerce/products/${productId}/variants/${variantId}`, {
        method: 'DELETE',
      })

      const result: ApiResponse<{ success: boolean }> = await response.json()

      if (result.success) {
        return true
      } else {
        setError(result.error || { code: 'DELETE_VARIANT_ERROR', message: 'Failed to delete variant' })
        return false
      }
    } catch (err) {
      console.error('Error deleting variant:', err)
      setError({
        code: 'NETWORK_ERROR',
        message: err instanceof Error ? err.message : 'An unexpected error occurred'
      })
      return false
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    createProduct,
    updateProduct,
    deleteProduct,
    duplicateProduct,
    createVariant,
    updateVariant,
    deleteVariant,
    loading,
    error,
    clearError
  }
} 

export interface UseProductVariantsReturn {
  variants: ProductVariant[]
  loading: boolean
  error: { code: string; message: string } | null
  refetch: () => Promise<void>
  clearError: () => void
} 
