import { PrismaClient, Prisma } from '@prisma/client'
import { InventoryService } from './inventory-service'
import {
  Order,
  OrderItem,
  ShippingAddress,
  CreateOrderRequest,
} from '../types/order'
// import { EmailService } from './email-service'

const prisma = new PrismaClient()

export class OrderService {
  private db: PrismaClient
  private inventoryService: InventoryService
  // private emailService: EmailService

  constructor(database?: PrismaClient) {
    this.db = database || prisma
    this.inventoryService = new InventoryService(database)
    // this.emailService = new EmailService()
  }

  async createOrder(request: CreateOrderRequest): Promise<Order> {
    try {
      // Generate order number
      const orderNumber = await this.generateOrderNumber()

      // Calculate totals
      const subtotal = request.items.reduce((sum: number, item: { price: number; quantity: number }) => sum + (item.price * item.quantity), 0)
      const shippingCost = await this.calculateShippingCost(request.shippingMethodId, request.shippingAddress)
      const taxAmount = this.calculateTax(subtotal)
      const discountCode = request.discountCodes?.[0]
      const discountAmount = discountCode ? await this.calculateDiscount(discountCode, subtotal) : 0
      const total = subtotal + shippingCost + taxAmount - discountAmount

      // Check inventory availability
      for (const item of request.items) {
        const available = await this.inventoryService.checkAvailability(item.productId, item.variantId, item.quantity)
        if (!available) {
          throw new Error(`Insufficient inventory for product: ${item.name}`)
        }
      }

      // Create order in database
      const order = await this.db.order.create({
        data: {
          orderNumber,
          userId: request.customer?.id,
          customerEmail: request.customer?.email || '',
          customerFirstName: request.customer?.firstName || request.shippingAddress.firstName,
          customerLastName: request.customer?.lastName || request.shippingAddress.lastName,
          customerPhone: request.customer?.phone,
          status: 'pending',
          paymentStatus: 'pending',
          itemCount: request.items.length,
          subtotal,
          totalShipping: shippingCost,
          totalTax: taxAmount,
          totalDiscount: discountAmount,
          total,
          currency: 'ZAR',
          shippingAddress: request.shippingAddress as any,
          billingAddress: (request.billingAddress || request.shippingAddress) as any,
          customerNote: request.customerNote,
          items: {
            create: request.items.map((item: any) => ({
              productId: item.productId,
              variantId: item.variantId,
              quantity: item.quantity,
              unitPrice: item.price,
              totalPrice: item.price * item.quantity,
              currency: 'ZAR',
              productTitle: item.name,
              productSlug: item.productId, // Will be updated with actual slug
              productImage: item.image,
              variantTitle: item.size || item.color || null,
              sku: `${item.productId}-${item.variantId || 'default'}`,
              fulfillmentStatus: 'unfulfilled',
              fulfillableQuantity: item.quantity,
              fulfilledQuantity: 0,
              returnableQuantity: item.quantity,
              refundableQuantity: item.quantity,
              compareAtPrice: item.price,
              weight: 0,
              weightUnit: 'kg',
              requiresShipping: true,
              isTaxable: true,
              customAttributes: {
                color: item.color,
                size: item.size
              }
            }))
          }
        },
        include: {
          items: true,
          user: true
        }
      })

      // Reserve inventory
      for (const item of request.items) {
        await this.inventoryService.reserveInventory(item.productId, item.variantId, item.quantity, order.id)
      }

      // Send order confirmation email
      // await this.emailService.sendOrderConfirmation(this.formatOrder(order))

      return this.formatOrder(order)
    } catch (error) {
      console.error('Order creation error:', error)
      throw new Error(`Could not create order: ${(error as Error).message}`)
    }
  }

  async getOrder(orderId: string): Promise<Order | null> {
    try {
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: { items: true }
      })

      return order ? this.formatOrder(order) : null
    } catch (error) {
      console.error('Get order error:', error)
      return null
    }
  }

  async getOrderByNumber(orderNumber: string): Promise<Order | null> {
    try {
      const order = await this.db.order.findUnique({
        where: { orderNumber },
        include: { items: true }
      })

      return order ? this.formatOrder(order) : null
    } catch (error) {
      console.error('Get order by number error:', error)
      return null
    }
  }

  async getCustomerOrders(customerId: string, limit = 10, offset = 0): Promise<Order[]> {
    try {
      const orders = await this.db.order.findMany({
        where: { userId: customerId },
        take: limit,
        skip: offset,
        orderBy: {
          createdAt: 'desc'
        },
        include: { items: true }
      })
      return orders.map((order: Prisma.OrderGetPayload<{ include: { items: true } }>) => this.formatOrder(order))
    } catch (error) {
      console.error('Get customer orders error:', error)
      return []
    }
  }

  async getAdminOrders(filters: {
    page?: number
    limit?: number
    status?: string
    paymentStatus?: string
    customerEmail?: string
    orderNumber?: string
    startDate?: Date | string
    endDate?: Date | string
  } = {}): Promise<{ orders: Order[]; total: number }> {
    const {
      page = 1,
      limit = 20,
      status,
      paymentStatus,
      customerEmail,
      orderNumber,
      startDate,
      endDate,
    } = filters

    const skip = (page - 1) * limit
    const where: any = {}

    if (status) where.status = status
    if (paymentStatus) where.paymentStatus = paymentStatus
    if (customerEmail) where.customerEmail = { contains: customerEmail, mode: 'insensitive' }
    if (orderNumber) where.orderNumber = { contains: orderNumber, mode: 'insensitive' }

    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) where.createdAt.gte = new Date(startDate)
      if (endDate) where.createdAt.lte = new Date(endDate)
    }

    const [orders, total] = await Promise.all([
      this.db.order.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          items: true,
          fulfillments: {
            include: {
              items: true
            }
          },
          payments: true,
        },
      }),
      this.db.order.count({ where }),
    ])

    return {
      orders: orders.map((order: Prisma.OrderGetPayload<{ include: { items: true } }>) => this.formatOrder(order)),
      total,
    }
  }

  async getAdminOrderById(orderId: string): Promise<any | null> {
    const order = await this.db.order.findUnique({
      where: { id: orderId },
      include: {
        items: true,
        fulfillments: {
          include: {
            items: true,
          },
        },
        payments: true,
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
            phone: true,
          },
        },
      },
    })

    if (!order) return null

    return this.formatOrder(order)
  }

  async getAllOrders(filters?: {
    page?: number
    limit?: number
    status?: string
    paymentStatus?: string
    startDate?: Date
    endDate?: Date
  }): Promise<{ orders: Order[], total: number }> {
    try {
      const {
        page = 1,
        limit = 20,
        status,
        paymentStatus,
        startDate,
        endDate
      } = filters || {}

      const whereClause: any = {}

      if (status) {
        whereClause.status = status
      }

      if (paymentStatus) {
        whereClause.paymentStatus = paymentStatus
      }

      if (startDate || endDate) {
        whereClause.createdAt = {}
        if (startDate) whereClause.createdAt.gte = startDate
        if (endDate) whereClause.createdAt.lte = endDate
      }

      const [orders, total] = await Promise.all([
        this.db.order.findMany({
          skip: (page - 1) * limit,
          take: limit,
          where: whereClause,
          orderBy: {
            createdAt: 'desc'
          },
          include: {
            items: true
          }
        }),
        this.db.order.count({ where: whereClause })
      ])

      return {
        orders: orders.map(this.formatOrder),
        total
      }
    } catch (error) {
      console.error('Get all orders error:', error)
      return { orders: [], total: 0 }
    }
  }

  async updateOrderStatus(orderId: string, status: string, trackingNumber?: string): Promise<Order | null> {
    try {
      const order = await this.db.order.update({
        where: { id: orderId },
        data: {
          status,
          updatedAt: new Date()
        },
        include: { items: true }
      })

      // Create fulfillment with tracking number if provided
      if (trackingNumber && status === 'shipped') {
        await this.createFulfillment(orderId, {
          trackingNumber,
          status: 'shipped',
          items: order.items.map((item: any) => ({
            orderItemId: item.id,
            quantity: item.quantity
          }))
        })
      }

      // Send status update email
      // Note: Email notification would be implemented here
      console.log('Order status updated:', order.id, 'to', status)

      // If cancelled, release inventory
      if (status === 'cancelled') {
        for (const item of order.items) {
          await this.inventoryService.releaseReservation(item.productId, item.variantId || undefined, item.quantity, orderId)
        }
      }

      // If confirmed, commit inventory
      if (status === 'confirmed') {
        for (const item of order.items) {
          await this.inventoryService.commitReservation(item.productId, item.variantId || undefined, item.quantity, orderId)
        }
      }

      return this.formatOrder(order)
    } catch (error) {
      console.error('Update order status error:', error)
      return null
    }
  }



  async updateOrder(orderId: string, updateData: Prisma.OrderUpdateInput): Promise<Order | null> {
    try {
      const order = await prisma.order.update({
        where: { id: orderId },
        data: {
          ...updateData,
          updatedAt: new Date()
        },
        include: {
          items: true,
          user: true,
          fulfillments: true,
          payments: true
        }
      })

      return this.formatOrder(order)
    } catch (error) {
      console.error('Update order error:', error)
      return null
    }
  }

  async updatePaymentStatus(orderId: string, paymentStatus: string): Promise<Order | null> {
    try {
      const order = await prisma.order.update({
        where: { id: orderId },
        data: {
          paymentStatus,
          updatedAt: new Date()
        },
        include: { items: true }
      })

      return this.formatOrder(order)
    } catch (error) {
      console.error('Update payment status error:', error)
      return null
    }
  }

  async addOrderTags(orderId: string, tags: string[]): Promise<Order | null> { 
    try {
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        select: { tags: true }
      })

      if (!order) return null

      const currentTags = order.tags || []
      const newTags = [...new Set([...currentTags, ...tags])]

      const updatedOrder = await prisma.order.update({
        where: { id: orderId },
        data: {
          tags: newTags,
          updatedAt: new Date()
        },
        include: { items: true }
      })

      return this.formatOrder(updatedOrder)
    } catch (error) {
      console.error('Add order tags error:', error)
      return null
    }
  }

  async removeOrderTags(orderId: string, tags: string[]): Promise<Order | null> { 
    try {
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        select: { tags: true }
      })

      if (!order) return null

      const currentTags = order.tags || []
      const filteredTags = currentTags.filter((tag: string) => !tags.includes(tag))

      const updatedOrder = await prisma.order.update({
        where: { id: orderId },
        data: {
          tags: filteredTags,
          updatedAt: new Date()
        },
        include: { items: true }
      })

      return this.formatOrder(updatedOrder)
    } catch (error) {
      console.error('Remove order tags error:', error)
      return null
    }
  }

  async getOrdersByIds(orderIds: string[]): Promise<Order[]> {
    try {
      const orders = await prisma.order.findMany({
        where: { id: { in: orderIds } },
        include: { items: true }
      })
      return orders.map((order: Prisma.OrderGetPayload<{ include: { items: true } }>) => this.formatOrder(order))
    } catch (error) {
      console.error('Get orders by IDs error:', error)
      return []
    }
  } 

  async deleteOrder(orderId: string): Promise<boolean> {
    try {
      // First release any inventory reservations
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: { items: true }
      })

      if (order) {
        for (const item of order.items) {
          await this.inventoryService.releaseReservation(item.productId, item.variantId || undefined, item.quantity, orderId)
        }
      }

      // Delete the order (cascade will handle related records)
      await prisma.order.delete({
        where: { id: orderId }
      })

      return true
    } catch (error) {
      console.error('Delete order error:', error)
      return false
    }
  }

  async getOrderStats(startDate?: Date, endDate?: Date) {
    try {
      const whereClause = {
        ...(startDate && endDate && {
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        })
      }

      const [totalOrders, totalRevenue, pendingOrders, shippedOrders] = await Promise.all([
        prisma.order.count({ where: whereClause }),
        prisma.order.aggregate({
          where: { ...whereClause, paymentStatus: 'paid' },
          _sum: { total: true }
        }),
        prisma.order.count({ where: { ...whereClause, status: 'pending' } }),
        prisma.order.count({ where: { ...whereClause, status: 'shipped' } })
      ])

      return {
        totalOrders,
        totalRevenue: totalRevenue._sum.total || 0,
        pendingOrders,
        shippedOrders,
        averageOrderValue: totalOrders > 0 ? Number(totalRevenue._sum.total || 0) / totalOrders : 0
      }
    } catch (error) {
      console.error('Get order stats error:', error)
      return {
        totalOrders: 0,
        totalRevenue: 0,
        pendingOrders: 0,
        shippedOrders: 0,
        averageOrderValue: 0
      }
    }
  }

  async exportOrders(orders: Order[], options?: any): Promise<any> {
    try {
      const { format = 'csv', includeItems = true, includeAddresses = true } = options || {}

      const exportData = orders.map(order => {
        const baseData = {
          orderNumber: order.orderNumber,
          status: order.status,
          paymentStatus: order.paymentStatus,
          total: order.total,
          currency: order.currency,
          createdAt: order.createdAt,
          updatedAt: order.updatedAt
        }

        // Customer data would be included here if needed

        if (includeAddresses) {
          Object.assign(baseData, {
            shippingAddress: JSON.stringify(order.shippingAddress),
            billingAddress: JSON.stringify(order.billingAddress)
          })
        }

        if (includeItems) {
          Object.assign(baseData, {
            itemCount: order.items?.length || 0,
            items: JSON.stringify(order.items)
          })
        }

        return baseData
      })

      if (format === 'json') {
        return exportData
      }

      if (format === 'csv') {
        const headers = Object.keys(exportData[0] || {})
        const csvContent = [
          headers.join(','),
          ...exportData.map(row => headers.map(header => `"${(row as any)[header] || ''}"`).join(','))
        ].join('\n')
        return csvContent
      }

      // For XLSX format, you would need to implement Excel generation
      // This is a placeholder
      return exportData
    } catch (error) {
      console.error('Export orders error:', error)
      throw new Error('Failed to export orders')
    }
  }

  async createFulfillment(orderId: string, fulfillmentData: any): Promise<any> { 
    try {
      const fulfillment = await this.db.orderFulfillment.create({
        data: {
          orderId,
          status: fulfillmentData.status || 'pending',
          trackingNumber: fulfillmentData.trackingNumber,
          trackingUrl: fulfillmentData.trackingUrl,
          trackingCompany: fulfillmentData.carrier,
          estimatedDeliveryAt: fulfillmentData.estimatedDelivery,
          notes: fulfillmentData.notes,
          items: {
            create: fulfillmentData.items.map((item: any) => ({
              orderItemId: item.orderItemId,
              quantity: item.quantity
            }))
          }
        },
        include: {
          items: true
        }
      })

      return fulfillment
    } catch (error) {
      console.error('Create fulfillment error:', error)
      throw new Error('Failed to create fulfillment')
    }
  }

  async checkIfOrderFullyFulfilled(orderId: string): Promise<boolean> {
    try {
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          items: true,
          fulfillments: {
            include: {
              items: true,
            },
          },
        },
      });

      if (!order) {
        return false;
      }

      for (const orderItem of order.items) {
        const totalFulfilled = order.fulfillments.reduce((total: number, fulfillment: { items: { orderItemId: string; quantity: number }[] }) => {
          const fulfillmentItem = fulfillment.items.find(
            (item: { orderItemId: string }) => item.orderItemId === orderItem.id
          );
          return total + (fulfillmentItem?.quantity || 0);
        }, 0);

        if (totalFulfilled < orderItem.quantity) {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error('Check if order fully fulfilled error:', error);
      return false;
    }
  }

  async sendFulfillmentNotification(orderId: string, fulfillmentId: string): Promise<void> {
    try {
      // This would integrate with email service to send fulfillment notification
      console.log(`Sending fulfillment notification for order ${orderId}, fulfillment ${fulfillmentId}`)
      // await this.emailService.sendFulfillmentNotification(orderId, fulfillmentId)
    } catch (error) {
      console.error('Send fulfillment notification error:', error)
    }
  }

  async getOrderFulfillments(orderId: string): Promise<any[]> {
    try {
      const fulfillments = await prisma.orderFulfillment.findMany({
        where: { orderId },
        include: {
          items: true
        },
        orderBy: { createdAt: 'desc' }
      })

      return fulfillments
    } catch (error) {
      console.error('Get order fulfillments error:', error)
      return []
    }
  }

  async updateFulfillment(fulfillmentId: string, updateData: any): Promise<any | null> {
    try {
      const fulfillment = await prisma.orderFulfillment.update({
        where: { id: fulfillmentId },
        data: {
          ...updateData,
          updatedAt: new Date()
        },
        include: {
          items: true
        }
      })

      return fulfillment
    } catch (error) {
      console.error('Update fulfillment error:', error)
      return null
    }
  }

  async checkIfAllFulfillmentsDelivered(orderId: string): Promise<boolean> {
    try {
      const fulfillments = await prisma.orderFulfillment.findMany({
        where: { orderId }
      })

      if (fulfillments.length === 0) return false

      return fulfillments.every((fulfillment: { status: string }) => fulfillment.status === 'delivered')
    } catch (error) {
      console.error('Check if all fulfillments delivered error:', error)
      return false
    }
  }

  async sendFulfillmentUpdateNotification(orderId: string, fulfillmentId: string): Promise<void> { // @ts-ignore
    try {
      // This would integrate with email service to send fulfillment update notification
      console.log(`Sending fulfillment update notification for order ${orderId}, fulfillment ${fulfillmentId}`)
      // await this.emailService.sendFulfillmentUpdateNotification(orderId, fulfillmentId)
    } catch (error) {
      console.error('Send fulfillment update notification error:', error)
    }
  }

  public async generateOrderNumber(): Promise<string> {
    const today = new Date()
    const year = today.getFullYear().toString().slice(-2)
    const month = (today.getMonth() + 1).toString().padStart(2, '0')
    const day = today.getDate().toString().padStart(2, '0')

    const prefix = `ORD${year}${month}${day}`

    // Get the count of orders created today
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1)

    const todayOrderCount = await prisma.order.count({
      where: {
        createdAt: {
          gte: startOfDay,
          lt: endOfDay
        }
      }
    })

    const sequence = (todayOrderCount + 1).toString().padStart(4, '0')
    return `${prefix}${sequence}`
  }

  public async calculateShippingCost(method: string, _address: ShippingAddress): Promise<number> {
    // Implement shipping cost calculation based on method and address
    // This would integrate with courier services
    switch (method) {
      case 'standard':
        return 99.00
      case 'express':
        return 149.00
      case 'overnight':
        return 199.00
      case 'free':
        return 0.00
      default:
        return 99.00
    }
  }

  public calculateTax(subtotal: number): number {
    // South African VAT is 15%
    return subtotal * 0.15
  }

  public async calculateDiscount(_couponCode: string, _subtotal: number): Promise<number> {
    // Implement coupon/discount calculation
    // This would check against a coupons table
    return 0
  }

  public formatOrder(order: Prisma.OrderGetPayload<{ include: { items: true } }>): Order {
    // Ensure all fields from the Order type are correctly mapped and formatted
    return {
      ...order,
      id: order.id,
      orderNumber: order.orderNumber,
      customerId: order.userId,
      customerEmail: order.customerEmail,
      total: Number(order.total),
      subtotal: Number(order.subtotal),
      totalShipping: Number(order.totalShipping),
      totalTax: Number(order.totalTax),
      totalDiscount: Number(order.totalDiscount),
      currency: order.currency,
      status: order.status,
      paymentStatus: order.paymentStatus,
      fulfillmentStatus: order.fulfillmentStatus,
      shippingAddress:
        order.shippingAddress && typeof order.shippingAddress === 'string'
          ? JSON.parse(order.shippingAddress)
          : order.shippingAddress || null,
      billingAddress:
        order.billingAddress && typeof order.billingAddress === 'string'
          ? JSON.parse(order.billingAddress)
          : order.billingAddress || null,
      items: (order.items || []).map((item: any) => ({
        ...item,
        unitPrice: Number(item.unitPrice),
        totalPrice: Number(item.totalPrice),
      })),
      // Add missing properties with defaults
      appliedDiscounts: [],
      taxLines: [],
      taxIncluded: false,
      shippingMethod: null,
      fulfillments: [],
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
    } as Order
  }
}
