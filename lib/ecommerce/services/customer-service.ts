// Customer Service - Customer management functionality
import { <PERSON>rismaClient, Prism<PERSON>, User, User<PERSON>ddress as PrismaUserAddress, Decimal } from '@prisma/client'
import {
  Customer,
  CreateCustomerInput,
  UpdateCustomerInput,
  CustomerSearchParams,
  PaginatedResponse,
  ApiResponse,
  NotFoundError,
  ValidationError,
  UserAddress
} from '../types'


const prisma = new PrismaClient()

// Helper functions
function createSuccessResponse<T>(data: T): ApiResponse<T> {
  return {
    success: true,
    data
  }
}

function createErrorResponse(message: string, code: string = 'INTERNAL_ERROR'): ApiResponse<any> {
  return {
    success: false,
    error: {
      code,
      message
    }
  }
}

type PrismaUser = Prisma.UserGetPayload<{
  include: {
    addresses: true;
  }
}>

// Transform Prisma User to Customer with proper type definitions
function transformToCustomer(user: PrismaUser): Customer {
  return {
    id: user.id,
    email: user.email,
    firstName: user.firstName ?? undefined,
    lastName: user.lastName ?? undefined,
    displayName: user.displayName ?? undefined,
    phone: user.phone ?? undefined,
    dateOfBirth: user.dateOfBirth ?? undefined,
    gender: user.gender ?? undefined,
    emailVerified: user.emailVerified,
    phoneVerified: user.phoneVerified,
    lastLoginAt: user.lastLoginAt ?? undefined,
    acceptsMarketing: user.acceptsMarketing,
    preferredLanguage: user.preferredLanguage,
    preferredCurrency: user.preferredCurrency,
    timezone: user.timezone ?? undefined,
    avatar: user.avatar ?? undefined,
    bio: user.bio ?? undefined,
    status: user.isBlocked ? 'blocked' : (user.isActive ? 'active' : 'inactive'),
    customerSince: user.customerSince ?? undefined,
    totalSpent: user.totalSpent?.toNumber() ?? 0,
    orderCount: user.orderCount ?? 0,
    averageOrderValue: user.averageOrderValue?.toNumber() ?? 0,
    lastOrderAt: user.lastOrderAt ?? undefined,
    loyaltyPoints: user.loyaltyPoints ?? 0,
    loyaltyTier: user.loyaltyTier ?? undefined,
    metafields: user.metafields as Record<string, any> ?? {},
    tags: (user.tags as string[]) ?? [],
    notes: user.notes ?? undefined,
    addresses: user.addresses?.map(addr => ({
      id: addr.id,
      customerId: addr.userId,
      firstName: addr.firstName,
      lastName: addr.lastName,
      company: addr.company ?? undefined,
      address1: addr.address1,
      address2: addr.address2 ?? undefined,
      city: addr.city,
      province: addr.province,
      country: addr.country,
      postalCode: addr.postalCode,
      phone: addr.phone ?? undefined,
      isDefault: addr.isDefault,
      type: addr.type
    })) ?? []
  }
}

export class CustomerService {
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * Get all customers with optional filtering, sorting, and pagination
   */
  async getCustomers(params: CustomerSearchParams = {}): Promise<ApiResponse<PaginatedResponse<Customer>>> {
    try {
      const {
        query = '',
        page = 1,
        limit = 10,
        filters = {},
        sort = { field: 'createdAt', order: 'desc' }
      } = params

      // Build where clause
      const where: Prisma.UserWhereInput = {
        AND: [
          // Search query
          query ? {
            OR: [
              { email: { contains: query, mode: 'insensitive' } },
              { firstName: { contains: query, mode: 'insensitive' } },
              { lastName: { contains: query, mode: 'insensitive' } },
              { displayName: { contains: query, mode: 'insensitive' } },
              { phone: { contains: query } }
            ]
          } : {},
          // Status filter
          filters.status ? {
            ...(filters.status === 'blocked' ? { isBlocked: true } :
              filters.status === 'active' ? { isActive: true, isBlocked: false } :
                { isActive: false, isBlocked: false })
          } : {},
          // Marketing preference filter
          filters.acceptsMarketing !== undefined ? {
            acceptsMarketing: filters.acceptsMarketing
          } : {},
          // Date range filter
          filters.dateRange ? {
            customerSince: {
              gte: filters.dateRange.start,
              lte: filters.dateRange.end
            }
          } : {},
          // Loyalty tier filter
          filters.loyaltyTier ? {
            loyaltyTier: filters.loyaltyTier
          } : {}
        ]
      }

      // Get total count for pagination
      const total = await prisma.user.count({ where })

      // Get customers with pagination and sorting
      const customers = await prisma.user.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy: {
          [sort.field]: sort.order.toLowerCase()
        },
        include: {
          addresses: true
        }
      })

      // Transform and return response
      return createSuccessResponse({
        items: customers.map(transformToCustomer),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      })

    } catch (error) {
      console.error('Get customers error:', error)
      return createErrorResponse('Failed to fetch customers')
    }
  }

  /**
   * Get a single customer by ID
   */
  async getCustomerById(id: string): Promise<ApiResponse<Customer>> {
    try {
      const customer = await prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          phone: true,
          dateOfBirth: true,
          gender: true,
          emailVerified: true,
          phoneVerified: true,
          lastLoginAt: true,
          acceptsMarketing: true,
          preferredLanguage: true,
          preferredCurrency: true,
          timezone: true,
          avatar: true,
          bio: true,
          isActive: true,
          isBlocked: true,
          customerSince: true,
          totalSpent: true,
          orderCount: true,
          averageOrderValue: true,
          lastOrderAt: true,
          loyaltyPoints: true,
          loyaltyTier: true,
          metafields: true,
          tags: true,
          notes: true,
          createdAt: true,
          updatedAt: true
        }
      })

      if (!customer) {
        throw new NotFoundError('Customer', id)
      }

      return createSuccessResponse(transformToCustomer(customer))
    } catch (error) {
      console.error('Get customer by ID error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to fetch customer')
    }
  }

  /**
   * Get customer by email
   */
  async getCustomerByEmail(email: string): Promise<ApiResponse<Customer>> {
    try {
      const customer = await prisma.user.findUnique({
        where: { email: email.toLowerCase() },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          phone: true,
          dateOfBirth: true,
          gender: true,
          emailVerified: true,
          phoneVerified: true,
          lastLoginAt: true,
          acceptsMarketing: true,
          preferredLanguage: true,
          preferredCurrency: true,
          timezone: true,
          avatar: true,
          bio: true,
          isActive: true,
          isBlocked: true,
          customerSince: true,
          totalSpent: true,
          orderCount: true,
          averageOrderValue: true,
          lastOrderAt: true,
          loyaltyPoints: true,
          loyaltyTier: true,
          metafields: true,
          tags: true,
          notes: true,
          createdAt: true,
          updatedAt: true
        }
      })

      if (!customer) {
        throw new NotFoundError('Customer', email)
      }

      return createSuccessResponse(transformToCustomer(customer))
    } catch (error) {
      console.error('Get customer by email error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to fetch customer')
    }
  }

  /**
   * Create a new customer
   */
  async createCustomer(input: CreateCustomerInput): Promise<ApiResponse<Customer>> {
    try {
      // Validate required fields
      if (!input.email) {
        throw new ValidationError('Email is required')
      }

      if (!this.isValidEmail(input.email)) {
        throw new ValidationError('Invalid email format')
      }

      // Check if customer already exists
      const existingCustomer = await prisma.user.findUnique({
        where: { email: input.email.toLowerCase() }
      })

      if (existingCustomer) {
        throw new ValidationError('Customer with this email already exists')
      }

      // Note: Password handling would be done separately in auth system

      // Create customer
      const customer = await prisma.user.create({
        data: {
          email: input.email.toLowerCase(),
          firstName: input.firstName,
          lastName: input.lastName,
          displayName: input.displayName,
          phone: input.phone,
          dateOfBirth: input.dateOfBirth,
          gender: input.gender,
          acceptsMarketing: input.acceptsMarketing || false,
          preferredLanguage: input.preferredLanguage || 'en',
          preferredCurrency: input.preferredCurrency || 'ZAR',
          timezone: input.timezone,
          avatar: input.avatar,
          bio: input.bio,
          emailVerified: false,
          phoneVerified: false,
          isActive: true,
          isBlocked: false,
          totalSpent: 0,
          orderCount: 0,
          averageOrderValue: 0,
          customerSince: new Date(),
          metafields: input.metafields,
          tags: input.tags,
          notes: input.notes
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          phone: true,
          dateOfBirth: true,
          gender: true,
          emailVerified: true,
          phoneVerified: true,
          lastLoginAt: true,
          acceptsMarketing: true,
          preferredLanguage: true,
          preferredCurrency: true,
          timezone: true,
          avatar: true,
          bio: true,
          isActive: true,
          isBlocked: true,
          customerSince: true,
          totalSpent: true,
          orderCount: true,
          averageOrderValue: true,
          lastOrderAt: true,
          loyaltyPoints: true,
          loyaltyTier: true,
          metafields: true,
          tags: true,
          notes: true,
          createdAt: true,
          updatedAt: true
        }
      })

      return createSuccessResponse(transformToCustomer(customer))
    } catch (error) {
      console.error('Create customer error:', error)
      if (error instanceof ValidationError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to create customer')
    }
  }

  /**
   * Update an existing customer
   */
  async updateCustomer(input: UpdateCustomerInput): Promise<ApiResponse<Customer>> {
    try {
      // Check if customer exists
      const existingCustomer = await prisma.user.findUnique({
        where: { id: input.id }
      })

      if (!existingCustomer) {
        throw new NotFoundError('Customer', input.id)
      }

      // If email is being updated, check for conflicts
      if (input.email && input.email !== existingCustomer.email) {
        const conflictingCustomer = await prisma.user.findUnique({
          where: { email: input.email.toLowerCase() }
        })

        if (conflictingCustomer) {
          throw new ValidationError('Customer with this email already exists')
        }
      }

      // Update customer
      const customer = await prisma.user.update({
        where: { id: input.id },
        data: {
          email: input.email?.toLowerCase(),
          firstName: input.firstName,
          lastName: input.lastName,
          displayName: input.displayName,
          phone: input.phone,
          dateOfBirth: input.dateOfBirth,
          gender: input.gender,
          acceptsMarketing: input.acceptsMarketing,
          preferredLanguage: input.preferredLanguage,
          preferredCurrency: input.preferredCurrency,
          timezone: input.timezone,
          avatar: input.avatar,
          bio: input.bio,
          isActive: input.isActive,
          isBlocked: input.status === 'blocked',
          loyaltyPoints: input.loyaltyPoints,
          loyaltyTier: input.loyaltyTier,
          metafields: input.metafields,
          tags: input.tags,
          notes: input.notes
        },
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          phone: true,
          dateOfBirth: true,
          gender: true,
          emailVerified: true,
          phoneVerified: true,
          lastLoginAt: true,
          acceptsMarketing: true,
          preferredLanguage: true,
          preferredCurrency: true,
          timezone: true,
          avatar: true,
          bio: true,
          isActive: true,
          isBlocked: true,
          customerSince: true,
          totalSpent: true,
          orderCount: true,
          averageOrderValue: true,
          lastOrderAt: true,
          loyaltyPoints: true,
          loyaltyTier: true,
          metafields: true,
          tags: true,
          notes: true,
          createdAt: true,
          updatedAt: true
        }
      })

      return createSuccessResponse(transformToCustomer(customer))
    } catch (error) {
      console.error('Update customer error:', error)
      if (error instanceof NotFoundError || error instanceof ValidationError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to update customer')
    }
  }

  /**
   * Delete a customer
   */
  async deleteCustomer(id: string): Promise<ApiResponse<void>> {
    try {
      // Check if customer exists
      const existingCustomer = await prisma.user.findUnique({
        where: { id }
      })

      if (!existingCustomer) {
        throw new NotFoundError('Customer', id)
      }

      // Check if customer has orders
      const orderCount = await prisma.order.count({
        where: { userId: id }
      })

      if (orderCount > 0) {
        // Instead of deleting, mark as inactive
        await prisma.user.update({
          where: { id },
          data: { 
            isActive: false,
            isBlocked: true,
            notes: `Account deactivated on ${new Date().toISOString()}`
          }
        })
      } else {
        // Safe to delete if no orders
        await prisma.user.delete({
          where: { id }
        })
      }

      return createSuccessResponse(undefined)
    } catch (error) {
      console.error('Delete customer error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to delete customer')
    }
  }

  /**
   * Get customer addresses
   */
  async getCustomerAddresses(customerId: string): Promise<ApiResponse<UserAddress[]>> {
    try {
      const addresses = await prisma.userAddress.findMany({
        where: { userId: customerId },
        orderBy: { isDefault: 'desc' }
      })

      return createSuccessResponse(addresses as UserAddress[])
    } catch (error) {
      console.error('Get customer addresses error:', error)
      return createErrorResponse('Failed to fetch customer addresses')
    }
  }

  /**
   * Add customer address
   */
  async addCustomerAddress(customerId: string, address: Omit<UserAddress, 'id' | 'userId' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<UserAddress>> {
    try {
      // Check if customer exists
      const customer = await prisma.user.findUnique({
        where: { id: customerId }
      })

      if (!customer) {
        throw new NotFoundError('Customer', customerId)
      }

      // If this is set as default, unset other defaults
      if (address.isDefault) {
        await prisma.userAddress.updateMany({
          where: { userId: customerId },
          data: { isDefault: false }
        })
      }

      const newAddress = await prisma.userAddress.create({
        data: {
          ...address,
          userId: customerId
        }
      })

      return createSuccessResponse(newAddress as UserAddress)
    } catch (error) {
      console.error('Add customer address error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to add customer address')
    }
  }

  /**
   * Update customer address
   */
  async updateCustomerAddress(customerId: string, addressId: string, updates: Partial<UserAddress>): Promise<ApiResponse<UserAddress>> {
    try {
      const address = await prisma.userAddress.findFirst({
        where: {
          id: addressId,
          userId: customerId
        }
      })

      if (!address) {
        throw new NotFoundError('Address', addressId)
      }

      // If this is being set as default, unset other defaults
      if (updates.isDefault) {
        await prisma.userAddress.updateMany({
          where: { 
            userId: customerId,
            id: { not: addressId }
          },
          data: { isDefault: false }
        })
      }

      const updatedAddress = await prisma.userAddress.update({
        where: { id: addressId },
        data: updates
      })

      return createSuccessResponse(updatedAddress as UserAddress)
    } catch (error) {
      console.error('Update customer address error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to update customer address')
    }
  }

  /**
   * Delete customer address
   */
  async deleteCustomerAddress(customerId: string, addressId: string): Promise<ApiResponse<void>> {
    try {
      const address = await prisma.userAddress.findFirst({
        where: {
          id: addressId,
          userId: customerId
        }
      })

      if (!address) {
        throw new NotFoundError('Address', addressId)
      }

      await prisma.userAddress.delete({
        where: { id: addressId }
      })

      return createSuccessResponse(undefined)
    } catch (error) {
      console.error('Delete customer address error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to delete customer address')
    }
  }

  /**
   * Get customer analytics
   */
  async getCustomerAnalytics(customerId: string, startDate: Date, endDate: Date): Promise<ApiResponse<any>> {
    try {
      const customer = await prisma.user.findUnique({
        where: { id: customerId }
      })

      if (!customer) {
        throw new NotFoundError('Customer', customerId)
      }

      // Get orders in date range
      const orders = await prisma.order.findMany({
        where: {
          userId: customerId,
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          paymentStatus: 'paid'
        },
        include: {
          items: {
            include: {
              product: {
                include: {
                  categories: {
                    include: {
                      category: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      const totalSpent = orders.reduce((sum, order) => sum + Number(order.total), 0)
      const orderCount = orders.length
      const averageOrderValue = orderCount > 0 ? totalSpent / orderCount : 0

      // Calculate lifetime value (all time)
      const lifetimeOrders = await prisma.order.aggregate({
        where: {
          userId: customerId,
          paymentStatus: 'paid'
        },
        _sum: { total: true },
        _count: true
      })

      const lifetimeValue = Number(lifetimeOrders._sum?.total || 0)

      // Get last activity
      const lastActivity = await prisma.userActivity.findFirst({
        where: { userId: customerId },
        orderBy: { createdAt: 'desc' }
      })

      // Calculate preferred categories
      const categoryMap = new Map<string, number>()
      orders.forEach(order => {
        order.items.forEach(item => {
          item.product.categories.forEach(categoryRel => {
            categoryMap.set(categoryRel.category.name, (categoryMap.get(categoryRel.category.name) || 0) + item.quantity)
          })
        })
      })

      const preferredCategories = Array.from(categoryMap.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5)
        .map(([category]) => category)

      // Simple engagement score calculation
      const daysSinceLastOrder = customer.lastOrderAt 
        ? Math.floor((Date.now() - customer.lastOrderAt.getTime()) / (1000 * 60 * 60 * 24))
        : 999

      let engagementScore = 0
      if (daysSinceLastOrder < 30) engagementScore += 40
      else if (daysSinceLastOrder < 90) engagementScore += 20
      
      if (orderCount > 5) engagementScore += 30
      else if (orderCount > 1) engagementScore += 15

      if (averageOrderValue > 1000) engagementScore += 30
      else if (averageOrderValue > 500) engagementScore += 15

      // Churn risk assessment
      let churnRisk: 'low' | 'medium' | 'high' = 'low'
      if (daysSinceLastOrder > 180) churnRisk = 'high'
      else if (daysSinceLastOrder > 90) churnRisk = 'medium'

      const analytics: any = {
        userId: customerId,
        totalSpent,
        orderCount,
        averageOrderValue,
        lifetimeValue,
        acquisitionDate: customer.customerSince || customer.createdAt,
        acquisitionChannel: 'direct', // Would need tracking implementation
        lastActivityDate: lastActivity?.createdAt || customer.lastLoginAt || customer.createdAt,
        engagementScore,
        churnRisk,
        preferredCategories,
        preferredBrands: [], // Would need brand tracking
        period: {
          start: startDate,
          end: endDate
        }
      }

      return createSuccessResponse(analytics)
    } catch (error) {
      console.error('Get customer analytics error:', error)
      if (error instanceof NotFoundError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to fetch customer analytics')
    }
  }

  /**
   * Update customer stats (called when orders are placed)
   */
  async updateCustomerStats(customerId: string): Promise<ApiResponse<void>> {
    try {
      const stats = await prisma.order.aggregate({
        where: {
          userId: customerId,
          paymentStatus: 'paid'
        },
        _sum: { total: true },
        _count: true,
        _avg: { total: true }
      })

      const lastOrder = await prisma.order.findFirst({
        where: {
          userId: customerId,
          paymentStatus: 'paid'
        },
        orderBy: { createdAt: 'desc' }
      })

      await prisma.user.update({
        where: { id: customerId },
        data: {
          totalSpent: Number(stats._sum?.total || 0),
          orderCount: stats._count,
          averageOrderValue: Number(stats._avg?.total || 0),
          lastOrderAt: lastOrder?.createdAt
        }
      })

      return createSuccessResponse(undefined)
    } catch (error) {
      console.error('Update customer stats error:', error)
      return createErrorResponse('Failed to update customer stats')
    }
  }

  /**
   * Search customers
   */
  async searchCustomers(query: string, limit: number = 20): Promise<ApiResponse<Customer[]>> {
    try {
      const customers = await prisma.user.findMany({
        where: {
          OR: [
            { email: { contains: query, mode: 'insensitive' } },
            { firstName: { contains: query, mode: 'insensitive' } },
            { lastName: { contains: query, mode: 'insensitive' } },
            { displayName: { contains: query, mode: 'insensitive' } },
            { phone: { contains: query, mode: 'insensitive' } }
          ]
        },
        take: limit,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          displayName: true,
          phone: true,
          avatar: true,
          isActive: true,
          isBlocked: true,
          totalSpent: true,
          orderCount: true,
          lastOrderAt: true,
          createdAt: true
        }
      })

      return createSuccessResponse(customers.map(transformToCustomer))
    } catch (error) {
      console.error('Search customers error:', error)
      return createErrorResponse('Failed to search customers')
    }
  }
}