import { PrismaClient } from '@prisma/client'
import { prisma } from '../config/database'
import { ValidationError, NotFoundError } from '../utils/errors'

export interface CreateCollectionInput {
  title: string
  slug?: string
  description?: string
  image?: string
  sortOrder?: string
  isVisible?: boolean
  seoTitle?: string
  seoDescription?: string
  productIds?: string[]
}

export interface UpdateCollectionInput extends Partial<CreateCollectionInput> {
  id: string
}

export class CollectionService {
  private db: PrismaClient

  constructor(database?: PrismaClient) {
    this.db = database || prisma
  }

  /**
   * Create a new collection
   */
  async createCollection(input: CreateCollectionInput) {
    try {
      // Generate slug if not provided
      const slug = input.slug || input.title.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')

      // Check if slug already exists
      const existingCollection = await this.db.productCollection.findUnique({
        where: { slug }
      })

      if (existingCollection) {
        throw new ValidationError(`Collection with slug '${slug}' already exists`)
      }

      // Validate product IDs if provided
      if (input.productIds && input.productIds.length > 0) {
        const existingProducts = await this.db.product.findMany({
          where: { id: { in: input.productIds } },
          select: { id: true }
        })

        const existingProductIds = existingProducts.map(p => p.id)
        const invalidProductIds = input.productIds.filter(id => !existingProductIds.includes(id))

        if (invalidProductIds.length > 0) {
          throw new ValidationError(`Invalid product IDs: ${invalidProductIds.join(', ')}`)
        }
      }

      // Create collection
      const collection = await this.db.productCollection.create({
        data: {
          title: input.title,
          slug,
          description: input.description,
          image: input.image,
          sortOrder: input.sortOrder || 'manual',
          isVisible: input.isVisible !== false,
          seoTitle: input.seoTitle,
          seoDescription: input.seoDescription
        }
      })

      // Add product associations if provided
      if (input.productIds && input.productIds.length > 0) {
        await this.db.productCollectionRelation.createMany({
          data: input.productIds.map((productId, index) => ({
            productId,
            collectionId: collection.id,
            position: index
          })),
          skipDuplicates: true
        })
      }

      // Fetch complete collection with relations
      const completeCollection = await this.getCollectionById(collection.id)

      return {
        success: true,
        data: completeCollection.data!
      }
    } catch (error) {
      console.error('Error creating collection:', error)
      return {
        success: false,
        error: {
          code: error instanceof ValidationError ? 'VALIDATION_ERROR' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to create collection'
        }
      }
    }
  }

  /**
   * Update a collection
   */
  async updateCollection(input: UpdateCollectionInput) {
    try {
      // Check if collection exists
      const existingCollection = await this.db.productCollection.findUnique({
        where: { id: input.id }
      })

      if (!existingCollection) {
        throw new NotFoundError('Collection not found')
      }

      // Generate slug if title is being updated
      let slug = input.slug
      if (input.title && !slug) {
        slug = input.title.toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '')
      }

      // Check if slug already exists (excluding current collection)
      if (slug) {
        const slugExists = await this.db.productCollection.findFirst({
          where: {
            slug,
            id: { not: input.id }
          }
        })

        if (slugExists) {
          throw new ValidationError(`Collection with slug '${slug}' already exists`)
        }
      }

      // Validate product IDs if provided
      if (input.productIds !== undefined) {
        if (input.productIds.length > 0) {
          const existingProducts = await this.db.product.findMany({
            where: { id: { in: input.productIds } },
            select: { id: true }
          })

          const existingProductIds = existingProducts.map(p => p.id)
          const invalidProductIds = input.productIds.filter(id => !existingProductIds.includes(id))

          if (invalidProductIds.length > 0) {
            throw new ValidationError(`Invalid product IDs: ${invalidProductIds.join(', ')}`)
          }
        }
      }

      // Update collection
      const collection = await this.db.productCollection.update({
        where: { id: input.id },
        data: {
          title: input.title,
          slug: slug || existingCollection.slug,
          description: input.description,
          image: input.image,
          sortOrder: input.sortOrder,
          isVisible: input.isVisible,
          seoTitle: input.seoTitle,
          seoDescription: input.seoDescription
        }
      })

      // Handle product associations update
      if (input.productIds !== undefined) {
        // Delete existing product relations
        await this.db.productCollectionRelation.deleteMany({
          where: { collectionId: input.id }
        })

        // Add new product relations if any
        if (input.productIds.length > 0) {
          await this.db.productCollectionRelation.createMany({
            data: input.productIds.map((productId, index) => ({
              productId,
              collectionId: input.id,
              position: index
            })),
            skipDuplicates: true
          })
        }
      }

      // Fetch complete updated collection
      const completeCollection = await this.getCollectionById(input.id)

      return {
        success: true,
        data: completeCollection.data!
      }
    } catch (error) {
      console.error('Error updating collection:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' 
                : error instanceof ValidationError ? 'VALIDATION_ERROR' 
                : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to update collection'
        }
      }
    }
  }

  /**
   * Get collection by ID
   */
  async getCollectionById(id: string) {
    try {
      const collection = await this.db.productCollection.findUnique({
        where: { id },
        include: {
          products: {
            include: {
              product: {
                select: {
                  id: true,
                  title: true,
                  slug: true,
                  price: true,
                  images: true
                }
              }
            },
            orderBy: { position: 'asc' }
          }
        }
      })

      if (!collection) {
        return {
          success: false,
          error: 'Collection not found'
        }
      }

      return {
        success: true,
        data: {
          ...collection,
          productCount: collection.products.length
        }
      }
    } catch (error) {
      console.error('Error fetching collection:', error)
      return {
        success: false,
        error: 'Failed to fetch collection'
      }
    }
  }

  /**
   * Delete a collection
   */
  async deleteCollection(id: string) {
    try {
      // Check if collection exists
      const collection = await this.db.productCollection.findUnique({
        where: { id },
        include: {
          _count: {
            select: { products: true }
          }
        }
      })

      if (!collection) {
        throw new NotFoundError('Collection not found')
      }

      // Delete collection (relations will be deleted automatically due to CASCADE)
      await this.db.productCollection.delete({
        where: { id }
      })

      return {
        success: true,
        data: { id }
      }
    } catch (error) {
      console.error('Error deleting collection:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to delete collection'
        }
      }
    }
  }

  /**
   * Add products to collection
   */
  async addProductsToCollection(collectionId: string, productIds: string[]) {
    try {
      // Validate collection exists
      const collection = await this.db.productCollection.findUnique({
        where: { id: collectionId }
      })

      if (!collection) {
        throw new NotFoundError('Collection not found')
      }

      // Validate all product IDs exist
      const existingProducts = await this.db.product.findMany({
        where: { id: { in: productIds } },
        select: { id: true }
      })

      const existingProductIds = existingProducts.map(p => p.id)
      const invalidProductIds = productIds.filter(id => !existingProductIds.includes(id))

      if (invalidProductIds.length > 0) {
        throw new ValidationError(`Invalid product IDs: ${invalidProductIds.join(', ')}`)
      }

      // Get current max position
      const maxPosition = await this.db.productCollectionRelation.findFirst({
        where: { collectionId },
        orderBy: { position: 'desc' },
        select: { position: true }
      })

      const startPosition = (maxPosition?.position || -1) + 1

      // Add products to collection
      await this.db.productCollectionRelation.createMany({
        data: productIds.map((productId, index) => ({
          productId,
          collectionId,
          position: startPosition + index
        })),
        skipDuplicates: true
      })

      return {
        success: true,
        data: { collectionId, addedProductIds: productIds }
      }
    } catch (error) {
      console.error('Error adding products to collection:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' 
                : error instanceof ValidationError ? 'VALIDATION_ERROR' 
                : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to add products to collection'
        }
      }
    }
  }
}
