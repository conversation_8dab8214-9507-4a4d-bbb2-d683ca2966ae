import { PrismaClient } from '@prisma/client'
import { prisma } from '../config/database'
import { ValidationError, NotFoundError } from '../utils/errors'

export interface CreateTagInput {
  name: string
  slug?: string
  description?: string
  color?: string
  isVisible?: boolean
  productIds?: string[]
}

export interface UpdateTagInput extends Partial<CreateTagInput> {
  id: string
}

export class TagService {
  private db: PrismaClient

  constructor(database?: PrismaClient) {
    this.db = database || prisma
  }

  /**
   * Create a new tag
   */
  async createTag(input: CreateTagInput) {
    try {
      // Generate slug if not provided
      const slug = input.slug || input.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')

      // Check if slug already exists
      const existingTag = await this.db.productTag.findUnique({
        where: { slug }
      })

      if (existingTag) {
        throw new ValidationError(`Tag with slug '${slug}' already exists`)
      }

      // Validate product IDs if provided
      if (input.productIds && input.productIds.length > 0) {
        const existingProducts = await this.db.product.findMany({
          where: { id: { in: input.productIds } },
          select: { id: true }
        })

        const existingProductIds = existingProducts.map(p => p.id)
        const invalidProductIds = input.productIds.filter(id => !existingProductIds.includes(id))

        if (invalidProductIds.length > 0) {
          throw new ValidationError(`Invalid product IDs: ${invalidProductIds.join(', ')}`)
        }
      }

      // Create tag
      const tag = await this.db.productTag.create({
        data: {
          name: input.name,
          slug,
          description: input.description,
          color: input.color,
          isVisible: input.isVisible !== false
        }
      })

      // Add product associations if provided
      if (input.productIds && input.productIds.length > 0) {
        await this.db.productTagRelation.createMany({
          data: input.productIds.map((productId) => ({
            productId,
            tagId: tag.id
          })),
          skipDuplicates: true
        })
      }

      // Fetch complete tag with relations
      const completeTag = await this.getTagById(tag.id)

      return {
        success: true,
        data: completeTag.data!
      }
    } catch (error) {
      console.error('Error creating tag:', error)
      return {
        success: false,
        error: {
          code: error instanceof ValidationError ? 'VALIDATION_ERROR' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to create tag'
        }
      }
    }
  }

  /**
   * Update a tag
   */
  async updateTag(input: UpdateTagInput) {
    try {
      // Check if tag exists
      const existingTag = await this.db.productTag.findUnique({
        where: { id: input.id }
      })

      if (!existingTag) {
        throw new NotFoundError('Tag not found')
      }

      // Generate slug if name is being updated
      let slug = input.slug
      if (input.name && !slug) {
        slug = input.name.toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/^-+|-+$/g, '')
      }

      // Check if slug already exists (excluding current tag)
      if (slug) {
        const slugExists = await this.db.productTag.findFirst({
          where: {
            slug,
            id: { not: input.id }
          }
        })

        if (slugExists) {
          throw new ValidationError(`Tag with slug '${slug}' already exists`)
        }
      }

      // Validate product IDs if provided
      if (input.productIds !== undefined) {
        if (input.productIds.length > 0) {
          const existingProducts = await this.db.product.findMany({
            where: { id: { in: input.productIds } },
            select: { id: true }
          })

          const existingProductIds = existingProducts.map(p => p.id)
          const invalidProductIds = input.productIds.filter(id => !existingProductIds.includes(id))

          if (invalidProductIds.length > 0) {
            throw new ValidationError(`Invalid product IDs: ${invalidProductIds.join(', ')}`)
          }
        }
      }

      // Update tag
      const tag = await this.db.productTag.update({
        where: { id: input.id },
        data: {
          name: input.name,
          slug: slug || existingTag.slug,
          description: input.description,
          color: input.color,
          isVisible: input.isVisible
        }
      })

      // Handle product associations update
      if (input.productIds !== undefined) {
        // Delete existing product relations
        await this.db.productTagRelation.deleteMany({
          where: { tagId: input.id }
        })

        // Add new product relations if any
        if (input.productIds.length > 0) {
          await this.db.productTagRelation.createMany({
            data: input.productIds.map((productId) => ({
              productId,
              tagId: input.id
            })),
            skipDuplicates: true
          })
        }
      }

      // Fetch complete updated tag
      const completeTag = await this.getTagById(input.id)

      return {
        success: true,
        data: completeTag.data!
      }
    } catch (error) {
      console.error('Error updating tag:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' 
                : error instanceof ValidationError ? 'VALIDATION_ERROR' 
                : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to update tag'
        }
      }
    }
  }

  /**
   * Get tag by ID
   */
  async getTagById(id: string) {
    try {
      const tag = await this.db.productTag.findUnique({
        where: { id },
        include: {
          products: {
            include: {
              product: {
                select: {
                  id: true,
                  title: true,
                  slug: true,
                  price: true,
                  images: true
                }
              }
            }
          }
        }
      })

      if (!tag) {
        return {
          success: false,
          error: 'Tag not found'
        }
      }

      return {
        success: true,
        data: {
          ...tag,
          productCount: tag.products.length
        }
      }
    } catch (error) {
      console.error('Error fetching tag:', error)
      return {
        success: false,
        error: 'Failed to fetch tag'
      }
    }
  }

  /**
   * Delete a tag
   */
  async deleteTag(id: string) {
    try {
      // Check if tag exists
      const tag = await this.db.productTag.findUnique({
        where: { id },
        include: {
          _count: {
            select: { products: true }
          }
        }
      })

      if (!tag) {
        throw new NotFoundError('Tag not found')
      }

      // Delete tag (relations will be deleted automatically due to CASCADE)
      await this.db.productTag.delete({
        where: { id }
      })

      return {
        success: true,
        data: { id }
      }
    } catch (error) {
      console.error('Error deleting tag:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to delete tag'
        }
      }
    }
  }

  /**
   * Add products to tag
   */
  async addProductsToTag(tagId: string, productIds: string[]) {
    try {
      // Validate tag exists
      const tag = await this.db.productTag.findUnique({
        where: { id: tagId }
      })

      if (!tag) {
        throw new NotFoundError('Tag not found')
      }

      // Validate all product IDs exist
      const existingProducts = await this.db.product.findMany({
        where: { id: { in: productIds } },
        select: { id: true }
      })

      const existingProductIds = existingProducts.map(p => p.id)
      const invalidProductIds = productIds.filter(id => !existingProductIds.includes(id))

      if (invalidProductIds.length > 0) {
        throw new ValidationError(`Invalid product IDs: ${invalidProductIds.join(', ')}`)
      }

      // Add products to tag
      await this.db.productTagRelation.createMany({
        data: productIds.map((productId) => ({
          productId,
          tagId
        })),
        skipDuplicates: true
      })

      return {
        success: true,
        data: { tagId, addedProductIds: productIds }
      }
    } catch (error) {
      console.error('Error adding products to tag:', error)
      return {
        success: false,
        error: {
          code: error instanceof NotFoundError ? 'NOT_FOUND' 
                : error instanceof ValidationError ? 'VALIDATION_ERROR' 
                : 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Failed to add products to tag'
        }
      }
    }
  }

  /**
   * Get all tags
   */
  async getTags(includeHidden: boolean = false) {
    try {
      const where = includeHidden ? {} : { isVisible: true }

      const tags = await this.db.productTag.findMany({
        where,
        include: {
          _count: {
            select: { products: true }
          }
        },
        orderBy: { name: 'asc' }
      })

      return {
        success: true,
        data: tags.map(tag => ({
          ...tag,
          productCount: tag._count.products
        }))
      }
    } catch (error) {
      console.error('Error fetching tags:', error)
      return {
        success: false,
        error: 'Failed to fetch tags'
      }
    }
  }
}
