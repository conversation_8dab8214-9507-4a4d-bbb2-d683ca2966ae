// Cart Service - Shopping cart management functionality
import { Prisma, PrismaClient } from '@prisma/client'
import {
  Cart,
  CartItem,
  AddToCartInput,
  UpdateCartItemInput,
  RemoveFromCartInput,
  ApplyDiscountInput,
  RemoveDiscountInput,
  UpdateShippingAddressInput,
  SelectShippingMethodInput,
  CartValidationResult,
} from '../types/cart'
import {
  Money,
  ApiResponse,
  Address,
} from '../types/base'
import {
  NotFoundError,
  ValidationError,
  InsufficientStockError
} from '../types/errors'
import { createSuccessResponse, createErrorResponse } from '../utils/api-response'

export class CartService {
  private prisma: PrismaClient

  constructor() {
    this.prisma = new PrismaClient()
  }

  private toDecimal(amount: number): Prisma.Decimal {
    return new Prisma.Decimal(amount.toString())
  }

  private fromDecimal(decimal: Prisma.Decimal): number {
    return decimal.toNumber()
  }

  private toMoney(decimal: Prisma.Decimal, currency: string): Money {
    return {
      amount: this.fromDecimal(decimal),
      currency
    }
  }

  private createMoneyInput(amount: number, currency: string = 'ZAR'): Prisma.Decimal {
    return this.toDecimal(amount)
  }

  private handleError(error: unknown, defaultMessage: string): ApiResponse<never> {
    console.error(defaultMessage, error)
    if (error && typeof error === 'object' && 'name' in error) {
      const errorName = (error as any).name
      if (errorName === 'NotFoundError' || errorName === 'ValidationError' || errorName === 'InsufficientStockError') {
        return createErrorResponse((error as Error).message)
      }
    }
    return createErrorResponse(defaultMessage)
  }

  /**
   * Get cart by ID
   */
  async getCart(cartId: string): Promise<ApiResponse<Cart>> {
    try {
      const cart = await this.prisma.cart.findUnique({
        where: { id: cartId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  images: true
                }
              },
              variant: true
            }
          },
          appliedDiscounts: true
        }
      })

      if (!cart) {
        throw new NotFoundError('Cart', cartId)
      }

      // Transform cart data
      const transformedCart = this.transformCart(cart)

      return createSuccessResponse(transformedCart)
    } catch (error) {
      console.error('Get cart error:', error)
      if (error && typeof error === 'object' && 'name' in error && (error as any).name === 'NotFoundError') {
        return createErrorResponse((error as Error).message)
      }
      return createErrorResponse('Failed to fetch cart')
    }
  }

  /**
   * Get cart by session ID
   */
  async getCartBySession(sessionId: string): Promise<ApiResponse<Cart>> {
    try {
      const cart = await this.prisma.cart.findFirst({
        where: { sessionId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  images: true
                }
              },
              variant: true
            }
          },
          appliedDiscounts: true
        }
      })

      if (!cart) {
        // Create a new cart for this session
        return this.createCart(sessionId)
      }

      const transformedCart = this.transformCart(cart)
      return createSuccessResponse(transformedCart)
    } catch (error) {
      console.error('Get cart by session error:', error)
      return createErrorResponse('Failed to fetch cart')
    }
  }

  /**
   * Get cart by user ID
   */
  async getCartByUser(userId: string): Promise<ApiResponse<Cart>> {
    try {
      const cart = await this.prisma.cart.findFirst({
        where: { userId },
        include: {
          items: {
            include: {
              product: {
                include: {
                  images: true
                }
              },
              variant: true
            }
          },
          appliedDiscounts: true
        }
      })

      if (!cart) {
        // Create a new cart for this user
        return this.createCart(undefined, userId)
      }

      const transformedCart = this.transformCart(cart)
      return createSuccessResponse(transformedCart)
    } catch (error) {
      console.error('Get cart by user error:', error)
      return createErrorResponse('Failed to fetch cart')
    }
  }

  /**
   * Create a new cart
   */
  async createCart(sessionId?: string, userId?: string): Promise<ApiResponse<Cart>> {
    try {
      const cart = await this.prisma.cart.create({
        data: {
          sessionId,
          userId,
          status: 'active',
          currency: 'ZAR',
          subtotal: this.toDecimal(0),
          totalDiscount: this.toDecimal(0),
          totalTax: this.toDecimal(0),
          totalShipping: this.toDecimal(0),
          total: this.toDecimal(0),
          itemCount: 0,
          taxIncluded: false,
          lastActivityAt: new Date()
        },
        include: {
          items: {
            include: {
              product: {
                include: {
                  images: true
                }
              },
              variant: true
            }
          },
          appliedDiscounts: true
        }
      })

      const transformedCart = this.transformCart(cart)
      return createSuccessResponse(transformedCart)
    } catch (error) {
      console.error('Create cart error:', error)
      return createErrorResponse('Failed to create cart')
    }
  }

  /**
   * Get or create cart by user ID or session ID
   */
  async getOrCreateCart(userId?: string, sessionId?: string): Promise<ApiResponse<Cart>> {
    try {
      if (!userId && !sessionId) {
        return createErrorResponse('User ID or session ID is required')
      }

      // Try to find existing cart
      let cart = null

      if (userId) {
        const userCartResult = await this.getCartByUser(userId)
        if (userCartResult.success) {
          return userCartResult
        }
      }

      if (sessionId) {
        const sessionCartResult = await this.getCartBySession(sessionId)
        if (sessionCartResult.success) {
          return sessionCartResult
        }
      }

      // If no cart found, create a new one
      return this.createCart(sessionId, userId)
    } catch (error) {
      console.error('Get or create cart error:', error)
      return createErrorResponse('Failed to get or create cart')
    }
  }

  /**
   * Add item to cart
   */
  async addToCart(cartId: string, input: AddToCartInput): Promise<ApiResponse<Cart>> {
    try {
      // Validate product and variant
      const product = await this.prisma.product.findUnique({
        where: { id: input.productId },
        include: {
          variants: true,
          images: true
        }
      })

      if (!product) {
        throw new NotFoundError('Product', input.productId)
      }

      if (!product.isAvailable || product.status !== 'active') {
        throw new ValidationError('Product is not available')
      }

      let variant = null
      if (input.variantId) {
        variant = product.variants.find(v => v.id === input.variantId)
        if (!variant) {
          throw new NotFoundError('Product variant', input.variantId)
        }
        if (!variant.available) {
          throw new ValidationError('Product variant is not available')
        }
      }

      // Check stock availability
      const availableQuantity = variant ? variant.inventoryQuantity : product.inventoryQuantity
      if (product.trackQuantity && availableQuantity < input.quantity) {
        throw new InsufficientStockError(input.productId, input.quantity, availableQuantity)
      }

      // Check if item already exists in cart
      const existingItem = await this.prisma.cartItem.findFirst({
        where: {
          cartId,
          productId: input.productId,
          variantId: input.variantId || null
        }
      })

      if (existingItem) {
        // Update existing item quantity
        const newQuantity = existingItem.quantity + input.quantity
        
        if (product.trackQuantity && availableQuantity < newQuantity) {
          throw new InsufficientStockError(input.productId, newQuantity, availableQuantity)
        }

        await this.prisma.cartItem.update({
          where: { id: existingItem.id },
          data: {
            quantity: newQuantity,
            totalPrice: this.toDecimal(this.fromDecimal(variant?.price || product.price) * newQuantity),
            updatedAt: new Date()
          }
        })
      } else {
        // Create new cart item
        const unitPriceDecimal = variant?.price || product.price
        const unitPrice = this.fromDecimal(unitPriceDecimal)
        const totalPriceAmount = unitPrice * input.quantity

        await this.prisma.cartItem.create({
          data: {
            cartId,
            productId: input.productId,
            variantId: input.variantId,
            quantity: input.quantity,
            unitPrice: unitPriceDecimal,
            totalPrice: this.toDecimal(totalPriceAmount),
            currency: 'ZAR',
            productTitle: product.title,
            productSlug: product.slug,
            productImage: product.images[0]?.url,
            variantTitle: variant?.title,
            isAvailable: true,
            maxQuantity: availableQuantity,
            customAttributes: input.customAttributes,
            personalizedMessage: input.personalizedMessage,
            giftWrap: input.giftWrap || false,
            compareAtPrice: variant?.compareAtPrice || product.compareAtPrice,
            addedAt: new Date()
          }
        })
      }

      // Update cart totals
      await this.recalculateCart(cartId)

      // Return updated cart
      return this.getCart(cartId)
    } catch (error) {
      console.error('Add to cart error:', error)
      return this.handleError(error, 'Failed to add item to cart')
    }
  }

  /**
   * Update cart item
   */
  async updateCartItem(cartId: string, input: UpdateCartItemInput): Promise<ApiResponse<Cart>> {
    try {
      const cartItem = await this.prisma.cartItem.findFirst({
        where: {
          id: input.itemId,
          cartId
        },
        include: {
          product: true,
          variant: true
        }
      })

      if (!cartItem) {
        throw new NotFoundError('Cart item', input.itemId)
      }

      // If quantity is being updated, check stock
      if (input.quantity !== undefined) {
        const availableQuantity = cartItem.variant 
          ? cartItem.variant.inventoryQuantity 
          : cartItem.product.inventoryQuantity

        if (cartItem.product.trackQuantity && availableQuantity < input.quantity) {
          throw new InsufficientStockError(cartItem.productId, input.quantity, availableQuantity)
        }

        // Update quantity and total price
        const unitPrice = cartItem.unitPrice
        const totalPriceAmount = this.fromDecimal(unitPrice) * input.quantity

        await this.prisma.cartItem.update({
          where: { id: input.itemId },
          data: {
            quantity: input.quantity,
            totalPrice: this.toDecimal(totalPriceAmount),
            customAttributes: input.customAttributes,
            personalizedMessage: input.personalizedMessage,
            giftWrap: input.giftWrap,
            updatedAt: new Date()
          }
        })
      } else {
        // Update other attributes only
        await this.prisma.cartItem.update({
          where: { id: input.itemId },
          data: {
            customAttributes: input.customAttributes,
            personalizedMessage: input.personalizedMessage,
            giftWrap: input.giftWrap,
            updatedAt: new Date()
          }
        })
      }

      // Update cart totals
      await this.recalculateCart(cartId)

      // Return updated cart
      return this.getCart(cartId)
    } catch (error) {
      console.error('Update cart item error:', error)
      return this.handleError(error, 'Failed to update cart item')
    }
  }

  /**
   * Remove item from cart
   */
  async removeFromCart(cartId: string, input: RemoveFromCartInput): Promise<ApiResponse<Cart>> {
    try {
      const cartItem = await this.prisma.cartItem.findFirst({
        where: {
          id: input.itemId,
          cartId
        }
      })

      if (!cartItem) {
        throw new NotFoundError('Cart item', input.itemId)
      }

      await this.prisma.cartItem.delete({
        where: { id: input.itemId }
      })

      // Update cart totals
      await this.recalculateCart(cartId)

      // Return updated cart
      return this.getCart(cartId)
    } catch (error) {
      console.error('Remove from cart error:', error)
      if (error && typeof error === 'object' && 'name' in error && (error as any).name === 'NotFoundError') {
        return createErrorResponse((error as Error).message)
      }
      return createErrorResponse('Failed to remove item from cart')
    }
  }

  /**
   * Clear cart
   */
  async clearCart(cartId: string): Promise<ApiResponse<Cart>> {
    try {
      // Delete all cart items
      await this.prisma.cartItem.deleteMany({
        where: { cartId }
      })

      // Reset cart totals
      await this.prisma.cart.update({
        where: { id: cartId },
        data: {
          subtotal: this.toDecimal(0),
          totalDiscount: this.toDecimal(0),
          totalTax: this.toDecimal(0),
          totalShipping: this.toDecimal(0),
          total: this.toDecimal(0),
          itemCount: 0,
          lastActivityAt: new Date()
        }
      })

      return this.getCart(cartId)
    } catch (error) {
      console.error('Clear cart error:', error)
      return createErrorResponse('Failed to clear cart')
    }
  }

  /**
   * Apply discount to cart
   */
  async applyDiscount(input: ApplyDiscountInput): Promise<ApiResponse<Cart>> {
    try {
      // Find the discount/coupon
      const discount = await this.prisma.coupon.findUnique({
        where: { code: input.code }
      })

      if (!discount) {
        throw new NotFoundError('Discount code', input.code)
      }

      if (!discount.isActive || (discount.expiresAt && discount.expiresAt < new Date())) {
        throw new ValidationError('Discount code is not valid or has expired')
      }

      if (discount.usageLimit && discount.usageCount >= discount.usageLimit) {
        throw new ValidationError('Discount code has reached its usage limit')
      }

      // Check if discount is already applied
      const existingDiscount = await this.prisma.appliedDiscount.findFirst({
        where: {
          cartId: input.cartId,
          code: input.code
        }
      })

      if (existingDiscount) {
        throw new ValidationError('Discount code is already applied')
      }

      // Apply the discount
      await this.prisma.appliedDiscount.create({
        data: {
          cartId: input.cartId,
          code: input.code,
          title: discount.name,
          description: discount.description,
          type: discount.type,
          value: discount.value,
          amount: this.toDecimal(0) // Will be calculated in recalculateCart
        }
      })

      // Update cart totals
      await this.recalculateCart(input.cartId)

      return this.getCart(input.cartId)
    } catch (error) {
      console.error('Apply discount error:', error)
      return this.handleError(error, 'Failed to apply discount')
    }
  }

  /**
   * Remove discount from cart
   */
  async removeDiscount(input: RemoveDiscountInput): Promise<ApiResponse<Cart>> {
    try {
      const appliedDiscount = await this.prisma.appliedDiscount.findFirst({
        where: {
          id: input.discountId,
          cartId: input.cartId
        }
      })

      if (!appliedDiscount) {
        throw new NotFoundError('Applied discount', input.discountId)
      }

      await this.prisma.appliedDiscount.delete({
        where: { id: input.discountId }
      })

      // Update cart totals
      await this.recalculateCart(input.cartId)

      return this.getCart(input.cartId)
    } catch (error) {
      console.error('Remove discount error:', error)
      if (error && typeof error === 'object' && 'name' in error && (error as any).name === 'NotFoundError') {
        return createErrorResponse((error as Error).message)
      }
      return createErrorResponse('Failed to remove discount')
    }
  }

  /**
   * Update shipping address
   */
  async updateShippingAddress(input: UpdateShippingAddressInput): Promise<ApiResponse<Cart>> {
    try {
      await this.prisma.cart.update({
        where: { id: input.cartId },
        data: {
          shippingAddress: input.address as any,
          lastActivityAt: new Date()
        }
      })

      // Recalculate shipping costs
      await this.recalculateCart(input.cartId)

      return this.getCart(input.cartId)
    } catch (error) {
      console.error('Update shipping address error:', error)
      return createErrorResponse('Failed to update shipping address')
    }
  }

  /**
   * Select shipping method
   */
  async selectShippingMethod(input: SelectShippingMethodInput): Promise<ApiResponse<Cart>> {
    try {
      // In a real implementation, you would fetch the shipping method details
      // For now, we'll use mock data
      const shippingMethod = {
        id: input.shippingMethodId,
        title: 'Standard Shipping',
        description: '5-7 business days',
        price: { amount: 99, currency: 'ZAR' },
        estimatedDelivery: { min: 5, max: 7, unit: 'days' as const }
      }

      await this.prisma.cart.update({
        where: { id: input.cartId },
        data: {
          shippingMethod,
          totalShipping: this.toDecimal(shippingMethod.price.amount),
          lastActivityAt: new Date()
        }
      })

      // Recalculate totals
      await this.recalculateCart(input.cartId)

      return this.getCart(input.cartId)
    } catch (error) {
      console.error('Select shipping method error:', error)
      return createErrorResponse('Failed to select shipping method')
    }
  }

  /**
   * Validate cart
   */
  async validateCart(cartId: string): Promise<ApiResponse<CartValidationResult>> {
    try {
      const cart = await this.prisma.cart.findUnique({
        where: { id: cartId },
        include: {
          items: {
            include: {
              product: true,
              variant: true
            }
          }
        }
      })

      if (!cart) {
        throw new NotFoundError('Cart', cartId)
      }

      const errors: any[] = []
      const warnings: any[] = []

      // Validate each cart item
      for (const item of cart.items) {
        // Check product availability
        if (!item.product.isAvailable || item.product.status !== 'active') {
          errors.push({
            type: 'item_unavailable',
            itemId: item.id,
            message: `${item.productTitle} is no longer available`,
            details: { productId: item.productId }
          })
          continue
        }

        // Check variant availability
        if (item.variant && !item.variant.available) {
          errors.push({
            type: 'item_unavailable',
            itemId: item.id,
            message: `${item.productTitle} (${item.variantTitle}) is no longer available`,
            details: { productId: item.productId, variantId: item.variantId }
          })
          continue
        }

        // Check stock levels
        const availableQuantity = item.variant 
          ? item.variant.inventoryQuantity 
          : item.product.inventoryQuantity

        if (item.product.trackQuantity) {
          if (availableQuantity < item.quantity) {
            errors.push({
              type: 'insufficient_stock',
              itemId: item.id,
              message: `Only ${availableQuantity} units of ${item.productTitle} are available`,
              details: { 
                productId: item.productId, 
                requested: item.quantity, 
                available: availableQuantity 
              }
            })
          } else if (availableQuantity <= 5) {
            warnings.push({
              type: 'low_stock',
              itemId: item.id,
              message: `Only ${availableQuantity} units of ${item.productTitle} remaining`,
              details: { productId: item.productId, available: availableQuantity }
            })
          }
        }

        // Check price changes
        const currentPriceDecimal = item.variant?.price || item.product.price
        const currentPrice = this.fromDecimal(currentPriceDecimal)
        const itemUnitPrice = this.fromDecimal(item.unitPrice)
        if (currentPrice !== itemUnitPrice) {
          if (currentPrice > itemUnitPrice) {
            warnings.push({
              type: 'price_increase',
              itemId: item.id,
              message: `Price of ${item.productTitle} has increased`,
              details: { 
                oldPrice: this.toMoney(item.unitPrice, item.currency), 
                newPrice: this.toMoney(currentPriceDecimal, item.currency)
              }
            })
          }
        }
      }

      const result: CartValidationResult = {
        isValid: errors.length === 0,
        errors,
        warnings
      }

      return createSuccessResponse(result)
    } catch (error) {
      console.error('Validate cart error:', error)
      if (error && typeof error === 'object' && 'name' in error && (error as any).name === 'NotFoundError') {
        return createErrorResponse((error as Error).message)
      }
      return createErrorResponse('Failed to validate cart')
    }
  }

  /**
   * Recalculate cart totals
   */
  private async recalculateCart(cartId: string): Promise<void> {
    const cart = await this.prisma.cart.findUnique({
      where: { id: cartId },
      include: {
        items: true,
        appliedDiscounts: true
      }
    })

    if (!cart) return

    // Calculate subtotal
    const subtotal = cart.items.reduce((sum, item) => sum + this.fromDecimal(item.totalPrice), 0)

    // Calculate discounts
    let totalDiscount = 0
    for (const discount of cart.appliedDiscounts) {
      let discountAmount = 0
      if (discount.type === 'percentage') {
        discountAmount = (subtotal * this.fromDecimal(discount.value)) / 100
      } else if (discount.type === 'fixed_amount') {
        discountAmount = this.fromDecimal(discount.value)
      }
      totalDiscount += discountAmount

      // Update the discount amount
      await this.prisma.appliedDiscount.update({
        where: { id: discount.id },
        data: {
          amount: this.toDecimal(discountAmount)
        }
      })
    }

    // Calculate tax (15% VAT for South Africa)
    const taxRate = 0.15
    const taxableAmount = subtotal - totalDiscount
    const totalTax = taxableAmount * taxRate

    // Get shipping cost
    const shippingMethod = cart.shippingMethod as any
    const totalShipping = shippingMethod?.price?.amount || this.fromDecimal(cart.totalShipping)

    // Calculate total
    const total = subtotal - totalDiscount + totalTax + totalShipping

    // Update cart
    await this.prisma.cart.update({
      where: { id: cartId },
      data: {
        itemCount: cart.items.length,
        subtotal: this.toDecimal(subtotal),
        totalDiscount: this.toDecimal(totalDiscount),
        totalTax: this.toDecimal(totalTax),
        totalShipping: this.toDecimal(totalShipping),
        total: this.toDecimal(total),
        lastActivityAt: new Date()
      }
    })
  }

  /**
   * Transform cart data for API response
   */
  private transformCart(cart: Prisma.CartGetPayload<{
    include: {
      items: {
        include: {
          product: { include: { images: true } },
          variant: true 
        }
      },
      appliedDiscounts: true
    }
  }>): Cart {
    return {
      id: cart.id,
      userId: cart.userId || undefined,
      sessionId: cart.sessionId || undefined,
      items: cart.items.map(item => ({
        id: item.id,
        cartId: item.cartId,
        productId: item.productId,
        variantId: item.variantId || undefined,
        quantity: item.quantity,
        unitPrice: this.toMoney(item.unitPrice, item.currency),
        totalPrice: this.toMoney(item.totalPrice, item.currency),
        productTitle: item.productTitle,
        productSlug: item.productSlug,
        productImage: item.productImage || undefined,
        variantTitle: item.variantTitle || undefined,
        variantOptions: [], // Not stored in database, would need to be fetched from variant if needed
        isAvailable: item.isAvailable,
        maxQuantity: item.maxQuantity || undefined,
        customAttributes: (item.customAttributes as Record<string, any>) || {},
        personalizedMessage: item.personalizedMessage || undefined,
        giftWrap: item.giftWrap || false,
        compareAtPrice: item.compareAtPrice ? this.toMoney(item.compareAtPrice, item.currency) : undefined,
        discountAmount: item.discountAmount ? this.toMoney(item.discountAmount, item.currency) : undefined,
        discountReason: item.discountReason || undefined,
        addedAt: item.addedAt,
        updatedAt: item.updatedAt
      })),
      itemCount: cart.itemCount,
      subtotal: this.toMoney(cart.subtotal, cart.currency),
      totalDiscount: this.toMoney(cart.totalDiscount, cart.currency),
      totalTax: this.toMoney(cart.totalTax, cart.currency), 
      totalShipping: this.toMoney(cart.totalShipping, cart.currency),
      total: this.toMoney(cart.total, cart.currency),
      appliedDiscounts: cart.appliedDiscounts.map(d => ({
        id: d.id,
        code: d.code || undefined,
        title: d.title,
        description: d.description || undefined,
        type: d.type as 'percentage' | 'fixed_amount' | 'free_shipping',
        value: this.fromDecimal(d.value),
        amount: this.toMoney(d.amount, d.currency),
        createdAt: d.createdAt,
        updatedAt: d.updatedAt
      })),
      shippingAddress: (cart.shippingAddress as Address) || null,
      shippingMethod: (cart.shippingMethod as CartShippingMethod) || null,
      taxLines: (cart.taxLines as TaxLine[]) || [],
      createdAt: cart.createdAt,
      updatedAt: cart.updatedAt
    }
  }
}
