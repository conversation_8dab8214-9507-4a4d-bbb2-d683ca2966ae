// Auth Service - Authentication and authorization functionality
import { prisma } from '../config/database'
import {
  User,
  LoginCredentials,
  RegisterCredentials,
  AuthResponse,
  PasswordResetRequest,
  PasswordReset,
  EmailVerification,
  PhoneVerification,
  CreateUserInput,
  UpdateUserInput,
  ChangePasswordInput,
  ApiResponse,
  NotFoundError,
  ValidationError,
  UnauthorizedError
} from '../types/index'
import { createSuccessResponse, createErrorResponse } from '../utils/api-response'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import crypto from 'crypto'

export class AuthService {
  private readonly jwtSecret: string
  private readonly jwtExpiresIn: string
  private readonly refreshTokenExpiresIn: string

  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key'
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h'
    this.refreshTokenExpiresIn = process.env.REFRESH_TOKEN_EXPIRES_IN || '7d'
  }

  /**
   * Register a new user
   */
  async register(credentials: RegisterCredentials): Promise<ApiResponse<AuthResponse>> {
    try {
      // Validate input
      if (!credentials.email || !credentials.password) {
        throw new ValidationError('Email and password are required')
      }

      if (!this.isValidEmail(credentials.email)) {
        throw new ValidationError('Invalid email format')
      }

      if (credentials.password.length < 8) {
        throw new ValidationError('Password must be at least 8 characters long')
      }

      // Check if user already exists
      const existingUser = await prisma.user.findUnique({
        where: { email: credentials.email.toLowerCase() }
      })

      if (existingUser) {
        throw new ValidationError('User with this email already exists')
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(credentials.password, 12)

      // Create user
      const user = await prisma.user.create({
        data: {
          email: credentials.email.toLowerCase(),
          password: hashedPassword,
          firstName: credentials.firstName,
          lastName: credentials.lastName,
          phone: credentials.phone,
          acceptsMarketing: credentials.acceptsMarketing || false,
          emailVerified: false,
          phoneVerified: false,
          isActive: true,
          isBlocked: false,
          preferredLanguage: 'en',
          preferredCurrency: 'ZAR'
        }
      })

      // Generate tokens
      const token = this.generateAccessToken(user.id)
      const refreshToken = this.generateRefreshToken(user.id)
      const expiresAt = new Date(Date.now() + this.parseTimeToMs(this.jwtExpiresIn))

      // Create session
      await prisma.userSession.create({
        data: {
          userId: user.id,
          token,
          refreshToken,
          expiresAt,
          isActive: true,
          lastActivityAt: new Date()
        }
      })

      // Generate email verification token
      const verificationToken = crypto.randomBytes(32).toString('hex')
      await prisma.emailVerificationToken.create({
        data: {
          userId: user.id,
          token: verificationToken,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
        }
      })

      // Remove password from response
      const { password, ...userWithoutPassword } = user

      const authResponse: AuthResponse = {
        success: true,
        user: userWithoutPassword as User,
        token,
        refreshToken,
        expiresAt
      }

      return createSuccessResponse(authResponse)
    } catch (error) {
      console.error('Register error:', error)
      if (error instanceof ValidationError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to register user')
    }
  }

  /**
   * Login user
   */
  async login(credentials: LoginCredentials): Promise<ApiResponse<AuthResponse>> {
    try {
      // Validate input
      if (!credentials.email || !credentials.password) {
        throw new ValidationError('Email and password are required')
      }

      // Find user
      const user = await prisma.user.findUnique({
        where: { email: credentials.email.toLowerCase() }
      })

      if (!user) {
        throw new UnauthorizedError('Invalid email or password')
      }

      if (user.isBlocked) {
        throw new UnauthorizedError('Account is blocked')
      }

      if (!user.isActive) {
        throw new UnauthorizedError('Account is inactive')
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(credentials.password, user.password)
      if (!isPasswordValid) {
        throw new UnauthorizedError('Invalid email or password')
      }

      // Generate tokens
      const token = this.generateAccessToken(user.id)
      const refreshToken = this.generateRefreshToken(user.id)
      const expiresAt = new Date(Date.now() + this.parseTimeToMs(this.jwtExpiresIn))

      // Deactivate old sessions if not remember me
      if (!credentials.rememberMe) {
        await prisma.userSession.updateMany({
          where: { userId: user.id },
          data: { isActive: false }
        })
      }

      // Create new session
      await prisma.userSession.create({
        data: {
          userId: user.id,
          token,
          refreshToken,
          expiresAt,
          isActive: true,
          lastActivityAt: new Date()
        }
      })

      // Update last login
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() }
      })

      // Remove password from response
      const { password, ...userWithoutPassword } = user

      const authResponse: AuthResponse = {
        success: true,
        user: userWithoutPassword as User,
        token,
        refreshToken,
        expiresAt
      }

      return createSuccessResponse(authResponse)
    } catch (error) {
      console.error('Login error:', error)
      if (error instanceof ValidationError || error instanceof UnauthorizedError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to login')
    }
  }

  /**
   * Logout user
   */
  async logout(token: string): Promise<ApiResponse<void>> {
    try {
      // Deactivate session
      await prisma.userSession.updateMany({
        where: { token },
        data: { isActive: false }
      })

      return createSuccessResponse(undefined)
    } catch (error) {
      console.error('Logout error:', error)
      return createErrorResponse('Failed to logout')
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<ApiResponse<AuthResponse>> {
    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, this.jwtSecret) as any
      
      // Find session
      const session = await prisma.userSession.findFirst({
        where: {
          refreshToken,
          isActive: true,
          expiresAt: { gt: new Date() }
        },
        include: { user: true }
      })

      if (!session || session.userId !== decoded.userId) {
        throw new UnauthorizedError('Invalid refresh token')
      }

      // Generate new tokens
      const newToken = this.generateAccessToken(session.userId)
      const newRefreshToken = this.generateRefreshToken(session.userId)
      const expiresAt = new Date(Date.now() + this.parseTimeToMs(this.jwtExpiresIn))

      // Update session
      await prisma.userSession.update({
        where: { id: session.id },
        data: {
          token: newToken,
          refreshToken: newRefreshToken,
          expiresAt,
          lastActivityAt: new Date()
        }
      })

      // Remove password from response
      const { password, ...userWithoutPassword } = session.user

      const authResponse: AuthResponse = {
        success: true,
        user: userWithoutPassword as User,
        token: newToken,
        refreshToken: newRefreshToken,
        expiresAt
      }

      return createSuccessResponse(authResponse)
    } catch (error) {
      console.error('Refresh token error:', error)
      if (error instanceof UnauthorizedError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to refresh token')
    }
  }

  /**
   * Get current user from token
   */
  async getCurrentUser(token: string): Promise<ApiResponse<User>> {
    try {
      // Verify token
      const decoded = jwt.verify(token, this.jwtSecret) as any
      
      // Find user and session
      const session = await prisma.userSession.findFirst({
        where: {
          token,
          isActive: true,
          expiresAt: { gt: new Date() }
        },
        include: { user: true }
      })

      if (!session || session.userId !== decoded.userId) {
        throw new UnauthorizedError('Invalid or expired token')
      }

      // Update last activity
      await prisma.userSession.update({
        where: { id: session.id },
        data: { lastActivityAt: new Date() }
      })

      // Remove password from response
      const { password, ...userWithoutPassword } = session.user

      return createSuccessResponse(userWithoutPassword as User)
    } catch (error) {
      console.error('Get current user error:', error)
      if (error instanceof UnauthorizedError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to get current user')
    }
  }

  /**
   * Request password reset
   */
  async requestPasswordReset(request: PasswordResetRequest): Promise<ApiResponse<void>> {
    try {
      const user = await prisma.user.findUnique({
        where: { email: request.email.toLowerCase() }
      })

      if (!user) {
        // Don't reveal if email exists or not
        return createSuccessResponse(undefined)
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex')
      const hashedToken = crypto.createHash('sha256').update(resetToken).digest('hex')

      // Save reset token
      await prisma.passwordResetToken.create({
        data: {
          userId: user.id,
          token: hashedToken,
          expiresAt: new Date(Date.now() + 60 * 60 * 1000) // 1 hour
        }
      })

      // In a real implementation, you would send an email here
      console.log(`Password reset token for ${user.email}: ${resetToken}`)

      return createSuccessResponse(undefined)
    } catch (error) {
      console.error('Request password reset error:', error)
      return createErrorResponse('Failed to request password reset')
    }
  }

  /**
   * Reset password
   */
  async resetPassword(reset: PasswordReset): Promise<ApiResponse<void>> {
    try {
      if (reset.newPassword.length < 8) {
        throw new ValidationError('Password must be at least 8 characters long')
      }

      // Hash the token to compare with stored hash
      const hashedToken = crypto.createHash('sha256').update(reset.token).digest('hex')

      // Find valid reset token
      const resetToken = await prisma.passwordResetToken.findFirst({
        where: {
          token: hashedToken,
          expiresAt: { gt: new Date() },
          used: false
        },
        include: { user: true }
      })

      if (!resetToken) {
        throw new ValidationError('Invalid or expired reset token')
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(reset.newPassword, 12)

      // Update password and mark token as used
      await Promise.all([
        prisma.user.update({
          where: { id: resetToken.userId },
          data: { password: hashedPassword }
        }),
        prisma.passwordResetToken.update({
          where: { id: resetToken.id },
          data: { used: true }
        }),
        // Deactivate all sessions
        prisma.userSession.updateMany({
          where: { userId: resetToken.userId },
          data: { isActive: false }
        })
      ])

      return createSuccessResponse(undefined)
    } catch (error) {
      console.error('Reset password error:', error)
      if (error instanceof ValidationError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to reset password')
    }
  }

  /**
   * Verify email
   */
  async verifyEmail(verification: EmailVerification): Promise<ApiResponse<void>> {
    try {
      const verificationToken = await prisma.emailVerificationToken.findFirst({
        where: {
          token: verification.token,
          expiresAt: { gt: new Date() },
          used: false
        }
      })

      if (!verificationToken) {
        throw new ValidationError('Invalid or expired verification token')
      }

      // Update user and mark token as used
      await Promise.all([
        prisma.user.update({
          where: { id: verificationToken.userId },
          data: { emailVerified: true }
        }),
        prisma.emailVerificationToken.update({
          where: { id: verificationToken.id },
          data: { used: true }
        })
      ])

      return createSuccessResponse(undefined)
    } catch (error) {
      console.error('Verify email error:', error)
      if (error instanceof ValidationError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to verify email')
    }
  }

  /**
   * Change password
   */
  async changePassword(input: ChangePasswordInput): Promise<ApiResponse<void>> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: input.userId }
      })

      if (!user) {
        throw new NotFoundError('User', input.userId)
      }

      // Verify current password
      const isCurrentPasswordValid = await bcrypt.compare(input.currentPassword, user.password)
      if (!isCurrentPasswordValid) {
        throw new ValidationError('Current password is incorrect')
      }

      if (input.newPassword.length < 8) {
        throw new ValidationError('New password must be at least 8 characters long')
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(input.newPassword, 12)

      // Update password
      await prisma.user.update({
        where: { id: input.userId },
        data: { password: hashedPassword }
      })

      // Deactivate all sessions except current one would require session tracking
      // For now, we'll deactivate all sessions
      await prisma.userSession.updateMany({
        where: { userId: input.userId },
        data: { isActive: false }
      })

      return createSuccessResponse(undefined)
    } catch (error) {
      console.error('Change password error:', error)
      if (error instanceof NotFoundError || error instanceof ValidationError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to change password')
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(input: UpdateUserInput): Promise<ApiResponse<User>> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: input.id }
      })

      if (!user) {
        throw new NotFoundError('User', input.id)
      }

      // If email is being updated, check for conflicts
      if (input.email && input.email !== user.email) {
        const existingUser = await prisma.user.findUnique({
          where: { email: input.email.toLowerCase() }
        })

        if (existingUser) {
          throw new ValidationError('User with this email already exists')
        }
      }

      // Update user
      const updatedUser = await prisma.user.update({
        where: { id: input.id },
        data: {
          email: input.email?.toLowerCase(),
          firstName: input.firstName,
          lastName: input.lastName,
          displayName: input.displayName,
          phone: input.phone,
          dateOfBirth: input.dateOfBirth,
          gender: input.gender,
          acceptsMarketing: input.acceptsMarketing,
          preferredLanguage: input.preferredLanguage,
          preferredCurrency: input.preferredCurrency,
          timezone: input.timezone,
          avatar: input.avatar,
          bio: input.bio,
          isActive: input.isActive,
          isBlocked: input.isBlocked,
          loyaltyPoints: input.loyaltyPoints,
          loyaltyTier: input.loyaltyTier,
          tags: input.tags,
          notes: input.notes,
          metafields: input.metafields
        }
      })

      // Remove password from response
      const { password, ...userWithoutPassword } = updatedUser

      return createSuccessResponse(userWithoutPassword as User)
    } catch (error) {
      console.error('Update profile error:', error)
      if (error instanceof NotFoundError || error instanceof ValidationError) {
        return createErrorResponse(error.message)
      }
      return createErrorResponse('Failed to update profile')
    }
  }

  /**
   * Generate access token
   */
  private generateAccessToken(userId: string): string {
    return jwt.sign(
      { userId, type: 'access' },
      this.jwtSecret,
      { expiresIn: this.jwtExpiresIn }
    )
  }

  /**
   * Generate refresh token
   */
  private generateRefreshToken(userId: string): string {
    return jwt.sign(
      { userId, type: 'refresh' },
      this.jwtSecret,
      { expiresIn: this.refreshTokenExpiresIn }
    )
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * Parse time string to milliseconds
   */
  private parseTimeToMs(timeString: string): number {
    const unit = timeString.slice(-1)
    const value = parseInt(timeString.slice(0, -1))
    
    switch (unit) {
      case 's': return value * 1000
      case 'm': return value * 60 * 1000
      case 'h': return value * 60 * 60 * 1000
      case 'd': return value * 24 * 60 * 60 * 1000
      default: return value
    }
  }
}