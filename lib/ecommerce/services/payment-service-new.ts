import { PrismaClient } from '@prisma/client'
import { 
  paymentService as newPaymentService, 
  PaymentService as NewPaymentService 
} from '../../payment-core'
import { 
  PaymentGateway, 
  PaymentMethod as CorePaymentMethod,
  PaymentStatus,
  PaymentRequest as CorePaymentRequest
} from '../../payment-core/types'
import { logger } from '../../payment-core/logger'
import { 
  processOrderPayment, 
  getCheckoutPaymentMethods 
} from '../../payment-core/ecommerce-integration'

const prisma = new PrismaClient()

// Enhanced payment method types for South African market
export interface PaymentMethod {
  id: string
  name: string
  type: 'payfast' | 'ozow' | 'card' | 'eft' | 'snapscan' | 'zapper' | 'cash'
  enabled: boolean
  config: Record<string, any>
  description?: string
  icon?: string
  fees?: {
    percentage?: number
    fixed?: number
    minimum?: number
    maximum?: number
  }
  supportedBanks?: string[]
  processingTime?: string
}

export interface PaymentRequest {
  orderId: string
  amount: number
  currency: 'ZAR'
  customerEmail: string
  customerName: string
  description: string
  returnUrl: string
  cancelUrl: string
  notifyUrl: string
  customerPhone?: string
  billingAddress?: {
    address1: string
    city: string
    province: string
    postalCode: string
    country: string
  }
  metadata?: Record<string, any>
}

export interface PaymentResponse {
  success: boolean
  paymentId: string
  redirectUrl?: string
  error?: string
  gatewayResponse?: any
}

/**
 * Enhanced Payment Service with comprehensive South African payment methods
 * This is a compatibility layer that uses the new payment-core library
 */
export class PaymentService {
  private coreService: NewPaymentService

  constructor() {
    this.coreService = newPaymentService
    
    // Initialize the core service
    this.coreService.init().catch(error => {
      logger.error('Failed to initialize core payment service', { error })
    })
  }

  /**
   * Get available payment methods
   */
  async getAvailablePaymentMethods(): Promise<PaymentMethod[]> {
    try {
      // Get methods from the core service
      const coreMethods = await this.coreService.getAvailablePaymentMethods()
      
      // Convert to the legacy format
      return coreMethods.map(method => ({
        id: method.method,
        name: method.displayName,
        type: this.mapMethodTypeToLegacy(method.method),
        enabled: true,
        description: method.description,
        icon: method.icon,
        fees: {
          percentage: 2.9, // Default values
          fixed: 0,
          minimum: 1,
          maximum: 100
        },
        processingTime: 'Instant to 3 business days',
        config: {
          acceptsCreditCards: method.method === CorePaymentMethod.CARD,
          acceptsEFT: method.method === CorePaymentMethod.EFT || method.method === CorePaymentMethod.INSTANT_EFT,
          instantVerification: method.method === CorePaymentMethod.INSTANT_EFT,
          qrCodePayment: method.method === CorePaymentMethod.QR_CODE,
          mobileWallet: method.method === CorePaymentMethod.MOBILE_MONEY,
          collectionOnly: method.method === CorePaymentMethod.CASH
        }
      }))
    } catch (error) {
      logger.error('Error getting payment methods', { error })
      
      // Return default methods for backward compatibility
      return [
        {
          id: 'payfast',
          name: 'PayFast',
          type: 'payfast',
          enabled: true,
          description: 'Credit cards, EFT, and SnapScan',
          icon: '💳',
          fees: {
            percentage: 2.9,
            fixed: 0,
            minimum: 1,
            maximum: 100
          },
          supportedBanks: ['All major South African banks'],
          processingTime: 'Instant to 3 business days',
          config: {
            acceptsCreditCards: true,
            acceptsEFT: true,
            acceptsSnapScan: true
          }
        },
        {
          id: 'ozow',
          name: 'Ozow',
          type: 'ozow',
          enabled: true,
          description: 'Instant EFT payments',
          icon: '🏦',
          fees: {
            percentage: 1.5,
            fixed: 0,
            minimum: 2,
            maximum: 50
          },
          supportedBanks: [
            'ABSA', 'Standard Bank', 'FNB', 'Nedbank', 'Capitec',
            'African Bank', 'Bidvest Bank', 'Discovery Bank',
            'Investec', 'TymeBank', 'Bank Zero'
          ],
          processingTime: 'Instant',
          config: {
            instantVerification: true,
            realTimePayments: true
          }
        },
        {
          id: 'cash',
          name: 'Cash on Collection',
          type: 'cash',
          enabled: true,
          description: 'Pay when collecting your order',
          icon: '💵',
          fees: {
            percentage: 0,
            fixed: 0
          },
          processingTime: 'On collection',
          config: {
            collectionOnly: true,
            noOnlineFees: true
          }
        }
      ]
    }
  }

  /**
   * Create a payment
   */
  async createPayment(method: string, request: PaymentRequest): Promise<PaymentResponse> {
    try {
      // Convert legacy method to new gateway type
      const gateway = this.mapMethodToGateway(method)
      
      // Convert legacy request to new request format
      const coreRequest: CorePaymentRequest = {
        amount: {
          amount: request.amount,
          currency: request.currency
        },
        customer: {
          email: request.customerEmail,
          firstName: request.customerName.split(' ')[0],
          lastName: request.customerName.split(' ').slice(1).join(' ') || 'Customer',
          phone: request.customerPhone
        },
        items: [], // We don't have items in the legacy request
        metadata: {
          orderId: request.orderId,
          customerId: request.metadata?.customerId || '',
          source: request.metadata?.source || 'ecommerce'
        },
        returnUrl: request.returnUrl,
        cancelUrl: request.cancelUrl,
        notifyUrl: request.notifyUrl,
        reference: `order_${request.orderId}`,
        description: request.description,
        paymentMethod: this.mapMethodToCore(method)
      }
      
      // If we have billing address, add it
      if (request.billingAddress) {
        coreRequest.customer.address = {
          line1: request.billingAddress.address1,
          city: request.billingAddress.city,
          state: request.billingAddress.province,
          postalCode: request.billingAddress.postalCode,
          country: request.billingAddress.country
        }
      }
      
      // Process payment using the core service
      const result = await this.coreService.createPayment(coreRequest, gateway)
      
      // Convert response to legacy format
      return {
        success: result.success,
        paymentId: result.transactionId || result.reference || '',
        redirectUrl: result.paymentUrl,
        error: result.error?.message,
        gatewayResponse: result.gatewayResponse
      }
    } catch (error) {
      logger.error('Payment creation error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        method,
        orderId: request.orderId
      })
      
      return {
        success: false,
        paymentId: '',
        error: error instanceof Error ? error.message : 'Payment processing failed'
      }
    }
  }

  /**
   * Verify a payment
   */
  async verifyPayment(method: string, data: Record<string, any>): Promise<boolean> {
    try {
      // Convert legacy method to new gateway type
      const gateway = this.mapMethodToGateway(method)
      
      // Extract transaction ID from data
      const transactionId = data.m_payment_id || data.TransactionReference || data.paymentId || ''
      
      if (!transactionId) {
        logger.warn('No transaction ID found in webhook data', { method, data })
        return false
      }
      
      // Get payment status from core service
      const status = await this.coreService.getPaymentStatus(transactionId, gateway)
      
      // Payment is verified if status is completed
      const isVerified = status === PaymentStatus.COMPLETED
      
      // If verified, update payment status
      if (isVerified) {
        await this.updatePaymentStatus(transactionId, 'completed', data)
      }
      
      return isVerified
    } catch (error) {
      logger.error('Payment verification error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        method
      })
      
      return false
    }
  }

  /**
   * Update payment status
   */
  async updatePaymentStatus(paymentId: string, status: string, metadata?: Record<string, any>) {
    try {
      // Map legacy status to core status
      const coreStatus = this.mapStatusToCore(status)
      
      // Update status using core service
      await this.coreService.updateTransactionStatus(paymentId, coreStatus, metadata)
    } catch (error) {
      logger.error('Payment status update error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        paymentId,
        status
      })
    }
  }

  /**
   * Process order payment (new method that uses the core service directly)
   */
  async processOrderPayment(
    orderId: string,
    gateway?: PaymentGateway,
    method?: CorePaymentMethod,
    returnUrl?: string,
    cancelUrl?: string,
    notifyUrl?: string
  ) {
    return processOrderPayment(orderId, gateway, method, returnUrl, cancelUrl, notifyUrl)
  }

  /**
   * Get checkout payment methods (new method that uses the core service directly)
   */
  async getCheckoutPaymentMethods() {
    return getCheckoutPaymentMethods()
  }

  /**
   * Map legacy method to core gateway
   */
  private mapMethodToGateway(method: string): PaymentGateway {
    switch (method) {
      case 'payfast':
        return PaymentGateway.PAYFAST
      case 'ozow':
        return PaymentGateway.OZOW
      case 'snapscan':
        return PaymentGateway.SNAPSCAN
      case 'zapper':
        return PaymentGateway.ZAPPER
      case 'cash':
        return PaymentGateway.MANUAL
      default:
        return PaymentGateway.PAYFAST
    }
  }

  /**
   * Map legacy method to core method
   */
  private mapMethodToCore(method: string): CorePaymentMethod {
    switch (method) {
      case 'card':
        return CorePaymentMethod.CARD
      case 'eft':
        return CorePaymentMethod.EFT
      case 'ozow':
        return CorePaymentMethod.INSTANT_EFT
      case 'snapscan':
      case 'zapper':
        return CorePaymentMethod.QR_CODE
      case 'cash':
        return CorePaymentMethod.CASH
      default:
        return CorePaymentMethod.CARD
    }
  }

  /**
   * Map core method to legacy type
   */
  private mapMethodTypeToLegacy(method: CorePaymentMethod): 'payfast' | 'ozow' | 'card' | 'eft' | 'snapscan' | 'zapper' | 'cash' {
    switch (method) {
      case CorePaymentMethod.CARD:
        return 'card'
      case CorePaymentMethod.EFT:
        return 'eft'
      case CorePaymentMethod.INSTANT_EFT:
        return 'ozow'
      case CorePaymentMethod.QR_CODE:
        return 'snapscan'
      case CorePaymentMethod.MOBILE_MONEY:
        return 'zapper'
      case CorePaymentMethod.CASH:
        return 'cash'
      default:
        return 'card'
    }
  }

  /**
   * Map legacy status to core status
   */
  private mapStatusToCore(status: string): PaymentStatus {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'complete':
      case 'success':
      case 'successful':
        return PaymentStatus.COMPLETED
      case 'pending':
      case 'waiting':
        return PaymentStatus.PENDING
      case 'processing':
        return PaymentStatus.PROCESSING
      case 'failed':
      case 'failure':
      case 'error':
        return PaymentStatus.FAILED
      case 'cancelled':
      case 'canceled':
        return PaymentStatus.CANCELLED
      case 'refunded':
        return PaymentStatus.REFUNDED
      case 'partially_refunded':
        return PaymentStatus.PARTIALLY_REFUNDED
      case 'expired':
        return PaymentStatus.EXPIRED
      default:
        return PaymentStatus.PENDING
    }
  }
}

// Export singleton instance
export const paymentService = new PaymentService()