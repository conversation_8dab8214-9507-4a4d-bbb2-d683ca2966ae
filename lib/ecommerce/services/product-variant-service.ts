import { prisma } from '@/lib/prisma'
import { InventoryService } from './inventory-service'

export interface ProductVariant {
  id: string
  productId: string
  sku: string
  name: string
  price: number
  compareAtPrice?: number
  color?: string
  size?: string
  material?: string
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  images: string[]
  isActive: boolean
  inventory?: {
    quantity: number
    reserved: number
    available: number
  }
  createdAt: Date
  updatedAt: Date
}

export interface CreateVariantRequest {
  productId: string
  name: string
  price: number
  compareAtPrice?: number
  color?: string
  size?: string
  material?: string
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  images?: string[]
  initialStock?: number
}

export class ProductVariantService {
  private inventoryService: InventoryService

  constructor() {
    this.inventoryService = new InventoryService()
  }

  async createVariant(data: CreateVariantRequest): Promise<ProductVariant> {
    try {
      // Validate that the product exists
      const product = await prisma.product.findUnique({
        where: { id: data.productId }
      })

      if (!product) {
        throw new Error(`Product with ID '${data.productId}' not found`)
      }

      // Generate SKU
      const sku = await this.generateVariantSKU(data.productId, data.color, data.size)

      // Create variant
      const variant = await prisma.productVariant.create({
        data: {
          productId: data.productId,
          sku,
          name: data.name,
          price: data.price,
          compareAtPrice: data.compareAtPrice,
          color: data.color,
          size: data.size,
          material: data.material,
          weight: data.weight,
          dimensions: data.dimensions,
          images: data.images || [],
          isActive: true
        }
      })

      // Initialize inventory if stock provided
      if (data.initialStock && data.initialStock > 0) {
        await this.inventoryService.updateInventory(
          data.productId,
          variant.id,
          data.initialStock,
          data.price * 0.6 // Assume 60% cost ratio
        )
      }

      return this.formatVariant(variant)
    } catch (error) {
      console.error('Create variant error:', error)
      throw new Error('Failed to create product variant')
    }
  }

  async getVariant(variantId: string): Promise<ProductVariant | null> {
    try {
      const variant = await prisma.productVariant.findUnique({
        where: { id: variantId }
      })

      if (!variant) return null

      return this.formatVariant(variant)
    } catch (error) {
      console.error('Get variant error:', error)
      return null
    }
  }

  async getProductVariants(productId: string): Promise<ProductVariant[]> {
    try {
      const variants = await prisma.productVariant.findMany({
        where: { productId },
        orderBy: { createdAt: 'asc' }
      })

      return Promise.all(variants.map(variant => this.formatVariant(variant)))
    } catch (error) {
      console.error('Get product variants error:', error)
      return []
    }
  }

  async updateVariant(variantId: string, data: Partial<CreateVariantRequest>): Promise<ProductVariant | null> {
    try {
      const variant = await prisma.productVariant.update({
        where: { id: variantId },
        data: {
          ...(data.name && { name: data.name }),
          ...(data.price && { price: data.price }),
          ...(data.compareAtPrice !== undefined && { compareAtPrice: data.compareAtPrice }),
          ...(data.color && { color: data.color }),
          ...(data.size && { size: data.size }),
          ...(data.material && { material: data.material }),
          ...(data.weight && { weight: data.weight }),
          ...(data.dimensions && { dimensions: data.dimensions }),
          ...(data.images && { images: data.images }),
          updatedAt: new Date()
        }
      })

      return this.formatVariant(variant)
    } catch (error) {
      console.error('Update variant error:', error)
      return null
    }
  }

  async deleteVariant(variantId: string): Promise<boolean> {
    try {
      // Check if variant has any orders
      const orderItems = await prisma.orderItem.findFirst({
        where: { variantId }
      })

      if (orderItems) {
        // Soft delete - mark as inactive
        await prisma.productVariant.update({
          where: { id: variantId },
          data: { isActive: false }
        })
      } else {
        // Hard delete if no orders
        await prisma.productVariant.delete({
          where: { id: variantId }
        })
      }

      return true
    } catch (error) {
      console.error('Delete variant error:', error)
      return false
    }
  }

  async getVariantsByAttributes(
    productId: string,
    attributes: { color?: string; size?: string; material?: string }
  ): Promise<ProductVariant[]> {
    try {
      const whereClause: any = { productId, isActive: true }

      if (attributes.color) whereClause.color = attributes.color
      if (attributes.size) whereClause.size = attributes.size
      if (attributes.material) whereClause.material = attributes.material

      const variants = await prisma.productVariant.findMany({
        where: whereClause,
        orderBy: { createdAt: 'asc' }
      })

      return Promise.all(variants.map(variant => this.formatVariant(variant)))
    } catch (error) {
      console.error('Get variants by attributes error:', error)
      return []
    }
  }

  async getAvailableColors(productId: string): Promise<string[]> {
    try {
      const variants = await prisma.productVariant.findMany({
        where: { productId, isActive: true, color: { not: null } },
        select: { color: true },
        distinct: ['color']
      })

      return variants.map(v => v.color!).filter(Boolean)
    } catch (error) {
      console.error('Get available colors error:', error)
      return []
    }
  }

  async getAvailableSizes(productId: string, color?: string): Promise<string[]> {
    try {
      const whereClause: any = { productId, isActive: true, size: { not: null } }
      if (color) whereClause.color = color

      const variants = await prisma.productVariant.findMany({
        where: whereClause,
        select: { size: true },
        distinct: ['size']
      })

      return variants.map(v => v.size!).filter(Boolean)
    } catch (error) {
      console.error('Get available sizes error:', error)
      return []
    }
  }

  async getLowestPrice(productId: string): Promise<number> {
    try {
      const result = await prisma.productVariant.aggregate({
        where: { productId, isActive: true },
        _min: { price: true }
      })

      return result._min.price || 0
    } catch (error) {
      console.error('Get lowest price error:', error)
      return 0
    }
  }

  async getHighestPrice(productId: string): Promise<number> {
    try {
      const result = await prisma.productVariant.aggregate({
        where: { productId, isActive: true },
        _max: { price: true }
      })

      return result._max.price || 0
    } catch (error) {
      console.error('Get highest price error:', error)
      return 0
    }
  }

  async bulkUpdatePrices(productId: string, priceAdjustment: number, isPercentage = false): Promise<number> {
    try {
      const variants = await prisma.productVariant.findMany({
        where: { productId, isActive: true }
      })

      let updatedCount = 0

      for (const variant of variants) {
        const newPrice = isPercentage
          ? variant.price * (1 + priceAdjustment / 100)
          : variant.price + priceAdjustment

        if (newPrice > 0) {
          await prisma.productVariant.update({
            where: { id: variant.id },
            data: { price: newPrice }
          })
          updatedCount++
        }
      }

      return updatedCount
    } catch (error) {
      console.error('Bulk update prices error:', error)
      return 0
    }
  }

  private async formatVariant(variant: any): Promise<ProductVariant> {
    // Get inventory information
    const inventory = await this.inventoryService.getInventory(variant.productId, variant.id)

    return {
      id: variant.id,
      productId: variant.productId,
      sku: variant.sku,
      name: variant.name,
      price: variant.price,
      compareAtPrice: variant.compareAtPrice,
      color: variant.color,
      size: variant.size,
      material: variant.material,
      weight: variant.weight,
      dimensions: variant.dimensions,
      images: variant.images || [],
      isActive: variant.isActive,
      inventory: inventory ? {
        quantity: inventory.quantity,
        reserved: inventory.reserved,
        available: inventory.available
      } : undefined,
      createdAt: variant.createdAt,
      updatedAt: variant.updatedAt
    }
  }

  private async generateVariantSKU(productId: string, color?: string, size?: string): Promise<string> {
    // Get product info
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { name: true }
    })

    if (!product) throw new Error('Product not found')

    // Create SKU components
    const productCode = product.name
      .toUpperCase()
      .replace(/[^A-Z0-9]/g, '')
      .substring(0, 4)

    const colorCode = color
      ? color.toUpperCase().substring(0, 2)
      : 'XX'

    const sizeCode = size
      ? size.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 2)
      : 'XX'

    const timestamp = Date.now().toString().slice(-4)

    return `${productCode}-${colorCode}${sizeCode}-${timestamp}`
  }
}
