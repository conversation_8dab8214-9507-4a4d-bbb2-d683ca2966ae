// Checkout store for managing checkout state across components
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { Address } from '../types/base'

export interface CheckoutFormData {
  // Customer Information
  email: string
  phone: string
  firstName: string
  lastName: string
  
  // Shipping Address
  shippingAddress: {
    address1: string
    address2?: string
    city: string
    province: string
    postalCode: string
    country: string
  }
  
  // Billing Address (optional, defaults to shipping)
  billingAddress?: {
    address1: string
    address2?: string
    city: string
    province: string
    postalCode: string
    country: string
  }
  
  // Preferences
  useBillingAsShipping: boolean
  saveAddressForFuture: boolean
  subscribeToNewsletter: boolean
  
  // Special Instructions
  orderNotes?: string
  giftMessage?: string
  isGift: boolean
}

export interface CheckoutValidation {
  isValid: boolean
  errors: Record<string, string>
  warnings: Record<string, string>
}

export interface CheckoutStep {
  id: 'customer' | 'shipping' | 'payment' | 'review'
  title: string
  completed: boolean
  current: boolean
  canAccess: boolean
}

export interface CheckoutState {
  // Current step
  currentStep: CheckoutStep['id']
  steps: CheckoutStep[]
  
  // Form data
  formData: CheckoutFormData
  
  // Validation
  validation: CheckoutValidation
  
  // Shipping & Payment
  selectedShippingMethod: string
  selectedPaymentMethod: string
  selectedPaymentGateway: string | null
  
  // Processing states
  isValidating: boolean
  isProcessing: boolean
  
  // Gift options
  giftWrap: boolean
  giftWrapMessage: string
  
  // Promo codes
  appliedPromoCodes: string[]
  promoCodeInput: string
  isApplyingPromo: boolean
  promoError: string | null
  
  // Order summary visibility (for mobile)
  showOrderSummary: boolean
  
  // Actions
  setCurrentStep: (step: CheckoutStep['id']) => void
  updateFormData: (data: Partial<CheckoutFormData>) => void
  validateStep: (step: CheckoutStep['id']) => CheckoutValidation
  validateAllSteps: () => CheckoutValidation
  setShippingMethod: (method: string) => void
  setPaymentMethod: (method: string) => void
  setPaymentGateway: (gateway: string | null) => void
  setProcessing: (processing: boolean) => void
  setGiftWrap: (enabled: boolean, message?: string) => void
  applyPromoCode: (code: string) => Promise<boolean>
  removePromoCode: (code: string) => void
  setPromoCodeInput: (code: string) => void
  toggleOrderSummary: () => void
  resetCheckout: () => void
  prefillFromUser: (userData: any) => void
  
  // Computed properties
  canProceedToNextStep: () => boolean
  getStepValidation: (step: CheckoutStep['id']) => CheckoutValidation
  isStepCompleted: (step: CheckoutStep['id']) => boolean
  getCompletedStepsCount: () => number
  getProgressPercentage: () => number
}

const initialFormData: CheckoutFormData = {
  email: '',
  phone: '',
  firstName: '',
  lastName: '',
  shippingAddress: {
    address1: '',
    address2: '',
    city: '',
    province: '',
    postalCode: '',
    country: 'ZA'
  },
  useBillingAsShipping: true,
  saveAddressForFuture: false,
  subscribeToNewsletter: false,
  orderNotes: '',
  giftMessage: '',
  isGift: false
}

const initialSteps: CheckoutStep[] = [
  {
    id: 'customer',
    title: 'Customer Info',
    completed: false,
    current: true,
    canAccess: true
  },
  {
    id: 'shipping',
    title: 'Shipping',
    completed: false,
    current: false,
    canAccess: false
  },
  {
    id: 'payment',
    title: 'Payment',
    completed: false,
    current: false,
    canAccess: false
  },
  {
    id: 'review',
    title: 'Review',
    completed: false,
    current: false,
    canAccess: false
  }
]

// Validation functions
const validateEmail = (email: string): string | null => {
  if (!email) return 'Email is required'
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) return 'Please enter a valid email address'
  return null
}

const validatePhone = (phone: string): string | null => {
  if (!phone) return 'Phone number is required'
  // South African phone number validation
  const phoneRegex = /^(\+27|0)[6-8][0-9]{8}$/
  if (!phoneRegex.test(phone.replace(/\s/g, ''))) {
    return 'Please enter a valid South African phone number'
  }
  return null
}

const validateName = (name: string, field: string): string | null => {
  if (!name) return `${field} is required`
  if (name.length < 2) return `${field} must be at least 2 characters`
  return null
}

const validateAddress = (address: CheckoutFormData['shippingAddress']): Record<string, string> => {
  const errors: Record<string, string> = {}
  
  if (!address.address1) errors.address1 = 'Street address is required'
  if (!address.city) errors.city = 'City is required'
  if (!address.province) errors.province = 'Province is required'
  if (!address.postalCode) errors.postalCode = 'Postal code is required'
  
  // South African postal code validation
  if (address.postalCode && !/^\d{4}$/.test(address.postalCode)) {
    errors.postalCode = 'Please enter a valid 4-digit postal code'
  }
  
  return errors
}

const validateCustomerStep = (formData: CheckoutFormData): CheckoutValidation => {
  const errors: Record<string, string> = {}
  
  const emailError = validateEmail(formData.email)
  if (emailError) errors.email = emailError
  
  const phoneError = validatePhone(formData.phone)
  if (phoneError) errors.phone = phoneError
  
  const firstNameError = validateName(formData.firstName, 'First name')
  if (firstNameError) errors.firstName = firstNameError
  
  const lastNameError = validateName(formData.lastName, 'Last name')
  if (lastNameError) errors.lastName = lastNameError
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings: {}
  }
}

const validateShippingStep = (formData: CheckoutFormData): CheckoutValidation => {
  const errors = validateAddress(formData.shippingAddress)
  
  // If not using billing as shipping, validate billing address too
  if (!formData.useBillingAsShipping && formData.billingAddress) {
    const billingErrors = validateAddress(formData.billingAddress)
    Object.keys(billingErrors).forEach(key => {
      errors[`billing_${key}`] = billingErrors[key]
    })
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings: {}
  }
}

const validatePaymentStep = (selectedMethod: string, selectedGateway: string | null): CheckoutValidation => {
  const errors: Record<string, string> = {}
  
  if (!selectedMethod) {
    errors.paymentMethod = 'Please select a payment method'
  }
  
  if (selectedMethod === 'card' && !selectedGateway) {
    errors.paymentGateway = 'Please select a payment gateway'
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors,
    warnings: {}
  }
}

export const useCheckoutStore = create<CheckoutState>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentStep: 'customer',
      steps: initialSteps,
      formData: initialFormData,
      validation: { isValid: false, errors: {}, warnings: {} },
      selectedShippingMethod: 'standard',
      selectedPaymentMethod: 'card',
      selectedPaymentGateway: null,
      isValidating: false,
      isProcessing: false,
      giftWrap: false,
      giftWrapMessage: '',
      appliedPromoCodes: [],
      promoCodeInput: '',
      isApplyingPromo: false,
      promoError: null,
      showOrderSummary: false,
      
      // Actions
      setCurrentStep: (step) => {
        const state = get()
        const stepIndex = state.steps.findIndex(s => s.id === step)
        
        if (stepIndex === -1 || !state.steps[stepIndex].canAccess) return
        
        const updatedSteps = state.steps.map((s, index) => ({
          ...s,
          current: s.id === step,
          completed: index < stepIndex ? true : s.completed
        }))
        
        set({ currentStep: step, steps: updatedSteps })
      },
      
      updateFormData: (data) => {
        const currentFormData = get().formData
        const updatedFormData = { ...currentFormData, ...data }
        
        // Auto-validate current step when form data changes
        const currentStep = get().currentStep
        let validation: CheckoutValidation
        
        switch (currentStep) {
          case 'customer':
            validation = validateCustomerStep(updatedFormData)
            break
          case 'shipping':
            validation = validateShippingStep(updatedFormData)
            break
          default:
            validation = get().validation
        }
        
        // Update step completion and access
        const updatedSteps = get().steps.map(step => {
          if (step.id === currentStep) {
            return { ...step, completed: validation.isValid }
          }
          // Enable next step if current step is valid
          if (step.id === 'shipping' && currentStep === 'customer' && validation.isValid) {
            return { ...step, canAccess: true }
          }
          if (step.id === 'payment' && currentStep === 'shipping' && validation.isValid) {
            return { ...step, canAccess: true }
          }
          if (step.id === 'review' && currentStep === 'payment' && validation.isValid) {
            return { ...step, canAccess: true }
          }
          return step
        })
        
        set({ 
          formData: updatedFormData, 
          validation,
          steps: updatedSteps
        })
      },
      
      validateStep: (step) => {
        const { formData, selectedPaymentMethod, selectedPaymentGateway } = get()
        
        switch (step) {
          case 'customer':
            return validateCustomerStep(formData)
          case 'shipping':
            return validateShippingStep(formData)
          case 'payment':
            return validatePaymentStep(selectedPaymentMethod, selectedPaymentGateway)
          case 'review':
            return { isValid: true, errors: {}, warnings: {} }
          default:
            return { isValid: false, errors: {}, warnings: {} }
        }
      },
      
      validateAllSteps: () => {
        const { validateStep } = get()
        const allErrors: Record<string, string> = {}
        const allWarnings: Record<string, string> = {}
        
        const steps: CheckoutStep['id'][] = ['customer', 'shipping', 'payment', 'review']
        let allValid = true
        
        steps.forEach(step => {
          const validation = validateStep(step)
          if (!validation.isValid) {
            allValid = false
            Object.keys(validation.errors).forEach(key => {
              allErrors[`${step}_${key}`] = validation.errors[key]
            })
          }
          Object.keys(validation.warnings).forEach(key => {
            allWarnings[`${step}_${key}`] = validation.warnings[key]
          })
        })
        
        return {
          isValid: allValid,
          errors: allErrors,
          warnings: allWarnings
        }
      },
      
      setShippingMethod: (method) => {
        set({ selectedShippingMethod: method })
      },
      
      setPaymentMethod: (method) => {
        const state = get()
        const validation = validatePaymentStep(method, state.selectedPaymentGateway)
        
        // Update payment step completion
        const updatedSteps = state.steps.map(step => {
          if (step.id === 'payment') {
            return { ...step, completed: validation.isValid }
          }
          if (step.id === 'review' && validation.isValid) {
            return { ...step, canAccess: true }
          }
          return step
        })
        
        set({ 
          selectedPaymentMethod: method,
          steps: updatedSteps
        })
      },
      
      setPaymentGateway: (gateway) => {
        const state = get()
        const validation = validatePaymentStep(state.selectedPaymentMethod, gateway)
        
        // Update payment step completion
        const updatedSteps = state.steps.map(step => {
          if (step.id === 'payment') {
            return { ...step, completed: validation.isValid }
          }
          if (step.id === 'review' && validation.isValid) {
            return { ...step, canAccess: true }
          }
          return step
        })
        
        set({ 
          selectedPaymentGateway: gateway,
          steps: updatedSteps
        })
      },
      
      setProcessing: (processing) => {
        set({ isProcessing: processing })
      },
      
      setGiftWrap: (enabled, message = '') => {
        set({ 
          giftWrap: enabled,
          giftWrapMessage: enabled ? message : ''
        })
      },
      
      applyPromoCode: async (code) => {
        if (!code.trim()) return false
        
        set({ isApplyingPromo: true, promoError: null })
        
        try {
          // TODO: Implement actual promo code API call
          // For now, simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000))
          
          // Mock validation - accept codes starting with 'SAVE'
          if (code.toUpperCase().startsWith('SAVE')) {
            const currentCodes = get().appliedPromoCodes
            if (!currentCodes.includes(code)) {
              set({ 
                appliedPromoCodes: [...currentCodes, code],
                promoCodeInput: '',
                isApplyingPromo: false
              })
              return true
            } else {
              set({ 
                promoError: 'This promo code has already been applied',
                isApplyingPromo: false
              })
              return false
            }
          } else {
            set({ 
              promoError: 'Invalid promo code',
              isApplyingPromo: false
            })
            return false
          }
        } catch (error) {
          set({ 
            promoError: 'Failed to apply promo code. Please try again.',
            isApplyingPromo: false
          })
          return false
        }
      },
      
      removePromoCode: (code) => {
        const currentCodes = get().appliedPromoCodes
        set({ 
          appliedPromoCodes: currentCodes.filter(c => c !== code)
        })
      },
      
      setPromoCodeInput: (code) => {
        set({ promoCodeInput: code, promoError: null })
      },
      
      toggleOrderSummary: () => {
        set({ showOrderSummary: !get().showOrderSummary })
      },
      
      resetCheckout: () => {
        set({
          currentStep: 'customer',
          steps: initialSteps,
          formData: initialFormData,
          validation: { isValid: false, errors: {}, warnings: {} },
          selectedShippingMethod: 'standard',
          selectedPaymentMethod: 'card',
          selectedPaymentGateway: null,
          isValidating: false,
          isProcessing: false,
          giftWrap: false,
          giftWrapMessage: '',
          appliedPromoCodes: [],
          promoCodeInput: '',
          isApplyingPromo: false,
          promoError: null,
          showOrderSummary: false
        })
      },
      
      prefillFromUser: (userData) => {
        if (!userData) return
        
        const prefillData: Partial<CheckoutFormData> = {}
        
        if (userData.email) prefillData.email = userData.email
        if (userData.firstName) prefillData.firstName = userData.firstName
        if (userData.lastName) prefillData.lastName = userData.lastName
        if (userData.phone) prefillData.phone = userData.phone
        
        // Prefill addresses if available
        if (userData.defaultShippingAddress) {
          prefillData.shippingAddress = {
            address1: userData.defaultShippingAddress.address1 || '',
            address2: userData.defaultShippingAddress.address2 || '',
            city: userData.defaultShippingAddress.city || '',
            province: userData.defaultShippingAddress.province || '',
            postalCode: userData.defaultShippingAddress.postalCode || '',
            country: userData.defaultShippingAddress.country || 'ZA'
          }
        }
        
        get().updateFormData(prefillData)
      },
      
      // Computed properties
      canProceedToNextStep: () => {
        const { currentStep, validation } = get()
        return validation.isValid
      },
      
      getStepValidation: (step) => {
        return get().validateStep(step)
      },
      
      isStepCompleted: (step) => {
        const stepObj = get().steps.find(s => s.id === step)
        return stepObj?.completed || false
      },
      
      getCompletedStepsCount: () => {
        return get().steps.filter(step => step.completed).length
      },
      
      getProgressPercentage: () => {
        const completedCount = get().getCompletedStepsCount()
        const totalSteps = get().steps.length
        return Math.round((completedCount / totalSteps) * 100)
      }
    }),
    {
      name: 'checkout-store'
    }
  )
)