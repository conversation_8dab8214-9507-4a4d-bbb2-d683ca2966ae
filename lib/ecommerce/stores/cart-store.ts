// Cart store using Zustand
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { 
  Cart, 
  EnhancedCart,
  AddToCartInput,
  UpdateCartItemInput,
  RemoveFromCartInput,
  ApplyDiscountInput,
  RemoveDiscountInput,
  ProductAvailabilityCheck,
  ProductCartValidation,
} from '../types/cart'
import { Product } from '../types/product'

interface CartState {
  // Cart data
  cart: Cart | null
  enhancedCart: EnhancedCart | null
  loading: boolean
  error: { code: string; message: string } | null
  sessionId: string | null
  
  // Actions
  setSessionId: (sessionId: string) => void
  setCart: (cart: Cart | null) => void
  setEnhancedCart: (enhancedCart: EnhancedCart | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: { code: string; message: string } | null) => void
  
  // Cart operations
  addToCart: (input: AddToCartInput) => Promise<void>
  updateCartItem: (input: UpdateCartItemInput) => Promise<void>
  removeFromCart: (input: RemoveFromCartInput) => Promise<void>
  applyDiscount: (input: ApplyDiscountInput) => Promise<void>
  removeDiscount: (input: RemoveDiscountInput) => Promise<void>
  clearCart: () => Promise<void>
  refetch: () => Promise<void>
  clearError: () => void
  
  // Enhanced operations
  validateCart: () => Promise<ProductCartValidation>
  checkProductAvailability: (productId: string, variantId?: string, quantity?: number) => Promise<ProductAvailabilityCheck>
  getRecommendations: () => Promise<Product[]>
  
  // Helpers
  hasOutOfStockItems: () => boolean
  getCartValue: () => number
  getCartWeight: () => number
}

export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      // Initial state
      cart: null,
      enhancedCart: null,
      loading: false,
      error: null,
      sessionId: null,
      
      // State setters
      setSessionId: (sessionId) => set({ sessionId }),
      setCart: (cart) => set({ cart }),
      setEnhancedCart: (enhancedCart) => set({ enhancedCart }),
      setLoading: (loading) => set({ loading }),
      setError: (error) => set({ error }),
      
      // Cart operations
      addToCart: async (input) => {
        const { sessionId } = get()
        set({ loading: true, error: null })
        
        try {
          const requestBody = {
            ...input,
            sessionId
          }
          
          console.log('CartStore: Adding item to cart', requestBody)
          
          const response = await fetch('/api/e-commerce/cart/add', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
          })
          
          if (!response.ok) {
            const errorText = await response.text()
            throw new Error(`HTTP ${response.status}: ${errorText}`)
          }
          
          const result = await response.json()
          
          if (result.success && result.data) {
            set({ 
              cart: result.data,
              enhancedCart: result.data,
              loading: false
            })
            console.log('CartStore: Item added successfully', result.data)
          } else {
            set({ 
              error: result.error || { code: 'ADD_TO_CART_ERROR', message: 'Failed to add item to cart' },
              loading: false
            })
          }
        } catch (err) {
          console.error('CartStore: Error adding to cart', err)
          set({ 
            error: { 
              code: 'NETWORK_ERROR', 
              message: err instanceof Error ? err.message : 'An unexpected error occurred' 
            },
            loading: false
          })
        }
      },
      
      updateCartItem: async (input) => {
        const { sessionId } = get()
        set({ loading: true, error: null })
        
        try {
          const requestBody = {
            ...input,
            sessionId
          }
          
          const response = await fetch('/api/e-commerce/cart/update', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
          })
          
          const result = await response.json()
          
          if (result.success && result.data) {
            set({ 
              cart: result.data,
              enhancedCart: result.data,
              loading: false
            })
          } else {
            set({ 
              error: result.error || { code: 'UPDATE_CART_ERROR', message: 'Failed to update cart item' },
              loading: false
            })
          }
        } catch (err) {
          set({ 
            error: { 
              code: 'NETWORK_ERROR', 
              message: err instanceof Error ? err.message : 'An unexpected error occurred' 
            },
            loading: false
          })
        }
      },
      
      removeFromCart: async (input) => {
        const { sessionId } = get()
        set({ loading: true, error: null })
        
        try {
          const requestBody = {
            ...input,
            sessionId
          }
          
          const response = await fetch('/api/e-commerce/cart/remove', {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
          })
          
          const result = await response.json()
          
          if (result.success && result.data) {
            set({ 
              cart: result.data,
              enhancedCart: result.data,
              loading: false
            })
          } else {
            set({ 
              error: result.error || { code: 'REMOVE_FROM_CART_ERROR', message: 'Failed to remove item from cart' },
              loading: false
            })
          }
        } catch (err) {
          set({ 
            error: { 
              code: 'NETWORK_ERROR', 
              message: err instanceof Error ? err.message : 'An unexpected error occurred' 
            },
            loading: false
          })
        }
      },
      
      applyDiscount: async (input) => {
        const { sessionId } = get()
        set({ loading: true, error: null })
        
        try {
          const requestBody = {
            ...input,
            sessionId
          }
          
          const response = await fetch('/api/e-commerce/cart/discount/apply', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
          })
          
          const result = await response.json()
          
          if (result.success && result.data) {
            set({ 
              cart: result.data,
              enhancedCart: result.data,
              loading: false
            })
          } else {
            set({ 
              error: result.error || { code: 'APPLY_DISCOUNT_ERROR', message: 'Failed to apply discount' },
              loading: false
            })
          }
        } catch (err) {
          set({ 
            error: { 
              code: 'NETWORK_ERROR', 
              message: err instanceof Error ? err.message : 'An unexpected error occurred' 
            },
            loading: false
          })
        }
      },
      
      removeDiscount: async (input) => {
        const { sessionId } = get()
        set({ loading: true, error: null })
        
        try {
          const requestBody = {
            ...input,
            sessionId
          }
          
          const response = await fetch('/api/e-commerce/cart/discount/remove', {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestBody),
          })
          
          const result = await response.json()
          
          if (result.success && result.data) {
            set({ 
              cart: result.data,
              enhancedCart: result.data,
              loading: false
            })
          } else {
            set({ 
              error: result.error || { code: 'REMOVE_DISCOUNT_ERROR', message: 'Failed to remove discount' },
              loading: false
            })
          }
        } catch (err) {
          set({ 
            error: { 
              code: 'NETWORK_ERROR', 
              message: err instanceof Error ? err.message : 'An unexpected error occurred' 
            },
            loading: false
          })
        }
      },
      
      clearCart: async () => {
        const { cart, sessionId } = get()
        if (!cart) return
        
        set({ loading: true, error: null })
        
        try {
          const response = await fetch(`/api/e-commerce/cart?cartId=${cart.id}`, {
            method: 'DELETE',
          })
          
          const result = await response.json()
          
          if (result.success) {
            set({ 
              cart: null,
              enhancedCart: null,
              loading: false
            })
          } else {
            set({ 
              error: result.error || { code: 'CLEAR_CART_ERROR', message: 'Failed to clear cart' },
              loading: false
            })
          }
        } catch (err) {
          set({ 
            error: { 
              code: 'NETWORK_ERROR', 
              message: err instanceof Error ? err.message : 'An unexpected error occurred' 
            },
            loading: false
          })
        }
      },
      
      refetch: async () => {
        const { sessionId } = get()
        if (!sessionId) return
        
        set({ loading: true, error: null })
        
        try {
          const params = new URLSearchParams()
          params.append('sessionId', sessionId)
          params.append('includeProducts', 'true')
          
          const response = await fetch(`/api/e-commerce/cart?${params.toString()}`)
          const result = await response.json()
          
          if (result.success && result.data) {
            set({ 
              cart: result.data,
              enhancedCart: result.data,
              loading: false
            })
          } else {
            set({ 
              error: result.error || { code: 'FETCH_ERROR', message: 'Failed to fetch cart' },
              loading: false
            })
          }
        } catch (err) {
          set({ 
            error: { 
              code: 'NETWORK_ERROR', 
              message: err instanceof Error ? err.message : 'An unexpected error occurred' 
            },
            loading: false
          })
        }
      },
      
      clearError: () => set({ error: null }),
      
      // Enhanced operations
      validateCart: async () => {
        const { cart } = get()
        if (!cart) return { valid: false, items: [] }
        
        try {
          const response = await fetch(`/api/e-commerce/cart/validate?cartId=${cart.id}`)
          const result = await response.json()
          
          if (result.success && result.data) {
            return result.data
          } else {
            return { valid: false, items: [] }
          }
        } catch (err) {
          console.error('Error validating cart:', err)
          return { valid: false, items: [] }
        }
      },
      
      checkProductAvailability: async (productId, variantId, quantity = 1) => {
        try {
          const params = new URLSearchParams()
          params.append('productId', productId)
          if (variantId) params.append('variantId', variantId)
          params.append('quantity', quantity.toString())
          
          const response = await fetch(`/api/e-commerce/products/availability?${params.toString()}`)
          const result = await response.json()
          
          if (result.success && result.data) {
            return result.data
          } else {
            return {
              isAvailable: false,
              availableQuantity: 0,
              isBackordered: false
            }
          }
        } catch (err) {
          console.error('Error checking product availability:', err)
          return {
            isAvailable: false,
            availableQuantity: 0,
            isBackordered: false
          }
        }
      },
      
      getRecommendations: async () => {
        const { cart } = get()
        if (!cart) return []
        
        try {
          const response = await fetch(`/api/e-commerce/recommendations?cartId=${cart.id}`)
          const result = await response.json()
          
          if (result.success && result.data) {
            return result.data
          } else {
            return []
          }
        } catch (err) {
          console.error('Error getting recommendations:', err)
          return []
        }
      },
      
      // Helpers
      hasOutOfStockItems: () => {
        const { enhancedCart } = get()
        if (!enhancedCart) return false
        
        return enhancedCart.items.some(item => !item.isAvailable)
      },
      
      getCartValue: () => {
        const { cart } = get()
        if (!cart) return 0
        
        return cart.subtotal.amount
      },
      
      getCartWeight: () => {
        const { enhancedCart } = get()
        if (!enhancedCart) return 0
        
        return enhancedCart.items.reduce((total, item) => {
          const itemWeight = item.product?.weight || 0
          return total + (itemWeight * item.quantity)
        }, 0)
      }
    }),
    {
      name: 'cart-storage',
      partialize: (state) => ({ 
        sessionId: state.sessionId 
      }),
    }
  )
)

// Initialize session ID on client side
if (typeof window !== 'undefined') {
  const initializeSession = () => {
    const store = useCartStore.getState()
    
    // Get session ID from storage or create a new one
    let sessionId = store.sessionId
    if (!sessionId) {
      sessionId = sessionStorage.getItem('sessionId')
      
      if (!sessionId) {
        sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        sessionStorage.setItem('sessionId', sessionId)
      }
      
      store.setSessionId(sessionId)
      console.log('CartStore: Initialized with sessionId', sessionId)
      
      // Fetch cart data
      store.refetch()
    }
  }
  
  // Run initialization
  initializeSession()
}