// Shopping cart related types and interfaces

import { BaseEntity, Money, Address } from './base'
import { Product, ProductVariant, ProductImage, ProductAvailability } from './product'

export interface CartItem {
  id: string
  cartId: string
  productId: string
  variantId?: string
  quantity: number
  unitPrice: Money
  totalPrice: Money
  
  // Product information (denormalized for performance)
  productTitle: string
  productSlug: string
  productImage?: string
  variantTitle?: string
  variantOptions?: Array<{
    name: string
    value: string
  }>
  
  // Inventory
  isAvailable: boolean
  maxQuantity?: number
  
  // Customization
  customAttributes?: Record<string, any>
  personalizedMessage?: string
  giftWrap?: boolean
  
  // Pricing
  compareAtPrice?: Money
  discountAmount?: Money
  discountReason?: string
  
  // Metadata
  addedAt: Date
  updatedAt: Date
}

export interface Cart extends BaseEntity {
  // Identification
  sessionId?: string
  userId?: string
  
  // Items
  items: CartItem[]
  itemCount: number
  
  // Pricing
  subtotal: Money
  totalDiscount: Money
  totalTax: Money
  totalShipping: Money
  total: Money
  
  // Discounts
  appliedDiscounts: AppliedDiscount[]
  availableDiscounts?: AvailableDiscount[]
  
  // Shipping
  shippingAddress?: Address
  shippingMethod?: CartShippingMethod
  estimatedShipping?: Money
  
  // Tax
  taxLines: TaxLine[]
  taxIncluded: boolean
  
  // Status
  status: 'active' | 'abandoned' | 'converted' | 'expired'
  
  // Metadata
  currency: string
  locale?: string
  customerNote?: string
  attributes?: Record<string, any>
  
  // Timestamps
  lastActivityAt: Date
  expiresAt?: Date
  convertedAt?: Date
  orderId?: string
}

export interface AppliedDiscount {
  id: string
  code?: string
  title: string
  description?: string
  type: 'percentage' | 'fixed_amount' | 'free_shipping'
  value: number
  amount: Money
  applicableItems?: string[] // Cart item IDs
}

export interface AvailableDiscount {
  id: string
  code?: string
  title: string
  description?: string
  type: 'percentage' | 'fixed_amount' | 'free_shipping'
  value: number
  minimumAmount?: Money
  applicableProducts?: string[]
  applicableCategories?: string[]
  expiresAt?: Date
  usageLimit?: number
  usageCount: number
  isAutomatic: boolean
}

export interface CartShippingMethod {
  id: string
  title: string
  description?: string
  price: Money
  estimatedDelivery?: {
    min: number
    max: number
    unit: 'days' | 'weeks'
  }
  carrier?: string
  service?: string
}

export interface TaxLine {
  title: string
  rate: number
  amount: Money
  jurisdiction?: string
}

// Cart operations
export interface AddToCartInput {
  productId: string
  variantId?: string
  quantity: number
  customAttributes?: Record<string, any>
  personalizedMessage?: string
  giftWrap?: boolean
}

export interface UpdateCartItemInput {
  itemId: string
  quantity?: number
  customAttributes?: Record<string, any>
  personalizedMessage?: string
  giftWrap?: boolean
}

export interface RemoveFromCartInput {
  itemId: string
}

export interface ApplyDiscountInput {
  code: string
  cartId: string
}

export interface RemoveDiscountInput {
  discountId: string
  cartId: string
}

export interface UpdateShippingAddressInput {
  cartId: string
  address: Address
}

export interface SelectShippingMethodInput {
  cartId: string
  shippingMethodId: string
}

// Cart validation
export interface CartValidationResult {
  isValid: boolean
  errors: CartValidationError[]
  warnings: CartValidationWarning[]
}

export interface CartValidationError {
  type: 'item_unavailable' | 'insufficient_stock' | 'price_changed' | 'discount_invalid' | 'shipping_unavailable'
  itemId?: string
  discountId?: string
  message: string
  details?: any
}

export interface CartValidationWarning {
  type: 'low_stock' | 'price_increase' | 'discount_expiring' | 'shipping_delay'
  itemId?: string
  discountId?: string
  message: string
  details?: any
}

// Cart abandonment
export interface AbandonedCart extends Cart {
  abandonedAt: Date
  recoveryEmailsSent: number
  lastRecoveryEmailAt?: Date
  recoveryToken?: string
  isRecovered: boolean
  recoveredAt?: Date
}

export interface CartRecoveryEmail {
  id: string
  cartId: string
  emailAddress: string
  templateId: string
  sentAt: Date
  openedAt?: Date
  clickedAt?: Date
  recoveredAt?: Date
}

// Cart analytics
export interface CartAnalytics {
  cartId: string
  sessionDuration: number
  itemsAdded: number
  itemsRemoved: number
  discountsApplied: number
  abandonmentReason?: string
  conversionValue?: Money
  timeToConversion?: number
  trafficSource?: string
  deviceType?: string
  browserType?: string
}

// Wishlist (cart-like functionality)
export interface Wishlist extends BaseEntity {
  userId: string
  name: string
  description?: string
  isPublic: boolean
  items: WishlistItem[]
  itemCount: number
  shareToken?: string
}

export interface WishlistItem {
  id: string
  wishlistId: string
  productId: string
  variantId?: string
  addedAt: Date
  priority?: number
  notes?: string
  
  // Product information (denormalized)
  productTitle: string
  productSlug: string
  productImage?: string
  variantTitle?: string
  currentPrice: Money
  originalPrice?: Money
  isAvailable: boolean
  isOnSale: boolean
}

export interface AddToWishlistInput {
  productId: string
  variantId?: string
  wishlistId?: string
  notes?: string
  priority?: number
}

export interface CreateWishlistInput {
  name: string
  description?: string
  isPublic?: boolean
}

export interface UpdateWishlistInput {
  id: string
  name?: string
  description?: string
  isPublic?: boolean
}

// Cart comparison
export interface CartComparison {
  cartId: string
  comparedWith: string[]
  differences: CartDifference[]
  recommendations: CartRecommendation[]
}

export interface CartDifference {
  type: 'item_missing' | 'quantity_different' | 'price_different' | 'discount_different'
  itemId?: string
  description: string
  impact: Money
}

export interface CartRecommendation {
  type: 'add_item' | 'increase_quantity' | 'apply_discount' | 'change_shipping'
  description: string
  potentialSavings?: Money
  action: any
}

// Cart sharing
export interface SharedCart extends BaseEntity {
  cartId: string
  sharedBy: string
  shareToken: string
  expiresAt?: Date
  isActive: boolean
  viewCount: number
  lastViewedAt?: Date
  permissions: {
    canView: boolean
    canEdit: boolean
    canCheckout: boolean
  }
}

// Cart templates/presets
export interface CartTemplate extends BaseEntity {
  userId: string
  name: string
  description?: string
  items: CartTemplateItem[]
  isPublic: boolean
  useCount: number
  lastUsedAt?: Date
}

export interface CartTemplateItem {
  productId: string
  variantId?: string
  quantity: number
  customAttributes?: Record<string, any>
}

// Quick add functionality
export interface QuickAddItem {
  productId: string
  variantId?: string
  quantity: number
}

export interface BulkAddToCartInput {
  items: QuickAddItem[]
  cartId?: string
}

export interface BulkAddResult {
  success: boolean
  addedItems: CartItem[]
  failedItems: Array<{
    item: QuickAddItem
    error: string
  }>
}

// Cart synchronization (for multi-device)
export interface CartSyncEvent {
  type: 'item_added' | 'item_updated' | 'item_removed' | 'discount_applied' | 'discount_removed'
  cartId: string
  timestamp: Date
  data: any
  deviceId?: string
  sessionId?: string
}

// Cart export/import
export interface CartExport {
  cart: Cart
  format: 'json' | 'csv'
  includeProductDetails: boolean
  createdAt: Date
  expiresAt: Date
}

export interface CartImport {
  items: Array<{
    productId: string
    variantId?: string
    quantity: number
  }>
  preserveExisting: boolean
}

// Enhanced cart item with full product information
export interface EnhancedCartItem extends CartItem {
  product?: Product
  variant?: ProductVariant
  availability?: ProductAvailability
  
  // Enhanced product information
  productImages?: ProductImage[]
  productCategories?: string[]
  productTags?: string[]
  
  // Variant-specific enhancements
  variantImage?: ProductImage
  variantWeight?: number
  variantDimensions?: Product['dimensions']
  
  // Inventory and availability
  inventoryQuantity?: number
  inventoryPolicy?: 'deny' | 'continue'
  trackQuantity?: boolean
  
  // Pricing enhancements
  originalPrice?: Money
  salePrice?: Money
  bulkPricing?: Array<{
    minQuantity: number
    price: Money
  }>
  
  // Product attributes
  attributes?: Array<{
    name: string
    value: string
  }>
  
  // Bundle information (if item is part of a bundle)
  bundleId?: string
  bundleDiscount?: number
  bundlePosition?: number
}

// Cart item with embedded product data for performance
export interface CartItemWithProduct extends CartItem {
  product: Product
  variant?: ProductVariant
  
  // Computed fields
  isOnSale: boolean
  discountPercentage?: number
  savingsAmount?: Money
  estimatedDelivery?: {
    min: number
    max: number
    unit: 'days' | 'weeks'
  }
}

// Product availability check for cart items
export interface ProductAvailabilityCheck {
  productId: string
  variantId?: string
  requestedQuantity: number
  availableQuantity: number
  isAvailable: boolean
  isBackordered: boolean
  estimatedRestockDate?: Date
  alternativeVariants?: Array<{
    variantId: string
    title: string
    availableQuantity: number
    price: Money
  }>
}

// Enhanced cart with product-aware features
export interface EnhancedCart extends Cart {
  items: EnhancedCartItem[]
  
  // Product-based analytics
  productCategories: Array<{
    categoryId: string
    categoryName: string
    itemCount: number
    totalValue: Money
  }>
  
  // Recommendations based on cart contents
  recommendedProducts?: Array<{
    product: Product
    reason: 'frequently_bought_together' | 'similar' | 'complementary' | 'upsell'
    score: number
  }>
  
  // Cross-sell opportunities
  crossSellProducts?: Product[]
  upsellProducts?: Product[]
  
  // Bundle opportunities
  availableBundles?: Array<{
    bundleId: string
    title: string
    description: string
    items: string[] // product IDs in cart that are part of this bundle
    potentialSavings: Money
    bundlePrice: Money
  }>
  
  // Inventory warnings
  inventoryWarnings?: Array<{
    itemId: string
    productId: string
    variantId?: string
    type: 'low_stock' | 'out_of_stock' | 'discontinued'
    message: string
    suggestedAction: 'reduce_quantity' | 'remove_item' | 'substitute_product'
    alternatives?: Product[]
  }>
}

// Enhanced add to cart with product validation
export interface EnhancedAddToCartInput extends AddToCartInput {
  // Product validation
  validateInventory?: boolean
  validatePrice?: boolean
  
  // Bundle handling
  bundleId?: string
  bundleItems?: Array<{
    productId: string
    variantId?: string
    quantity: number
  }>
  
  // Subscription options
  subscription?: {
    frequency: 'weekly' | 'monthly' | 'quarterly'
    discount?: number
  }
  
  // Product customization
  customization?: Array<{
    attributeId: string
    value: string
  }>
}

// Bulk operations with product awareness
export interface BulkCartOperation {
  operation: 'add' | 'update' | 'remove' | 'validate'
  items: Array<{
    productId: string
    variantId?: string
    quantity?: number
    customAttributes?: Record<string, any>
  }>
  options?: {
    validateInventory: boolean
    validatePricing: boolean
    allowPartialSuccess: boolean
  }
}

export interface BulkCartResult {
  success: boolean
  processedCount: number
  failedCount: number
  results: Array<{
    productId: string
    variantId?: string
    success: boolean
    error?: string
    cartItem?: EnhancedCartItem
  }>
  warnings?: Array<{
    productId: string
    variantId?: string
    type: 'inventory_low' | 'price_changed' | 'product_discontinued'
    message: string
  }>
}

// Product-aware cart validation
export interface ProductCartValidation extends CartValidationResult {
  productValidations: Array<{
    productId: string
    variantId?: string
    isValid: boolean
    issues: Array<{
      type: 'inventory' | 'pricing' | 'availability' | 'attributes'
      severity: 'error' | 'warning'
      message: string
      suggestedFix?: string
    }>
  }>
  
  // Pricing validation
  pricingChanges: Array<{
    itemId: string
    productId: string
    variantId?: string
    oldPrice: Money
    newPrice: Money
    changePercentage: number
  }>
  
  // Inventory validation
  inventoryIssues: Array<{
    itemId: string
    productId: string
    variantId?: string
    requestedQuantity: number
    availableQuantity: number
    backorderAvailable: boolean
  }>
}

// Smart cart recommendations
export interface CartRecommendationEngine {
  getRecommendations(cart: Cart): Promise<Array<{
    type: 'product' | 'bundle' | 'discount' | 'shipping'
    product?: Product
    bundle?: {
      id: string
      title: string
      items: Product[]
      savings: Money
    }
    discount?: {
      code: string
      description: string
      savings: Money
    }
    shipping?: {
      method: string
      savings: Money
    }
    priority: number
    reason: string
  }>>
  
  getFrequentlyBoughtTogether(productIds: string[]): Promise<Product[]>
  getAlternativeProducts(productId: string, reason: 'out_of_stock' | 'price' | 'similar'): Promise<Product[]>
}

// Cart persistence with product data
export interface CartPersistence {
  saveCart(cart: EnhancedCart): Promise<void>
  loadCart(cartId: string): Promise<EnhancedCart | null>
  syncCartWithProducts(cart: Cart): Promise<EnhancedCart>
  validateCartProducts(cart: Cart): Promise<ProductCartValidation>
}
