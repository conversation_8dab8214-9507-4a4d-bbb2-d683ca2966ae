// Custom error classes for e-commerce operations

export class NotFoundError extends Error {
  constructor(resourceType: string, identifier: string | number) {
    super(`${resourceType} not found with identifier: ${identifier}`)
    this.name = 'NotFoundError'
  }
}

export class ValidationError extends Error {
  constructor(message: string) {
    super(message)
    this.name = 'ValidationError'
  }
}

export class InsufficientStockError extends Error {
  constructor(productId: string, requested: number, available: number) {
    super(`Insufficient stock for product ${productId}: requested ${requested}, available ${available}`)
    this.name = 'InsufficientStockError'
  }
}

export class UnauthorizedError extends Error {
  constructor(message: string = 'Unauthorized access') {
    super(message)
    this.name = 'UnauthorizedError'
  }
}

export class ForbiddenError extends Error {
  constructor(message: string = 'Access forbidden') {
    super(message)
    this.name = 'ForbiddenError'
  }
}

// General API error interface
export interface ApiError {
  code: string
  message: string
  statusCode?: number
}
