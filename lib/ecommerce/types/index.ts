// Core E-commerce Library Types
// This file contains all the main type definitions for the e-commerce library

// Export base types first
export * from './base'

// Re-export all types from individual modules
export * from './product'
export * from './user'
export * from './order'
export * from './cart'
export * from './errors'
export * from './inventory'
export * from './payment'
export * from './shipping'
export * from './admin'

// Re-export customer types explicitly to avoid conflicts
export type {
  Customer,
  CreateCustomerInput,
  UpdateCustomerInput,
  CustomerAddress,
  CustomerAnalytics
} from './customer'

// Re-export error classes explicitly
export {
  NotFoundError,
  ValidationError,
  InsufficientStockError,
  UnauthorizedError,
  ForbiddenError
} from './base'

// Re-export common utility types
export type {
  ApiResponse,
  Money,
  BaseEntity,
  PaginatedResponse,
  PaginationInfo
} from './base'
