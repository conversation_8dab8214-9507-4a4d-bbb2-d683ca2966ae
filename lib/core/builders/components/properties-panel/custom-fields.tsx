'use client'

import React, { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { 
  Collapsible, 
  CollapsibleContent, 
  CollapsibleTrigger 
} from '@/components/ui/collapsible'
import { 
  ChevronDown, 
  ChevronRight, 
  Plus, 
  Trash2, 
  Info,
  AlertCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'

// Base Field Props
interface BaseFieldProps {
  label: string
  description?: string
  error?: string
  required?: boolean
  disabled?: boolean
  className?: string
}

// Text Field Component
interface TextFieldProps extends BaseFieldProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  type?: 'text' | 'email' | 'url' | 'password'
  maxLength?: number
}

export function TextField({
  label,
  description,
  error,
  required = false,
  disabled = false,
  className,
  value,
  onChange,
  placeholder,
  type = 'text',
  maxLength
}: TextFieldProps) {
  return (
    <div className={cn('space-y-2', className)}>
      <Label htmlFor={label} className="text-sm font-medium">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
      
      <Input
        id={label}
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        maxLength={maxLength}
        className={cn(error && 'border-red-500')}
      />
      
      {maxLength && (
        <div className="text-xs text-muted-foreground text-right">
          {value.length}/{maxLength}
        </div>
      )}
      
      {error && (
        <div className="flex items-center gap-1 text-xs text-red-600">
          <AlertCircle className="w-3 h-3" />
          {error}
        </div>
      )}
    </div>
  )
}

// Textarea Field Component
interface TextareaFieldProps extends BaseFieldProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  rows?: number
  maxLength?: number
}

export function TextareaField({
  label,
  description,
  error,
  required = false,
  disabled = false,
  className,
  value,
  onChange,
  placeholder,
  rows = 3,
  maxLength
}: TextareaFieldProps) {
  return (
    <div className={cn('space-y-2', className)}>
      <Label htmlFor={label} className="text-sm font-medium">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
      
      <Textarea
        id={label}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        rows={rows}
        maxLength={maxLength}
        className={cn(error && 'border-red-500')}
      />
      
      {maxLength && (
        <div className="text-xs text-muted-foreground text-right">
          {value.length}/{maxLength}
        </div>
      )}
      
      {error && (
        <div className="flex items-center gap-1 text-xs text-red-600">
          <AlertCircle className="w-3 h-3" />
          {error}
        </div>
      )}
    </div>
  )
}

// Boolean Field Component
interface BooleanFieldProps extends BaseFieldProps {
  value: boolean
  onChange: (value: boolean) => void
}

export function BooleanField({
  label,
  description,
  error,
  disabled = false,
  className,
  value,
  onChange
}: BooleanFieldProps) {
  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <Label className="text-sm font-medium">{label}</Label>
          {description && (
            <p className="text-xs text-muted-foreground">{description}</p>
          )}
        </div>
        
        <Switch
          checked={value}
          onCheckedChange={onChange}
          disabled={disabled}
        />
      </div>
      
      {error && (
        <div className="flex items-center gap-1 text-xs text-red-600">
          <AlertCircle className="w-3 h-3" />
          {error}
        </div>
      )}
    </div>
  )
}

// Select Field Component
interface SelectOption {
  value: string
  label: string
  description?: string
  disabled?: boolean
}

interface SelectFieldProps extends BaseFieldProps {
  value: string
  onChange: (value: string) => void
  options: SelectOption[]
  placeholder?: string
}

export function SelectField({
  label,
  description,
  error,
  required = false,
  disabled = false,
  className,
  value,
  onChange,
  options,
  placeholder = 'Select an option...'
}: SelectFieldProps) {
  return (
    <div className={cn('space-y-2', className)}>
      <Label className="text-sm font-medium">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      
      {description && (
        <p className="text-xs text-muted-foreground">{description}</p>
      )}
      
      <Select value={value} onValueChange={onChange} disabled={disabled}>
        <SelectTrigger className={cn(error && 'border-red-500')}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem 
              key={option.value} 
              value={option.value}
              disabled={option.disabled}
            >
              <div>
                <div>{option.label}</div>
                {option.description && (
                  <div className="text-xs text-muted-foreground">
                    {option.description}
                  </div>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {error && (
        <div className="flex items-center gap-1 text-xs text-red-600">
          <AlertCircle className="w-3 h-3" />
          {error}
        </div>
      )}
    </div>
  )
}

// Object Field Component (for complex nested objects)
interface ObjectFieldProps extends BaseFieldProps {
  value: any
  onChange: (value: any) => void
  schema: ObjectSchema
}

interface ObjectSchema {
  type: 'object'
  properties: Record<string, PropertySchema>
  required?: string[]
}

interface PropertySchema {
  type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  title: string
  description?: string
  default?: any
  enum?: string[]
  items?: PropertySchema
  properties?: Record<string, PropertySchema>
}

export function ObjectField({
  label,
  description,
  error,
  className,
  value,
  onChange,
  schema
}: ObjectFieldProps) {
  const [isOpen, setIsOpen] = useState(false)

  const handlePropertyChange = (propertyKey: string, propertyValue: any) => {
    onChange({
      ...value,
      [propertyKey]: propertyValue
    })
  }

  const renderProperty = (key: string, propertySchema: PropertySchema) => {
    const propertyValue = value?.[key] ?? propertySchema.default
    const isRequired = schema.required?.includes(key) ?? false

    switch (propertySchema.type) {
      case 'string':
        if (propertySchema.enum) {
          return (
            <SelectField
              key={key}
              label={propertySchema.title}
              description={propertySchema.description}
              value={propertyValue || ''}
              onChange={(val) => handlePropertyChange(key, val)}
              options={propertySchema.enum.map(option => ({
                value: option,
                label: option
              }))}
              required={isRequired}
            />
          )
        }
        return (
          <TextField
            key={key}
            label={propertySchema.title}
            description={propertySchema.description}
            value={propertyValue || ''}
            onChange={(val) => handlePropertyChange(key, val)}
            required={isRequired}
          />
        )
      
      case 'boolean':
        return (
          <BooleanField
            key={key}
            label={propertySchema.title}
            description={propertySchema.description}
            value={propertyValue ?? false}
            onChange={(val) => handlePropertyChange(key, val)}
          />
        )
      
      case 'number':
        return (
          <TextField
            key={key}
            label={propertySchema.title}
            description={propertySchema.description}
            value={String(propertyValue || '')}
            onChange={(val) => handlePropertyChange(key, Number(val) || 0)}
            type="text"
            required={isRequired}
          />
        )
      
      default:
        return null
    }
  }

  return (
    <div className={cn('space-y-2', className)}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="w-full justify-between p-0 h-auto">
            <div className="text-left">
              <div className="text-sm font-medium">{label}</div>
              {description && (
                <div className="text-xs text-muted-foreground">{description}</div>
              )}
            </div>
            {isOpen ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        
        <CollapsibleContent className="space-y-3 pt-2">
          {Object.entries(schema.properties).map(([key, propertySchema]) =>
            renderProperty(key, propertySchema)
          )}
        </CollapsibleContent>
      </Collapsible>
      
      {error && (
        <div className="flex items-center gap-1 text-xs text-red-600">
          <AlertCircle className="w-3 h-3" />
          {error}
        </div>
      )}
    </div>
  )
}

// Field Group Component
interface FieldGroupProps {
  children: React.ReactNode
  className?: string
}

export function FieldGroup({ children, className }: FieldGroupProps) {
  return (
    <div className={cn('space-y-4', className)}>
      {children}
    </div>
  )
}

// Field Section Component
interface FieldSectionProps {
  title: string
  description?: string
  icon?: React.ReactNode
  children: React.ReactNode
  collapsible?: boolean
  defaultOpen?: boolean
  className?: string
}

export function FieldSection({
  title,
  description,
  icon,
  children,
  collapsible = false,
  defaultOpen = true,
  className
}: FieldSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen)

  if (collapsible) {
    return (
      <Card className={className}>
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {icon}
                  <div>
                    <CardTitle className="text-sm font-medium">{title}</CardTitle>
                    {description && (
                      <p className="text-xs text-muted-foreground mt-1">{description}</p>
                    )}
                  </div>
                </div>
                {isOpen ? (
                  <ChevronDown className="w-4 h-4" />
                ) : (
                  <ChevronRight className="w-4 h-4" />
                )}
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          
          <CollapsibleContent>
            <CardContent className="pt-0">
              {children}
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center gap-2">
          {icon}
          <div>
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
            {description && (
              <p className="text-xs text-muted-foreground mt-1">{description}</p>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  )
}

// Array Field Component (for managing lists)
interface ArrayFieldProps extends BaseFieldProps {
  value: any[]
  onChange: (value: any[]) => void
  itemSchema: PropertySchema
  addButtonText?: string
  maxItems?: number
}

export function ArrayField({
  label,
  description,
  error,
  className,
  value = [],
  onChange,
  itemSchema,
  addButtonText = 'Add Item',
  maxItems
}: ArrayFieldProps) {
  const addItem = () => {
    if (maxItems && value.length >= maxItems) return
    
    const newItem = itemSchema.default ?? (
      itemSchema.type === 'string' ? '' :
      itemSchema.type === 'number' ? 0 :
      itemSchema.type === 'boolean' ? false :
      {}
    )
    
    onChange([...value, newItem])
  }

  const removeItem = (index: number) => {
    const newValue = [...value]
    newValue.splice(index, 1)
    onChange(newValue)
  }

  const updateItem = (index: number, itemValue: any) => {
    const newValue = [...value]
    newValue[index] = itemValue
    onChange(newValue)
  }

  return (
    <div className={cn('space-y-3', className)}>
      <div className="flex items-center justify-between">
        <div>
          <Label className="text-sm font-medium">{label}</Label>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
        </div>
        
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addItem}
          disabled={maxItems ? value.length >= maxItems : false}
          className="gap-1"
        >
          <Plus className="w-3 h-3" />
          {addButtonText}
        </Button>
      </div>

      {value.length > 0 && (
        <div className="space-y-2">
          {value.map((item, index) => (
            <div key={index} className="flex items-start gap-2 p-3 border rounded-lg">
              <div className="flex-1">
                {itemSchema.type === 'string' && (
                  <TextField
                    label={`Item ${index + 1}`}
                    value={item}
                    onChange={(val) => updateItem(index, val)}
                  />
                )}
                {itemSchema.type === 'object' && itemSchema.properties && (
                  <ObjectField
                    label={`Item ${index + 1}`}
                    value={item}
                    onChange={(val) => updateItem(index, val)}
                    schema={{
                      type: 'object',
                      properties: itemSchema.properties
                    }}
                  />
                )}
              </div>
              
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeItem(index)}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {value.length === 0 && (
        <div className="text-center py-6 text-sm text-muted-foreground border-2 border-dashed rounded-lg">
          No items added yet. Click "{addButtonText}" to get started.
        </div>
      )}

      {error && (
        <div className="flex items-center gap-1 text-xs text-red-600">
          <AlertCircle className="w-3 h-3" />
          {error}
        </div>
      )}
    </div>
  )
}