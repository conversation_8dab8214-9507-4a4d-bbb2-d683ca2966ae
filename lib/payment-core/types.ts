/**
 * Payment Core Library Types
 * 
 * Comprehensive type definitions for the payment processing system.
 */

// Core payment types
export interface PaymentAmount {
  amount: number
  currency: string
  formatted?: string
}

export interface PaymentCustomer {
  id?: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  address?: PaymentAddress
}

export interface PaymentAddress {
  line1: string
  line2?: string
  city: string
  state: string
  postalCode: string
  country: string
}

export interface PaymentItem {
  id: string
  name: string
  description?: string
  quantity: number
  unitPrice: number
  totalPrice: number
  sku?: string
  category?: string
  taxRate?: number
  discountAmount?: number
}

export interface PaymentMetadata {
  orderId: string
  customerId?: string
  source: string
  [key: string]: any
}

export interface PaymentRequest {
  amount: PaymentAmount
  customer: PaymentCustomer
  items: PaymentItem[]
  metadata: PaymentMetadata
  returnUrl: string
  cancelUrl: string
  notifyUrl: string
  reference: string
  description: string
  paymentMethod?: PaymentMethod
}

export interface PaymentResponse {
  success: boolean
  transactionId?: string
  paymentUrl?: string
  qrCode?: string
  reference?: string
  status: PaymentStatus
  message?: string
  error?: PaymentError
  gatewayResponse?: any
  redirectMethod?: 'GET' | 'POST'
  formData?: Record<string, string>
}

export interface PaymentError {
  code: string
  message: string
  details?: any
  retryable?: boolean
}

export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded',
  EXPIRED = 'expired'
}

export enum PaymentMethod {
  CARD = 'card',
  EFT = 'eft',
  QR_CODE = 'qr_code',
  INSTANT_EFT = 'instant_eft',
  BANK_TRANSFER = 'bank_transfer',
  MOBILE_MONEY = 'mobile_money',
  CRYPTO = 'crypto',
}

export enum PaymentGateway {
  PAYFAST = 'payfast',
  OZOW = 'ozow',
  SNAPSCAN = 'snapscan',
  YOCO = 'yoco',
  PAYU = 'payu',
  ZAPPER = 'zapper',
}

// Webhook types
export interface WebhookPayload {
  gateway: PaymentGateway
  event: WebhookEvent
  data: any
  signature?: string
  timestamp: string
}

export interface WebhookRequest {
  gateway: PaymentGateway
  payload: any
  signature?: string
}

export interface WebhookResponse {
  success: boolean
  transactionId?: string
  event?: WebhookEvent
  message?: string
  error?: string
}

export enum WebhookEvent {
  PAYMENT_COMPLETED = 'payment.completed',
  PAYMENT_FAILED = 'payment.failed',
  PAYMENT_CANCELLED = 'payment.cancelled',
  PAYMENT_REFUNDED = 'payment.refunded',
  PAYMENT_PENDING = 'payment.pending',
  PAYMENT_PROCESSING = 'payment.processing'
}

// Gateway-specific configurations
export interface PayFastConfig {
  merchantId: string
  merchantKey: string
  passphrase: string
  sandbox: boolean
  enabled: boolean
}

export interface OzowConfig {
  apiKey: string
  privateKey: string
  siteCode: string
  sandbox: boolean
  enabled: boolean
}

export interface SnapScanConfig {
  apiKey: string
  merchantId: string
  sandbox: boolean
  enabled: boolean
}

export interface YocoConfig {
  secretKey: string
  publicKey: string
  sandbox: boolean
  enabled: boolean
}

export interface PayUConfig {
  apiKey: string
  safeKey: string
  merchantId: string
  sandbox: boolean
  enabled: boolean
}

export interface ZapperConfig {
  merchantId: string
  apiKey: string
  siteId: string
  sandbox: boolean
  enabled: boolean
}

export interface ManualBankPaymentConfig {
  bankName: string
  accountName: string
  accountNumber: string
  branchCode: string
  branchName?: string
  accountType: 'cheque' | 'savings' | 'current' | 'business'
  referenceFormat: string
  instructions: string
  proofOfPaymentRequired: boolean
  contactEmail?: string
  contactPhone?: string
  enabled: boolean
  currency: string
}

export interface ManualBankPaymentDetails {
  bankName: string
  accountName: string
  accountNumber: string
  branchCode: string
  branchName?: string
  accountType: 'cheque' | 'savings' | 'current' | 'business'
  reference: string
  instructions: string
  proofOfPaymentUrl?: string
  paymentDeadline?: Date
  status: 'pending' | 'received' | 'confirmed' | 'failed'
  receivedAt?: Date
  confirmedAt?: Date
  notes?: string
}

export interface PaymentGatewayConfig {
  payfast?: PayFastConfig
  ozow?: OzowConfig
  snapscan?: SnapScanConfig
  yoco?: YocoConfig
  payu?: PayUConfig
  zapper?: ZapperConfig
  //manual?: ManualBankPaymentConfig
  [key: string]: any // Allow additional gateways
  enabledGateways?: PaymentGateway[]
  defaultGateway?: PaymentGateway
  defaultCurrency?: string
  autoRetry?: boolean
  maxRetries?: number
  retryDelay?: number
  webhookSecret?: string
  logLevel?: 'debug' | 'info' | 'warn' | 'error'
  storageAdapter?: any // Custom storage adapter for persistence
}

// Payment Gateway Interface
export interface IPaymentGateway {
  name: PaymentGateway
  displayName: string
  supportedMethods: PaymentMethod[]
  supportedCurrencies: string[]
  
  // Core payment methods
  createPayment(request: PaymentRequest): Promise<PaymentResponse>
  getPaymentStatus(transactionId: string): Promise<PaymentStatus>
  refundPayment(transactionId: string, amount?: number): Promise<PaymentResponse>
  
  // Webhook handling
  verifyWebhook(payload: any, signature: string): boolean
  processWebhook(payload: WebhookPayload): Promise<void>
  
  // Validation
  validateConfig(): boolean
  validatePaymentRequest(request: PaymentRequest): PaymentValidationResult
}

export interface PaymentValidationResult {
  isValid: boolean
  errors: string[]
}

// Transaction logging
export interface PaymentTransaction {
  id: string
  orderId: string
  customerId?: string
  gateway: PaymentGateway
  method: PaymentMethod
  amount: PaymentAmount
  status: PaymentStatus
  reference: string
  transactionId?: string
  gatewayResponse?: any
  createdAt: Date
  updatedAt: Date
  metadata?: any
}

// Refund types
export interface RefundRequest {
  transactionId: string
  amount?: number
  reason: string
  metadata?: any
}

export interface RefundResponse {
  success: boolean
  refundId?: string
  amount?: number
  status: PaymentStatus
  message?: string
  error?: PaymentError
}

// Payment analytics
export interface PaymentAnalytics {
  totalTransactions: number
  totalAmount: number
  successRate: number
  averageTransactionValue: number
  topPaymentMethods: Array<{
    method: PaymentMethod
    count: number
    percentage: number
  }>
  gatewayPerformance: Array<{
    gateway: PaymentGateway
    successRate: number
    averageProcessingTime: number
  }>
}

// Security and compliance
export interface SecurityConfig {
  encryptionKey: string
  hashAlgorithm: string
  tokenExpiry: number
  maxRetries: number
  rateLimitWindow: number
  rateLimitMax: number
  webhookSecret: string
}

export interface ComplianceData {
  pciCompliant: boolean
  popiaCompliant: boolean
  vatNumber?: string
  businessRegistration?: string
  auditTrail: boolean
}

// Error codes
export enum PaymentErrorCode {
  INVALID_AMOUNT = 'INVALID_AMOUNT',
  INVALID_CURRENCY = 'INVALID_CURRENCY',
  INVALID_CUSTOMER = 'INVALID_CUSTOMER',
  GATEWAY_ERROR = 'GATEWAY_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  INSUFFICIENT_FUNDS = 'INSUFFICIENT_FUNDS',
  CARD_DECLINED = 'CARD_DECLINED',
  EXPIRED_CARD = 'EXPIRED_CARD',
  INVALID_CARD = 'INVALID_CARD',
  FRAUD_DETECTED = 'FRAUD_DETECTED',
  RATE_LIMITED = 'RATE_LIMITED',
  MAINTENANCE = 'MAINTENANCE',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  GATEWAY_UNAVAILABLE = 'GATEWAY_UNAVAILABLE',
  TIMEOUT = 'TIMEOUT',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  PARTIAL_REFUND_NOT_ALLOWED = 'PARTIAL_REFUND_NOT_ALLOWED',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
}

// Rate limiting
export interface RateLimitConfig {
  windowMs: number
  max: number
  message: string
  standardHeaders: boolean
  legacyHeaders: boolean
}

// Logging
export interface PaymentLog {
  id: string
  level: 'info' | 'warn' | 'error' | 'debug'
  message: string
  gateway?: PaymentGateway
  transactionId?: string
  orderId?: string
  customerId?: string
  metadata?: any
  timestamp: Date
}

// Payment method display configuration
export interface PaymentMethodConfig {
  displayName: string
  description: string
  icon: string
  supportedGateways: PaymentGateway[]
  processingTime: string
  fees: {
    percentage: number
    fixed: number
  }
}

// Payment service configuration
export interface PaymentServiceConfig {
  defaultGateway?: PaymentGateway
  defaultCurrency?: string
  autoRetry?: boolean
  maxRetries?: number
  retryDelay?: number
  webhookSecret?: string
  logLevel?: 'debug' | 'info' | 'warn' | 'error'
  storageAdapter?: any
}

// Ecommerce integration types
export interface EcommerceOrder {
  id: string
  orderNumber: string
  customerId?: string
  customerEmail: string
  customerName: string
  total: number
  currency: string
  status: string
  items: Array<{
    id: string
    name: string
    quantity: number
    price: number
    total: number
  }>
  billingAddress?: PaymentAddress
  shippingAddress?: PaymentAddress
}

export interface EcommercePaymentResult {
  success: boolean
  orderId: string
  transactionId?: string
  status: PaymentStatus
  paymentUrl?: string
  message?: string
  error?: PaymentError
}

// Storage adapter interface for persistence
export interface IStorageAdapter {
  saveTransaction(transaction: PaymentTransaction): Promise<void>
  getTransaction(transactionId: string): Promise<PaymentTransaction | null>
  updateTransactionStatus(transactionId: string, status: PaymentStatus, metadata?: any): Promise<void>
  getTransactionsByOrderId(orderId: string): Promise<PaymentTransaction[]>
  getTransactionsByCustomerId(customerId: string): Promise<PaymentTransaction[]>
}

// Default export all types
export default {
  PaymentStatus,
  PaymentMethod,
  PaymentGateway,
  WebhookEvent,
  PaymentErrorCode
}