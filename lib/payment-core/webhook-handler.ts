/**
 * Payment Webhook Handler
 * 
 * Processes incoming webhooks from payment gateways.
 */

import type { NextApiRequest, NextApiResponse } from 'next'
import type { 
  PaymentGateway, 
  WebhookPayload
} from './types'
import { PaymentStatus } from './types'
import { WebhookEvent } from './types'

import { gatewayFactory } from './gateway-factory'
import { logger, webhookLogger } from './logger'
import { paymentService } from './service'

/**
 * Process webhook from payment gateway
 */
export async function processWebhook(
  gateway: PaymentGateway,
  payload: any,
  signature?: string
): Promise<{
  success: boolean
  message: string
  event?: WebhookEvent
  transactionId?: string
  status?: PaymentStatus
}> {
  try {
    // Get gateway instance
    const gatewayInstance = gatewayFactory.getGateway(gateway)
    if (!gatewayInstance) {
      throw new Error(`Gateway ${gateway} not available`)
    }

    // Verify webhook signature if provided
    if (signature && !gatewayInstance.verifyWebhook(payload, signature)) {
      logger.warn('Invalid webhook signature', { gateway })
      return {
        success: false,
        message: 'Invalid signature'
      }
    }

    // Determine event type and transaction ID from payload
    // This is gateway-specific and would be handled by the gateway implementation
    const webhookPayload: WebhookPayload = {
      gateway,
      event: WebhookEvent.PAYMENT_COMPLETED, // Default, will be overridden by gateway
      data: payload,
      signature,
      timestamp: new Date().toISOString()
    }

    // Process webhook with gateway-specific logic
    await gatewayInstance.processWebhook(webhookPayload)

    // Extract transaction ID and status from payload
    // This is a simplified example - in reality, each gateway would have its own logic
    const transactionId = payload.transaction_id || payload.transactionId || payload.m_payment_id || ''
    const status = payload.status || payload.payment_status || PaymentStatus.COMPLETED

    // Update transaction status in database
    if (transactionId) {
      // Get the transaction first
      const transaction = await paymentService.getTransaction(transactionId);
      
      if (transaction) {
        // Update the transaction status
        transaction.status = status as PaymentStatus;
        transaction.updatedAt = new Date();
        transaction.metadata = {
          ...transaction.metadata,
          webhookReceived: true,
          webhookTimestamp: new Date().toISOString(),
          webhookData: payload
        };
        
        // Store the updated transaction using the storage adapter
        const { storageAdapter } = await import('./storage-adapter');
        await storageAdapter.updateTransactionStatus(transactionId, status as PaymentStatus, {
          webhookReceived: true,
          webhookTimestamp: new Date().toISOString(),
          webhookData: payload
        });
      }
    }

    webhookLogger.processed({
      gateway,
      event: webhookPayload.event,
      transactionId,
      status
    })

    return {
      success: true,
      message: 'Webhook processed successfully',
      event: webhookPayload.event,
      transactionId,
      status: status as PaymentStatus
    }
  } catch (error) {
    webhookLogger.error({
      gateway,
      error: error instanceof Error ? error.message : 'Unknown error',
      payload
    })

    return {
      success: false,
      message: error instanceof Error ? error.message : 'Webhook processing failed'
    }
  }
}

/**
 * Next.js API route handler for webhooks
 */
export async function webhookHandler(
  req: NextApiRequest,
  res: NextApiResponse,
  gateway: PaymentGateway
) {
  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' })
    }

    // Log webhook received
    webhookLogger.received({
      gateway,
      body: req.body,
      headers: req.headers
    })

    // Get signature from headers
    const signature = req.headers['x-signature'] as string || 
                     req.headers['signature'] as string ||
                     req.headers['x-payfast-signature'] as string ||
                     req.headers['x-ozow-signature'] as string ||
                     ''

    // Process webhook
    const result = await processWebhook(gateway, req.body, signature)

    if (result.success) {
      return res.status(200).json({ message: result.message })
    } else {
      return res.status(400).json({ error: result.message })
    }
  } catch (error) {
    logger.error('Webhook handler error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      gateway
    })

    return res.status(500).json({ error: 'Internal server error' })
  }
}

export default webhookHandler