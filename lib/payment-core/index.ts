/**
 * Payment Core Library
 * 
 * A robust payment processing library designed to integrate seamlessly with the ecommerce system.
 * This library provides a unified interface for multiple payment gateways with South African focus.
 */

// Export core types
export * from './types'

// Export logger
export * from './logger'

// Export main service
export { paymentService } from './service'

// Export gateway factory and payment manager
export { gatewayFactory, paymentManager } from './gateway-factory'

// Export webhook handler
export { processWebhook } from './webhook-handler'

// Export utilities
export {
  validatePaymentRequest,
  formatAmount,
  generateTransactionId,
  generatePaymentReference,
  isValidEmail,
  isValidPhone,
  isValidUrl,
  generateHash,
  generateHmacSignature,
  safeJsonParse,
  safeJsonStringify,
  maskSensitiveData
} from './utils'

// Export error handling utilities
export {
  createPaymentError,
  handleError,
  isRetryableError,
  retryWithBackoff
} from './error-handler'

// Export configuration
export {
  getGatewayUrl,
  isGatewayEnabled,
  getEnabledGateways,
  getGatewaysForPaymentMethod,
  getPaymentMethodConfig,
  getDefaultGateway,
  calculateTransactionFee,
  PAYMENT_METHOD_CONFIG,
  GATEWAY_PRIORITY,
  TRANSACTION_LIMITS,
  VAT_CONFIG
} from './config'

// Export storage adapter
export { storageAdapter } from './storage-adapter'

// Export initialization function
export { initializePaymentCore } from './initialize'

// Export version information
export const VERSION = '1.2.0'
export const LIBRARY_NAME = 'Payment Core Library'

// Default export the payment service
import { paymentService } from './service'
export default paymentService