/**
 * Payment Gateway Factory
 * 
 * Factory pattern implementation for creating and managing payment gateway instances.
 */

import {
  IPaymentGateway,
  PaymentRequest,
  PaymentResponse,
  PaymentStatus,
  PaymentErrorCode,
  PaymentMethod,
  PaymentGateway,
  PaymentError
} from './types'

import { 
  GATEWAY_PRIORITY, 
  getEnabledGateways, 
  isGatewayEnabled,
  PAYMENT_METHOD_CONFIG,
  getGatewaysForPaymentMethod
} from './config'

import { logger, paymentLogger } from './logger'

// Import gateway implementations
import { PayFastGateway } from './gateways/payfast'
import { OzowGateway } from './gateways/ozow'
import { SnapScanGateway } from './gateways/snapscan'
import { YocoGateway } from './gateways/yoco'
import { ManualGateway } from './gateways/manual'
import { retryWithBackoff } from './utils'

/**
 * Payment Gateway Factory
 * Creates and manages payment gateway instances
 */
export class PaymentGatewayFactory {
  private static instance: PaymentGatewayFactory
  private gateways: Map<PaymentGateway, IPaymentGateway> = new Map()
  private initialized: boolean = false

  private constructor() {
    // Initialization is deferred to init() method
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): PaymentGatewayFactory {
    if (!PaymentGatewayFactory.instance) {
      PaymentGatewayFactory.instance = new PaymentGatewayFactory()
    }
    return PaymentGatewayFactory.instance
  }

  /**
   * Initialize available gateways
   */
  public async init(): Promise<void> {
    if (this.initialized) {
      return
    }

    try {
      // Initialize PayFast
      if (isGatewayEnabled(PaymentGateway.PAYFAST)) {
        this.gateways.set(PaymentGateway.PAYFAST, new PayFastGateway())
        logger.info('PayFast gateway initialized')
      }

      // Initialize Ozow
      if (isGatewayEnabled(PaymentGateway.OZOW)) {
        this.gateways.set(PaymentGateway.OZOW, new OzowGateway())
        logger.info('Ozow gateway initialized')
      }

      // Initialize SnapScan
      if (isGatewayEnabled(PaymentGateway.SNAPSCAN)) {
        this.gateways.set(PaymentGateway.SNAPSCAN, new SnapScanGateway())
        logger.info('SnapScan gateway initialized')
      }

      // Initialize Yoco
      if (isGatewayEnabled(PaymentGateway.YOCO)) {
        this.gateways.set(PaymentGateway.YOCO, new YocoGateway())
        logger.info('Yoco gateway initialized')
      }

      // Always initialize PayFast gateway
      this.gateways.set(PaymentGateway.PAYFAST, new PayFastGateway())
      logger.info('PayFast gateway initialized')

      logger.info(`Initialized ${this.gateways.size} payment gateways`, {
        gateways: Array.from(this.gateways.keys()),
      })

      this.initialized = true
    } catch (error) {
      logger.error('Failed to initialize payment gateways', {
        error: error instanceof Error ? error.message : 'Unknown error',
      })
      throw error
    }
  }

  /**
   * Get gateway instance by name
   */
  public getGateway(gateway: PaymentGateway): IPaymentGateway | null {
    if (!this.initialized) {
      logger.warn('Gateway factory not initialized, initializing now')
      this.init().catch(error => {
        logger.error('Failed to initialize gateway factory', { error })
      })
    }
    return this.gateways.get(gateway) || null
  }

  /**
   * Get all available gateways
   */
  public getAvailableGateways(): PaymentGateway[] {
    return Array.from(this.gateways.keys())
  }

  /**
   * Get gateways that support a specific payment method
   */
  public getGatewaysForMethod(method: PaymentMethod): PaymentGateway[] {
    const supportedGateways: PaymentGateway[] = []

    for (const [gatewayName, gateway] of this.gateways) {
      if (gateway.supportedMethods.includes(method)) {
        supportedGateways.push(gatewayName)
      }
    }

    // Sort by priority
    return supportedGateways.sort((a, b) => {
      const priorityA = GATEWAY_PRIORITY.indexOf(a)
      const priorityB = GATEWAY_PRIORITY.indexOf(b)
      return priorityA - priorityB
    })
  }

  /**
   * Get best gateway for a payment request
   */
  public getBestGateway(request: PaymentRequest, preferredMethod?: PaymentMethod): IPaymentGateway | null {
    const currency = request.amount.currency
    const amount = request.amount.amount

    // Filter gateways by currency support
    const compatibleGateways = Array.from(this.gateways.entries()).filter(([_, gateway]) => 
      gateway.supportedCurrencies.includes(currency)
    )

    if (compatibleGateways.length === 0) {
      logger.warn('No gateways support the requested currency', { currency })
      return null
    }

    // If preferred method is specified, filter by method support
    if (preferredMethod) {
      const methodCompatibleGateways = compatibleGateways.filter(([_, gateway]) =>
        gateway.supportedMethods.includes(preferredMethod)
      )

      if (methodCompatibleGateways.length > 0) {
        // Return the highest priority gateway that supports the method
        for (const gatewayName of GATEWAY_PRIORITY) {
          const gateway = methodCompatibleGateways.find(([name]) => name === gatewayName)
          if (gateway) {
            return gateway[1]
          }
        }
      }
    }

    // Return the highest priority compatible gateway
    for (const gatewayName of GATEWAY_PRIORITY) {
      const gateway = compatibleGateways.find(([name]) => name === gatewayName)
      if (gateway) {
        return gateway[1]
      }
    }

    return null
  }

  /**
   * Validate gateway configuration
   */
  public validateGatewayConfig(gateway: PaymentGateway): boolean {
    const gatewayInstance = this.getGateway(gateway)
    return gatewayInstance ? gatewayInstance.validateConfig() : false
  }
}

/**
 * Payment Manager
 * High-level payment processing with fallback and retry logic
 */
export class PaymentManager {
  private factory: PaymentGatewayFactory
  private initialized: boolean = false

  constructor() {
    this.factory = PaymentGatewayFactory.getInstance()
  }

  /**
   * Initialize the payment manager
   */
  public async init(): Promise<void> {
    if (!this.initialized) {
      await this.factory.init()
      this.initialized = true
    }
  }

  /**
   * Process payment with automatic gateway selection and fallback
   */
  async processPayment(
    request: PaymentRequest,
    preferredGateway?: PaymentGateway,
    preferredMethod?: PaymentMethod
  ): Promise<PaymentResponse> {
    const startTime = Date.now()

    try {
      // Ensure initialized
      if (!this.initialized) {
        await this.init()
      }

      paymentLogger.initiated({
        gateway: preferredGateway || 'auto',
        reference: request.reference,
        amount: request.amount.amount,
        currency: request.amount.currency,
        customerId: request.metadata.customerId,
        orderId: request.metadata.orderId,
      })

      // Get primary gateway
      let gateway: IPaymentGateway | null = null

      if (preferredGateway) {
        gateway = this.factory.getGateway(preferredGateway)
        if (!gateway) {
          logger.warn('Preferred gateway not available', { gateway: preferredGateway })
        }
      }

      // If request has a payment method, try to find a gateway that supports it
      if (!gateway && request.paymentMethod) {
        const supportedGateways = getGatewaysForPaymentMethod(request.paymentMethod)
        if (supportedGateways.length > 0) {
          gateway = this.factory.getGateway(supportedGateways[0])
        }
      }

      // Fallback to best available gateway
      if (!gateway) {
        gateway = this.factory.getBestGateway(request, preferredMethod || request.paymentMethod)
      }

      if (!gateway) {
        const error = 'No suitable payment gateway available'
        paymentLogger.failed({
          gateway: preferredGateway || 'auto',
          reference: request.reference,
          amount: request.amount.amount,
          currency: request.amount.currency,
          customerId: request.metadata.customerId,
          orderId: request.metadata.orderId,
          errorCode: PaymentErrorCode.GATEWAY_UNAVAILABLE,
          errorMessage: error,
          processingTime: Date.now() - startTime,
        })

        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: {
            code: PaymentErrorCode.GATEWAY_UNAVAILABLE,
            message: error,
          },
        }
      }

      // Process payment with retry logic
      const response = await retryWithBackoff(
        async () => await gateway!.createPayment(request),
        3,
        1000
      )

      const processingTime = Date.now() - startTime

      if (response.success) {
        paymentLogger.initiated({
          gateway: gateway.name,
          reference: request.reference,
          amount: request.amount.amount,
          currency: request.amount.currency,
          customerId: request.metadata.customerId,
          orderId: request.metadata.orderId,
          transactionId: response.transactionId,
          status: response.status,
        })

        logger.debug('Payment performance metrics', {
          gateway: gateway.name,
          operation: 'create_payment',
          duration: processingTime,
          success: true,
        })
      } else {
        paymentLogger.failed({
          gateway: gateway.name,
          reference: request.reference,
          amount: request.amount.amount,
          currency: request.amount.currency,
          customerId: request.metadata.customerId,
          orderId: request.metadata.orderId,
          errorCode: response.error?.code || PaymentErrorCode.UNKNOWN_ERROR,
          errorMessage: response.error?.message || 'Unknown error',
          processingTime,
        })
      }

      return response

    } catch (error: any) {
      const processingTime = Date.now() - startTime

      paymentLogger.failed({
        gateway: preferredGateway || 'auto',
        reference: request.reference,
        amount: request.amount.amount,
        currency: request.amount.currency,
        customerId: request.metadata.customerId,
        orderId: request.metadata.orderId,
        errorCode: PaymentErrorCode.UNKNOWN_ERROR,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        processingTime,
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.UNKNOWN_ERROR,
          message: error instanceof Error ? error.message : 'Payment processing failed',
        },
      }
    }
  }

  /**
   * Get payment status from gateway
   */
  async getPaymentStatus(
    transactionId: string,
    gateway: PaymentGateway
  ): Promise<PaymentStatus> {
    try {
      // Ensure initialized
      if (!this.initialized) {
        await this.init()
      }

      const gatewayInstance = this.factory.getGateway(gateway)
      if (!gatewayInstance) {
        throw new Error(`Gateway ${gateway} not available`)
      }

      return await gatewayInstance.getPaymentStatus(transactionId)

    } catch (error) {
      logger.error('Failed to get payment status', {
        gateway,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error',
      })

      return PaymentStatus.FAILED
    }
  }

  /**
   * Process refund
   */
  async processRefund(
    transactionId: string,
    gateway: PaymentGateway,
    amount?: number,
    reason?: string
  ): Promise<PaymentResponse> {
    try {
      // Ensure initialized
      if (!this.initialized) {
        await this.init()
      }

      const gatewayInstance = this.factory.getGateway(gateway)
      if (!gatewayInstance) {
        throw new Error(`Gateway ${gateway} not available`)
      }

      logger.info('Initiating refund', {
        gateway,
        originalTransactionId: transactionId,
        refundAmount: amount || 0,
        currency: 'ZAR', // Default to ZAR
        reason: reason || 'Customer request',
      })

      const response = await gatewayInstance.refundPayment(transactionId, amount)

      if (response.success) {
        logger.info('Refund completed', {
          gateway,
          originalTransactionId: transactionId,
          refundTransactionId: response.transactionId || '',
          refundAmount: amount || 0,
          currency: 'ZAR',
        })
      }

      return response

    } catch (error) {
      logger.error('Failed to process refund', {
        gateway,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error',
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.UNKNOWN_ERROR,
          message: error instanceof Error ? error.message : 'Refund processing failed',
        },
      }
    }
  }

  /**
   * Get available payment methods
   */
  getAvailablePaymentMethods(): Array<{
    method: PaymentMethod
    displayName: string
    description: string
    icon: string
    gateways: PaymentGateway[]
  }> {
    const methods: Array<{
      method: PaymentMethod
      displayName: string
      description: string
      icon: string
      gateways: PaymentGateway[]
    }> = []

    for (const [method, config] of Object.entries(PAYMENT_METHOD_CONFIG)) {
      const supportedGateways = this.factory.getGatewaysForMethod(method as PaymentMethod)
      
      if (supportedGateways.length > 0) {
        methods.push({
          method: method as PaymentMethod,
          displayName: config.displayName,
          description: config.description,
          icon: config.icon,
          gateways: supportedGateways
        })
      }
    }

    return methods
  }

  /**
   * Get gateway for a specific payment method
   */
  getGatewayForMethod(method: PaymentMethod): PaymentGateway | null {
    const gateways = this.factory.getGatewaysForMethod(method)
    return gateways.length > 0 ? gateways[0] : null
  }
}

// Create and export singleton instances
export const gatewayFactory = PaymentGatewayFactory.getInstance()
export const paymentManager = new PaymentManager()

export default {
  gatewayFactory,
  paymentManager
}