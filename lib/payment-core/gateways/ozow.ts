/**
 * Ozow Payment Gateway Implementation
 * 
 * Handles integration with the Ozow payment gateway.
 * Documentation: https://ozow.com/integrations
 */

import crypto from 'crypto'
import { 
  IPaymentGateway, 
  PaymentGateway, 
  PaymentMethod, 
  PaymentRequest, 
  PaymentResponse, 
  PaymentStatus,
  PaymentValidationResult,
  WebhookPayload,
  WebhookEvent,
  OzowConfig,
  PaymentErrorCode
} from '../types'
import { paymentConfig, getGatewayUrl } from '../config'
import { logger } from '../logger'

export class OzowGateway implements IPaymentGateway {
  name = PaymentGateway.OZOW
  displayName = 'Ozow'
  supportedMethods = [
    PaymentMethod.INSTANT_EFT,
    PaymentMethod.EFT
  ]
  supportedCurrencies = ['ZAR']

  private config: OzowConfig
  private apiBaseUrl: string

  constructor() {
    this.config = paymentConfig.ozow as OzowConfig
    
    // Set API URL based on sandbox mode
    this.apiBaseUrl = this.config.sandbox 
      ? 'https://pay.sandbox.ozow.com'
      : 'https://pay.ozow.com'
    
    if (!this.validateConfig()) {
      logger.warn('Ozow configuration is incomplete', { gateway: this.name })
    }
  }

  validateConfig(): boolean {
    return !!(
      this.config?.apiKey &&
      this.config?.privateKey &&
      this.config?.siteCode
    )
  }

  validatePaymentRequest(request: PaymentRequest): PaymentValidationResult {
    const errors: string[] = []

    // Amount validation
    if (!request.amount || request.amount.amount <= 0) {
      errors.push('Invalid amount')
    }

    if (request.amount.currency !== 'ZAR') {
      errors.push('Ozow only supports ZAR currency')
    }

    // Customer validation
    if (!request.customer.email) {
      errors.push('Customer email is required')
    }

    // URLs validation
    if (!request.returnUrl || !request.cancelUrl || !request.notifyUrl) {
      errors.push('Return, cancel, and notify URLs are required')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      logger.info('Creating Ozow payment', {
        gateway: this.name,
        reference: request.reference,
        amount: request.amount.amount
      })

      // Validate request
      const validation = this.validatePaymentRequest(request)
      if (!validation.isValid) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: {
            code: PaymentErrorCode.VALIDATION_ERROR,
            message: validation.errors.join(', ')
          }
        }
      }

      // Create Ozow payment data
      const paymentData = this.createPaymentData(request)
      
      // Generate hash check
      const hashCheck = this.generateHashCheck(paymentData)
      paymentData.HashCheck = hashCheck

      // Create payment URL
      const queryString = new URLSearchParams(paymentData).toString()
      const paymentUrl = `${this.apiBaseUrl}?${queryString}`

      logger.info('Ozow payment URL created', {
        gateway: this.name,
        reference: request.reference,
        transactionId: paymentData.TransactionReference,
        hasHashCheck: !!hashCheck
      })

      return {
        success: true,
        paymentUrl,
        transactionId: paymentData.TransactionReference,
        reference: request.reference,
        status: PaymentStatus.PENDING,
        message: 'Redirecting to Ozow payment page',
        redirectMethod: 'GET'
      }

    } catch (error) {
      logger.error('Ozow payment creation failed', {
        gateway: this.name,
        reference: request.reference,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.GATEWAY_ERROR,
          message: 'Failed to create payment',
          details: error instanceof Error ? error.message : undefined
        }
      }
    }
  }

  async getPaymentStatus(transactionId: string): Promise<PaymentStatus> {
    try {
      logger.info('Getting Ozow payment status', {
        gateway: this.name,
        transactionId
      })

      // Ozow provides a status check API
      // For a real implementation with API access:
      
      // const statusData = {
      //   SiteCode: this.config.siteCode,
      //   TransactionReference: transactionId,
      //   ApiKey: this.config.apiKey
      // }
      
      // // Generate hash check
      // const hashCheck = this.generateHashCheck(statusData)
      // statusData.HashCheck = hashCheck
      
      // // Build query string
      // const queryString = new URLSearchParams(statusData).toString()
      
      // // Make API request
      // const response = await fetch(`${this.apiBaseUrl}/api/status?${queryString}`, {
      //   method: 'GET',
      //   headers: {
      //     'Accept': 'application/json'
      //   }
      // })
      
      // if (response.ok) {
      //   const data = await response.json()
      //   return this.mapOzowStatus(data.Status)
      // }

      return PaymentStatus.PENDING
    } catch (error) {
      logger.error('Failed to get Ozow payment status', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return PaymentStatus.FAILED
    }
  }

  async refundPayment(transactionId: string, amount?: number): Promise<PaymentResponse> {
    try {
      logger.info('Processing Ozow refund', {
        gateway: this.name,
        transactionId,
        amount
      })

      // Ozow provides a refund API
      // For a real implementation with API access:
      
      // const refundData = {
      //   SiteCode: this.config.siteCode,
      //   TransactionReference: transactionId,
      //   Amount: amount ? amount.toFixed(2) : undefined,
      //   ApiKey: this.config.apiKey,
      //   IsTest: this.config.sandbox ? 'true' : 'false'
      // }
      
      // // Generate hash check
      // const hashCheck = this.generateHashCheck(refundData)
      // refundData.HashCheck = hashCheck
      
      // // Make API request
      // const response = await fetch(`${this.apiBaseUrl}/api/refund`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Accept': 'application/json'
      //   },
      //   body: JSON.stringify(refundData)
      // })
      
      // if (response.ok) {
      //   const data = await response.json()
      //   if (data.Success) {
      //     return {
      //       success: true,
      //       status: PaymentStatus.REFUNDED,
      //       message: 'Refund processed successfully',
      //       transactionId
      //     }
      //   } else {
      //     return {
      //       success: false,
      //       status: PaymentStatus.FAILED,
      //       error: {
      //         code: 'REFUND_FAILED',
      //         message: data.Error || 'Refund failed'
      //       }
      //     }
      //   }
      // }
      
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: 'Automated refunds not implemented for Ozow. Please contact support.'
        }
      }
    } catch (error) {
      logger.error('Ozow refund failed', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.GATEWAY_ERROR,
          message: 'Failed to process refund',
          details: error instanceof Error ? error.message : undefined
        }
      }
    }
  }

  verifyWebhook(payload: any, signature: string): boolean {
    try {
      // Ozow uses a hash check for verification
      const receivedHash = payload.HashCheck
      
      // Create a copy of the payload without the HashCheck
      const { HashCheck, ...dataToVerify } = payload
      
      // Generate expected hash
      const calculatedHash = this.generateHashCheck(dataToVerify)
      
      return receivedHash === calculatedHash
    } catch (error) {
      logger.error('Ozow webhook verification failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return false
    }
  }

  async processWebhook(payload: WebhookPayload): Promise<void> {
    try {
      logger.info('Processing Ozow webhook', {
        gateway: this.name,
        event: payload.event,
        data: payload.data
      })

      // Extract payment data from payload
      const data = payload.data
      const status = this.mapOzowStatus(data.Status)
      const transactionId = data.TransactionReference || ''
      const orderId = data.Optional1 || ''
      
      // Determine event type based on status
      let event: WebhookEvent
      switch (status) {
        case PaymentStatus.COMPLETED:
          event = WebhookEvent.PAYMENT_COMPLETED
          break
        case PaymentStatus.FAILED:
          event = WebhookEvent.PAYMENT_FAILED
          break
        case PaymentStatus.CANCELLED:
          event = WebhookEvent.PAYMENT_CANCELLED
          break
        default:
          event = WebhookEvent.PAYMENT_PENDING
      }

      // Here you would typically:
      // 1. Update payment status in database
      // 2. Send notifications
      // 3. Update order status
      // 4. Trigger business logic

      logger.info('Ozow webhook processed', {
        gateway: this.name,
        event,
        status,
        transactionId,
        orderId
      })

    } catch (error) {
      logger.error('Ozow webhook processing failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      throw error
    }
  }

  private createPaymentData(request: PaymentRequest): Record<string, string> {
    // Generate a unique transaction reference
    const transactionReference = request.reference || `OZ_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    
    // Create bank reference (max 20 chars)
    const bankReference = request.reference.substring(0, 20)
    
    return {
      // Required fields
      SiteCode: this.config.siteCode,
      CountryCode: 'ZA',
      CurrencyCode: request.amount.currency,
      Amount: request.amount.amount.toFixed(2),
      TransactionReference: transactionReference,
      BankReference: bankReference,
      Customer: request.customer.email,
      
      // URLs
      CancelUrl: request.cancelUrl,
      ErrorUrl: request.cancelUrl,
      SuccessUrl: request.returnUrl,
      NotifyUrl: request.notifyUrl,
      
      // API key
      ApiKey: this.config.apiKey,
      
      // Test mode
      IsTest: this.config.sandbox ? 'true' : 'false',
      
      // Optional fields
      Optional1: request.metadata.orderId || '',
      Optional2: request.metadata.customerId || '',
      Optional3: request.metadata.source || 'payment-core',
      Optional4: new Date().toISOString(),
      Optional5: request.paymentMethod || PaymentMethod.INSTANT_EFT
    }
  }

  private generateHashCheck(data: Record<string, string>): string {
    // Sort keys alphabetically
    const sortedKeys = Object.keys(data).sort()
    
    // Concatenate values
    const concatenated = sortedKeys.map(key => data[key]).join('')
    
    // Add private key
    const withKey = concatenated + this.config.privateKey
    
    // Generate SHA-512 hash (lowercase)
    return crypto.createHash('sha512').update(withKey.toLowerCase()).digest('hex')
  }

  private mapOzowStatus(status: string): PaymentStatus {
    switch (status?.toLowerCase()) {
      case 'complete':
      case 'completed':
        return PaymentStatus.COMPLETED
      case 'processing':
        return PaymentStatus.PROCESSING
      case 'error':
      case 'failed':
        return PaymentStatus.FAILED
      case 'cancelled':
        return PaymentStatus.CANCELLED
      case 'timeout':
        return PaymentStatus.EXPIRED
      default:
        return PaymentStatus.PENDING
    }
  }
}

export default OzowGateway