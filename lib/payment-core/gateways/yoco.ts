/**
 * Yoco Payment Gateway Implementation
 * 
 * Handles integration with the Yoco payment gateway.
 * Documentation: https://developer.yoco.com
 */

import crypto from 'crypto'
import { 
  IPaymentGateway, 
  PaymentGateway, 
  PaymentMethod, 
  PaymentRequest, 
  PaymentResponse, 
  PaymentStatus,
  PaymentValidationResult,
  WebhookPayload,
  WebhookEvent,
  YocoConfig,
  PaymentErrorCode
} from '../types'
import { paymentConfig, getGatewayUrl } from '../config'
import { logger } from '../logger'

export class YocoGateway implements IPaymentGateway {
  name = PaymentGateway.YOCO
  displayName = 'Yoco'
  supportedMethods = [PaymentMethod.CARD]
  supportedCurrencies = ['ZAR']

  private config: YocoConfig
  private apiBaseUrl = 'https://online.yoco.com/v1'

  constructor() {
    this.config = paymentConfig.yoco as YocoConfig
    
    // Set test mode URL if in sandbox mode
    if (this.config.sandbox) {
      this.apiBaseUrl = 'https://online.yoco.com/v1/test'
    }
    
    if (!this.validateConfig()) {
      logger.warn('Yoco configuration is incomplete', { gateway: this.name })
    }
  }

  validateConfig(): boolean {
    return !!(
      this.config?.secretKey &&
      this.config?.publicKey
    )
  }

  validatePaymentRequest(request: PaymentRequest): PaymentValidationResult {
    const errors: string[] = []

    // Amount validation
    if (!request.amount || request.amount.amount <= 0) {
      errors.push('Invalid amount')
    }

    if (request.amount.currency !== 'ZAR') {
      errors.push('Yoco only supports ZAR currency')
    }

    // Customer validation
    if (!request.customer.email) {
      errors.push('Customer email is required')
    }

    // URLs validation
    if (!request.returnUrl || !request.cancelUrl) {
      errors.push('Return and cancel URLs are required')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      logger.info('Creating Yoco payment', {
        gateway: this.name,
        reference: request.reference,
        amount: request.amount.amount
      })

      // Validate request
      const validation = this.validatePaymentRequest(request)
      if (!validation.isValid) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: {
            code: PaymentErrorCode.VALIDATION_ERROR,
            message: validation.errors.join(', ')
          }
        }
      }

      // Yoco API integration
      // For a real implementation with API access:
      
      // const paymentData = {
      //   amount: Math.round(request.amount.amount * 100), // Yoco expects amount in cents
      //   currency: request.amount.currency,
      //   name: request.customer.firstName + ' ' + (request.customer.lastName || ''),
      //   email: request.customer.email,
      //   metadata: {
      //     reference: request.reference,
      //     orderId: request.metadata.orderId,
      //     customerId: request.metadata.customerId,
      //     source: request.metadata.source || 'payment-core'
      //   },
      //   successUrl: request.returnUrl,
      //   cancelUrl: request.cancelUrl,
      //   failureUrl: request.cancelUrl
      // }
      
      // const response = await fetch(`${this.apiBaseUrl}/checkout`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Authorization': `Bearer ${this.config.secretKey}`
      //   },
      //   body: JSON.stringify(paymentData)
      // })
      
      // if (response.ok) {
      //   const data = await response.json()
      //   return {
      //     success: true,
      //     transactionId: data.id,
      //     reference: request.reference,
      //     paymentUrl: data.redirectUrl,
      //     status: PaymentStatus.PENDING,
      //     message: 'Redirecting to Yoco payment page'
      //   }
      // } else {
      //   const errorData = await response.json()
      //   throw new Error(errorData.message || 'Failed to create Yoco payment')
      // }
      
      // For now, we'll create a simulated payment URL
      // Create a unique ID for this payment
      const paymentId = `yoco_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      
      // Construct the payment URL
      const checkoutUrl = `${this.apiBaseUrl}/checkout/`
      const paymentUrl = `${checkoutUrl}${paymentId}`
      
      logger.info('Yoco payment created', {
        gateway: this.name,
        reference: request.reference,
        paymentId,
        paymentUrl
      })

      return {
        success: true,
        transactionId: paymentId,
        reference: request.reference,
        paymentUrl,
        status: PaymentStatus.PENDING,
        message: 'Redirecting to Yoco payment page'
      }

    } catch (error) {
      logger.error('Yoco payment creation failed', {
        gateway: this.name,
        reference: request.reference,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.GATEWAY_ERROR,
          message: 'Failed to create payment',
          details: error instanceof Error ? error.message : undefined
        }
      }
    }
  }

  async getPaymentStatus(transactionId: string): Promise<PaymentStatus> {
    try {
      logger.info('Getting Yoco payment status', {
        gateway: this.name,
        transactionId
      })

      // Yoco API integration
      // For a real implementation with API access:
      
      // const response = await fetch(`${this.apiBaseUrl}/charges/${transactionId}`, {
      //   method: 'GET',
      //   headers: {
      //     'Authorization': `Bearer ${this.config.secretKey}`
      //   }
      // })
      
      // if (response.ok) {
      //   const data = await response.json()
      //   return this.mapYocoStatus(data.status)
      // }
      
      return PaymentStatus.PENDING
    } catch (error) {
      logger.error('Failed to get Yoco payment status', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return PaymentStatus.FAILED
    }
  }

  async refundPayment(transactionId: string, amount?: number): Promise<PaymentResponse> {
    try {
      logger.info('Processing Yoco refund', {
        gateway: this.name,
        transactionId,
        amount
      })

      // Yoco API integration
      // For a real implementation with API access:
      
      // const refundData = {
      //   amount: amount ? Math.round(amount * 100) : undefined, // Yoco expects amount in cents
      //   reason: 'Customer requested refund'
      // }
      
      // const response = await fetch(`${this.apiBaseUrl}/charges/${transactionId}/refunds`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Authorization': `Bearer ${this.config.secretKey}`
      //   },
      //   body: JSON.stringify(refundData)
      // })
      
      // if (response.ok) {
      //   const data = await response.json()
      //   return {
      //     success: true,
      //     transactionId: data.id,
      //     reference: transactionId,
      //     status: PaymentStatus.REFUNDED,
      //     message: 'Refund processed successfully'
      //   }
      // } else {
      //   const errorData = await response.json()
      //   throw new Error(errorData.message || 'Failed to process refund')
      // }
      
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: 'Automated refunds not implemented for Yoco. Please contact support.'
        }
      }
    } catch (error) {
      logger.error('Yoco refund failed', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.GATEWAY_ERROR,
          message: 'Failed to process refund',
          details: error instanceof Error ? error.message : undefined
        }
      }
    }
  }

  verifyWebhook(payload: any, signature: string): boolean {
    try {
      // Yoco uses HMAC-SHA256 for webhook verification
      if (!signature || !this.config.secretKey) {
        return false
      }
      
      // Convert payload to string
      const payloadString = typeof payload === 'string' 
        ? payload 
        : JSON.stringify(payload)
      
      // Generate HMAC signature
      const hmac = crypto.createHmac('sha256', this.config.secretKey)
      const calculatedSignature = hmac.update(payloadString).digest('hex')
      
      return signature === calculatedSignature
    } catch (error) {
      logger.error('Yoco webhook verification failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return false
    }
  }

  async processWebhook(payload: WebhookPayload): Promise<void> {
    try {
      logger.info('Processing Yoco webhook', {
        gateway: this.name,
        event: payload.event,
        data: payload.data
      })

      // Extract payment data from payload
      const data = payload.data
      const status = this.mapYocoStatus(data.status)
      const transactionId = data.id || data.chargeId || ''
      const reference = data.metadata?.reference || ''
      const orderId = data.metadata?.orderId || ''
      
      // Determine event type based on status
      let event: WebhookEvent
      switch (status) {
        case PaymentStatus.COMPLETED:
          event = WebhookEvent.PAYMENT_COMPLETED
          break
        case PaymentStatus.FAILED:
          event = WebhookEvent.PAYMENT_FAILED
          break
        case PaymentStatus.CANCELLED:
          event = WebhookEvent.PAYMENT_CANCELLED
          break
        case PaymentStatus.REFUNDED:
          event = WebhookEvent.PAYMENT_REFUNDED
          break
        default:
          event = WebhookEvent.PAYMENT_PENDING
      }

      // Here you would typically:
      // 1. Update payment status in database
      // 2. Send notifications
      // 3. Update order status
      // 4. Trigger business logic

      logger.info('Yoco webhook processed', {
        gateway: this.name,
        event,
        status,
        transactionId,
        reference,
        orderId
      })

    } catch (error) {
      logger.error('Yoco webhook processing failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      throw error
    }
  }

  private mapYocoStatus(status: string): PaymentStatus {
    switch (status?.toLowerCase()) {
      case 'succeeded':
      case 'success':
      case 'successful':
      case 'completed':
        return PaymentStatus.COMPLETED
      case 'pending':
      case 'waiting':
        return PaymentStatus.PENDING
      case 'processing':
        return PaymentStatus.PROCESSING
      case 'failed':
      case 'failure':
      case 'error':
        return PaymentStatus.FAILED
      case 'cancelled':
      case 'canceled':
        return PaymentStatus.CANCELLED
      case 'refunded':
        return PaymentStatus.REFUNDED
      case 'expired':
      case 'timeout':
        return PaymentStatus.EXPIRED
      default:
        return PaymentStatus.PENDING
    }
  }
}

export default YocoGateway