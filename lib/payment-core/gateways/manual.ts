/**
 * Manual Payment Gateway Implementation
 * 
 * Handles manual payment methods like cash on delivery.
 */

import { 
  IPaymentGateway, 
  PaymentGateway, 
  PaymentMethod, 
  PaymentRequest, 
  PaymentResponse, 
  PaymentStatus,
  PaymentValidationResult,
  WebhookPayload,
  WebhookEvent,
  PaymentErrorCode
} from '../types'
import { logger } from '../logger'

export class ManualGateway implements IPaymentGateway {
  name = PaymentGateway.MANUAL
  displayName = 'Manual Payment'
  supportedMethods = [
    PaymentMethod.CASH,
    PaymentMethod.BANK_TRANSFER
  ]
  supportedCurrencies = ['ZAR', 'USD', 'EUR', 'GBP']

  constructor() {
    // No configuration needed for manual gateway
  }

  validateConfig(): boolean {
    // Manual gateway is always valid
    return true
  }

  validatePaymentRequest(request: PaymentRequest): PaymentValidationResult {
    const errors: string[] = []

    // Basic validation
    if (!request.amount || request.amount.amount <= 0) {
      errors.push('Invalid amount')
    }

    if (!request.customer.email) {
      errors.push('Customer email is required')
    }

    if (!request.reference) {
      errors.push('Payment reference is required')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      logger.info('Creating manual payment', {
        gateway: this.name,
        reference: request.reference,
        amount: request.amount.amount,
        method: request.paymentMethod || PaymentMethod.CASH
      })

      // Validate request
      const validation = this.validatePaymentRequest(request)
      if (!validation.isValid) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: {
            code: PaymentErrorCode.VALIDATION_ERROR,
            message: validation.errors.join(', ')
          }
        }
      }

      // For cash payments, we just return success with instructions
      if (request.paymentMethod === PaymentMethod.CASH) {
        return {
          success: true,
          transactionId: request.reference,
          reference: request.reference,
          status: PaymentStatus.PENDING,
          message: 'Cash payment will be collected on delivery',
          paymentUrl: request.returnUrl // Redirect to success page
        }
      }

      // For bank transfers, provide banking details
      if (request.paymentMethod === PaymentMethod.BANK_TRANSFER) {
        const bankDetails = this.getBankDetails()
        
        return {
          success: true,
          transactionId: request.reference,
          reference: request.reference,
          status: PaymentStatus.PENDING,
          message: `Please transfer ${request.amount.formatted || request.amount.amount} to our bank account using reference: ${request.reference}`,
          paymentUrl: request.returnUrl, // Redirect to success page
          gatewayResponse: {
            bankDetails,
            reference: request.reference,
            amount: request.amount
          }
        }
      }

      // Default response for other methods
      return {
        success: true,
        transactionId: request.reference,
        reference: request.reference,
        status: PaymentStatus.PENDING,
        message: 'Manual payment created successfully',
        paymentUrl: request.returnUrl // Redirect to success page
      }

    } catch (error) {
      logger.error('Manual payment creation failed', {
        gateway: this.name,
        reference: request.reference,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.GATEWAY_ERROR,
          message: 'Failed to create manual payment',
          details: error instanceof Error ? error.message : undefined
        }
      }
    }
  }

  async getPaymentStatus(transactionId: string): Promise<PaymentStatus> {
    // Manual payments stay in pending until manually updated
    return PaymentStatus.PENDING
  }

  async refundPayment(transactionId: string, amount?: number): Promise<PaymentResponse> {
    logger.info('Processing manual refund', {
      gateway: this.name,
      transactionId,
      amount
    })

    // Manual refunds need to be processed manually
    return {
      success: false,
      status: PaymentStatus.FAILED,
      error: {
        code: 'MANUAL_PROCESS',
        message: 'Manual refunds need to be processed manually by staff'
      }
    }
  }

  verifyWebhook(payload: any, signature: string): boolean {
    // Manual gateway doesn't use webhooks
    return false
  }

  async processWebhook(payload: WebhookPayload): Promise<void> {
    // Manual gateway doesn't use webhooks
    logger.warn('Manual gateway received webhook, ignoring', {
      gateway: this.name,
      event: payload.event
    })
  }

  private getBankDetails(): Record<string, string> {
    // These would typically come from configuration
    return {
      bankName: 'Standard Bank',
      accountNumber: '*********',
      branchCode: '051001',
      accountType: 'Current',
      accountHolder: 'Coco Milk Store',
      reference: 'Order #'
    }
  }
}

export default ManualGateway