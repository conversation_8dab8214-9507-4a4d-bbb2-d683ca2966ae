/**
 * PayFast Payment Gateway Implementation
 * 
 * Handles integration with the PayFast payment gateway.
 * Documentation: https://developers.payfast.co.za/
 */

import crypto from 'crypto'
import { 
  IPaymentGateway, 
  PaymentGateway, 
  PaymentMethod, 
  PaymentRequest, 
  PaymentResponse, 
  PaymentStatus,
  PaymentValidationResult,
  WebhookPayload,
  WebhookEvent,
  PayFastConfig,
  PaymentErrorCode
} from '../types'
import { paymentConfig, getGatewayUrl } from '../config'
import { logger } from '../logger'
import { generateHash } from '../utils'

export class PayFastGateway implements IPaymentGateway {
  name = PaymentGateway.PAYFAST
  displayName = 'PayFast'
  supportedMethods = [
    PaymentMethod.CARD, 
    PaymentMethod.EFT, 
    PaymentMethod.BANK_TRANSFER,
    PaymentMethod.QR_CODE,
    PaymentMethod.MOBILE_MONEY
  ]
  supportedCurrencies = ['ZAR']

  private config: PayFastConfig
  private apiBaseUrl = 'https://api.payfast.co.za'

  constructor() {
    this.config = paymentConfig.payfast as PayFastConfig
    
    // Set sandbox API URL if in sandbox mode
    if (this.config.sandbox) {
      this.apiBaseUrl = 'https://sandbox.payfast.co.za/eng/process'
    }
    
    if (!this.validateConfig()) {
      logger.warn('PayFast configuration is incomplete', { gateway: this.name })
    }
  }

  validateConfig(): boolean {
    return !!(
      this.config?.merchantId &&
      this.config?.merchantKey
    )
  }

  validatePaymentRequest(request: PaymentRequest): PaymentValidationResult {
    const errors: string[] = []

    // Amount validation
    if (!request.amount || request.amount.amount <= 0) {
      errors.push('Invalid amount')
    }

    if (request.amount.currency !== 'ZAR') {
      errors.push('PayFast only supports ZAR currency')
    }

    // Customer validation
    if (!request.customer.email) {
      errors.push('Customer email is required')
    }

    if (!request.customer.firstName) {
      errors.push('Customer first name is required')
    }

    // URLs validation
    if (!request.returnUrl || !request.cancelUrl || !request.notifyUrl) {
      errors.push('Return, cancel, and notify URLs are required')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      logger.info('Creating PayFast payment', {
        gateway: this.name,
        reference: request.reference,
        amount: request.amount.amount
      })

      // Validate request
      const validation = this.validatePaymentRequest(request)
      if (!validation.isValid) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: {
            code: PaymentErrorCode.VALIDATION_ERROR,
            message: validation.errors.join(', ')
          }
        }
      }

      // Create PayFast payment data
      const paymentData = this.createPaymentData(request)
      
      // Generate signature
      const signature = this.generateSignature(paymentData)
      paymentData.signature = signature

      // Create payment URL
      const baseUrl = getGatewayUrl(PaymentGateway.PAYFAST)
      const queryString = new URLSearchParams(paymentData).toString()
      const paymentUrl = `${baseUrl}?${queryString}`

      logger.info('PayFast payment URL created', {
        gateway: this.name,
        reference: request.reference,
        hasSignature: !!signature,
        transactionId: paymentData.m_payment_id
      })

      return {
        success: true,
        paymentUrl,
        transactionId: paymentData.m_payment_id,
        reference: request.reference,
        status: PaymentStatus.PENDING,
        message: 'Payment URL created successfully',
        redirectMethod: 'GET'
      }

    } catch (error) {
      logger.error('PayFast payment creation failed', {
        gateway: this.name,
        reference: request.reference,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.GATEWAY_ERROR,
          message: 'Failed to create payment',
          details: error instanceof Error ? error.message : undefined
        }
      }
    }
  }

  async getPaymentStatus(transactionId: string): Promise<PaymentStatus> {
    try {
      logger.info('Getting PayFast payment status', {
        gateway: this.name,
        transactionId
      })

      // PayFast doesn't have a direct API to check payment status for standard integration
      // For recurring billing or tokenization, we could use the API
      // For now, we'll implement a basic version that returns pending
      
      // If we had a merchant account with API access, we could use:
      // const timestamp = new Date().toISOString()
      // const headers = {
      //   'merchant-id': this.config.merchantId,
      //   'version': 'v1',
      //   'timestamp': timestamp,
      //   'signature': this.generateApiSignature({
      //     'merchant-id': this.config.merchantId,
      //     'version': 'v1',
      //     'timestamp': timestamp
      //   })
      // }
      
      // const response = await fetch(`${this.apiBaseUrl}/process/query/${transactionId}`, {
      //   method: 'GET',
      //   headers
      // })
      
      // if (response.ok) {
      //   const data = await response.json()
      //   if (data.data && data.data.response) {
      //     return this.mapPayFastStatus(data.data.response.status)
      //   }
      // }

      return PaymentStatus.PENDING
    } catch (error) {
      logger.error('Failed to get PayFast payment status', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return PaymentStatus.FAILED
    }
  }

  async refundPayment(transactionId: string, amount?: number): Promise<PaymentResponse> {
    try {
      logger.info('Processing PayFast refund', {
        gateway: this.name,
        transactionId,
        amount
      })

      // PayFast has a refund API, but it requires merchant API access
      // For a real implementation with API access:
      
      // const timestamp = new Date().toISOString()
      // const refundData = {
      //   amount: amount ? amount * 100 : undefined, // Convert to cents
      //   reason: 'Customer requested refund',
      //   notify_buyer: true,
      //   notify_merchant: true
      // }
      
      // const headers = {
      //   'merchant-id': this.config.merchantId,
      //   'version': 'v1',
      //   'timestamp': timestamp,
      //   'signature': this.generateApiSignature({
      //     ...refundData,
      //     'merchant-id': this.config.merchantId,
      //     'version': 'v1',
      //     'timestamp': timestamp
      //   }),
      //   'Content-Type': 'application/json'
      // }
      
      // const response = await fetch(`${this.apiBaseUrl}/refunds/${transactionId}`, {
      //   method: 'POST',
      //   headers,
      //   body: JSON.stringify(refundData)
      // })
      
      // if (response.ok) {
      //   const data = await response.json()
      //   if (data.data && data.data.response === true) {
      //     return {
      //       success: true,
      //       status: PaymentStatus.REFUNDED,
      //       message: 'Refund processed successfully',
      //       transactionId
      //     }
      //   }
      // }
      
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: 'Automated refunds not implemented. Please process through PayFast merchant portal.'
        }
      }
    } catch (error) {
      logger.error('PayFast refund failed', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.GATEWAY_ERROR,
          message: 'Failed to process refund',
          details: error instanceof Error ? error.message : undefined
        }
      }
    }
  }

  verifyWebhook(payload: any, signature: string): boolean {
    try {
      // PayFast uses a server-to-server validation process
      // 1. Verify that the request is coming from PayFast's IP range
      // 2. Validate the data by sending it back to PayFast
      // 3. Check the signature if passphrase is set
      
      // For this implementation, we'll focus on signature validation
      if (!this.config.passphrase) {
        // If no passphrase is set, we can't verify the signature
        return true
      }
      
      // Remove signature from payload for verification
      const { signature: _, ...dataToVerify } = payload
      
      // Generate expected signature
      const expectedSignature = this.generateSignature(dataToVerify)
      
      return expectedSignature === signature
    } catch (error) {
      logger.error('PayFast webhook verification failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return false
    }
  }

  async processWebhook(payload: WebhookPayload): Promise<void> {
    try {
      logger.info('Processing PayFast webhook', {
        gateway: this.name,
        event: payload.event,
        data: payload.data
      })

      // Extract payment data from payload
      const data = payload.data
      const paymentStatus = this.mapPayFastStatus(data.payment_status)
      const transactionId = data.m_payment_id || data.pf_payment_id || ''
      const orderId = data.custom_str1 || data.custom_str2 || ''
      
      // Determine event type based on status
      let event: WebhookEvent
      switch (paymentStatus) {
        case PaymentStatus.COMPLETED:
          event = WebhookEvent.PAYMENT_COMPLETED
          break
        case PaymentStatus.FAILED:
          event = WebhookEvent.PAYMENT_FAILED
          break
        case PaymentStatus.CANCELLED:
          event = WebhookEvent.PAYMENT_CANCELLED
          break
        case PaymentStatus.REFUNDED:
          event = WebhookEvent.PAYMENT_REFUNDED
          break
        default:
          event = WebhookEvent.PAYMENT_PENDING
      }

      // Here you would typically:
      // 1. Update payment status in database
      // 2. Send notifications
      // 3. Update order status
      // 4. Trigger business logic

      logger.info('PayFast webhook processed', {
        gateway: this.name,
        event,
        status: paymentStatus,
        transactionId,
        orderId
      })

    } catch (error) {
      logger.error('PayFast webhook processing failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      throw error
    }
  }

  private createPaymentData(request: PaymentRequest): Record<string, string> {
    // Generate a unique payment ID if not provided
    const paymentId = request.reference || `pf_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    
    return {
      // Merchant details
      merchant_id: this.config.merchantId,
      merchant_key: this.config.merchantKey,
      
      // Payment details
      amount: request.amount.amount.toFixed(2),
      item_name: request.description?.substring(0, 100) || `Order ${request.reference}`,
      item_description: request.items?.length > 0 
        ? request.items.map(item => `${item.name} x${item.quantity}`).join(', ').substring(0, 255)
        : '',
      
      // Customer details
      name_first: request.customer.firstName.substring(0, 100),
      name_last: request.customer.lastName?.substring(0, 100) || 'Customer',
      email_address: request.customer.email,
      cell_number: request.customer.phone || '',
      
      // URLs
      return_url: request.returnUrl,
      cancel_url: request.cancelUrl,
      notify_url: request.notifyUrl,
      
      // Custom fields
      custom_str1: request.reference,
      custom_str2: request.metadata.orderId,
      custom_str3: request.metadata.customerId || '',
      custom_str4: request.metadata.source || 'payment-core',
      custom_str5: new Date().toISOString(),
      
      // Payment reference
      m_payment_id: paymentId,
    }
  }

  private generateSignature(data: Record<string, string>): string {
    // Create parameter string
    const paramString = Object.keys(data)
      .sort()
      .map(key => `${key}=${encodeURIComponent(data[key]).replace(/%20/g, '+')}`)
      .join('&')

    // Add passphrase if set
    const stringToHash = this.config.passphrase
      ? `${paramString}&passphrase=${encodeURIComponent(this.config.passphrase)}`
      : paramString

    // Generate MD5 hash
    return generateHash(stringToHash, 'md5')
  }

  private generateApiSignature(data: Record<string, any>): string {
    // Sort all the submitted variables alphabetically
    const sortedData = { ...data }
    if (this.config.passphrase) {
      sortedData.passphrase = this.config.passphrase
    }
    
    // Create parameter string
    const keys = Object.keys(sortedData).sort()
    const paramString = keys
      .map(key => `${key}=${encodeURIComponent(sortedData[key])}`)
      .join('&')
    
    // Generate MD5 hash
    return crypto.createHash('md5').update(paramString).digest('hex')
  }

  private mapPayFastStatus(status: string): PaymentStatus {
    switch (status?.toUpperCase()) {
      case 'COMPLETE':
        return PaymentStatus.COMPLETED
      case 'PENDING':
        return PaymentStatus.PENDING
      case 'FAILED':
        return PaymentStatus.FAILED
      case 'CANCELLED':
        return PaymentStatus.CANCELLED
      case 'REFUNDED':
        return PaymentStatus.REFUNDED
      default:
        return PaymentStatus.PENDING
    }
  }
}

export default PayFastGateway