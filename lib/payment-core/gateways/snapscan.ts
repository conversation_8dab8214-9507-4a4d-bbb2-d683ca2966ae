/**
 * SnapScan Payment Gateway Implementation
 * 
 * Handles integration with the SnapScan payment gateway.
 * Documentation: https://developer.getsnapscan.com
 */

import crypto from 'crypto'
import { 
  IPaymentGateway, 
  PaymentGateway, 
  PaymentMethod, 
  PaymentRequest, 
  PaymentResponse, 
  PaymentStatus,
  PaymentValidationResult,
  WebhookPayload,
  WebhookEvent,
  SnapScanConfig,
  PaymentErrorCode
} from '../types'
import { paymentConfig, getGatewayUrl } from '../config'
import { logger } from '../logger'

export class SnapScanGateway implements IPaymentGateway {
  name = PaymentGateway.SNAPSCAN
  displayName = 'SnapScan'
  supportedMethods = [PaymentMethod.QR_CODE]
  supportedCurrencies = ['ZAR']

  private config: SnapScanConfig
  private apiBaseUrl = 'https://api.getsnapscan.com/v1'

  constructor() {
    this.config = paymentConfig.snapscan as SnapScanConfig
    
    // Set sandbox API URL if in sandbox mode
    if (this.config.sandbox) {
      this.apiBaseUrl = 'https://api.getsnapscan.com/v1/sandbox'
    }
    
    if (!this.validateConfig()) {
      logger.warn('SnapScan configuration is incomplete', { gateway: this.name })
    }
  }

  validateConfig(): boolean {
    return !!(
      this.config?.apiKey &&
      this.config?.merchantId
    )
  }

  validatePaymentRequest(request: PaymentRequest): PaymentValidationResult {
    const errors: string[] = []

    // Amount validation
    if (!request.amount || request.amount.amount <= 0) {
      errors.push('Invalid amount')
    }

    if (request.amount.currency !== 'ZAR') {
      errors.push('SnapScan only supports ZAR currency')
    }

    // Reference validation
    if (!request.reference) {
      errors.push('Payment reference is required')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      logger.info('Creating SnapScan payment', {
        gateway: this.name,
        reference: request.reference,
        amount: request.amount.amount
      })

      // Validate request
      const validation = this.validatePaymentRequest(request)
      if (!validation.isValid) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: {
            code: PaymentErrorCode.VALIDATION_ERROR,
            message: validation.errors.join(', ')
          }
        }
      }

      // SnapScan API integration
      // For a real implementation with API access:
      
      // const paymentData = {
      //   merchantReference: request.reference,
      //   amount: request.amount.amount,
      //   currency: request.amount.currency,
      //   description: request.description || `Order ${request.reference}`,
      //   callbackUrl: request.notifyUrl,
      //   redirectUrl: request.returnUrl,
      //   metadata: {
      //     orderId: request.metadata.orderId,
      //     customerId: request.metadata.customerId,
      //     source: request.metadata.source || 'payment-core'
      //   }
      // }
      
      // const response = await fetch(`${this.apiBaseUrl}/merchants/${this.config.merchantId}/payments`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Authorization': `Bearer ${this.config.apiKey}`
      //   },
      //   body: JSON.stringify(paymentData)
      // })
      
      // if (response.ok) {
      //   const data = await response.json()
      //   return {
      //     success: true,
      //     transactionId: data.id,
      //     reference: request.reference,
      //     paymentUrl: data.paymentUrl,
      //     qrCode: data.qrCode,
      //     status: PaymentStatus.PENDING,
      //     message: 'Scan the QR code with the SnapScan app to complete payment'
      //   }
      // } else {
      //   const errorData = await response.json()
      //   throw new Error(errorData.message || 'Failed to create SnapScan payment')
      // }
      
      // For now, we'll create a simulated QR code URL
      const snapscanUrl = this.config.sandbox 
        ? 'https://pos.snapscan.io/qr/test/'
        : 'https://pos.snapscan.io/qr/'
      
      const qrCodeUrl = `${snapscanUrl}${this.config.merchantId}?id=${request.reference}&amount=${request.amount.amount.toFixed(2)}`
      
      logger.info('SnapScan payment created', {
        gateway: this.name,
        reference: request.reference,
        qrCodeUrl
      })

      return {
        success: true,
        transactionId: request.reference,
        reference: request.reference,
        paymentUrl: qrCodeUrl,
        qrCode: qrCodeUrl,
        status: PaymentStatus.PENDING,
        message: 'Scan the QR code with the SnapScan app to complete payment'
      }

    } catch (error) {
      logger.error('SnapScan payment creation failed', {
        gateway: this.name,
        reference: request.reference,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.GATEWAY_ERROR,
          message: 'Failed to create payment',
          details: error instanceof Error ? error.message : undefined
        }
      }
    }
  }

  async getPaymentStatus(transactionId: string): Promise<PaymentStatus> {
    try {
      logger.info('Getting SnapScan payment status', {
        gateway: this.name,
        transactionId
      })

      // SnapScan API integration
      // For a real implementation with API access:
      
      // const response = await fetch(`${this.apiBaseUrl}/payments/${transactionId}`, {
      //   method: 'GET',
      //   headers: {
      //     'Authorization': `Bearer ${this.config.apiKey}`
      //   }
      // })
      
      // if (response.ok) {
      //   const data = await response.json()
      //   return this.mapSnapScanStatus(data.status)
      // }
      
      return PaymentStatus.PENDING
    } catch (error) {
      logger.error('Failed to get SnapScan payment status', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return PaymentStatus.FAILED
    }
  }

  async refundPayment(transactionId: string, amount?: number): Promise<PaymentResponse> {
    try {
      logger.info('Processing SnapScan refund', {
        gateway: this.name,
        transactionId,
        amount
      })

      // SnapScan API integration
      // For a real implementation with API access:
      
      // const refundData = {
      //   amount: amount || undefined, // If not provided, full refund
      //   reason: 'Customer requested refund'
      // }
      
      // const response = await fetch(`${this.apiBaseUrl}/payments/${transactionId}/refunds`, {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Authorization': `Bearer ${this.config.apiKey}`
      //   },
      //   body: JSON.stringify(refundData)
      // })
      
      // if (response.ok) {
      //   const data = await response.json()
      //   return {
      //     success: true,
      //     transactionId: data.id,
      //     reference: transactionId,
      //     status: PaymentStatus.REFUNDED,
      //     message: 'Refund processed successfully'
      //   }
      // } else {
      //   const errorData = await response.json()
      //   throw new Error(errorData.message || 'Failed to process refund')
      // }
      
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: 'Automated refunds not implemented for SnapScan. Please contact support.'
        }
      }
    } catch (error) {
      logger.error('SnapScan refund failed', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.GATEWAY_ERROR,
          message: 'Failed to process refund',
          details: error instanceof Error ? error.message : undefined
        }
      }
    }
  }

  verifyWebhook(payload: any, signature: string): boolean {
    try {
      // SnapScan uses HMAC-SHA256 for webhook verification
      if (!signature || !this.config.apiKey) {
        return false
      }
      
      // Convert payload to string
      const payloadString = typeof payload === 'string' 
        ? payload 
        : JSON.stringify(payload)
      
      // Generate HMAC signature
      const hmac = crypto.createHmac('sha256', this.config.apiKey)
      const calculatedSignature = hmac.update(payloadString).digest('hex')
      
      return signature === calculatedSignature
    } catch (error) {
      logger.error('SnapScan webhook verification failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return false
    }
  }

  async processWebhook(payload: WebhookPayload): Promise<void> {
    try {
      logger.info('Processing SnapScan webhook', {
        gateway: this.name,
        event: payload.event,
        data: payload.data
      })

      // Extract payment data from payload
      const data = payload.data
      const status = this.mapSnapScanStatus(data.status)
      const transactionId = data.id || data.paymentId || ''
      const merchantReference = data.merchantReference || ''
      
      // Determine event type based on status
      let event: WebhookEvent
      switch (status) {
        case PaymentStatus.COMPLETED:
          event = WebhookEvent.PAYMENT_COMPLETED
          break
        case PaymentStatus.FAILED:
          event = WebhookEvent.PAYMENT_FAILED
          break
        case PaymentStatus.CANCELLED:
          event = WebhookEvent.PAYMENT_CANCELLED
          break
        case PaymentStatus.REFUNDED:
          event = WebhookEvent.PAYMENT_REFUNDED
          break
        default:
          event = WebhookEvent.PAYMENT_PENDING
      }

      // Here you would typically:
      // 1. Update payment status in database
      // 2. Send notifications
      // 3. Update order status
      // 4. Trigger business logic

      logger.info('SnapScan webhook processed', {
        gateway: this.name,
        event,
        status,
        transactionId,
        merchantReference
      })

    } catch (error) {
      logger.error('SnapScan webhook processing failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      throw error
    }
  }

  private mapSnapScanStatus(status: string): PaymentStatus {
    switch (status?.toLowerCase()) {
      case 'completed':
      case 'success':
      case 'successful':
        return PaymentStatus.COMPLETED
      case 'pending':
      case 'waiting':
        return PaymentStatus.PENDING
      case 'processing':
        return PaymentStatus.PROCESSING
      case 'failed':
      case 'failure':
      case 'error':
        return PaymentStatus.FAILED
      case 'cancelled':
      case 'canceled':
        return PaymentStatus.CANCELLED
      case 'refunded':
        return PaymentStatus.REFUNDED
      case 'expired':
      case 'timeout':
        return PaymentStatus.EXPIRED
      default:
        return PaymentStatus.PENDING
    }
  }
}

export default SnapScanGateway