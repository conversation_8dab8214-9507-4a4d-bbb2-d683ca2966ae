/**
 * Payment Core Library Configuration
 * 
 * Central configuration for payment gateways and settings.
 */

import type { 
  PaymentGatewayConfig, 
  SecurityConfig, 
  ComplianceData, 
  RateLimitConfig,
  PaymentMethodConfig
} from './types'
import { PaymentGateway, PaymentMethod } from './types'
import { logger } from './logger'

/**
 * Get environment variable with fallback
 */
function getEnv(key: string, fallback: string = ''): string {
  return process.env[key] || fallback;
}

/**
 * Check if environment variable is defined
 */
function hasEnv(key: string): boolean {
  return !!process.env[key];
}

/**
 * Get boolean environment variable
 */
function getBoolEnv(key: string, fallback: boolean = false): boolean {
  const value = process.env[key];
  if (value === undefined) return fallback;
  return value === 'true' || value === '1' || value === 'yes';
}

/**
 * Get number environment variable
 */
function getNumEnv(key: string, fallback: number): number {
  const value = process.env[key];
  if (value === undefined) return fallback;
  const num = Number(value);
  return isNaN(num) ? fallback : num;
}

// Environment validation
const requiredEnvVars = [
  // Required for production only
  ...(process.env.NODE_ENV === 'production' ? [
    'PAYFAST_MERCHANT_ID',
    'PAYFAST_MERCHANT_KEY',
    'PAYFAST_PASSPHRASE',
  ] : [])
] as const

// Payment gateway configurations
export const paymentConfig: PaymentGatewayConfig = {
  payfast: {
    merchantId: getEnv('PAYFAST_MERCHANT_ID'),
    merchantKey: getEnv('PAYFAST_MERCHANT_KEY'),
    passphrase: getEnv('PAYFAST_PASSPHRASE'),
    sandbox: getBoolEnv('PAYFAST_SANDBOX', process.env.NODE_ENV !== 'production'),
    enabled: hasEnv('PAYFAST_MERCHANT_ID') || process.env.NODE_ENV !== 'production',
  },
  ozow: {
    apiKey: getEnv('OZOW_API_KEY'),
    privateKey: getEnv('OZOW_PRIVATE_KEY'),
    siteCode: getEnv('OZOW_SITE_CODE'),
    sandbox: getBoolEnv('OZOW_SANDBOX', process.env.NODE_ENV !== 'production'),
    enabled: hasEnv('OZOW_API_KEY') && hasEnv('OZOW_PRIVATE_KEY'),
  },
  snapscan: {
    apiKey: getEnv('SNAPSCAN_API_KEY'),
    merchantId: getEnv('SNAPSCAN_MERCHANT_ID'),
    sandbox: getBoolEnv('SNAPSCAN_SANDBOX', process.env.NODE_ENV !== 'production'),
    enabled: hasEnv('SNAPSCAN_API_KEY') && hasEnv('SNAPSCAN_MERCHANT_ID'),
  },
  yoco: {
    secretKey: getEnv('YOCO_SECRET_KEY'),
    publicKey: getEnv('YOCO_PUBLIC_KEY'),
    sandbox: getBoolEnv('YOCO_SANDBOX', process.env.NODE_ENV !== 'production'),
    enabled: hasEnv('YOCO_SECRET_KEY') && hasEnv('YOCO_PUBLIC_KEY'),
  },
  payu: {
    apiKey: getEnv('PAYU_API_KEY'),
    safeKey: getEnv('PAYU_SAFE_KEY'),
    merchantId: getEnv('PAYU_MERCHANT_ID'),
    sandbox: getBoolEnv('PAYU_SANDBOX', process.env.NODE_ENV !== 'production'),
    enabled: hasEnv('PAYU_API_KEY') && hasEnv('PAYU_SAFE_KEY'),
  },
}

// Security configuration
export const securityConfig: SecurityConfig = {
  encryptionKey: getEnv('PAYMENT_ENCRYPTION_KEY', process.env.NODE_ENV === 'production' 
    ? '' // Force error in production if not set
    : 'default-key-change-in-production'),
  hashAlgorithm: getEnv('PAYMENT_HASH_ALGORITHM', 'sha256'),
  tokenExpiry: getNumEnv('PAYMENT_TOKEN_EXPIRY', 3600), // 1 hour
  maxRetries: getNumEnv('PAYMENT_MAX_RETRIES', 3),
  rateLimitWindow: getNumEnv('PAYMENT_RATE_LIMIT_WINDOW', 15 * 60 * 1000), // 15 minutes
  rateLimitMax: getNumEnv('PAYMENT_RATE_LIMIT_MAX', 100),
  webhookSecret: getEnv('PAYMENT_WEBHOOK_SECRET', process.env.NODE_ENV === 'production'
    ? '' // Force error in production if not set
    : 'default-webhook-secret-change-in-production'),
}

// Compliance configuration for South Africa
export const complianceConfig: ComplianceData = {
  pciCompliant: true,
  popiaCompliant: true,
  vatNumber: process.env.SA_VAT_NUMBER || '',
  businessRegistration: process.env.SA_BUSINESS_REGISTRATION || '',
  auditTrail: true,
}

// Rate limiting configuration
export const rateLimitConfig: RateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many payment requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
}

// Gateway URLs
export const GATEWAY_URLS = {
  payfast: {
    sandbox: 'https://sandbox.payfast.co.za/eng/process',
    production: 'https://www.payfast.co.za/eng/process',
    api: {
      sandbox: 'https://api.payfast.co.za',
      production: 'https://api.payfast.co.za',
    },
  },
  ozow: {
    sandbox: 'https://pay.ozow.com',
    production: 'https://pay.ozow.com',
    api: {
      sandbox: 'https://api.ozow.com',
      production: 'https://api.ozow.com',
    },
  },
  snapscan: {
    sandbox: 'https://pos.snapscan.io/qr/PAY-',
    production: 'https://pos.snapscan.io/qr/PAY-',
    api: {
      sandbox: 'https://api.snapscan.io/v1',
      production: 'https://api.snapscan.io/v1',
    }
  },
  yoco: {
    sandbox: 'https://checkout.yoco.com/pay/',
    production: 'https://checkout.yoco.com/pay/',
    api: {
      sandbox: 'https://online.yoco.com/v1',
      production: 'https://online.yoco.com/v1',
    }
  },
  payu: {
    sandbox: 'https://staging.payu.co.za/api',
    production: 'https://secure.payu.co.za/api',
  },
  zapper: {
    sandbox: 'https://sandbox.zapper.com/pay',
    production: 'https://www.zapper.com/pay',
    api: {
      sandbox: 'https://api.sandbox.zapper.com',
      production: 'https://api.zapper.com',
    }
  },
} as const

// Supported currencies (South African focus)
export const SUPPORTED_CURRENCIES = ['ZAR', 'USD', 'EUR', 'GBP'] as const

// Payment method configurations
export const PAYMENT_METHOD_CONFIG: Record<PaymentMethod, PaymentMethodConfig> = {
  [PaymentMethod.CARD]: {
    displayName: 'Credit/Debit Card',
    description: 'Pay securely with your credit or debit card',
    icon: 'credit-card',
    supportedGateways: [PaymentGateway.PAYFAST, PaymentGateway.YOCO, PaymentGateway.PAYU],
    processingTime: '1-2 minutes',
    fees: {
      percentage: 2.9,
      fixed: 0,
    },
  },
  [PaymentMethod.EFT]: {
    displayName: 'EFT/Bank Transfer',
    description: 'Electronic Funds Transfer from your bank account',
    icon: 'bank',
    supportedGateways: [PaymentGateway.PAYFAST, PaymentGateway.OZOW],
    processingTime: '1-3 business days',
    fees: {
      percentage: 1.5,
      fixed: 0,
    },
  },
  [PaymentMethod.INSTANT_EFT]: {
    displayName: 'Instant EFT',
    description: 'Instant payment directly from your bank account',
    icon: 'zap',
    supportedGateways: [PaymentGateway.OZOW],
    processingTime: 'Instant',
    fees: {
      percentage: 1.5,
      fixed: 0,
    },
  },
  [PaymentMethod.QR_CODE]: {
    displayName: 'QR Code Payment',
    description: 'Scan QR code with your banking app',
    icon: 'qr-code',
    supportedGateways: [PaymentGateway.SNAPSCAN, PaymentGateway.ZAPPER],
    processingTime: 'Instant',
    fees: {
      percentage: 2.5,
      fixed: 0,
    },
  },
  [PaymentMethod.BANK_TRANSFER]: {
    displayName: 'Bank Transfer',
    description: 'Direct bank-to-bank transfer',
    icon: 'bank',
    supportedGateways: [PaymentGateway.PAYFAST, PaymentGateway.OZOW],
    processingTime: '1-3 business days',
    fees: {
      percentage: 1.0,
      fixed: 0,
    },
  },
  [PaymentMethod.MOBILE_MONEY]: {
    displayName: 'Mobile Money',
    description: 'Pay using mobile money services',
    icon: 'smartphone',
    supportedGateways: [PaymentGateway.ZAPPER],
    processingTime: 'Instant',
    fees: {
      percentage: 2.0,
      fixed: 0,
    },
  },
  [PaymentMethod.CRYPTO]: {
    displayName: 'Cryptocurrency',
    description: 'Pay with Bitcoin or other cryptocurrencies',
    icon: 'bitcoin',
    supportedGateways: [],
    processingTime: '10-60 minutes',
    fees: {
      percentage: 1.0,
      fixed: 0,
    },
  },
  
}

// Gateway priority order (for fallback)
export const GATEWAY_PRIORITY = [
  PaymentGateway.PAYFAST,
  PaymentGateway.OZOW,
  PaymentGateway.YOCO,
  PaymentGateway.SNAPSCAN,
  PaymentGateway.PAYU,
  PaymentGateway.ZAPPER,
] as const

// Minimum and maximum transaction amounts (in ZAR)
export const TRANSACTION_LIMITS = {
  min: 1.00,
  max: 50000.00,
  dailyLimit: 100000.00,
  monthlyLimit: 500000.00,
} as const

// Webhook endpoints
export const WEBHOOK_ENDPOINTS = {
  payfast: '/api/webhooks/payments/payfast',
  ozow: '/api/webhooks/payments/ozow',
  snapscan: '/api/webhooks/payments/snapscan',
  yoco: '/api/webhooks/payments/yoco',
  payu: '/api/webhooks/payments/payu',
  zapper: '/api/webhooks/payments/zapper',
} as const

// Error messages
export const PAYMENT_ERROR_MESSAGES = {
  INVALID_AMOUNT: 'Invalid payment amount',
  INVALID_CURRENCY: 'Currency not supported',
  INVALID_CUSTOMER: 'Invalid customer information',
  GATEWAY_ERROR: 'Payment gateway error',
  NETWORK_ERROR: 'Network connection error',
  AUTHENTICATION_ERROR: 'Authentication failed',
  INSUFFICIENT_FUNDS: 'Insufficient funds',
  CARD_DECLINED: 'Card declined by bank',
  EXPIRED_CARD: 'Card has expired',
  INVALID_CARD: 'Invalid card details',
  FRAUD_DETECTED: 'Transaction flagged as potentially fraudulent',
  RATE_LIMITED: 'Too many requests, please try again later',
  MAINTENANCE: 'Payment system under maintenance',
  UNKNOWN_ERROR: 'An unexpected error occurred',
  CONFIGURATION_ERROR: 'Payment gateway configuration error',
  VALIDATION_ERROR: 'Payment validation failed',
  GATEWAY_UNAVAILABLE: 'Payment gateway is currently unavailable',
} as const

// Success messages
export const PAYMENT_SUCCESS_MESSAGES = {
  PAYMENT_INITIATED: 'Payment initiated successfully',
  PAYMENT_COMPLETED: 'Payment completed successfully',
  REFUND_INITIATED: 'Refund initiated successfully',
  REFUND_COMPLETED: 'Refund completed successfully',
} as const

// Timeout configurations (in milliseconds)
export const TIMEOUT_CONFIG = {
  paymentRequest: 30000, // 30 seconds
  webhookResponse: 5000, // 5 seconds
  statusCheck: 10000, // 10 seconds
  refundRequest: 30000, // 30 seconds
} as const

// Retry configurations
export const RETRY_CONFIG = {
  maxAttempts: 3,
  backoffMultiplier: 2,
  initialDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
} as const

// Logging configuration
export const LOGGING_CONFIG = {
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: 'json',
  maxFiles: 10,
  maxSize: '10m',
  datePattern: 'YYYY-MM-DD',
} as const

// South African banking details for EFT
export const SA_BANKING_CONFIG = {
  supportedBanks: [
    'ABSA',
    'Standard Bank',
    'FNB',
    'Nedbank',
    'Capitec',
    'African Bank',
    'Bidvest Bank',
    'Discovery Bank',
    'TymeBank',
    'Bank Zero',
  ],
  clearingCodes: {
    'ABSA': '632005',
    'Standard Bank': '051001',
    'FNB': '250655',
    'Nedbank': '198765',
    'Capitec': '470010',
  },
} as const

// VAT configuration for South Africa
export const VAT_CONFIG = {
  rate: 0.15, // 15% VAT
  inclusive: true, // Prices include VAT
  number: process.env.SA_VAT_NUMBER || '',
  registration: process.env.SA_VAT_REGISTRATION || '',
} as const

// Validation functions
export function validateEnvironmentVariables(): { valid: boolean, missing: string[] } {
  const missingVars = requiredEnvVars.filter(
    (varName) => !process.env[varName]
  )

  if (missingVars.length > 0) {
    logger.warn(`Missing payment environment variables: ${missingVars.join(', ')}`)
    return { valid: false, missing: missingVars as unknown as string[] }
  }

  return { valid: true, missing: [] }
}

export function getGatewayUrl(gateway: PaymentGateway, endpoint?: string): string {
  const config = GATEWAY_URLS[gateway]
  const isSandbox = process.env.NODE_ENV !== 'production'
  
  if (endpoint && 'api' in config) {
    return `${config.api[isSandbox ? 'sandbox' : 'production']}${endpoint}`
  }
  
  return isSandbox ? config.sandbox : config.production
}

export function isGatewayEnabled(gateway: PaymentGateway): boolean {
  const config = paymentConfig[gateway.toLowerCase() as keyof typeof paymentConfig]
  if (!config) return false
  
  // Check if required configuration is present
  switch (gateway) {
    case PaymentGateway.PAYFAST:
      const payfastConfig = config as any
      return !!(payfastConfig.merchantId && payfastConfig.merchantKey && payfastConfig.passphrase)
    case PaymentGateway.OZOW:
      const ozowConfig = config as any
      return !!(ozowConfig.apiKey && ozowConfig.privateKey && ozowConfig.siteCode)
    case PaymentGateway.SNAPSCAN:
      const snapscanConfig = config as any
      return !!(snapscanConfig.apiKey && snapscanConfig.merchantId)
    case PaymentGateway.YOCO:
      const yocoConfig = config as any
      return !!(yocoConfig.secretKey && yocoConfig.publicKey)
    case PaymentGateway.PAYU:
      const payuConfig = config as any
      return !!(payuConfig.apiKey && payuConfig.safeKey && payuConfig.merchantId)
    case PaymentGateway.ZAPPER:
      const zapperConfig = config as any
      return !!(zapperConfig?.merchantId && zapperConfig?.apiKey)
    default:
      return false
  }
}

export function getEnabledGateways(): PaymentGateway[] {
  return GATEWAY_PRIORITY.filter(isGatewayEnabled)
}

export function calculateTransactionFee(
  amount: number, 
  method: PaymentMethod
): number {
  const config = PAYMENT_METHOD_CONFIG[method]
  if (!config) return 0
  
  const percentageFee = (amount * config.fees.percentage) / 100
  return percentageFee + config.fees.fixed
}

export function getPaymentMethodsForGateway(gateway: PaymentGateway): PaymentMethod[] {
  return Object.entries(PAYMENT_METHOD_CONFIG)
    .filter(([_, config]) => config.supportedGateways.includes(gateway))
    .map(([method]) => method as PaymentMethod)
}

export function getGatewaysForPaymentMethod(method: PaymentMethod): PaymentGateway[] {
  const config = PAYMENT_METHOD_CONFIG[method]
  if (!config) return []
  
  // Filter by enabled gateways
  return config.supportedGateways.filter(isGatewayEnabled)
}

export function getPaymentMethodConfig(method: PaymentMethod): PaymentMethodConfig | null {
  return PAYMENT_METHOD_CONFIG[method] || null
}

export function getDefaultGateway(): PaymentGateway | null {
  const enabled = getEnabledGateways()
  return enabled.length > 0 ? enabled[0] : null
}

export default {
  paymentConfig,
  securityConfig,
  complianceConfig,
  GATEWAY_URLS,
  SUPPORTED_CURRENCIES,
  PAYMENT_METHOD_CONFIG,
  GATEWAY_PRIORITY,
  getGatewayUrl,
  isGatewayEnabled,
  getEnabledGateways,
  calculateTransactionFee
}