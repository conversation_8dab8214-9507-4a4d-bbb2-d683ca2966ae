/**
 * PayFast Utility Functions
 * 
 * Utility functions for working with PayFast payments
 */

import crypto from 'crypto'
import { PaymentStatus } from '../types'

/**
 * Generate MD5 hash for PayFast signature
 */
export function generateMd5Hash(data: string): string {
  return crypto.createHash('md5').update(data).digest('hex')
}

/**
 * Create parameter string for PayFast signature
 */
export function createParamString(data: Record<string, string>, passphrase?: string): string {
  // Sort keys alphabetically
  const sortedKeys = Object.keys(data).sort()
  
  // Create parameter string
  const paramString = sortedKeys
    .map(key => `${key}=${encodeURIComponent(data[key]).replace(/%20/g, '+')}`)
    .join('&')
  
  // Add passphrase if provided
  if (passphrase) {
    return `${paramString}&passphrase=${encodeURIComponent(passphrase)}`
  }
  
  return paramString
}

/**
 * Generate signature for PayFast request
 */
export function generateSignature(data: Record<string, string>, passphrase?: string): string {
  const paramString = createParamString(data, passphrase)
  return generateMd5Hash(paramString)
}

/**
 * Validate PayFast ITN (Instant Transaction Notification)
 */
export async function validateItn(
  data: Record<string, string>,
  signature: string,
  passphrase?: string,
  validateServerIp: boolean = true
): Promise<boolean> {
  try {
    // 1. Verify signature if passphrase is provided
    if (passphrase) {
      const { signature: _, ...dataToVerify } = data
      const expectedSignature = generateSignature(dataToVerify, passphrase)
      
      if (expectedSignature !== signature) {
        console.error('PayFast ITN signature validation failed')
        return false
      }
    }
    
    // 2. Verify server IP (in production)
    if (validateServerIp) {
      // In a real implementation, you would check if the request IP is from PayFast
      // This is a simplified version that always returns true
    }
    
    // 3. Verify data with PayFast server (in production)
    // In a real implementation, you would send the data back to PayFast for verification
    // This is a simplified version that always returns true
    
    return true
  } catch (error) {
    console.error('PayFast ITN validation error:', error)
    return false
  }
}

/**
 * Map PayFast payment status to our payment status
 */
export function mapPayfastStatus(payfastStatus: string): PaymentStatus {
  switch (payfastStatus?.toLowerCase()) {
    case 'complete':
      return PaymentStatus.COMPLETED
    case 'cancelled':
      return PaymentStatus.CANCELLED
    case 'failed':
      return PaymentStatus.FAILED
    case 'pending':
      return PaymentStatus.PENDING
    case 'refunded':
      return PaymentStatus.REFUNDED
    default:
      return PaymentStatus.PENDING
  }
}

/**
 * Format amount for PayFast (2 decimal places)
 */
export function formatAmount(amount: number): string {
  return amount.toFixed(2)
}

/**
 * Validate PayFast merchant credentials
 */
export async function validateMerchantCredentials(
  merchantId: string,
  merchantKey: string,
  sandbox: boolean = false
): Promise<boolean> {
  try {
    // In a real implementation, you would validate the credentials with PayFast
    // This is a simplified version that checks if the credentials are not empty
    return !!merchantId && !!merchantKey
  } catch (error) {
    console.error('PayFast merchant validation error:', error)
    return false
  }
}

/**
 * Get PayFast API URL based on environment
 */
export function getPayfastApiUrl(sandbox: boolean = false): string {
  return sandbox 
    ? 'https://sandbox.payfast.co.za/eng/process' 
    : 'https://www.payfast.co.za/eng/process'
}

/**
 * Get PayFast API endpoint URL
 */
export function getPayfastApiEndpoint(endpoint: string, sandbox: boolean = false): string {
  const baseUrl = sandbox 
    ? 'https://api.sandbox.payfast.co.za' 
    : 'https://api.payfast.co.za'
  
  return `${baseUrl}/${endpoint}`
}

/**
 * Create PayFast payment data object
 */
export function createPayfastPaymentData(
  merchantId: string,
  merchantKey: string,
  amount: number,
  itemName: string,
  firstName: string,
  lastName: string,
  email: string,
  returnUrl: string,
  cancelUrl: string,
  notifyUrl: string,
  reference: string,
  options: {
    itemDescription?: string
    phone?: string
    customStr1?: string
    customStr2?: string
    customStr3?: string
    customStr4?: string
    customStr5?: string
  } = {}
): Record<string, string> {
  return {
    // Merchant details
    merchant_id: merchantId,
    merchant_key: merchantKey,
    
    // Payment details
    amount: formatAmount(amount),
    item_name: itemName.substring(0, 100),
    item_description: options.itemDescription?.substring(0, 255) || '',
    
    // Customer details
    name_first: firstName.substring(0, 100),
    name_last: lastName.substring(0, 100),
    email_address: email,
    cell_number: options.phone || '',
    
    // URLs
    return_url: returnUrl,
    cancel_url: cancelUrl,
    notify_url: notifyUrl,
    
    // Custom fields
    custom_str1: options.customStr1 || reference,
    custom_str2: options.customStr2 || '',
    custom_str3: options.customStr3 || '',
    custom_str4: options.customStr4 || '',
    custom_str5: options.customStr5 || '',
    
    // Payment reference
    m_payment_id: reference,
  }
}