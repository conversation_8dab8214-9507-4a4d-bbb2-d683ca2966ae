/**
 * Payment Core Initialization
 * 
 * This module provides a standardized way to initialize the payment core library.
 * It ensures that all required components are properly initialized and validates
 * the configuration.
 */

import { paymentService } from './service'
import { logger } from './logger'
import { paymentConfig, securityConfig } from './config'
import { PaymentGateway } from './types'

/**
 * Initialize the payment core library
 */
export async function initializePaymentCore(options: {
  validateConfig?: boolean;
  throwOnError?: boolean;
} = {}): Promise<boolean> {
  const { validateConfig = true, throwOnError = false } = options;
  
  try {
    logger.info('Initializing payment core library...');
    
    // Validate configuration if required
    if (validateConfig) {
      const configErrors = validateConfiguration();
      
      if (configErrors.length > 0) {
        logger.error('Payment core configuration validation failed', {
          errors: configErrors
        });
        
        if (throwOnError) {
          throw new Error(`Payment core configuration validation failed: ${configErrors.join(', ')}`);
        }
        
        return false;
      }
    }
    
    // Initialize the payment service
    await paymentService.init();
    
    logger.info('Payment core library initialized successfully');
    return true;
  } catch (error) {
    logger.error('Failed to initialize payment core library', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    
    if (throwOnError) {
      throw error;
    }
    
    return false;
  }
}

/**
 * Validate the payment core configuration
 */
function validateConfiguration(): string[] {
  const errors: string[] = [];
  
  // Check if we're in production
  const isProduction = process.env.NODE_ENV === 'production';
  
  // In production, we need at least one payment gateway configured
  if (isProduction) {
    const enabledGateways = Object.entries(paymentConfig)
      .filter(([_, config]) => config.enabled)
      .map(([key]) => key);
    
    if (enabledGateways.length === 0) {
      errors.push('No payment gateways are enabled. At least one gateway must be configured in production.');
    }
    
    // Check security configuration
    if (!securityConfig.encryptionKey) {
      errors.push('PAYMENT_ENCRYPTION_KEY is required in production.');
    }
    
    if (!securityConfig.webhookSecret) {
      errors.push('PAYMENT_WEBHOOK_SECRET is required in production.');
    }
    
    // Check PayFast configuration (our default gateway)
    if (enabledGateways.includes(PaymentGateway.PAYFAST)) {
      const payfastConfig = paymentConfig.payfast;
      
      if (!payfastConfig) {
        errors.push('PayFast configuration is missing.');
        return errors;
      }
      
      const { merchantId, merchantKey, passphrase } = payfastConfig;
      
      if (!merchantId) {
        errors.push('PAYFAST_MERCHANT_ID is required when PayFast is enabled.');
      }
      
      if (!merchantKey) {
        errors.push('PAYFAST_MERCHANT_KEY is required when PayFast is enabled.');
      }
      
      if (!passphrase) {
        errors.push('PAYFAST_PASSPHRASE is required when PayFast is enabled.');
      }
    }
  }
  
  return errors;
}

export default initializePaymentCore;