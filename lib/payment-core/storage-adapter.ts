/**
 * Payment Storage Adapter
 * 
 * Provides persistent storage for payment transactions.
 * This implementation uses Prisma, but can be replaced with any storage solution.
 * Includes a fallback in-memory adapter for environments where Prisma is not available.
 */

import type { 
  IStorageAdapter, 
  PaymentTransaction, 
  PaymentStatus,
  PaymentGateway,
  PaymentMethod
} from './types'
import { logger } from './logger'

// Try to import Prisma client, but don't fail if it's not available
let prisma: any;
try {
  const { PrismaClient } = require('@prisma/client');
  prisma = new PrismaClient();
} catch (error) {
  logger.warn('Prisma client not available, using in-memory storage adapter', {
    error: error instanceof Error ? error.message : 'Unknown error'
  });
  prisma = null;
}

/**
 * Prisma Storage Adapter
 * Implements the IStorageAdapter interface using Prisma ORM
 */
export class PrismaStorageAdapter implements IStorageAdapter {
  /**
   * Save a transaction to the database
   */
  async saveTransaction(transaction: PaymentTransaction): Promise<void> {
    try {
      await prisma.payment.create({
        data: {
          id: transaction.id,
          paymentNumber: transaction.reference,
          orderId: transaction.orderId,
          customerId: transaction.customerId,
          amount: transaction.amount.amount,
          currency: transaction.amount.currency,
          status: transaction.status,
          gatewayId: transaction.gateway,
          gatewayPaymentId: transaction.transactionId || transaction.id,
          paymentMethod: transaction.method,
          metadata: transaction.metadata || {},
          createdAt: transaction.createdAt,
          updatedAt: transaction.updatedAt
        }
      })
      
      logger.info('Transaction saved to database', {
        transactionId: transaction.id,
        orderId: transaction.orderId
      })
    } catch (error) {
      logger.error('Failed to save transaction to database', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId: transaction.id
      })
      throw error
    }
  }
  
  /**
   * Get a transaction by ID
   */
  async getTransaction(transactionId: string): Promise<PaymentTransaction | null> {
    try {
      const payment = await prisma.payment.findFirst({
        where: {
          OR: [
            { id: transactionId },
            { gatewayPaymentId: transactionId },
            { paymentNumber: transactionId }
          ]
        }
      })
      
      if (!payment) {
        return null
      }
      
      return {
        id: payment.id,
        transactionId: payment.gatewayPaymentId,
        orderId: payment.orderId || 'unknown',
        customerId: payment.customerId || undefined,
        gateway: payment.gatewayId as PaymentGateway,
        method: payment.paymentMethod as PaymentMethod,
        amount: {
          amount: payment.amount,
          currency: payment.currency
        },
        status: payment.status as PaymentStatus,
        reference: payment.paymentNumber,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
        metadata: payment.metadata as any
      }
    } catch (error) {
      logger.error('Failed to get transaction from database', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      })
      return null
    }
  }
  
  /**
   * Update transaction status
   */
  async updateTransactionStatus(
    transactionId: string, 
    status: PaymentStatus, 
    metadata?: any
  ): Promise<void> {
    try {
      await prisma.payment.updateMany({
        where: {
          OR: [
            { id: transactionId },
            { gatewayPaymentId: transactionId },
            { paymentNumber: transactionId }
          ]
        },
        data: {
          status,
          metadata: metadata ? { ...metadata } : undefined,
          updatedAt: new Date()
        }
      })
      
      logger.info('Transaction status updated in database', {
        transactionId,
        status
      })
    } catch (error) {
      logger.error('Failed to update transaction status in database', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      })
      throw error
    }
  }
  
  /**
   * Get transactions by order ID
   */
  async getTransactionsByOrderId(orderId: string): Promise<PaymentTransaction[]> {
    try {
      const payments = await prisma.payment.findMany({
        where: { orderId }
      })
      
      interface PaymentDbRecord {
        id: string;
        gatewayPaymentId: string;
        orderId?: string;
        customerId?: string;
        gatewayId: string;
        paymentMethod: string;
        amount: number;
        currency: string;
        status: string;
        paymentNumber: string;
        createdAt: Date;
        updatedAt: Date;
        metadata: any;
      }

      interface PaymentAmount {
        amount: number;
        currency: string;
      }

      interface PaymentTransactionMapped {
        id: string;
        transactionId: string;
        orderId: string;
        customerId?: string;
        gateway: PaymentGateway;
        method: PaymentMethod;
        amount: PaymentAmount;
        status: PaymentStatus;
        reference: string;
        createdAt: Date;
        updatedAt: Date;
        metadata: any;
      }

      return (payments as PaymentDbRecord[]).map((payment: PaymentDbRecord): PaymentTransactionMapped => ({
        id: payment.id,
        transactionId: payment.gatewayPaymentId,
        orderId: payment.orderId || 'unknown',
        customerId: payment.customerId || undefined,
        gateway: payment.gatewayId as PaymentGateway,
        method: payment.paymentMethod as PaymentMethod,
        amount: {
          amount: payment.amount,
          currency: payment.currency
        },
        status: payment.status as PaymentStatus,
        reference: payment.paymentNumber,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
        metadata: payment.metadata as any
      }))
    } catch (error) {
      logger.error('Failed to get transactions by order ID from database', {
        error: error instanceof Error ? error.message : 'Unknown error',
        orderId
      })
      return []
    }
  }
  
  /**
   * Get transactions by customer ID
   */
  async getTransactionsByCustomerId(customerId: string): Promise<PaymentTransaction[]> {
    try {
      const payments = await prisma.payment.findMany({
        where: { customerId }
      })
      
      interface PaymentDbRecord {
        id: string;
        gatewayPaymentId: string;
        orderId?: string;
        customerId?: string;
        gatewayId: string;
        paymentMethod: string;
        amount: number;
        currency: string;
        status: string;
        paymentNumber: string;
        createdAt: Date;
        updatedAt: Date;
        metadata: any;
      }

      interface PaymentAmount {
        amount: number;
        currency: string;
      }

      interface PaymentTransactionMapped {
        id: string;
        transactionId: string;
        orderId: string;
        customerId?: string;
        gateway: PaymentGateway;
        method: PaymentMethod;
        amount: PaymentAmount;
        status: PaymentStatus;
        reference: string;
        createdAt: Date;
        updatedAt: Date;
        metadata: any;
      }

      return (payments as PaymentDbRecord[]).map((payment: PaymentDbRecord): PaymentTransactionMapped => ({
        id: payment.id,
        transactionId: payment.gatewayPaymentId,
        orderId: payment.orderId || 'unknown',
        customerId: payment.customerId || undefined,
        gateway: payment.gatewayId as PaymentGateway,
        method: payment.paymentMethod as PaymentMethod,
        amount: {
          amount: payment.amount,
          currency: payment.currency
        },
        status: payment.status as PaymentStatus,
        reference: payment.paymentNumber,
        createdAt: payment.createdAt,
        updatedAt: payment.updatedAt,
        metadata: payment.metadata as any
      }))
    } catch (error) {
      logger.error('Failed to get transactions by customer ID from database', {
        error: error instanceof Error ? error.message : 'Unknown error',
        customerId
      })
      return []
    }
  }
}

/**
 * In-Memory Storage Adapter
 * Fallback implementation when Prisma is not available
 */
export class InMemoryStorageAdapter implements IStorageAdapter {
  private transactions: Map<string, PaymentTransaction> = new Map();
  
  /**
   * Save a transaction to memory
   */
  async saveTransaction(transaction: PaymentTransaction): Promise<void> {
    try {
      this.transactions.set(transaction.id, { ...transaction });
      
      // Also index by transactionId if different from id
      if (transaction.transactionId && transaction.transactionId !== transaction.id) {
        this.transactions.set(transaction.transactionId, { ...transaction });
      }
      
      // Also index by reference if different from id
      if (transaction.reference && transaction.reference !== transaction.id) {
        this.transactions.set(transaction.reference, { ...transaction });
      }
      
      logger.info('Transaction saved to in-memory storage', {
        transactionId: transaction.id,
        orderId: transaction.orderId
      });
    } catch (error) {
      logger.error('Failed to save transaction to in-memory storage', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId: transaction.id
      });
      throw error;
    }
  }
  
  /**
   * Get a transaction by ID
   */
  async getTransaction(transactionId: string): Promise<PaymentTransaction | null> {
    try {
      return this.transactions.get(transactionId) || null;
    } catch (error) {
      logger.error('Failed to get transaction from in-memory storage', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      });
      return null;
    }
  }
  
  /**
   * Update transaction status
   */
  async updateTransactionStatus(
    transactionId: string, 
    status: PaymentStatus, 
    metadata?: any
  ): Promise<void> {
    try {
      const transaction = this.transactions.get(transactionId);
      
      if (!transaction) {
        logger.warn('Transaction not found for status update', { transactionId });
        return;
      }
      
      // Update the transaction
      transaction.status = status;
      transaction.updatedAt = new Date();
      
      if (metadata) {
        transaction.metadata = {
          ...transaction.metadata,
          ...metadata
        };
      }
      
      // Update all references to this transaction
      if (transaction.transactionId && transaction.transactionId !== transaction.id) {
        this.transactions.set(transaction.transactionId, { ...transaction });
      }
      
      if (transaction.reference && transaction.reference !== transaction.id) {
        this.transactions.set(transaction.reference, { ...transaction });
      }
      
      logger.info('Transaction status updated in in-memory storage', {
        transactionId,
        status
      });
    } catch (error) {
      logger.error('Failed to update transaction status in in-memory storage', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      });
      throw error;
    }
  }
  
  /**
   * Get transactions by order ID
   */
  async getTransactionsByOrderId(orderId: string): Promise<PaymentTransaction[]> {
    try {
      const result: PaymentTransaction[] = [];
      const seen = new Set<string>();
      
      // Filter transactions by order ID
      for (const transaction of this.transactions.values()) {
        if (transaction.orderId === orderId && !seen.has(transaction.id)) {
          result.push({ ...transaction });
          seen.add(transaction.id);
        }
      }
      
      return result;
    } catch (error) {
      logger.error('Failed to get transactions by order ID from in-memory storage', {
        error: error instanceof Error ? error.message : 'Unknown error',
        orderId
      });
      return [];
    }
  }
  
  /**
   * Get transactions by customer ID
   */
  async getTransactionsByCustomerId(customerId: string): Promise<PaymentTransaction[]> {
    try {
      const result: PaymentTransaction[] = [];
      const seen = new Set<string>();
      
      // Filter transactions by customer ID
      for (const transaction of this.transactions.values()) {
        if (transaction.customerId === customerId && !seen.has(transaction.id)) {
          result.push({ ...transaction });
          seen.add(transaction.id);
        }
      }
      
      return result;
    } catch (error) {
      logger.error('Failed to get transactions by customer ID from in-memory storage', {
        error: error instanceof Error ? error.message : 'Unknown error',
        customerId
      });
      return [];
    }
  }
}

// Create and export the appropriate adapter based on environment
export const storageAdapter: IStorageAdapter = prisma 
  ? new PrismaStorageAdapter() 
  : new InMemoryStorageAdapter();

export default storageAdapter