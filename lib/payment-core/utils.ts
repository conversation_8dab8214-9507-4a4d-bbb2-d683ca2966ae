/**
 * Payment Core Library Utilities
 * 
 * Utility functions for payment processing.
 */

import crypto from 'crypto'
import { 
  PaymentRequest, 
  PaymentValidationResult, 
  PaymentErrorCode,
  PaymentError
} from './types'
import { TRANSACTION_LIMITS, VAT_CONFIG } from './config'
import { logger } from './logger'

/**
 * Validate a payment request
 */
export function validatePaymentRequest(request: PaymentRequest): PaymentValidationResult {
  const errors: string[] = []

  // Validate amount
  if (!request.amount || !request.amount.amount || request.amount.amount <= 0) {
    errors.push('Invalid payment amount')
  } else {
    // Check against transaction limits
    if (request.amount.amount < TRANSACTION_LIMITS.min) {
      errors.push(`Amount below minimum of ${TRANSACTION_LIMITS.min}`)
    }
    if (request.amount.amount > TRANSACTION_LIMITS.max) {
      errors.push(`Amount exceeds maximum of ${TRANSACTION_LIMITS.max}`)
    }
  }

  // Validate currency
  if (!request.amount?.currency) {
    errors.push('Currency is required')
  }

  // Validate customer
  if (!request.customer) {
    errors.push('Customer information is required')
  } else {
    if (!request.customer.email) {
      errors.push('Customer email is required')
    } else if (!isValidEmail(request.customer.email)) {
      errors.push('Invalid customer email format')
    }

    if (!request.customer.firstName) {
      errors.push('Customer first name is required')
    }

    if (!request.customer.lastName) {
      errors.push('Customer last name is required')
    }

    if (request.customer.phone && !isValidPhone(request.customer.phone)) {
      errors.push('Invalid phone number format')
    }
  }

  // Validate URLs
  if (!request.returnUrl) {
    errors.push('Return URL is required')
  } else if (!isValidUrl(request.returnUrl)) {
    errors.push('Invalid return URL format')
  }

  if (!request.cancelUrl) {
    errors.push('Cancel URL is required')
  } else if (!isValidUrl(request.cancelUrl)) {
    errors.push('Invalid cancel URL format')
  }

  if (!request.notifyUrl) {
    errors.push('Notification URL is required')
  } else if (!isValidUrl(request.notifyUrl)) {
    errors.push('Invalid notification URL format')
  }

  // Validate reference
  if (!request.reference) {
    errors.push('Payment reference is required')
  }

  // Validate description
  if (!request.description) {
    errors.push('Payment description is required')
  }

  // Validate items
  if (!request.items || request.items.length === 0) {
    errors.push('At least one item is required')
  } else {
    let totalItemsAmount = 0
    
    for (const item of request.items) {
      if (!item.id) {
        errors.push('Item ID is required')
      }
      
      if (!item.name) {
        errors.push('Item name is required')
      }
      
      if (item.quantity <= 0) {
        errors.push(`Invalid quantity for item ${item.name || item.id}`)
      }
      
      if (item.unitPrice < 0) {
        errors.push(`Invalid unit price for item ${item.name || item.id}`)
      }
      
      if (item.totalPrice < 0) {
        errors.push(`Invalid total price for item ${item.name || item.id}`)
      }
      
      // Check if total price matches quantity * unit price
      const calculatedTotal = item.quantity * item.unitPrice
      if (Math.abs(calculatedTotal - item.totalPrice) > 0.01) {
        errors.push(`Total price mismatch for item ${item.name || item.id}`)
      }
      
      totalItemsAmount += item.totalPrice
    }
    
    // Check if total items amount matches request amount
    if (Math.abs(totalItemsAmount - request.amount.amount) > 0.01) {
      errors.push('Total items amount does not match payment amount')
    }
  }

  // Validate metadata
  if (!request.metadata) {
    errors.push('Payment metadata is required')
  } else {
    if (!request.metadata.orderId) {
      errors.push('Order ID is required in metadata')
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Format amount with currency symbol
 */
export function formatAmount(amount: number, currency: string = 'ZAR'): string {
  try {
    const formatter = new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
    
    return formatter.format(amount)
  } catch (error) {
    logger.error('Error formatting amount', { amount, currency, error })
    return `${currency} ${amount.toFixed(2)}`
  }
}

/**
 * Calculate VAT amount
 */
export function calculateVAT(amount: number, inclusive: boolean = VAT_CONFIG.inclusive): {
  vatAmount: number
  totalWithVAT: number
  totalWithoutVAT: number
} {
  const vatRate = VAT_CONFIG.rate
  
  if (inclusive) {
    // Amount already includes VAT
    const totalWithVAT = amount
    const totalWithoutVAT = amount / (1 + vatRate)
    const vatAmount = totalWithVAT - totalWithoutVAT
    
    return {
      vatAmount,
      totalWithVAT,
      totalWithoutVAT
    }
  } else {
    // Amount excludes VAT
    const totalWithoutVAT = amount
    const vatAmount = amount * vatRate
    const totalWithVAT = amount + vatAmount
    
    return {
      vatAmount,
      totalWithVAT,
      totalWithoutVAT
    }
  }
}

/**
 * Generate a unique transaction ID
 */
export function generateTransactionId(prefix: string = 'TXN'): string {
  const timestamp = Date.now().toString()
  const random = Math.random().toString(36).substring(2, 10).toUpperCase()
  return `${prefix}_${timestamp}_${random}`
}

/**
 * Generate a payment reference
 */
export function generatePaymentReference(orderId: string): string {
  const timestamp = Date.now().toString().substring(8, 13)
  const random = Math.random().toString(36).substring(2, 5).toUpperCase()
  return `PAY_${orderId}_${timestamp}${random}`
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  return emailRegex.test(email)
}

/**
 * Validate phone number format
 */
export function isValidPhone(phone: string): boolean {
  // Basic validation for international phone numbers
  const phoneRegex = /^\+?[0-9]{10,15}$/
  return phoneRegex.test(phone.replace(/[\s-]/g, ''))
}

/**
 * Validate South African phone number
 */
export function isValidSouthAfricanPhone(phone: string): boolean {
  // South African phone numbers: +27 followed by 9 digits, or 0 followed by 9 digits
  const zaPhoneRegex = /^(\+27|0)[0-9]{9}$/
  return zaPhoneRegex.test(phone.replace(/[\s-]/g, ''))
}

/**
 * Validate URL format
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url)
    return true
  } catch (error) {
    return false
  }
}

/**
 * Generate a hash using the specified algorithm
 */
export function generateHash(data: string, algorithm: string = 'md5'): string {
  return crypto.createHash(algorithm).update(data).digest('hex')
}

/**
 * Generate a HMAC signature
 */
export function generateHmacSignature(data: string, secret: string, algorithm: string = 'sha256'): string {
  return crypto.createHmac(algorithm, secret).update(data).digest('hex')
}

/**
 * Retry an operation with exponential backoff
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  initialDelay: number = 1000,
  backoffFactor: number = 2
): Promise<T> {
  let lastError: Error | null = null
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error: any) {
      lastError = error
      
      // Check if error is retryable
      if (!isRetryableError(error)) {
        logger.warn('Non-retryable error encountered, aborting retry', { 
          error: error.message, 
          attempt 
        })
        throw error
      }
      
      if (attempt === maxRetries) {
        logger.error('Max retries reached', { 
          error: error.message, 
          maxRetries 
        })
        throw error
      }
      
      const delay = initialDelay * Math.pow(backoffFactor, attempt - 1)
      logger.debug(`Retrying operation after ${delay}ms`, { attempt, maxRetries })
      
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  throw lastError || new Error('Unknown error during retry')
}

/**
 * Check if an error is retryable
 */
export function isRetryableError(error: any): boolean {
  // Network errors are generally retryable
  if (error.code === 'ECONNRESET' || 
      error.code === 'ETIMEDOUT' || 
      error.code === 'ECONNREFUSED' ||
      error.code === 'ENOTFOUND' ||
      error.message?.includes('network') ||
      error.message?.includes('timeout')) {
    return true
  }
  
  // Check for rate limiting
  if (error.status === 429 || 
      error.statusCode === 429 ||
      error.message?.includes('rate limit') ||
      error.message?.includes('too many requests')) {
    return true
  }
  
  // Check for server errors (5xx)
  if (error.status >= 500 || error.statusCode >= 500) {
    return true
  }
  
  // Check for custom retryable flag
  if (error.retryable === true) {
    return true
  }
  
  // If it's our custom PaymentError, check the code
  if (
    typeof error === 'object' &&
    error !== null &&
    'code' in error &&
    'retryable' in error &&
    Object.values(PaymentErrorCode).includes(error.code)
  ) {
    return error.retryable === true || 
           error.code === PaymentErrorCode.NETWORK_ERROR ||
           error.code === PaymentErrorCode.GATEWAY_ERROR ||
           error.code === PaymentErrorCode.RATE_LIMITED
  }
  
  return false
}

/**
 * Create a PaymentError instance
 */
export function createPaymentError(
  message: string,
  code: PaymentErrorCode,
  retryable: boolean = false,
  details?: any
): PaymentError {
  return {
    code,
    message,
    details,
    retryable
  }
}

/**
 * Safely parse JSON
 */
export function safeJsonParse(data: string): any {
  try {
    return JSON.parse(data)
  } catch (error) {
    logger.warn('Failed to parse JSON', { data: data.substring(0, 100) })
    return null
  }
}

/**
 * Safely stringify JSON
 */
export function safeJsonStringify(data: any): string {
  try {
    return JSON.stringify(data)
  } catch (error) {
    logger.warn('Failed to stringify JSON', { error })
    return '{}'
  }
}

/**
 * Mask sensitive data for logging
 */
export function maskSensitiveData(data: Record<string, any>): Record<string, any> {
  const sensitiveFields = [
    'password', 'passphrase', 'secret', 'key', 'token', 'apiKey', 'privateKey',
    'cardNumber', 'cvv', 'cvc', 'expiryDate', 'pin'
  ]
  
  const masked = { ...data }
  
  for (const field of sensitiveFields) {
    if (field in masked && typeof masked[field] === 'string') {
      const value = masked[field] as string
      masked[field] = value.length > 4 
        ? `${value.substring(0, 2)}${'*'.repeat(value.length - 4)}${value.substring(value.length - 2)}`
        : '****'
    }
  }
  
  return masked
}

export default {
  validatePaymentRequest,
  formatAmount,
  calculateVAT,
  generateTransactionId,
  generatePaymentReference,
  isValidEmail,
  isValidPhone,
  isValidSouthAfricanPhone,
  isValidUrl,
  generateHash,
  generateHmacSignature,
  retryWithBackoff,
  isRetryableError,
  createPaymentError,
  safeJsonParse,
  safeJsonStringify,
  maskSensitiveData
}