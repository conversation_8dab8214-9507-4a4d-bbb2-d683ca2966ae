/**
 * PayFast Configuration
 * 
 * Configuration settings for PayFast payment gateway
 */

import { PayFastConfig, PaymentMethod } from '../types'

/**
 * Default PayFast configuration
 */
export const DEFAULT_PAYFAST_CONFIG: PayFastConfig = {
  merchantId: process.env.PAYFAST_MERCHANT_ID || '',
  merchantKey: process.env.PAYFAST_MERCHANT_KEY || '',
  passphrase: process.env.PAYFAST_PASSPHRASE || '',
  sandbox: process.env.PAYFAST_SANDBOX === 'true',
  enabled: process.env.PAYFAST_ENABLED === 'true'
}

/**
 * PayFast API URLs
 */
export const PAYFAST_URLS = {
  production: {
    process: 'https://www.payfast.co.za/eng/process',
    api: 'https://api.payfast.co.za'
  },
  sandbox: {
    process: 'https://sandbox.payfast.co.za/eng/process',
    api: 'https://api.sandbox.payfast.co.za'
  }
}

/**
 * Get PayFast process URL based on environment
 */
export function getPayfastProcessUrl(sandbox: boolean = false): string {
  return sandbox ? PAYFAST_URLS.sandbox.process : PAYFAST_URLS.production.process
}

/**
 * Get PayFast API URL based on environment
 */
export function getPayfastApiUrl(sandbox: boolean = false): string {
  return sandbox ? PAYFAST_URLS.sandbox.api : PAYFAST_URLS.production.api
}

/**
 * PayFast supported payment methods
 */
export const PAYFAST_SUPPORTED_METHODS = [
  PaymentMethod.CARD,
  PaymentMethod.EFT,
  PaymentMethod.BANK_TRANSFER,
  PaymentMethod.QR_CODE,
  PaymentMethod.MOBILE_MONEY
]

/**
 * PayFast supported currencies
 */
export const PAYFAST_SUPPORTED_CURRENCIES = ['ZAR']

/**
 * PayFast server IP ranges for webhook validation
 */
export const PAYFAST_SERVER_IPS = [
  '**************/28',
  '*************/27'
]

/**
 * Check if an IP address is in a CIDR range
 */
export function isIpInCidrRange(ip: string, cidr: string): boolean {
  // This is a simplified implementation
  // In a real application, you would use a proper IP CIDR check library
  return true
}

/**
 * Validate if an IP address is from PayFast
 */
export function isPayfastIp(ip: string): boolean {
  return PAYFAST_SERVER_IPS.some(cidr => isIpInCidrRange(ip, cidr))
}

/**
 * Get PayFast configuration
 */
export function getPayfastConfig(): PayFastConfig {
  return {
    merchantId: process.env.PAYFAST_MERCHANT_ID || '',
    merchantKey: process.env.PAYFAST_MERCHANT_KEY || '',
    passphrase: process.env.PAYFAST_PASSPHRASE || '',
    sandbox: process.env.PAYFAST_SANDBOX === 'true',
    enabled: process.env.PAYFAST_ENABLED === 'true'
  }
}