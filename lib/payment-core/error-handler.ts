/**
 * Payment Error Handler
 * 
 * Provides standardized error handling for the payment core library.
 */

import { logger } from './logger'
import { PaymentErrorCode, PaymentError } from './types'

/**
 * Create a standardized payment error
 */
export function createPaymentError(
  message: string,
  code: PaymentErrorCode = PaymentErrorCode.UNKNOWN_ERROR,
  retryable: boolean = false,
  details?: any
): PaymentError {
  return {
    code,
    message,
    retryable,
    details
  }
}

/**
 * Handle errors in a standardized way
 */
export function handleError(
  error: any,
  context: string,
  defaultMessage: string = 'An error occurred',
  defaultCode: PaymentErrorCode = PaymentErrorCode.UNKNOWN_ERROR,
  retryable: boolean = false
): PaymentError {
  // If it's already a PaymentError, just return it
  if (error && typeof error === 'object' && 'code' in error && 'message' in error) {
    return error as PaymentError
  }
  
  // Log the error
  logger.error(`${context}: ${defaultMessage}`, {
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
    context
  })
  
  // Extract error message
  const message = error instanceof Error 
    ? error.message 
    : (typeof error === 'string' ? error : defaultMessage)
  
  // Determine error code
  let code = defaultCode
  
  // Check for specific error types
  if (error instanceof Error) {
    if (error.message.includes('network') || error.message.includes('connection')) {
      code = PaymentErrorCode.NETWORK_ERROR
      retryable = true
    } else if (error.message.includes('timeout')) {
      code = PaymentErrorCode.TIMEOUT
      retryable = true
    } else if (error.message.includes('validation')) {
      code = PaymentErrorCode.VALIDATION_ERROR
      retryable = false
    } else if (error.message.includes('authentication') || error.message.includes('auth')) {
      code = PaymentErrorCode.AUTHENTICATION_ERROR
      retryable = false
    } else if (error.message.includes('permission') || error.message.includes('access')) {
      code = PaymentErrorCode.PERMISSION_ERROR
      retryable = false
    } else if (error.message.includes('not found')) {
      code = PaymentErrorCode.NOT_FOUND
      retryable = false
    } else if (error.message.includes('rate limit') || error.message.includes('too many requests')) {
      code = PaymentErrorCode.RATE_LIMITED
      retryable = true
    }
  }
  
  // Create and return the payment error
  return createPaymentError(message, code, retryable, {
    originalError: error instanceof Error ? error.message : String(error),
    context
  })
}

/**
 * Check if an error is retryable
 */
export function isRetryableError(error: any): boolean {
  // If it's a PaymentError, check the retryable flag
  if (error && typeof error === 'object' && 'retryable' in error) {
    return !!error.retryable
  }
  
  // Network errors are generally retryable
  if (error instanceof Error) {
    if (error.message.includes('network') || 
        error.message.includes('connection') ||
        error.message.includes('timeout') ||
        error.message.includes('rate limit') ||
        error.message.includes('too many requests')) {
      return true
    }
  }
  
  // Check for HTTP status codes
  if (error && typeof error === 'object') {
    const status = error.status || error.statusCode
    if (status && (status === 429 || status >= 500)) {
      return true
    }
  }
  
  return false
}

/**
 * Retry an operation with exponential backoff
 */
export async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  options: {
    maxRetries?: number,
    initialDelay?: number,
    backoffFactor?: number,
    retryCondition?: (error: any) => boolean,
    onRetry?: (error: any, attempt: number, delay: number) => void
  } = {}
): Promise<T> {
  const {
    maxRetries = 3,
    initialDelay = 1000,
    backoffFactor = 2,
    retryCondition = isRetryableError,
    onRetry = () => {}
  } = options
  
  let lastError: Error | null = null
  
  for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
    try {
      return await operation()
    } catch (error: any) {
      lastError = error instanceof Error ? error : new Error(String(error))
      
      // If this was the last attempt, throw the error
      if (attempt > maxRetries) {
        throw lastError
      }
      
      // Check if error is retryable
      if (!retryCondition(error)) {
        logger.warn('Non-retryable error encountered, aborting retry', { 
          error: lastError.message, 
          attempt 
        })
        throw lastError
      }
      
      // Calculate delay with exponential backoff
      const delay = initialDelay * Math.pow(backoffFactor, attempt - 1)
      
      // Call onRetry callback
      onRetry(error, attempt, delay)
      
      logger.debug(`Retrying operation after ${delay}ms`, { 
        attempt, 
        maxRetries,
        error: lastError.message
      })
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }
  
  // This should never happen, but TypeScript requires a return
  throw lastError || new Error('Unknown error during retry')
}

export default {
  createPaymentError,
  handleError,
  isRetryableError,
  retryWithBackoff
}