/**
 * Payment Service
 * 
 * Core service for handling payment operations across multiple gateways.
 */

import { logger } from './logger'
import { 
  PaymentGateway, 
  PaymentMethod, 
  PaymentRequest, 
  PaymentResponse,
  PaymentStatus,
  WebhookEvent,
  WebhookRequest,
  WebhookResponse,
  PaymentTransaction,
  PaymentErrorCode,
  IStorageAdapter
} from './types'
import { paymentManager, gatewayFactory } from './gateway-factory'
import { validatePaymentRequest, generateTransactionId } from './utils'
import { getEnabledGateways, getGatewaysForPaymentMethod, PAYMENT_METHOD_CONFIG } from './config'
import { storageAdapter } from './storage-adapter'

/**
 * Payment Service class
 * Handles all payment operations across multiple gateways
 */
class PaymentService {
  private initialized = false
  private defaultGateway: PaymentGateway = PaymentGateway.PAYFAST
  private defaultCurrency = 'ZAR'
  
  // In-memory storage for transactions
  private transactions: Map<string, PaymentTransaction> = new Map()
  
  // Storage adapter for persistent storage
  private storage: IStorageAdapter = storageAdapter
  
  /**
   * Initialize the payment service
   */
  async init(): Promise<void> {
    if (this.initialized) {
      return
    }
    
    try {
      // Load configuration from environment variables
      this.defaultGateway = (process.env.PAYMENT_DEFAULT_GATEWAY as PaymentGateway) || PaymentGateway.PAYFAST
      this.defaultCurrency = process.env.PAYMENT_DEFAULT_CURRENCY || 'ZAR'
      
      // Initialize gateway factory and payment manager
      await gatewayFactory.init()
      await paymentManager.init()
      
      logger.info('Payment service initialized', {
        defaultGateway: this.defaultGateway,
        defaultCurrency: this.defaultCurrency,
        enabledGateways: getEnabledGateways()
      })
      
      this.initialized = true
    } catch (error) {
      logger.error('Failed to initialize payment service', {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }
  
  /**
   * Get available payment methods
   */
  async getAvailablePaymentMethods(): Promise<Array<{
    method: PaymentMethod
    displayName: string
    description?: string
    icon?: string
    gateways: PaymentGateway[]
  }>> {
    await this.ensureInitialized()
    
    // Get enabled gateways
    const enabledGateways = getEnabledGateways()
    
    // Get available payment methods from configuration
    const availableMethods = Object.entries(PAYMENT_METHOD_CONFIG)
      .map(([methodKey, config]) => {
        const method = methodKey as PaymentMethod
        // Filter gateways to only include enabled ones
        const supportedGateways = config.supportedGateways.filter(
          gateway => enabledGateways.includes(gateway)
        )
        
        // Only include methods that have at least one enabled gateway
        if (supportedGateways.length === 0) {
          return null
        }
        
        return {
          method,
          displayName: config.displayName,
          description: config.description,
          icon: config.icon,
          gateways: supportedGateways,
          processingTime: config.processingTime
        }
      })
      .filter(Boolean) as Array<{
        method: PaymentMethod
        displayName: string
        description: string
        icon: string
        gateways: PaymentGateway[]
        processingTime: string
      }>
    
    logger.info('Available payment methods', {
      methodCount: availableMethods.length,
      methods: availableMethods.map(m => m.method)
    })
    
    return availableMethods
  }
  
  /**
   * Create a payment
   */
  async createPayment(
    request: PaymentRequest,
    gateway: PaymentGateway = this.defaultGateway
  ): Promise<PaymentResponse> {
    await this.ensureInitialized()
    
    try {
      logger.info('Creating payment', {
        gateway,
        amount: request.amount.amount,
        currency: request.amount.currency,
        reference: request.reference
      })
      
      // Validate the payment request
      const validation = validatePaymentRequest(request)
      if (!validation.isValid) {
        logger.warn('Invalid payment request', { 
          errors: validation.errors,
          reference: request.reference
        })
        
        return {
          success: false,
          reference: request.reference,
          status: PaymentStatus.FAILED,
          gatewayResponse: { gateway },
          error: {
            code: PaymentErrorCode.VALIDATION_ERROR,
            message: validation.errors.join(', ')
          }
        }
      }
      
      // Process payment through the payment manager
      const response = await paymentManager.processPayment(request, gateway, request.paymentMethod)
      
      // If payment creation was successful, store the transaction
      if (response.success && response.transactionId) {
        // Create transaction record
        const transaction: PaymentTransaction = {
          id: response.transactionId,
          transactionId: response.transactionId,
          orderId: request.metadata.orderId,
          customerId: request.metadata.customerId,
          gateway,
          method: request.paymentMethod || PaymentMethod.CARD,
          amount: request.amount,
          status: response.status,
          reference: request.reference,
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: {
            ...request.metadata,
            gatewayResponse: response
          }
        }
        
        // Store transaction in memory
        if (transaction.transactionId) {
          this.transactions.set(transaction.transactionId, transaction)
        } else {
          logger.error('Transaction has no transactionId, cannot store in memory', { transaction })
        }
        
        // Store transaction in persistent storage
        try {
          await this.storage.saveTransaction(transaction)
        } catch (storageError) {
          logger.error('Failed to save transaction to persistent storage', {
            error: storageError instanceof Error ? storageError.message : 'Unknown error',
            transactionId: transaction.transactionId
          })
          // Continue even if storage fails
        }
        
        logger.info('Payment transaction stored', {
          transactionId: transaction.transactionId,
          status: transaction.status
        })
      }
      
      return response
    } catch (error) {
      logger.error('Payment creation failed', {
        gateway,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return {
        success: false,
        reference: request.reference,
        status: PaymentStatus.FAILED,
        gatewayResponse: { gateway },
        error: {
          code: PaymentErrorCode.UNKNOWN_ERROR,
          message: error instanceof Error ? error.message : 'Failed to create payment'
        }
      }
    }
  }
  
  /**
   * Get payment status
   */
  async getPaymentStatus(
    transactionId: string,
    gateway: PaymentGateway = this.defaultGateway
  ): Promise<PaymentStatus> {
    await this.ensureInitialized()
    
    try {
      logger.info('Checking payment status', {
        transactionId,
        gateway
      })
      
      // First, check if we have the transaction in our in-memory storage
      let storedTransaction = this.transactions.get(transactionId)
      
      // If not in memory, try to get from persistent storage
      if (!storedTransaction) {
        try {
          const transactionResult = await this.storage.getTransaction(transactionId)
          storedTransaction = transactionResult === null ? undefined : transactionResult
          
          // If found in persistent storage, add to in-memory cache
          if (storedTransaction) {
            this.transactions.set(transactionId, storedTransaction)
          }
        } catch (storageError) {
          logger.error('Failed to get transaction from persistent storage', {
            error: storageError instanceof Error ? storageError.message : 'Unknown error',
            transactionId
          })
          // Continue even if storage fails
        }
      }
      
      if (storedTransaction) {
        logger.info('Payment status found in storage', {
          transactionId,
          status: storedTransaction.status
        })
        
        // If the status is final, return it directly
        if ([
          PaymentStatus.COMPLETED,
          PaymentStatus.FAILED,
          PaymentStatus.CANCELLED,
          PaymentStatus.REFUNDED
        ].includes(storedTransaction.status)) {
          return storedTransaction.status
        }
        
        // Otherwise, check with the gateway for the latest status
      }
      
      // Get status from the payment manager
      const status = await paymentManager.getPaymentStatus(transactionId, gateway)
      
      logger.info('Payment status result from gateway', {
        transactionId,
        gateway,
        status
      })
      
      // Store the status update
      if (storedTransaction) {
        // Update existing transaction
        const previousStatus = storedTransaction.status
        storedTransaction.status = status
        storedTransaction.updatedAt = new Date()
        
        // Update in memory
        this.transactions.set(transactionId, storedTransaction)
        
        // Update in persistent storage
        try {
          await this.storage.updateTransactionStatus(transactionId, status, {
            updatedAt: new Date(),
            previousStatus
          })
        } catch (storageError) {
          logger.error('Failed to update transaction status in persistent storage', {
            error: storageError instanceof Error ? storageError.message : 'Unknown error',
            transactionId
          })
          // Continue even if storage fails
        }
        
        logger.info('Payment status updated', {
          transactionId,
          previousStatus,
          newStatus: status
        })
      } else if (status !== PaymentStatus.PENDING) {
        // Create a minimal transaction record if we don't have one
        const transaction: PaymentTransaction = {
          id: transactionId,
          transactionId,
          orderId: 'unknown', // We don't have this information
          gateway,
          method: PaymentMethod.CARD, // Default
          amount: { amount: 0, currency: 'ZAR' }, // Default
          status,
          reference: transactionId, // Use transaction ID as reference
          createdAt: new Date(),
          updatedAt: new Date()
        }
        
        // Store in memory
        this.transactions.set(transactionId, transaction)
        
        // Store in persistent storage
        try {
          await this.storage.saveTransaction(transaction)
        } catch (storageError) {
          logger.error('Failed to save transaction to persistent storage', {
            error: storageError instanceof Error ? storageError.message : 'Unknown error',
            transactionId
          })
          // Continue even if storage fails
        }
        
        logger.info('Payment status stored', {
          transactionId,
          status
        })
      }
      
      return status
    } catch (error) {
      logger.error('Failed to check payment status', {
        transactionId,
        gateway,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return PaymentStatus.FAILED
    }
  }
  
  /**
   * Handle webhook from payment gateway
   */
  async handleWebhook(request: WebhookRequest): Promise<WebhookResponse> {
    await this.ensureInitialized()
    
    try {
      const { gateway, payload, signature } = request
      
      logger.info('Processing webhook', {
        gateway,
        payloadSize: JSON.stringify(payload).length,
        hasSignature: !!signature
      })
      
      // Use the webhook handler to process the webhook
      const { processWebhook } = await import('./webhook-handler')
      const result = await processWebhook(gateway, payload, signature)
      
      if (result.success && result.transactionId) {
        // Update transaction in memory if we have it
        const transaction = this.transactions.get(result.transactionId)
        
        if (transaction) {
          // Update existing transaction
          const previousStatus = transaction.status
          transaction.status = result.status || transaction.status
          transaction.updatedAt = new Date()
          transaction.metadata = {
            ...transaction.metadata,
            webhookReceived: new Date(),
            webhookPayload: payload,
            webhookEvent: result.event
          }
          
          // Update in memory
          this.transactions.set(result.transactionId, transaction)
          
          // Update in persistent storage
          try {
            await this.storage.updateTransactionStatus(result.transactionId, transaction.status, {
              webhookReceived: new Date(),
              webhookPayload: payload,
              webhookEvent: result.event,
              previousStatus
            })
          } catch (storageError) {
            logger.error('Failed to update transaction status in persistent storage from webhook', {
              error: storageError instanceof Error ? storageError.message : 'Unknown error',
              transactionId: result.transactionId
            })
            // Continue even if storage fails
          }
          
          logger.info('Transaction status updated from webhook', {
            transactionId: result.transactionId,
            previousStatus,
            newStatus: result.status,
            event: result.event
          })
        } else if (result.status) {
          // Create new transaction record if it doesn't exist
          const newTransaction: PaymentTransaction = {
            id: result.transactionId,
            transactionId: result.transactionId,
            orderId: payload.order_id || payload.orderId || 'unknown',
            gateway,
            method: PaymentMethod.CARD, // Default
            amount: { 
              amount: parseFloat(payload.amount || '0'), 
              currency: payload.currency || 'ZAR'
            },
            status: result.status,
            reference: payload.reference || result.transactionId,
            createdAt: new Date(),
            updatedAt: new Date(),
            metadata: {
              webhookReceived: new Date(),
              webhookPayload: payload,
              webhookEvent: result.event
            }
          }
          
          // Store in memory
          this.transactions.set(result.transactionId, newTransaction)
          
          // Store in persistent storage
          try {
            await this.storage.saveTransaction(newTransaction)
          } catch (storageError) {
            logger.error('Failed to save transaction to persistent storage from webhook', {
              error: storageError instanceof Error ? storageError.message : 'Unknown error',
              transactionId: result.transactionId
            })
            // Continue even if storage fails
          }
          
          logger.info('New transaction created from webhook', {
            transactionId: result.transactionId,
            status: result.status,
            event: result.event
          })
        }
      }
      
      return {
        success: result.success,
        transactionId: result.transactionId,
        event: result.event,
        message: result.message
      }
    } catch (error) {
      logger.error('Webhook processing failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process webhook'
      }
    }
  }
  
  /**
   * Process a refund
   */
  async processRefund(
    transactionId: string,
    amount?: number,
    reason?: string,
    gateway?: PaymentGateway
  ): Promise<PaymentResponse> {
    await this.ensureInitialized()
    
    try {
      // First, check if we have the transaction in our in-memory storage
      const storedTransaction = this.transactions.get(transactionId)
      
      // If we have the transaction, use its gateway
      if (storedTransaction) {
        gateway = storedTransaction.gateway
      }
      
      // If gateway is still not specified, use default
      if (!gateway) {
        gateway = this.defaultGateway
      }
      
      logger.info('Processing refund', {
        transactionId,
        gateway,
        amount: amount || 'full amount',
        reason: reason || 'Customer request'
      })
      
      // Process refund through the payment manager
      const response = await paymentManager.processRefund(
        transactionId,
        gateway,
        amount,
        reason
      )
      
      // If refund was successful, update the transaction
      if (response.success && storedTransaction) {
        storedTransaction.status = PaymentStatus.REFUNDED
        storedTransaction.updatedAt = new Date()
        storedTransaction.metadata = {
          ...storedTransaction.metadata,
          refundedAt: new Date(),
          refundAmount: amount,
          refundReason: reason,
          refundResponse: response
        }
        
        // Update in memory
        this.transactions.set(transactionId, storedTransaction)
        
        // Update in persistent storage
        try {
          await this.storage.updateTransactionStatus(transactionId, PaymentStatus.REFUNDED, {
            refundedAt: new Date(),
            refundAmount: amount,
            refundReason: reason,
            refundResponse: response
          })
        } catch (storageError) {
          logger.error('Failed to update transaction status in persistent storage after refund', {
            error: storageError instanceof Error ? storageError.message : 'Unknown error',
            transactionId
          })
          // Continue even if storage fails
        }
        
        logger.info('Transaction updated after refund', {
          transactionId,
          status: PaymentStatus.REFUNDED
        })
      }
      
      return response
    } catch (error) {
      logger.error('Refund processing failed', {
        transactionId,
        gateway,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return {
        success: false,
        status: PaymentStatus.FAILED,
        gatewayResponse: gateway ? { gateway } : undefined,
        error: {
          code: PaymentErrorCode.UNKNOWN_ERROR,
          message: error instanceof Error ? error.message : 'Failed to process refund'
        }
      }
    }
  }
  
  /**
   * Get transaction by ID
   */
  async getTransaction(transactionId: string): Promise<PaymentTransaction | null> {
    await this.ensureInitialized()
    
    // First check in-memory cache
    let transaction = this.transactions.get(transactionId)
    
    // If not found in memory, try persistent storage
    if (!transaction) {
      try {
        const result = await this.storage.getTransaction(transactionId)
        transaction = result === null ? undefined : result
        
        // Add to in-memory cache if found
        if (transaction) {
          this.transactions.set(transactionId, transaction)
        }
      } catch (error) {
        logger.error('Failed to get transaction from storage', {
          error: error instanceof Error ? error.message : 'Unknown error',
          transactionId
        })
      }
    }
    
    return transaction || null
  }
  
  /**
   * Get transactions by order ID
   */
  async getTransactionsByOrderId(orderId: string): Promise<PaymentTransaction[]> {
    await this.ensureInitialized()
    
    // First get from in-memory cache
    const memoryTransactions = Array.from(this.transactions.values())
      .filter(transaction => transaction.orderId === orderId)
    
    try {
      // Then get from persistent storage
      const storageTransactions = await this.storage.getTransactionsByOrderId(orderId)
      
      // Merge results, preferring in-memory versions
      const transactionMap = new Map<string, PaymentTransaction>()
      
      // Add storage transactions first
      storageTransactions.forEach(transaction => {
        transactionMap.set(transaction.id, transaction)
      })
      
      // Then override with memory transactions (which might be more up-to-date)
      memoryTransactions.forEach(transaction => {
        transactionMap.set(transaction.id, transaction)
      })
      
      return Array.from(transactionMap.values())
    } catch (error) {
      logger.error('Failed to get transactions by order ID from storage', {
        error: error instanceof Error ? error.message : 'Unknown error',
        orderId
      })
      
      // Return just the in-memory transactions if storage fails
      return memoryTransactions
    }
  }
  
  /**
   * Get transactions by customer ID
   */
  async getTransactionsByCustomerId(customerId: string): Promise<PaymentTransaction[]> {
    await this.ensureInitialized()
    
    // First get from in-memory cache
    const memoryTransactions = Array.from(this.transactions.values())
      .filter(transaction => transaction.customerId === customerId)
    
    try {
      // Then get from persistent storage
      const storageTransactions = await this.storage.getTransactionsByCustomerId(customerId)
      
      // Merge results, preferring in-memory versions
      const transactionMap = new Map<string, PaymentTransaction>()
      
      // Add storage transactions first
      storageTransactions.forEach(transaction => {
        transactionMap.set(transaction.id, transaction)
      })
      
      // Then override with memory transactions (which might be more up-to-date)
      memoryTransactions.forEach(transaction => {
        transactionMap.set(transaction.id, transaction)
      })
      
      return Array.from(transactionMap.values())
    } catch (error) {
      logger.error('Failed to get transactions by customer ID from storage', {
        error: error instanceof Error ? error.message : 'Unknown error',
        customerId
      })
      
      // Return just the in-memory transactions if storage fails
      return memoryTransactions
    }
  }
  
  /**
   * Ensure the service is initialized
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.init()
    }
  }
}

// Create and export singleton instance
export const paymentService = new PaymentService()

// Export default
export default paymentService