/**
 * Payfast Payment Service
 * 
 * Service for handling Payfast payment operations
 */

import crypto from 'crypto'
import { 
  PaymentRequest, 
  PaymentResponse, 
  PaymentStatus, 
  PaymentMethod,
  PaymentGateway,
  PaymentErrorCode,
  PayFastConfig,
  WebhookPayload,
  WebhookEvent
} from '../types'
import { paymentConfig } from '../config'
import { logger } from '../logger'

export class PayfastService {
  private config: PayFastConfig
  private baseUrl: string
  private apiUrl: string

  constructor() {
    this.config = paymentConfig.payfast as PayFastConfig
    
    // Set URLs based on sandbox mode
    if (this.config.sandbox) {
      this.baseUrl = 'https://sandbox.payfast.co.za'
      this.apiUrl = 'https://api.sandbox.payfast.co.za'
    } else {
      this.baseUrl = 'https://www.payfast.co.za'
      this.apiUrl = 'https://api.payfast.co.za'
    }
  }

  /**
   * Create payment data for Payfast
   */
  private createPaymentData(request: PaymentRequest): Record<string, string> {
    // Generate a unique payment ID if not provided
    const paymentId = request.reference || `pf_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
    
    return {
      // Merchant details
      merchant_id: this.config.merchantId,
      merchant_key: this.config.merchantKey,
      
      // Payment details
      amount: request.amount.amount.toFixed(2),
      item_name: request.description?.substring(0, 100) || `Order ${request.reference}`,
      item_description: request.items?.length > 0 
        ? request.items.map(item => `${item.name} x${item.quantity}`).join(', ').substring(0, 255)
        : '',
      
      // Customer details
      name_first: request.customer.firstName.substring(0, 100),
      name_last: request.customer.lastName?.substring(0, 100) || 'Customer',
      email_address: request.customer.email,
      cell_number: request.customer.phone || '',
      
      // URLs
      return_url: request.returnUrl,
      cancel_url: request.cancelUrl,
      notify_url: request.notifyUrl,
      
      // Custom fields
      custom_str1: request.reference,
      custom_str2: request.metadata.orderId,
      custom_str3: request.metadata.customerId || '',
      custom_str4: request.metadata.source || 'payment-core',
      custom_str5: new Date().toISOString(),
      
      // Payment reference
      m_payment_id: paymentId,
    }
  }

  /**
   * Generate signature for Payfast request
   */
  private generateSignature(data: Record<string, string>): string {
    // Create parameter string
    const paramString = Object.keys(data)
      .sort()
      .map(key => `${key}=${encodeURIComponent(data[key]).replace(/%20/g, '+')}`)
      .join('&')

    // Add passphrase if set
    const stringToHash = this.config.passphrase
      ? `${paramString}&passphrase=${encodeURIComponent(this.config.passphrase)}`
      : paramString

    // Generate MD5 hash
    return crypto.createHash('md5').update(stringToHash).digest('hex')
  }

  /**
   * Create a payment with Payfast
   */
  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      logger.info('Creating Payfast payment', {
        reference: request.reference,
        amount: request.amount.amount,
        currency: request.amount.currency
      })

      // Validate currency
      if (request.amount.currency !== 'ZAR') {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: {
            code: PaymentErrorCode.INVALID_CURRENCY,
            message: 'Payfast only supports ZAR currency'
          }
        }
      }

      // Create payment data
      const paymentData = this.createPaymentData(request)
      
      // Generate signature
      const signature = this.generateSignature(paymentData)
      paymentData.signature = signature

      // Create payment URL
      const queryString = new URLSearchParams(paymentData).toString()
      const paymentUrl = `${this.baseUrl}/eng/process?${queryString}`

      return {
        success: true,
        paymentUrl,
        transactionId: paymentData.m_payment_id,
        reference: request.reference,
        status: PaymentStatus.PENDING,
        message: 'Payment URL created successfully',
        redirectMethod: 'GET'
      }
    } catch (error) {
      logger.error('Payfast payment creation failed', {
        reference: request.reference,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.GATEWAY_ERROR,
          message: 'Failed to create payment',
          details: error instanceof Error ? error.message : undefined
        }
      }
    }
  }

  /**
   * Verify Payfast webhook signature
   */
  verifyWebhookSignature(payload: any, signature: string): boolean {
    try {
      if (!this.config.passphrase) {
        // If no passphrase is set, we can't verify the signature
        return true
      }
      
      // Remove signature from payload for verification
      const { signature: _, ...dataToVerify } = payload
      
      // Generate expected signature
      const expectedSignature = this.generateSignature(dataToVerify)
      
      return expectedSignature === signature
    } catch (error) {
      logger.error('Payfast webhook verification failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return false
    }
  }

  /**
   * Process Payfast webhook
   */
  async processWebhook(payload: any, signature?: string): Promise<WebhookPayload> {
    try {
      // Verify signature if provided
      if (signature && !this.verifyWebhookSignature(payload, signature)) {
        throw new Error('Invalid webhook signature')
      }

      // Map Payfast status to our payment status
      const paymentStatus = this.mapPayfastStatus(payload.payment_status)
      
      // Determine event type based on status
      let event: WebhookEvent
      switch (paymentStatus) {
        case PaymentStatus.COMPLETED:
          event = WebhookEvent.PAYMENT_COMPLETED
          break
        case PaymentStatus.FAILED:
          event = WebhookEvent.PAYMENT_FAILED
          break
        case PaymentStatus.CANCELLED:
          event = WebhookEvent.PAYMENT_CANCELLED
          break
        case PaymentStatus.REFUNDED:
          event = WebhookEvent.PAYMENT_REFUNDED
          break
        default:
          event = WebhookEvent.PAYMENT_PENDING
      }

      return {
        gateway: PaymentGateway.PAYFAST,
        event,
        data: payload,
        signature: signature || '',
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      logger.error('Payfast webhook processing failed', {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      throw error
    }
  }

  /**
   * Map Payfast status to our payment status
   */
  private mapPayfastStatus(payfastStatus: string): PaymentStatus {
    switch (payfastStatus?.toLowerCase()) {
      case 'complete':
        return PaymentStatus.COMPLETED
      case 'cancelled':
        return PaymentStatus.CANCELLED
      case 'failed':
        return PaymentStatus.FAILED
      case 'pending':
        return PaymentStatus.PENDING
      case 'refunded':
        return PaymentStatus.REFUNDED
      default:
        return PaymentStatus.PENDING
    }
  }

  /**
   * Validate Payfast configuration
   */
  validateConfig(): boolean {
    return !!(
      this.config?.merchantId &&
      this.config?.merchantKey
    )
  }
}