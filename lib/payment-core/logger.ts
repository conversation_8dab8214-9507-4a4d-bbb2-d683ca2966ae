/**
 * Payment Core Library Logger
 * 
 * Centralized logging system for payment operations.
 */

// Define log levels
type LogLevel = 'debug' | 'info' | 'warn' | 'error'

// Define log entry structure
interface LogEntry {
  timestamp: string
  level: LogLevel
  message: string
  context?: Record<string, any>
}

// Default logging configuration
const DEFAULT_LOGGING_CONFIG = {
  level: 'info',
  logToConsole: true,
  logToFile: false,
  logFilePath: './logs/payment.log'
}

// Simple logger implementation
class PaymentLogger {
  private level: LogLevel
  private logToConsole: boolean
  private logToFile: boolean
  private logFilePath?: string

  constructor() {
    this.level = (process.env.PAYMENT_LOG_LEVEL as LogLevel) || DEFAULT_LOGGING_CONFIG.level as LogLevel
    this.logToConsole = process.env.PAYMENT_LOG_TO_CONSOLE !== 'false'
    this.logToFile = process.env.PAYMENT_LOG_TO_FILE === 'true'
    this.logFilePath = process.env.PAYMENT_LOG_FILE_PATH || DEFAULT_LOGGING_CONFIG.logFilePath
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: Record<LogLevel, number> = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    }

    return levels[level] >= levels[this.level]
  }

  private formatLogEntry(level: LogLevel, message: string, context?: Record<string, any>): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      context
    }
  }

  private writeLog(entry: LogEntry): void {
    // Convert to JSON string
    const logString = JSON.stringify(entry)

    // Log to console if enabled
    if (this.logToConsole) {
      switch (entry.level) {
        case 'debug':
          console.debug(`[PAYMENT] ${logString}`)
          break
        case 'info':
          console.info(`[PAYMENT] ${logString}`)
          break
        case 'warn':
          console.warn(`[PAYMENT] ${logString}`)
          break
        case 'error':
          console.error(`[PAYMENT] ${logString}`)
          break
      }
    }

    // Log to file if enabled (would implement file logging here)
    if (this.logToFile && this.logFilePath) {
      // In a real implementation, we would write to a file
      // For now, we'll just note that we would log to a file
      if (entry.level === 'error') {
        console.info(`Would log error to file: ${this.logFilePath}`)
      }
    }
  }

  debug(message: string, context?: Record<string, any>): void {
    if (this.shouldLog('debug')) {
      this.writeLog(this.formatLogEntry('debug', message, context))
    }
  }

  info(message: string, context?: Record<string, any>): void {
    if (this.shouldLog('info')) {
      this.writeLog(this.formatLogEntry('info', message, context))
    }
  }

  warn(message: string, context?: Record<string, any>): void {
    if (this.shouldLog('warn')) {
      this.writeLog(this.formatLogEntry('warn', message, context))
    }
  }

  error(message: string, context?: Record<string, any>): void {
    if (this.shouldLog('error')) {
      this.writeLog(this.formatLogEntry('error', message, context))
    }
  }

  // Specialized payment logging methods
  logPaymentInitiated(data: Record<string, any>): void {
    this.info('Payment initiated', data)
  }

  logPaymentCompleted(data: Record<string, any>): void {
    this.info('Payment completed', data)
  }

  logPaymentFailed(data: Record<string, any>): void {
    this.error('Payment failed', data)
  }

  logPaymentCancelled(data: Record<string, any>): void {
    this.info('Payment cancelled', data)
  }

  logRefundInitiated(data: Record<string, any>): void {
    this.info('Refund initiated', data)
  }

  logRefundCompleted(data: Record<string, any>): void {
    this.info('Refund completed', data)
  }

  logWebhookReceived(data: Record<string, any>): void {
    this.info('Webhook received', data)
  }

  logWebhookProcessed(data: Record<string, any>): void {
    this.info('Webhook processed', data)
  }

  logWebhookError(data: Record<string, any>): void {
    this.error('Webhook error', data)
  }

  logPerformanceMetrics(data: Record<string, any>): void {
    this.debug('Performance metrics', data)
  }
}

// Create and export singleton instance
export const logger = new PaymentLogger()

// Export specialized loggers
export const paymentLogger = {
  initiated: (data: Record<string, any>) => logger.logPaymentInitiated(data),
  completed: (data: Record<string, any>) => logger.logPaymentCompleted(data),
  failed: (data: Record<string, any>) => logger.logPaymentFailed(data),
  cancelled: (data: Record<string, any>) => logger.logPaymentCancelled(data),
}

export const refundLogger = {
  initiated: (data: Record<string, any>) => logger.logRefundInitiated(data),
  completed: (data: Record<string, any>) => logger.logRefundCompleted(data),
}

export const webhookLogger = {
  received: (data: Record<string, any>) => logger.logWebhookReceived(data),
  processed: (data: Record<string, any>) => logger.logWebhookProcessed(data),
  error: (data: Record<string, any>) => logger.logWebhookError(data),
}

export const performanceLogger = {
  record: (data: Record<string, any>) => logger.logPerformanceMetrics(data),
}

export default logger