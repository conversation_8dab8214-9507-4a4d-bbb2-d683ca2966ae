/**
 * Payment Service
 * 
 * Main service for processing payments, integrating with the ecommerce system.
 */

import { PrismaClient } from '@prisma/client'
import { 
  PaymentRequest, 
  PaymentResponse, 
  PaymentGateway, 
  PaymentMethod,
  PaymentStatus,
  PaymentErrorCode,
  EcommerceOrder,
  EcommercePaymentResult,
  PaymentServiceConfig
} from './types'

import { paymentManager, gatewayFactory } from './gateway-factory'
import { logger } from './logger'
import { 
  generateTransactionId, 
  generatePaymentReference,
  validatePaymentRequest,
  formatAmount
} from './utils'

// Initialize Prisma client
const prisma = new PrismaClient()

/**
 * Payment Service
 * High-level service for processing payments
 */
export class PaymentService {
  private config: PaymentServiceConfig
  private initialized: boolean = false

  constructor(config: PaymentServiceConfig = {}) {
    this.config = {
      defaultGateway: config.defaultGateway,
      defaultCurrency: config.defaultCurrency || 'ZAR',
      autoRetry: config.autoRetry !== false,
      maxRetries: config.maxRetries || 3,
      retryDelay: config.retryDelay || 1000,
      webhookSecret: config.webhookSecret || process.env.PAYMENT_WEBHOOK_SECRET,
      logLevel: config.logLevel || 'info',
      storageAdapter: config.storageAdapter
    }
  }

  /**
   * Initialize the payment service
   */
  public async init(): Promise<void> {
    if (this.initialized) {
      return
    }

    try {
      // Initialize the gateway factory
      await gatewayFactory.init()
      
      // Initialize the payment manager
      await paymentManager.init()

      this.initialized = true
      logger.info('Payment service initialized successfully')
    } catch (error) {
      logger.error('Failed to initialize payment service', {
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      throw error
    }
  }

  /**
   * Process a payment for an order
   */
  public async processOrderPayment(
    order: EcommerceOrder,
    gateway?: PaymentGateway,
    method?: PaymentMethod,
    returnUrl?: string,
    cancelUrl?: string,
    notifyUrl?: string
  ): Promise<EcommercePaymentResult> {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.init()
      }

      // Generate reference
      const reference = generatePaymentReference(order.id)
      
      // Create payment request
      const request: PaymentRequest = {
        amount: {
          amount: order.total,
          currency: order.currency || this.config.defaultCurrency || 'ZAR',
          formatted: formatAmount(order.total, order.currency)
        },
        customer: {
          id: order.customerId,
          email: order.customerEmail,
          firstName: order.customerName.split(' ')[0],
          lastName: order.customerName.split(' ').slice(1).join(' ') || 'Customer',
          address: order.billingAddress
        },
        items: order.items.map(item => ({
          id: item.id,
          name: item.name,
          quantity: item.quantity,
          unitPrice: item.price,
          totalPrice: item.total
        })),
        metadata: {
          orderId: order.id,
          customerId: order.customerId || '',
          source: 'ecommerce'
        },
        returnUrl: returnUrl || `${process.env.NEXT_PUBLIC_APP_URL}/checkout/success?orderId=${order.id}`,
        cancelUrl: cancelUrl || `${process.env.NEXT_PUBLIC_APP_URL}/checkout/cancel?orderId=${order.id}`,
        notifyUrl: notifyUrl || `${process.env.NEXT_PUBLIC_APP_URL}/api/webhooks/payments`,
        reference,
        description: `Order #${order.orderNumber}`,
        paymentMethod: method
      }
      
      // Validate request
      const validation = validatePaymentRequest(request)
      if (!validation.isValid) {
        logger.error('Invalid payment request', { errors: validation.errors })
        return {
          success: false,
          orderId: order.id,
          status: PaymentStatus.FAILED,
          error: {
            code: PaymentErrorCode.VALIDATION_ERROR,
            message: validation.errors.join(', ')
          }
        }
      }
      
      // Process payment
      const response = await paymentManager.processPayment(request, gateway, method)
      
      // Store transaction in database
      if (response.success) {
        try {
          await this.storeTransaction({
            orderId: order.id,
            reference,
            transactionId: response.transactionId || reference,
            gateway: gateway || 'payfast',
            amount: order.total,
            currency: order.currency || 'ZAR',
            status: response.status,
            customerEmail: order.customerEmail,
            metadata: {
              paymentUrl: response.paymentUrl,
              gatewayResponse: response.gatewayResponse
            }
          })
        } catch (dbError) {
          logger.error('Failed to store transaction', { error: dbError })
          // Continue even if storage fails
        }
      }
      
      // Return result
      return {
        success: response.success,
        orderId: order.id,
        transactionId: response.transactionId,
        status: response.status,
        paymentUrl: response.paymentUrl,
        message: response.message,
        error: response.error
      }
    } catch (error) {
      logger.error('Payment processing error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        orderId: order.id
      })
      
      return {
        success: false,
        orderId: order.id,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.UNKNOWN_ERROR,
          message: error instanceof Error ? error.message : 'Unknown payment error'
        }
      }
    }
  }

  /**
   * Create a direct payment
   */
  public async createPayment(
    request: PaymentRequest,
    gateway?: PaymentGateway
  ): Promise<PaymentResponse> {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.init()
      }
      
      // Validate request
      const validation = validatePaymentRequest(request)
      if (!validation.isValid) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: {
            code: PaymentErrorCode.VALIDATION_ERROR,
            message: validation.errors.join(', ')
          }
        }
      }
      
      // Process payment
      return await paymentManager.processPayment(request, gateway, request.paymentMethod)
    } catch (error) {
      logger.error('Direct payment error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        reference: request.reference
      })
      
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.UNKNOWN_ERROR,
          message: error instanceof Error ? error.message : 'Unknown payment error'
        }
      }
    }
  }

  /**
   * Get payment status
   */
  public async getPaymentStatus(
    transactionId: string,
    gateway: PaymentGateway
  ): Promise<PaymentStatus> {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.init()
      }
      
      return await paymentManager.getPaymentStatus(transactionId, gateway)
    } catch (error) {
      logger.error('Get payment status error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      })
      
      return PaymentStatus.FAILED
    }
  }

  /**
   * Process refund
   */
  public async processRefund(
    transactionId: string,
    gateway: PaymentGateway,
    amount?: number,
    reason?: string
  ): Promise<PaymentResponse> {
    try {
      // Ensure service is initialized
      if (!this.initialized) {
        await this.init()
      }
      
      return await paymentManager.processRefund(transactionId, gateway, amount, reason)
    } catch (error) {
      logger.error('Refund error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      })
      
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: PaymentErrorCode.UNKNOWN_ERROR,
          message: error instanceof Error ? error.message : 'Unknown refund error'
        }
      }
    }
  }

  /**
   * Get available payment methods
   */
  public async getAvailablePaymentMethods() {
    // Ensure service is initialized
    if (!this.initialized) {
      await this.init()
    }
    
    return paymentManager.getAvailablePaymentMethods()
  }

  /**
   * Store transaction in database
   */
  private async storeTransaction(data: {
    orderId: string
    reference: string
    transactionId: string
    gateway: string
    amount: number
    currency: string
    status: PaymentStatus
    customerEmail: string
    metadata?: any
  }) {
    try {
      // Use custom storage adapter if provided
      if (this.config.storageAdapter) {
        return await this.config.storageAdapter.saveTransaction({
          id: data.transactionId,
          orderId: data.orderId,
          gateway: data.gateway as PaymentGateway,
          method: PaymentMethod.CARD, // Default
          amount: {
            amount: data.amount,
            currency: data.currency
          },
          status: data.status,
          reference: data.reference,
          transactionId: data.transactionId,
          gatewayResponse: data.metadata?.gatewayResponse,
          createdAt: new Date(),
          updatedAt: new Date(),
          metadata: data.metadata
        })
      }
      
      // Otherwise use Prisma
      return await prisma.payment.create({
        data: {
          id: generateTransactionId(),
          paymentNumber: data.reference,
          orderId: data.orderId,
          amount: data.amount,
          currency: data.currency,
          status: data.status,
          gatewayId: data.gateway,
          gatewayPaymentId: data.transactionId,
          customerId: data.customerEmail, // Using email as customer ID for now
          receiptEmail: data.customerEmail, // Store email in the appropriate field
          metadata: data.metadata || {}
        }
      })
    } catch (error) {
      logger.error('Failed to store transaction', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId: data.transactionId
      })
      throw error
    }
  }

  /**
   * Update transaction status
   */
  public async updateTransactionStatus(
    transactionId: string,
    status: PaymentStatus,
    metadata?: any
  ) {
    try {
      // Use custom storage adapter if provided
      if (this.config.storageAdapter) {
        return await this.config.storageAdapter.updateTransactionStatus(
          transactionId,
          status,
          metadata
        )
      }
      
      // Otherwise use Prisma
      return await prisma.payment.updateMany({
        where: { 
          OR: [
            { id: transactionId },
            { gatewayPaymentId: transactionId },
            { paymentNumber: transactionId }
          ]
        },
        data: {
          status,
          metadata: metadata ? { ...metadata } : undefined,
          updatedAt: new Date()
        }
      })
    } catch (error) {
      logger.error('Failed to update transaction status', {
        error: error instanceof Error ? error.message : 'Unknown error',
        transactionId
      })
      throw error
    }
  }
}

// Create and export singleton instance
export const paymentService = new PaymentService()

export default paymentService