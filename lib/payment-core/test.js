/**
 * Simple test script for the payment service
 */

// Import the payment service
const { paymentService } = require('./service');

// Test the payment service
async function testPaymentService() {
  try {
    console.log('Initializing payment service...');
    await paymentService.init();
    console.log('Payment service initialized successfully!');
    
    // Get available payment methods
    console.log('\nGetting available payment methods...');
    const methods = await paymentService.getAvailablePaymentMethods();
    console.log(`Found ${methods.length} payment methods`);
    
    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Test failed with error:', error);
  }
}

// Run the test
testPaymentService().catch(console.error);