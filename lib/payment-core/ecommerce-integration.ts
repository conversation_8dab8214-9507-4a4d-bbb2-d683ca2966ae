/**
 * Payment Core - Ecommerce Integration
 * 
 * Integrates the payment core library with the ecommerce system.
 */

import { PrismaClient } from '@prisma/client'
import { 
  PaymentGateway, 
  PaymentMethod, 
  PaymentStatus,
  EcommerceOrder
} from './types'

// Fix for type imports
const PAYMENT_GATEWAY = {
  PAYFAST: 'payfast',
  OZOW: 'ozow',
  SNAPSCAN: 'snapscan',
  YOCO: 'yoco',
  PAYU: 'payu',
  ZAPPER: 'zapper'
} as const

const PAYMENT_METHOD = {
  CARD: 'card',
  EFT: 'eft',
  QR_CODE: 'qr_code',
  INSTANT_EFT: 'instant_eft',
  BANK_TRANSFER: 'bank_transfer',
  MOBILE_MONEY: 'mobile_money',
  CRYPTO: 'crypto'
} as const

const PAYMENT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
  PARTIALLY_REFUNDED: 'partially_refunded',
  EXPIRED: 'expired'
} as const

import { paymentService } from './payment-service'
import { logger } from './logger'

// Initialize Prisma client
const prisma = new PrismaClient()

/**
 * Process payment for an order
 */
export async function processOrderPayment(
  orderId: string,
  gateway?: PaymentGateway,
  method?: PaymentMethod,
  returnUrl?: string,
  cancelUrl?: string,
  notifyUrl?: string
) {
  try {
    // Get order from database
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        user: true,
        items: true
      }
    })

    if (!order) {
      throw new Error(`Order not found: ${orderId}`)
    }

    // Convert to EcommerceOrder format
    const ecommerceOrder: EcommerceOrder = {
      id: order.id,
      orderNumber: order.orderNumber,
      customerId: order.userId || undefined,
      customerEmail: order.user?.email || order.customerEmail || '',
      customerName: `${order.user?.firstName || order.customerFirstName || ''} ${order.user?.lastName || order.customerLastName || ''}`.trim() || 'Customer',
      total: Number(order.total),
      currency: order.currency || 'ZAR',
      status: order.status,
      items: order.items.map(item => ({
        id: item.id,
        name: item.productTitle,
        quantity: item.quantity,
        price: Number(item.unitPrice),
        total: Number(item.totalPrice)
      })),
      billingAddress: order.billingAddress ? {
        line1: (order.billingAddress as any).line1 || (order.billingAddress as any).street || (order.billingAddress as any).address1 || '',
        line2: (order.billingAddress as any).line2 || (order.billingAddress as any).street2 || (order.billingAddress as any).address2 || '',
        city: (order.billingAddress as any).city || '',
        state: (order.billingAddress as any).state || (order.billingAddress as any).province || '',
        postalCode: (order.billingAddress as any).postalCode || (order.billingAddress as any).postal_code || (order.billingAddress as any).zip || '',
        country: (order.billingAddress as any).country || ''
      } : undefined
    }

    // Process payment
    const result = await paymentService.processOrderPayment(
      ecommerceOrder,
      gateway,
      method,
      returnUrl,
      cancelUrl,
      notifyUrl
    )

    // Update order status based on payment result
    if (result.success) {
      await prisma.order.update({
        where: { id: orderId },
        data: {
          paymentStatus: result.status,
          // Store payment details in metadata since there's no direct field
          attributes: {
            paymentId: result.transactionId,
            paymentGateway: gateway || PAYMENT_GATEWAY.PAYFAST,
            paymentMethod: method || PAYMENT_METHOD.CARD,
            paymentUrl: result.paymentUrl
          },
          updatedAt: new Date()
        }
      })
    } else {
      await prisma.order.update({
        where: { id: orderId },
        data: {
          paymentStatus: PAYMENT_STATUS.FAILED,
          attributes: {
            paymentError: result.error?.message
          },
          updatedAt: new Date()
        }
      })
    }

    return result
  } catch (error) {
    logger.error('Order payment processing error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      orderId
    })
    
    // Update order with error
    try {
      await prisma.order.update({
        where: { id: orderId },
        data: {
          paymentStatus: PAYMENT_STATUS.FAILED,
          attributes: {
            paymentError: error instanceof Error ? error.message : 'Unknown payment error'
          },
          updatedAt: new Date()
        }
      })
    } catch (dbError) {
      logger.error('Failed to update order with payment error', { 
        error: dbError, 
        orderId 
      })
    }
    
    throw error
  }
}

/**
 * Update order status based on payment status
 */
export async function updateOrderPaymentStatus(
  orderId: string,
  paymentStatus: PaymentStatus,
  transactionId?: string,
  metadata?: any
) {
  try {
    // Update order payment status
    await prisma.order.update({
      where: { id: orderId },
      data: {
        paymentStatus,
        attributes: {
          paymentId: transactionId || undefined
        },
        updatedAt: new Date()
      }
    })

    // If payment is completed, update order status to processing
    if (paymentStatus === PAYMENT_STATUS.COMPLETED) {
      await prisma.order.update({
        where: { id: orderId },
        data: {
          status: 'processing',
          updatedAt: new Date()
        }
      })
    }

    // If payment failed or was cancelled, update order status
    if (paymentStatus === PAYMENT_STATUS.FAILED || paymentStatus === PAYMENT_STATUS.CANCELLED) {
      await prisma.order.update({
        where: { id: orderId },
        data: {
          status: 'payment_failed',
          attributes: {
            paymentError: metadata?.error || 'Payment failed or cancelled'
          },
          updatedAt: new Date()
        }
      })
    }

    logger.info('Order payment status updated', {
      orderId,
      paymentStatus,
      transactionId
    })

    return true
  } catch (error) {
    logger.error('Failed to update order payment status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      orderId,
      paymentStatus
    })
    
    return false
  }
}

/**
 * Get available payment methods for checkout
 */
export async function getCheckoutPaymentMethods() {
  try {
    // Get available payment methods from payment service
    const methods = await paymentService.getAvailablePaymentMethods()
    
    // Format for checkout UI
    return methods.map(method => ({
      id: method.method,
      name: method.displayName,
      description: method.description,
      icon: method.icon,
      gateways: method.gateways
    }))
  } catch (error) {
    logger.error('Failed to get checkout payment methods', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    // Return default methods
    return [
      {
        id: PAYMENT_METHOD.CARD,
        name: 'Credit/Debit Card',
        description: 'Pay securely with your credit or debit card',
        icon: 'credit-card',
        gateways: [PAYMENT_GATEWAY.PAYFAST]
      },
      {
        id: PAYMENT_METHOD.EFT,
        name: 'EFT/Bank Transfer',
        description: 'Electronic Funds Transfer from your bank account',
        icon: 'bank',
        gateways: [PAYMENT_GATEWAY.PAYFAST]
      }
    ]
  }
}

export default {
  processOrderPayment,
  updateOrderPaymentStatus,
  getCheckoutPaymentMethods
}