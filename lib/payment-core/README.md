# Payment Core Library

A robust payment processing library designed for South African e-commerce applications, with a focus on reliability, extensibility, and comprehensive payment gateway support.

## Features

- **Multiple Gateway Support**: Integrates with PayFast, Ozow, SnapScan, Yoco, and more
- **Unified API**: Consistent interface across all payment gateways
- **Type Safety**: Comprehensive TypeScript types for all operations
- **Error Handling**: Robust error handling and logging
- **Webhook Processing**: Standardized webhook handling for all gateways
- **Retry Logic**: Automatic retries for transient failures
- **Validation**: Request validation before processing
- **Ecommerce Integration**: Seamless integration with the ecommerce library
- **Persistent Storage**: Transaction storage with Prisma ORM

## Supported Payment Methods

- Credit/Debit Cards
- EFT (Electronic Funds Transfer)
- Instant EFT
- QR Code Payments (SnapScan, Zapper)
- Mobile Money
- Cash on Delivery
- Bank Transfers

## Supported Gateways

- PayFast
- Ozow
- SnapScan
- Yoco
- Manual payments

## Usage

### Basic Payment Processing

```typescript
import { paymentService, PaymentGateway, PaymentMethod } from '@/lib/payment-core';

// Initialize the service
await paymentService.init();

// Create a payment request
const request = {
  amount: {
    amount: 100.00,
    currency: 'ZAR'
  },
  customer: {
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe'
  },
  items: [
    {
      id: 'product-1',
      name: 'Product 1',
      quantity: 1,
      unitPrice: 100.00,
      totalPrice: 100.00
    }
  ],
  metadata: {
    orderId: 'order-123',
    customerId: 'customer-456',
    source: 'website'
  },
  returnUrl: 'https://example.com/checkout/success',
  cancelUrl: 'https://example.com/checkout/cancel',
  notifyUrl: 'https://example.com/api/webhooks/payments',
  reference: 'INV-123',
  description: 'Order #123'
};

// Process payment with PayFast
const response = await paymentService.createPayment(request, PaymentGateway.PAYFAST);

if (response.success) {
  // Redirect to payment URL
  window.location.href = response.paymentUrl;
} else {
  // Handle error
  console.error(response.error);
}
```

### Processing Order Payments

```typescript
import { processOrderPayment, PaymentGateway, PaymentMethod } from '@/lib/payment-core/ecommerce-integration';

// Process payment for an order
const result = await processOrderPayment(
  'order-123',
  PaymentGateway.PAYFAST,
  PaymentMethod.CARD
);

if (result.success) {
  // Redirect to payment URL
  window.location.href = result.paymentUrl;
} else {
  // Handle error
  console.error(result.error);
}
```

### Handling Webhooks

```typescript
import { paymentService, PaymentGateway } from '@/lib/payment-core';

// In your API route handler
export async function POST(request) {
  const body = await request.json();
  const signature = request.headers.get('x-signature');
  
  const result = await paymentService.handleWebhook({
    gateway: PaymentGateway.PAYFAST,
    payload: body,
    signature
  });
  
  if (result.success) {
    return Response.json({ success: true });
  } else {
    return Response.json({ success: false, error: result.message }, { status: 400 });
  }
}
```

### Checking Payment Status

```typescript
import { paymentService, PaymentGateway, PaymentStatus } from '@/lib/payment-core';

// Check payment status
const status = await paymentService.getPaymentStatus('transaction-123', PaymentGateway.PAYFAST);

if (status === PaymentStatus.COMPLETED) {
  // Payment is complete, fulfill order
} else if (status === PaymentStatus.FAILED) {
  // Payment failed, notify customer
}
```

### Processing Refunds

```typescript
import { paymentService, PaymentGateway } from '@/lib/payment-core';

// Process a full refund
const refundResult = await paymentService.processRefund(
  'transaction-123',
  undefined, // Full refund
  'Customer requested refund',
  PaymentGateway.PAYFAST
);

// Process a partial refund
const partialRefundResult = await paymentService.processRefund(
  'transaction-123',
  50.00, // Partial refund amount
  'Customer requested partial refund',
  PaymentGateway.PAYFAST
);

if (refundResult.success) {
  // Refund processed successfully
} else {
  // Handle refund error
  console.error(refundResult.error);
}
```

### Retrieving Transactions

```typescript
import { paymentService } from '@/lib/payment-core';

// Get a transaction by ID
const transaction = await paymentService.getTransaction('transaction-123');

// Get all transactions for an order
const orderTransactions = await paymentService.getTransactionsByOrderId('order-123');

// Get all transactions for a customer
const customerTransactions = await paymentService.getTransactionsByCustomerId('customer-123');
```

## Configuration

The library uses environment variables for configuration:

```
# PayFast
PAYFAST_MERCHANT_ID=your-merchant-id
PAYFAST_MERCHANT_KEY=your-merchant-key
PAYFAST_PASSPHRASE=your-passphrase

# Ozow
OZOW_API_KEY=your-api-key
OZOW_PRIVATE_KEY=your-private-key
OZOW_SITE_CODE=your-site-code

# SnapScan
SNAPSCAN_API_KEY=your-api-key
SNAPSCAN_MERCHANT_ID=your-merchant-id

# Yoco
YOCO_SECRET_KEY=your-secret-key
YOCO_PUBLIC_KEY=your-public-key

# Payment Core Configuration
PAYMENT_WEBHOOK_SECRET=your-webhook-secret-key
PAYMENT_DEFAULT_GATEWAY=payfast
PAYMENT_DEFAULT_CURRENCY=ZAR
PAYMENT_AUTO_RETRY=true
PAYMENT_MAX_RETRIES=3
PAYMENT_RETRY_DELAY=1000
PAYMENT_LOG_LEVEL=info
```

## Architecture

The library follows a modular architecture:

- **Gateway Factory**: Creates and manages payment gateway instances
- **Payment Service**: High-level service for processing payments
- **Gateway Implementations**: Individual gateway implementations
- **Webhook Handler**: Processes incoming webhooks
- **Validation**: Validates payment requests
- **Logging**: Comprehensive logging system
- **Error Handling**: Standardized error handling
- **Types**: Comprehensive type definitions
- **Storage Adapter**: Persistent storage for transactions

## Error Handling

The library provides detailed error information:

```typescript
if (!response.success && response.error) {
  console.error(`Payment failed: ${response.error.message}`);
  console.error(`Error code: ${response.error.code}`);
  
  // Check if the error is retryable
  if (response.error.retryable) {
    // Retry the payment
  }
}
```

## Logging

The library includes a comprehensive logging system:

```typescript
import { logger } from '@/lib/payment-core';

// Log levels
logger.debug('Debug message', { context: 'value' });
logger.info('Info message', { context: 'value' });
logger.warn('Warning message', { context: 'value' });
logger.error('Error message', { context: 'value' });

// Specialized loggers
import { paymentLogger, webhookLogger } from '@/lib/payment-core';

paymentLogger.initiated({ /* payment data */ });
paymentLogger.completed({ /* payment data */ });
webhookLogger.received({ /* webhook data */ });
```

## Testing

The library includes test scripts to verify functionality:

```bash
# Run the payment service test
npx ts-node scripts/test-payment-service.ts

# Run the payment core test
npx ts-node scripts/test-payment-core.ts
```

## Contributing

1. Create a new gateway implementation in `gateways/`
2. Update the gateway factory to include your new gateway
3. Add configuration to `config.ts`
4. Update types in `types.ts`
5. Add tests for your implementation

## License

MIT