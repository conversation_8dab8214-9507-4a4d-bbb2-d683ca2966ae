import { 
  NotificationService, 
  NotificationRequest, 
  NotificationResult, 
  DeliveryStatus,
  NotificationChannel,
  NotificationStatus,
  NotificationRecord,
  NotificationPriority,
  RecipientType,
  NotificationType
} from '../types'
import { logger, errorUtils } from '../utils'
import { getAppwriteClient } from '@/lib/appwrite/client'
import { getAppwriteServer } from '@/lib/appwrite/server'
import { ID, Query, Permission, Role } from 'appwrite'
import { APPWRITE_COLLECTIONS } from '@/lib/appwrite/config'

// Define collection ID for notifications
const NOTIFICATIONS_COLLECTION = APPWRITE_COLLECTIONS.NOTIFICATIONS || 'notifications'
const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || 'main'

/**
 * Appwrite Notification Service
 * Handles storing and retrieving notifications using Appwrite
 */
export class AppwriteNotificationService implements NotificationService {
  private initialized = false;

  constructor() {
    // Delay initialization to avoid immediate errors during app startup
    setTimeout(() => {
      this.initializeCollection().catch(error => {
        logger.error('Failed to initialize Appwrite notification collection', { error })
      })
    }, 1000)
  }

  /**
   * Initialize Appwrite collection for notifications
   */
  private async initializeCollection(): Promise<void> {
    try {
      if (this.initialized) return;

      // Check if we're in a browser environment
      if (typeof window !== 'undefined') {
        logger.info('Skipping server-side Appwrite initialization in browser environment');
        this.initialized = true;
        return;
      }

      let databases;
      try {
        const appwriteServer = getAppwriteServer();
        databases = appwriteServer.getDatabases();
        
        if (!databases) {
          throw new Error('Appwrite databases service not available');
        }
      } catch (error) {
        logger.error('Failed to initialize Appwrite server', { error });
        // Mark as initialized to prevent further attempts
        this.initialized = true;
        return;
      }

      // Check if collection exists
      try {
        await databases.getCollection(DATABASE_ID, NOTIFICATIONS_COLLECTION);
        this.initialized = true;
        logger.info('Appwrite notification collection already exists');
      } catch (error: any) {
        // Create collection if it doesn't exist
        if (error.code === 404) {
          logger.info('Creating Appwrite notification collection...');
          
          // Create collection
          await databases.createCollection(
            DATABASE_ID,
            NOTIFICATIONS_COLLECTION,
            'Notifications',
            [
              Permission.read(Role.user('{recipientId}')),
              Permission.update(Role.user('{recipientId}')),
              Permission.delete(Role.user('{recipientId}')),
              Permission.create(Role.any()),
              Permission.read(Role.team('admins')),
              Permission.update(Role.team('admins')),
              Permission.delete(Role.team('admins'))
            ],
            true
          );

          // Create attributes
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'type', 255, true);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'channel', 50, true);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'title', 255, true);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'content', 5000, true);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'recipientId', 255, true);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'recipientType', 50, false);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'recipientEmail', 255, false);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'recipientPhone', 50, false);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'status', 50, true);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'priority', 50, true);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'templateId', 255, false);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'campaignId', 255, false);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'data', 255, false);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'metadata', 255, false);
          await databases.createDatetimeAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'scheduledAt', false);
          await databases.createDatetimeAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'sentAt', false);
          await databases.createDatetimeAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'deliveredAt', false);
          await databases.createDatetimeAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'readAt', false);
          await databases.createDatetimeAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'clickedAt', false);
          await databases.createDatetimeAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'failedAt', false);
          await databases.createDatetimeAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'expiresAt', false);
          await databases.createIntegerAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'retryCount', true, 0);
          await databases.createIntegerAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'maxRetries', true, 3);
          await databases.createStringAttribute(DATABASE_ID, NOTIFICATIONS_COLLECTION, 'error', 1000, false);

          // Create indexes
          await databases.createIndex(
            DATABASE_ID,
            NOTIFICATIONS_COLLECTION,
            'recipient_status_index',
            'key',
            ['recipientId', 'status']
          );
          
          await databases.createIndex(
            DATABASE_ID,
            NOTIFICATIONS_COLLECTION,
            'channel_type_index',
            'key',
            ['channel', 'type']
          );

          await databases.createIndex(
            DATABASE_ID,
            NOTIFICATIONS_COLLECTION,
            'scheduled_index',
            'key',
            ['scheduledAt', 'status']
          );

          this.initialized = true;
          logger.info('Appwrite notification collection created successfully');
        } else {
          throw error;
        }
      }
    } catch (error) {
      logger.error('Failed to initialize Appwrite notification collection', { error });
      throw error;
    }
  }

  /**
   * Send notification (store in Appwrite)
   */
  async send(notification: NotificationRequest): Promise<NotificationResult> {
    try {
      // Validate notification
      const validation = this.validateNotification(notification);
      if (!validation.isValid) {
        return {
          success: false,
          error: `Validation failed: ${validation.errors.join(', ')}`
        };
      }

      // Create notification record
      const notificationRecord = await this.createNotificationRecord(notification);

      logger.info('Notification stored in Appwrite', {
        notificationId: notificationRecord.id,
        recipient: notification.recipientId || notification.recipientEmail || notification.recipientPhone
      });

      return {
        success: true,
        notificationId: notificationRecord.id,
        messageId: notificationRecord.id,
        metadata: {
          provider: 'appwrite',
          createdAt: notificationRecord.createdAt
        }
      };

    } catch (error) {
      const errorMessage = errorUtils.createErrorMessage(error);
      logger.error('Appwrite notification send failed', { error: errorMessage, notification });

      return {
        success: false,
        error: errorMessage,
        metadata: {
          provider: 'appwrite'
        }
      };
    }
  }

  /**
   * Create notification record in Appwrite
   */
  private async createNotificationRecord(request: NotificationRequest): Promise<NotificationRecord> {
    try {
      await this.initializeCollection();
      
      const appwriteServer = getAppwriteServer();
      const databases = appwriteServer.getDatabases();
      
      if (!databases) {
        throw new Error('Appwrite databases service not available');
      }

      const now = new Date();
      
      const notificationData = {
        type: request.type,
        channel: request.channel,
        title: request.title,
        content: request.content,
        data: request.data || {},
        recipientId: request.recipientId || '',
        recipientType: request.recipientType || RecipientType.CUSTOMER,
        recipientEmail: request.recipientEmail || '',
        recipientPhone: request.recipientPhone || '',
        status: request.scheduledAt && request.scheduledAt > now 
          ? NotificationStatus.SCHEDULED 
          : NotificationStatus.PENDING,
        priority: request.priority || NotificationPriority.NORMAL,
        templateId: request.templateId || '',
        scheduledAt: request.scheduledAt ? request.scheduledAt.toISOString() : null,
        retryCount: 0,
        maxRetries: 3,
        metadata: request.metadata || {},
        expiresAt: request.expiresAt ? request.expiresAt.toISOString() : null,
        createdAt: now.toISOString(),
        updatedAt: now.toISOString()
      };

      const response = await databases.createDocument(
        DATABASE_ID,
        NOTIFICATIONS_COLLECTION,
        ID.unique(),
        notificationData
      );

      return this.mapAppwriteDocumentToNotificationRecord(response);
    } catch (error) {
      logger.error('Failed to create notification record in Appwrite', { error, request });
      throw new Error('Failed to create notification record');
    }
  }

  /**
   * Get notifications for a user
   */
  async getNotifications(
    userId: string,
    options: {
      limit?: number;
      offset?: number;
      unreadOnly?: boolean;
      type?: string;
    } = {}
  ): Promise<NotificationRecord[]> {
    try {
      await this.initializeCollection();
      
      // Use client-side for user-specific queries (with proper permissions)
      // or server-side for admin/system operations
      const isClientSide = typeof window !== 'undefined';
      let databases;
      
      if (isClientSide) {
        // Client-side: user is fetching their own notifications
        // Use the AppwriteServices class to get the databases service
        const { databases: clientDatabases } = await import('@/lib/appwrite/client').then(module => module.AppwriteServices.getInstance());
        databases = clientDatabases;
      } else {
        // Server-side: system is fetching notifications (e.g., for admin panel)
        const appwriteServer = getAppwriteServer();
        databases = appwriteServer.getDatabases();
        
        if (!databases) {
          throw new Error('Appwrite databases service not available');
        }
      }
      
      const queries: string[] = [
        Query.equal('recipientId', userId)
      ];

      // Filter by read status
      if (options.unreadOnly) {
        queries.push(Query.equal('readAt', null));
      }

      // Filter by type
      if (options.type) {
        queries.push(Query.equal('type', options.type));
      }

      // Filter out expired notifications
      const now = new Date().toISOString();
      queries.push(Query.or([
        Query.equal('expiresAt', null),
        Query.greaterThan('expiresAt', now)
      ]));

      // Apply pagination
      const limit = options.limit || 50;
      const offset = options.offset || 0;

      const response = await databases.listDocuments(
        DATABASE_ID,
        NOTIFICATIONS_COLLECTION,
        [
          ...queries,
          Query.orderDesc('$createdAt'),
          Query.limit(limit),
          Query.offset(offset)
        ]
      );

      return response.documents.map(doc => this.mapAppwriteDocumentToNotificationRecord(doc));
    } catch (error) {
      logger.error('Failed to get notifications from Appwrite', { error, userId });
      return [];
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(userId: string, notificationId: string): Promise<boolean> {
    try {
      await this.initializeCollection();
      
      // Use client-side for user-specific operations (with proper permissions)
      // or server-side for admin/system operations
      const isClientSide = typeof window !== 'undefined';
      let databases;
      
      if (isClientSide) {
        // Client-side: user is marking their own notification as read
        // Use the AppwriteServices class to get the databases service
        const { databases: clientDatabases } = await import('@/lib/appwrite/client').then(module => module.AppwriteServices.getInstance());
        databases = clientDatabases;
      } else {
        // Server-side: system is marking notification as read (e.g., for admin panel)
        const appwriteServer = getAppwriteServer();
        databases = appwriteServer.getDatabases();
        
        if (!databases) {
          throw new Error('Appwrite databases service not available');
        }
      }
      
      // First, verify the notification belongs to the user
      const notification = await databases.getDocument(
        DATABASE_ID,
        NOTIFICATIONS_COLLECTION,
        notificationId
      );

      if (notification.recipientId !== userId) {
        logger.warn('Unauthorized attempt to mark notification as read', { userId, notificationId });
        return false;
      }

      // Update the notification
      const now = new Date().toISOString();
      await databases.updateDocument(
        DATABASE_ID,
        NOTIFICATIONS_COLLECTION,
        notificationId,
        {
          readAt: now,
          status: NotificationStatus.READ,
          updatedAt: now
        }
      );

      logger.info('Notification marked as read', { notificationId, userId });
      return true;
    } catch (error) {
      logger.error('Failed to mark notification as read in Appwrite', { error, notificationId, userId });
      return false;
    }
  }

  /**
   * Mark all notifications as read for a user
   */
  async markAllAsRead(userId: string): Promise<number> {
    try {
      await this.initializeCollection();
      
      // Use client-side for user-specific operations (with proper permissions)
      // or server-side for admin/system operations
      const isClientSide = typeof window !== 'undefined';
      let databases;
      
      if (isClientSide) {
        // Client-side: user is marking their own notifications as read
        // Use the AppwriteServices class to get the databases service
        const { databases: clientDatabases } = await import('@/lib/appwrite/client').then(module => module.AppwriteServices.getInstance());
        databases = clientDatabases;
      } else {
        // Server-side: system is marking notifications as read (e.g., for admin panel)
        const appwriteServer = getAppwriteServer();
        databases = appwriteServer.getDatabases();
        
        if (!databases) {
          throw new Error('Appwrite databases service not available');
        }
      }
      
      // Find all unread notifications for the user
      let markedCount = 0;
      let offset = 0;
      const limit = 100; // Process in batches
      let hasMore = true;
      const now = new Date().toISOString();
      
      while (hasMore) {
        const response = await databases.listDocuments(
          DATABASE_ID,
          NOTIFICATIONS_COLLECTION,
          [
            Query.equal('recipientId', userId),
            Query.equal('readAt', null),
            Query.limit(limit),
            Query.offset(offset)
          ]
        );
        
        // If no documents returned, we're done
        if (response.documents.length === 0) {
          hasMore = false;
          break;
        }
        
        // Mark each notification as read
        for (const doc of response.documents) {
          try {
            await databases.updateDocument(
              DATABASE_ID,
              NOTIFICATIONS_COLLECTION,
              doc.$id,
              {
                readAt: now,
                status: NotificationStatus.READ,
                updatedAt: now
              }
            );
            markedCount++;
          } catch (updateError) {
            // Log error but continue with other documents
            logger.error('Failed to mark notification as read', { 
              id: doc.$id, 
              error: errorUtils.createErrorMessage(updateError) 
            });
          }
        }
        
        // If we got fewer documents than the limit, we're done
        if (response.documents.length < limit) {
          hasMore = false;
        } else {
          // Otherwise, move to the next batch
          offset += limit;
        }
      }
      
      if (markedCount > 0) {
        logger.info('All notifications marked as read', { userId, count: markedCount });
      }

      return markedCount;
    } catch (error) {
      logger.error('Failed to mark all notifications as read in Appwrite', { error, userId });
      return 0;
    }
  }

  /**
   * Delete notification
   */
  async deleteNotification(userId: string, notificationId: string): Promise<boolean> {
    try {
      await this.initializeCollection();
      
      // Use client-side for user-specific operations (with proper permissions)
      // or server-side for admin/system operations
      const isClientSide = typeof window !== 'undefined';
      let databases;
      
      if (isClientSide) {
        // Client-side: user is deleting their own notification
        // Use the AppwriteServices class to get the databases service
        const { databases: clientDatabases } = await import('@/lib/appwrite/client').then(module => module.AppwriteServices.getInstance());
        databases = clientDatabases;
      } else {
        // Server-side: system is deleting notification (e.g., for admin panel)
        const appwriteServer = getAppwriteServer();
        databases = appwriteServer.getDatabases();
        
        if (!databases) {
          throw new Error('Appwrite databases service not available');
        }
      }
      
      // First, verify the notification belongs to the user
      const notification = await databases.getDocument(
        DATABASE_ID,
        NOTIFICATIONS_COLLECTION,
        notificationId
      );

      if (notification.recipientId !== userId) {
        logger.warn('Unauthorized attempt to delete notification', { userId, notificationId });
        return false;
      }

      // Delete the notification
      await databases.deleteDocument(
        DATABASE_ID,
        NOTIFICATIONS_COLLECTION,
        notificationId
      );

      logger.info('Notification deleted', { notificationId, userId });
      return true;
    } catch (error) {
      logger.error('Failed to delete notification in Appwrite', { error, notificationId, userId });
      return false;
    }
  }

  /**
   * Get unread count for a user
   */
  async getUnreadCount(userId: string): Promise<number> {
    try {
      await this.initializeCollection();
      
      // Use client-side for user-specific queries (with proper permissions)
      // or server-side for admin/system operations
      const isClientSide = typeof window !== 'undefined';
      let databases;
      
      if (isClientSide) {
        // Client-side: user is getting their own unread count
        // Use the AppwriteServices class to get the databases service
        const { databases: clientDatabases } = await import('@/lib/appwrite/client').then(module => module.AppwriteServices.getInstance());
        databases = clientDatabases;
      } else {
        // Server-side: system is getting unread count (e.g., for admin panel)
        const appwriteServer = getAppwriteServer();
        databases = appwriteServer.getDatabases();
        
        if (!databases) {
          throw new Error('Appwrite databases service not available');
        }
      }
      
      const now = new Date().toISOString();

      const response = await databases.listDocuments(
        DATABASE_ID,
        NOTIFICATIONS_COLLECTION,
        [
          Query.equal('recipientId', userId),
          Query.equal('readAt', null),
          Query.or([
            Query.equal('expiresAt', null),
            Query.greaterThan('expiresAt', now)
          ])
        ]
      );

      return response.total;
    } catch (error) {
      logger.error('Failed to get unread count from Appwrite', { error, userId });
      return 0;
    }
  }

  /**
   * Get notification by ID
   */
  async getNotificationById(notificationId: string): Promise<NotificationRecord | null> {
    try {
      await this.initializeCollection();
      
      // Use client-side for user-specific queries (with proper permissions)
      // or server-side for admin/system operations
      const isClientSide = typeof window !== 'undefined';
      let databases;
      
      if (isClientSide) {
        // Client-side: user is fetching notification
        // Use the AppwriteServices class to get the databases service
        const { databases: clientDatabases } = await import('@/lib/appwrite/client').then(module => module.AppwriteServices.getInstance());
        databases = clientDatabases;
      } else {
        // Server-side: system is fetching notification
        const appwriteServer = getAppwriteServer();
        databases = appwriteServer.getDatabases();
        
        if (!databases) {
          throw new Error('Appwrite databases service not available');
        }
      }
      
      const notification = await databases.getDocument(
        DATABASE_ID,
        NOTIFICATIONS_COLLECTION,
        notificationId
      );

      return this.mapAppwriteDocumentToNotificationRecord(notification);
    } catch (error) {
      logger.error('Failed to get notification from Appwrite', { error, notificationId });
      return null;
    }
  }

  /**
   * Get delivery status for notification
   */
  async getDeliveryStatus(messageId: string): Promise<DeliveryStatus> {
    try {
      await this.initializeCollection();
      
      const notification = await this.getNotificationById(messageId);
      
      if (!notification) {
        throw new Error('Notification not found');
      }

      return {
        status: notification.status,
        timestamp: notification.deliveredAt || notification.sentAt || notification.createdAt,
        metadata: {
          messageId,
          provider: 'appwrite',
          readAt: notification.readAt
        }
      };
    } catch (error) {
      logger.error('Failed to get delivery status from Appwrite', { error, messageId });
      return {
        status: NotificationStatus.FAILED,
        error: errorUtils.createErrorMessage(error)
      };
    }
  }

  /**
   * Validate notification
   */
  private validateNotification(notification: NotificationRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!notification.channel) {
      errors.push('Channel is required');
    }

    if (!notification.title) {
      errors.push('Title is required');
    }

    if (!notification.content) {
      errors.push('Content is required');
    }

    // Channel-specific validation
    if (notification.channel === NotificationChannel.EMAIL && !notification.recipientEmail) {
      errors.push('recipientEmail is required for email notifications');
    }

    if (notification.channel === NotificationChannel.SMS && !notification.recipientPhone) {
      errors.push('recipientPhone is required for SMS notifications');
    }

    if (notification.channel === NotificationChannel.IN_APP && !notification.recipientId) {
      errors.push('recipientId is required for in-app notifications');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Map Appwrite document to NotificationRecord
   */
  private mapAppwriteDocumentToNotificationRecord(doc: any): NotificationRecord {
    return {
      id: doc.$id,
      type: doc.type as NotificationType,
      channel: doc.channel as NotificationChannel,
      title: doc.title,
      content: doc.content,
      data: doc.data || {},
      recipientId: doc.recipientId,
      recipientType: doc.recipientType as RecipientType,
      recipientEmail: doc.recipientEmail,
      recipientPhone: doc.recipientPhone,
      status: doc.status as NotificationStatus,
      priority: doc.priority as NotificationPriority,
      templateId: doc.templateId,
      campaignId: doc.campaignId,
      scheduledAt: doc.scheduledAt ? new Date(doc.scheduledAt) : undefined,
      sentAt: doc.sentAt ? new Date(doc.sentAt) : undefined,
      deliveredAt: doc.deliveredAt ? new Date(doc.deliveredAt) : undefined,
      readAt: doc.readAt ? new Date(doc.readAt) : undefined,
      clickedAt: doc.clickedAt ? new Date(doc.clickedAt) : undefined,
      failedAt: doc.failedAt ? new Date(doc.failedAt) : undefined,
      retryCount: doc.retryCount || 0,
      maxRetries: doc.maxRetries || 3,
      error: doc.error,
      metadata: doc.metadata || {},
      expiresAt: doc.expiresAt ? new Date(doc.expiresAt) : undefined,
      createdAt: new Date(doc.$createdAt),
      updatedAt: new Date(doc.$updatedAt)
    };
  }

  /**
   * Validate Appwrite service configuration
   */
  async validateConfig(): Promise<boolean> {
    try {
      await this.initializeCollection();
      return this.initialized;
    } catch (error) {
      logger.error('Appwrite configuration validation failed', { error });
      return false;
    }
  }

  /**
   * Clean up expired notifications
   */
  async cleanupExpired(): Promise<number> {
    try {
      await this.initializeCollection();
      
      const appwriteServer = getAppwriteServer();
      const databases = appwriteServer.getDatabases();
      
      if (!databases) {
        throw new Error('Appwrite databases service not available');
      }

      const now = new Date().toISOString();
      let deletedCount = 0;
      let offset = 0;
      const limit = 100; // Process in batches of 100
      let hasMore = true;
      
      // Process in batches to handle large numbers of expired notifications
      while (hasMore) {
        // Find expired notifications
        const response = await databases.listDocuments(
          DATABASE_ID,
          NOTIFICATIONS_COLLECTION,
          [
            Query.notEqual('expiresAt', null),
            Query.lessThan('expiresAt', now),
            Query.limit(limit),
            Query.offset(offset)
          ]
        );

        // If no documents returned, we're done
        if (response.documents.length === 0) {
          hasMore = false;
          break;
        }

        // Delete each expired notification
        for (const doc of response.documents) {
          try {
            await databases.deleteDocument(
              DATABASE_ID,
              NOTIFICATIONS_COLLECTION,
              doc.$id
            );
            deletedCount++;
          } catch (deleteError) {
            // Log error but continue with other documents
            logger.error('Failed to delete expired notification', { 
              id: doc.$id, 
              error: errorUtils.createErrorMessage(deleteError) 
            });
          }
        }
        
        // If we got fewer documents than the limit, we're done
        if (response.documents.length < limit) {
          hasMore = false;
        } else {
          // Otherwise, move to the next batch
          offset += limit;
        }
        
        // Add a small delay to avoid rate limiting
        if (hasMore) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      if (deletedCount > 0) {
        logger.info('Expired notifications cleaned up', { count: deletedCount });
      }

      return deletedCount;
    } catch (error) {
      logger.error('Failed to cleanup expired notifications in Appwrite', { error });
      return 0;
    }
  }

  /**
   * Get Appwrite service statistics
   */
  async getStats(): Promise<{
    provider: string;
    configured: boolean;
    totalNotifications: number;
    totalUsers: number;
    unreadCount: number;
  }> {
    try {
      await this.initializeCollection();
      
      const appwriteServer = getAppwriteServer();
      const databases = appwriteServer.getDatabases();
      
      if (!databases) {
        throw new Error('Appwrite databases service not available');
      }

      // Get total notifications - just need the count, not the actual documents
      const totalResponse = await databases.listDocuments(
        DATABASE_ID,
        NOTIFICATIONS_COLLECTION,
        [Query.limit(1)]
      );

      // Get unread count - just need the count, not the actual documents
      const unreadResponse = await databases.listDocuments(
        DATABASE_ID,
        NOTIFICATIONS_COLLECTION,
        [
          Query.equal('readAt', null),
          Query.limit(1)
        ]
      );

      // Get unique users with pagination to handle large datasets
      const uniqueUsers = new Set<string>();
      let offset = 0;
      const limit = 100; // Process in batches
      let hasMore = true;
      
      while (hasMore) {
        const usersResponse = await databases.listDocuments(
          DATABASE_ID,
          NOTIFICATIONS_COLLECTION,
          [
            Query.select(['recipientId']),
            Query.limit(limit),
            Query.offset(offset)
          ]
        );
        
        // If no documents returned, we're done
        if (usersResponse.documents.length === 0) {
          hasMore = false;
          break;
        }
        
        // Add unique recipient IDs to the set
        usersResponse.documents.forEach(doc => {
          if (doc.recipientId) {
            uniqueUsers.add(doc.recipientId);
          }
        });
        
        // If we got fewer documents than the limit, we're done
        if (usersResponse.documents.length < limit) {
          hasMore = false;
        } else {
          // Otherwise, move to the next batch
          offset += limit;
        }
      }

      return {
        provider: 'appwrite',
        configured: this.initialized,
        totalNotifications: totalResponse.total,
        totalUsers: uniqueUsers.size,
        unreadCount: unreadResponse.total
      };
    } catch (error) {
      logger.error('Failed to get Appwrite notification stats', { error });
      return {
        provider: 'appwrite',
        configured: this.initialized,
        totalNotifications: 0,
        totalUsers: 0,
        unreadCount: 0
      };
    }
  }
}

// Export singleton instance
export const appwriteNotificationService = new AppwriteNotificationService();