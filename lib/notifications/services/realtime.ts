import { 
  NotificationRecord,
  NotificationChannel,
  NotificationStatus
} from '../types'
import { logger } from '../utils'
import { getAppwriteClient } from '@/lib/appwrite/client'
import { REALTIME_CHANNEL_PATTERNS } from '@/lib/appwrite/config'

// Define collection ID for notifications
const NOTIFICATIONS_COLLECTION = 'notifications'
const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID || 'main'

/**
 * Appwrite Realtime Notification Service
 * Handles real-time updates for notifications using Appwrite's realtime API
 */
export class RealtimeNotificationService {
  private subscribers: Map<string, ((notifications: NotificationRecord[]) => void)[]> = new Map()
  private unsubscribeFunctions: Map<string, () => void> = new Map()
  private notificationCache: Map<string, NotificationRecord[]> = new Map()
  private initialized = false

  constructor() {
    this.initialize()
  }

  /**
   * Initialize the realtime service
   */
  private initialize(): void {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined') {
        logger.warn('RealtimeNotificationService: Not initializing in server environment')
        return
      }

      this.initialized = true
      logger.info('RealtimeNotificationService initialized')
    } catch (error) {
      logger.error('Failed to initialize RealtimeNotificationService', { error })
    }
  }

  /**
   * Subscribe to real-time notification updates for a user
   */
  subscribe(userId: string, callback: (notifications: NotificationRecord[]) => void): () => void {
    try {
      if (!this.initialized) {
        this.initialize()
      }

      if (!userId) {
        logger.warn('RealtimeNotificationService: Cannot subscribe without userId')
        return () => {}
      }

      // Add to subscribers list
      if (!this.subscribers.has(userId)) {
        this.subscribers.set(userId, [])
        this.setupRealtimeListener(userId)
      }

      const userSubscribers = this.subscribers.get(userId)!
      userSubscribers.push(callback)
      this.subscribers.set(userId, userSubscribers)

      // Send initial notifications if available
      if (this.notificationCache.has(userId)) {
        const cachedNotifications = this.notificationCache.get(userId)!
        callback(cachedNotifications)
      } else {
        // Fetch initial notifications
        this.fetchNotifications(userId).then(notifications => {
          this.notificationCache.set(userId, notifications)
          callback(notifications)
        })
      }

      // Return unsubscribe function
      return () => {
        this.unsubscribe(userId, callback)
      }
    } catch (error) {
      logger.error('Failed to subscribe to notifications', { error, userId })
      return () => {}
    }
  }

  /**
   * Unsubscribe from real-time notification updates
   */
  private unsubscribe(userId: string, callback: (notifications: NotificationRecord[]) => void): void {
    try {
      const userSubscribers = this.subscribers.get(userId) || []
      const index = userSubscribers.indexOf(callback)
      
      if (index !== -1) {
        userSubscribers.splice(index, 1)
        this.subscribers.set(userId, userSubscribers)
      }

      // If no more subscribers for this user, remove the realtime listener
      if (userSubscribers.length === 0) {
        this.removeRealtimeListener(userId)
        this.subscribers.delete(userId)
        this.notificationCache.delete(userId)
      }
    } catch (error) {
      logger.error('Failed to unsubscribe from notifications', { error, userId })
    }
  }

  /**
   * Set up realtime listener for a user's notifications
   */
  private setupRealtimeListener(userId: string): void {
    try {
      const appwriteClient = getAppwriteClient()
      
      // Create channel pattern for notifications collection
      const channelPattern = REALTIME_CHANNEL_PATTERNS.COLLECTIONS(DATABASE_ID, NOTIFICATIONS_COLLECTION)
      
      // Subscribe to realtime updates
      const unsubscribe = appwriteClient.subscribe(channelPattern, response => {
        // Only process events for this user's notifications
        if (response.payload && response.payload.recipientId === userId) {
          this.handleRealtimeEvent(userId, response)
        }
      })

      // Store unsubscribe function
      this.unsubscribeFunctions.set(userId, unsubscribe)
      
      logger.info('Realtime notification listener set up', { userId, channel: channelPattern })
    } catch (error) {
      logger.error('Failed to set up realtime notification listener', { error, userId })
    }
  }

  /**
   * Remove realtime listener for a user
   */
  private removeRealtimeListener(userId: string): void {
    try {
      const unsubscribe = this.unsubscribeFunctions.get(userId)
      
      if (unsubscribe) {
        unsubscribe()
        this.unsubscribeFunctions.delete(userId)
        logger.info('Realtime notification listener removed', { userId })
      }
    } catch (error) {
      logger.error('Failed to remove realtime notification listener', { error, userId })
    }
  }

  /**
   * Handle realtime event
   */
  private handleRealtimeEvent(userId: string, event: any): void {
    try {
      const eventType = event.events[0] // Get the first event type
      const payload = event.payload

      // Get current notifications for this user
      let notifications = this.notificationCache.get(userId) || []
      
      // Handle different event types
      if (eventType.includes('create')) {
        // New notification created
        const newNotification = this.mapAppwriteDocumentToNotificationRecord(payload)
        notifications = [newNotification, ...notifications]
      } 
      else if (eventType.includes('update')) {
        // Notification updated
        const updatedNotification = this.mapAppwriteDocumentToNotificationRecord(payload)
        notifications = notifications.map(n => 
          n.id === updatedNotification.id ? updatedNotification : n
        )
      } 
      else if (eventType.includes('delete')) {
        // Notification deleted
        notifications = notifications.filter(n => n.id !== payload.$id)
      }

      // Update cache
      this.notificationCache.set(userId, notifications)
      
      // Notify subscribers
      this.notifySubscribers(userId, notifications)
      
      logger.debug('Processed realtime notification event', { 
        userId, 
        eventType, 
        notificationId: payload.$id 
      })
    } catch (error) {
      logger.error('Failed to handle realtime notification event', { error, userId, event })
    }
  }

  /**
   * Notify subscribers of notification updates
   */
  private notifySubscribers(userId: string, notifications: NotificationRecord[]): void {
    try {
      const subscribers = this.subscribers.get(userId) || []
      
      subscribers.forEach(callback => {
        try {
          callback(notifications)
        } catch (error) {
          logger.error('Error in notification subscriber callback', { error, userId })
        }
      })
    } catch (error) {
      logger.error('Failed to notify notification subscribers', { error, userId })
    }
  }

  /**
   * Fetch notifications for a user
   */
  private async fetchNotifications(userId: string): Promise<NotificationRecord[]> {
    try {
      const appwriteClient = getAppwriteClient()
      const databases = appwriteClient.databases
      
      // Get notifications for this user
      const response = await databases.listDocuments(
        DATABASE_ID,
        NOTIFICATIONS_COLLECTION,
        [
          `recipientId=${userId}`,
          'orderDesc=$createdAt',
          'limit=50'
        ]
      )

      return response.documents.map(doc => this.mapAppwriteDocumentToNotificationRecord(doc))
    } catch (error) {
      logger.error('Failed to fetch notifications', { error, userId })
      return []
    }
  }

  /**
   * Map Appwrite document to NotificationRecord
   */
  private mapAppwriteDocumentToNotificationRecord(doc: any): NotificationRecord {
    return {
      id: doc.$id,
      type: doc.type,
      channel: doc.channel,
      title: doc.title,
      content: doc.content,
      data: doc.data || {},
      recipientId: doc.recipientId,
      recipientType: doc.recipientType,
      recipientEmail: doc.recipientEmail,
      recipientPhone: doc.recipientPhone,
      status: doc.status,
      priority: doc.priority,
      templateId: doc.templateId,
      campaignId: doc.campaignId,
      scheduledAt: doc.scheduledAt ? new Date(doc.scheduledAt) : undefined,
      sentAt: doc.sentAt ? new Date(doc.sentAt) : undefined,
      deliveredAt: doc.deliveredAt ? new Date(doc.deliveredAt) : undefined,
      readAt: doc.readAt ? new Date(doc.readAt) : undefined,
      clickedAt: doc.clickedAt ? new Date(doc.clickedAt) : undefined,
      failedAt: doc.failedAt ? new Date(doc.failedAt) : undefined,
      retryCount: doc.retryCount || 0,
      maxRetries: doc.maxRetries || 3,
      error: doc.error,
      metadata: doc.metadata || {},
      expiresAt: doc.expiresAt ? new Date(doc.expiresAt) : undefined,
      createdAt: new Date(doc.$createdAt),
      updatedAt: new Date(doc.$updatedAt)
    }
  }

  /**
   * Mark notification as read
   */
  async markAsRead(userId: string, notificationId: string): Promise<boolean> {
    try {
      const appwriteClient = getAppwriteClient()
      const databases = appwriteClient.databases
      
      // Update the notification
      const now = new Date().toISOString()
      await databases.updateDocument(
        DATABASE_ID,
        NOTIFICATIONS_COLLECTION,
        notificationId,
        {
          readAt: now,
          status: NotificationStatus.READ,
          updatedAt: now
        }
      )

      // Update local cache
      if (this.notificationCache.has(userId)) {
        const notifications = this.notificationCache.get(userId)!
        const updatedNotifications = notifications.map(n => {
          if (n.id === notificationId) {
            return {
              ...n,
              readAt: new Date(now),
              status: NotificationStatus.READ,
              updatedAt: new Date(now)
            }
          }
          return n
        })
        this.notificationCache.set(userId, updatedNotifications)
      }

      return true
    } catch (error) {
      logger.error('Failed to mark notification as read', { error, notificationId, userId })
      return false
    }
  }

  /**
   * Get unread count for a user
   */
  getUnreadCount(userId: string): number {
    try {
      if (!this.notificationCache.has(userId)) {
        return 0
      }

      const notifications = this.notificationCache.get(userId)!
      const now = new Date()
      
      return notifications.filter(n => 
        !n.readAt && 
        (!n.expiresAt || n.expiresAt > now)
      ).length
    } catch (error) {
      logger.error('Failed to get unread count', { error, userId })
      return 0
    }
  }

  /**
   * Get notification by ID
   */
  getNotificationById(userId: string, notificationId: string): NotificationRecord | undefined {
    try {
      if (!this.notificationCache.has(userId)) {
        return undefined
      }

      const notifications = this.notificationCache.get(userId)!
      return notifications.find(n => n.id === notificationId)
    } catch (error) {
      logger.error('Failed to get notification by ID', { error, notificationId, userId })
      return undefined
    }
  }
}

// Export singleton instance
export const realtimeNotificationService = new RealtimeNotificationService()