import { 
  NotificationStatus, 
  NotificationPriority, 
  NotificationChannel,
  NotificationType,
  RecipientType
} from './types'

/**
 * Logger utility for notifications
 */
export const logger = {
  info: (message: string, data?: any) => {
    console.log(`[NOTIFICATION INFO] ${message}`, data ? JSON.stringify(data, null, 2) : '')
  },
  
  error: (message: string, data?: any) => {
    console.error(`[NOTIFICATION ERROR] ${message}`, data ? JSON.stringify(data, null, 2) : '')
  },
  
  warn: (message: string, data?: any) => {
    console.warn(`[NOTIFICATION WARN] ${message}`, data ? JSON.stringify(data, null, 2) : '')
  },
  
  debug: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[NOTIFICATION DEBUG] ${message}`, data ? JSON.stringify(data, null, 2) : '')
    }
  }
}

/**
 * Validation utilities
 */
export const validators = {
  /**
   * Validate email address
   */
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  /**
   * Validate South African phone number
   */
  isValidSAPhoneNumber: (phone: string): boolean => {
    // South African phone number patterns
    const saPhoneRegex = /^(\+27|0)[1-9][0-9]{8}$/
    return saPhoneRegex.test(phone.replace(/\s/g, ''))
  },

  /**
   * Validate international phone number
   */
  isValidPhoneNumber: (phone: string): boolean => {
    const phoneRegex = /^\+[1-9]\d{1,14}$/
    return phoneRegex.test(phone.replace(/\s/g, ''))
  },

  /**
   * Validate notification content
   */
  isValidContent: (content: string): boolean => {
    return content && content.trim().length > 0 && content.length <= 10000
  },

  /**
   * Validate notification title
   */
  isValidTitle: (title: string): boolean => {
    return title && title.trim().length > 0 && title.length <= 255
  }
}

/**
 * Formatting utilities
 */
export const formatters = {
  /**
   * Format phone number for South African standards
   */
  formatSAPhoneNumber: (phone: string): string => {
    const cleaned = phone.replace(/\D/g, '')
    
    if (cleaned.startsWith('27')) {
      return `+${cleaned}`
    } else if (cleaned.startsWith('0')) {
      return `+27${cleaned.substring(1)}`
    } else if (cleaned.length === 9) {
      return `+27${cleaned}`
    }
    
    return phone
  },

  /**
   * Format notification content for different channels
   */
  formatContentForChannel: (content: string, channel: NotificationChannel): string => {
    switch (channel) {
      case NotificationChannel.SMS:
        // Limit SMS to 160 characters
        return content.length > 160 ? content.substring(0, 157) + '...' : content
      
      case NotificationChannel.PUSH:
        // Limit push notifications to 100 characters
        return content.length > 100 ? content.substring(0, 97) + '...' : content
      
      case NotificationChannel.EMAIL:
      case NotificationChannel.IN_APP:
      default:
        return content
    }
  },

  /**
   * Format date for display
   */
  formatDate: (date: Date): string => {
    return new Intl.DateTimeFormat('en-ZA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Africa/Johannesburg'
    }).format(date)
  },

  /**
   * Format currency for South African Rand
   */
  formatCurrency: (amount: number): string => {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount)
  }
}

/**
 * Template variable utilities
 */
export const templateUtils = {
  /**
   * Extract variables from template content
   */
  extractVariables: (content: string): string[] => {
    const variableRegex = /\{\{([^}]+)\}\}/g
    const variables: string[] = []
    let match

    while ((match = variableRegex.exec(content)) !== null) {
      const variable = match[1].trim()
      if (!variables.includes(variable)) {
        variables.push(variable)
      }
    }

    return variables
  },

  /**
   * Replace variables in template content
   */
  replaceVariables: (content: string, variables: Record<string, any>): string => {
    let result = content

    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g')
      result = result.replace(regex, String(value))
    })

    return result
  },

  /**
   * Validate template variables
   */
  validateVariables: (content: string, variables: Record<string, any>): { isValid: boolean; missingVariables: string[] } => {
    const requiredVariables = templateUtils.extractVariables(content)
    const missingVariables = requiredVariables.filter(variable => !(variable in variables))

    return {
      isValid: missingVariables.length === 0,
      missingVariables
    }
  }
}

/**
 * Priority utilities
 */
export const priorityUtils = {
  /**
   * Get priority weight for sorting
   */
  getPriorityWeight: (priority: NotificationPriority): number => {
    switch (priority) {
      case NotificationPriority.URGENT:
        return 4
      case NotificationPriority.HIGH:
        return 3
      case NotificationPriority.NORMAL:
        return 2
      case NotificationPriority.LOW:
        return 1
      default:
        return 2
    }
  },

  /**
   * Compare priorities for sorting
   */
  comparePriorities: (a: NotificationPriority, b: NotificationPriority): number => {
    return priorityUtils.getPriorityWeight(b) - priorityUtils.getPriorityWeight(a)
  }
}

/**
 * Status utilities
 */
export const statusUtils = {
  /**
   * Check if status is final (no further processing needed)
   */
  isFinalStatus: (status: NotificationStatus): boolean => {
    return [
      NotificationStatus.DELIVERED,
      NotificationStatus.READ,
      NotificationStatus.CLICKED,
      NotificationStatus.FAILED,
      NotificationStatus.CANCELLED,
      NotificationStatus.EXPIRED
    ].includes(status)
  },

  /**
   * Check if status indicates success
   */
  isSuccessStatus: (status: NotificationStatus): boolean => {
    return [
      NotificationStatus.SENT,
      NotificationStatus.DELIVERED,
      NotificationStatus.READ,
      NotificationStatus.CLICKED
    ].includes(status)
  },

  /**
   * Check if status indicates failure
   */
  isFailureStatus: (status: NotificationStatus): boolean => {
    return [
      NotificationStatus.FAILED,
      NotificationStatus.CANCELLED,
      NotificationStatus.EXPIRED
    ].includes(status)
  },

  /**
   * Get next status in the delivery flow
   */
  getNextStatus: (current: NotificationStatus, channel: NotificationChannel): NotificationStatus | null => {
    switch (current) {
      case NotificationStatus.SCHEDULED:
        return NotificationStatus.PENDING
      case NotificationStatus.PENDING:
        return NotificationStatus.QUEUED
      case NotificationStatus.QUEUED:
        return NotificationStatus.SENDING
      case NotificationStatus.SENDING:
        return NotificationStatus.SENT
      case NotificationStatus.SENT:
        return channel === NotificationChannel.IN_APP ? NotificationStatus.DELIVERED : null
      default:
        return null
    }
  }
}

/**
 * Retry utilities
 */
export const retryUtils = {
  /**
   * Calculate retry delay with exponential backoff
   */
  calculateRetryDelay: (attempt: number, baseDelay: number = 60000): number => {
    return Math.min(baseDelay * Math.pow(2, attempt), 3600000) // Max 1 hour
  },

  /**
   * Check if notification should be retried
   */
  shouldRetry: (retryCount: number, maxRetries: number, status: NotificationStatus): boolean => {
    return retryCount < maxRetries && status === NotificationStatus.FAILED
  }
}

/**
 * Channel utilities
 */
export const channelUtils = {
  /**
   * Get channel display name
   */
  getChannelDisplayName: (channel: NotificationChannel): string => {
    switch (channel) {
      case NotificationChannel.EMAIL:
        return 'Email'
      case NotificationChannel.SMS:
        return 'SMS'
      case NotificationChannel.PUSH:
        return 'Push Notification'
      case NotificationChannel.IN_APP:
        return 'In-App Notification'
      case NotificationChannel.WEBHOOK:
        return 'Webhook'
      default:
        return 'Unknown'
    }
  },

  /**
   * Get channel icon
   */
  getChannelIcon: (channel: NotificationChannel): string => {
    switch (channel) {
      case NotificationChannel.EMAIL:
        return '📧'
      case NotificationChannel.SMS:
        return '📱'
      case NotificationChannel.PUSH:
        return '🔔'
      case NotificationChannel.IN_APP:
        return '💬'
      case NotificationChannel.WEBHOOK:
        return '🔗'
      default:
        return '❓'
    }
  }
}

/**
 * Type utilities
 */
export const typeUtils = {
  /**
   * Get notification type display name
   */
  getTypeDisplayName: (type: NotificationType): string => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  },

  /**
   * Get notification type category
   */
  getTypeCategory: (type: NotificationType): string => {
    if (type.includes('ORDER')) return 'Orders'
    if (type.includes('PAYMENT')) return 'Payments'
    if (type.includes('SHIPPING') || type.includes('DELIVERY')) return 'Shipping'
    if (type.includes('INVENTORY')) return 'Inventory'
    if (type.includes('PROMOTIONAL') || type.includes('NEWSLETTER')) return 'Marketing'
    if (type.includes('SECURITY') || type.includes('SYSTEM')) return 'System'
    if (type.includes('WELCOME') || type.includes('VERIFICATION')) return 'Account'
    return 'General'
  }
}

/**
 * Error utilities
 */
export const errorUtils = {
  /**
   * Create standardized error message
   */
  createErrorMessage: (error: unknown): string => {
    if (error instanceof Error) {
      return error.message
    }
    if (typeof error === 'string') {
      return error
    }
    return 'Unknown error occurred'
  },

  /**
   * Check if error is retryable
   */
  isRetryableError: (error: string): boolean => {
    const retryableErrors = [
      'timeout',
      'network',
      'connection',
      'rate limit',
      'temporary',
      'service unavailable'
    ]
    
    const lowerError = error.toLowerCase()
    return retryableErrors.some(retryable => lowerError.includes(retryable))
  }
}
