'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Bell, 
  Package, 
  ShoppingCart, 
  Users, 
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  DollarSign,
  X,
  Filter,
  RefreshCw,
  Search,
  ExternalLink
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useNotificationCenter } from '../hooks'
import { NotificationRecord, NotificationPriority, NotificationType } from '../types'
import { Skeleton } from '@/components/ui/skeleton'
import { Input } from '@/components/ui/input'
import { useRouter } from 'next/navigation'

interface NotificationSummaryProps {
  className?: string
  userId?: string
  enableRealTime?: boolean
  maxNotifications?: number
}

export function NotificationSummary({ 
  className,
  userId,
  enableRealTime = true,
  maxNotifications = 50
}: NotificationSummaryProps) {
  const router = useRouter()
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  
  const {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refresh,
    setSearchFilter,
    setUnreadOnlyFilter,
    activeFilters,
    isOpen,
    open,
    close,
    toggle
  } = useNotificationCenter({
    userId,
    enableRealTime,
    maxNotifications
  })

  // Map notification type to icon
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case NotificationType.ORDER_CONFIRMATION:
      case NotificationType.ORDER_STATUS_UPDATE:
        return ShoppingCart
      case NotificationType.INVENTORY_ALERT:
        return Package
      case NotificationType.WELCOME:
      case NotificationType.EMAIL_VERIFICATION:
        return Users
      case NotificationType.PAYMENT_CONFIRMATION:
      case NotificationType.PAYMENT_FAILED:
        return DollarSign
      case NotificationType.SHIPPING_UPDATE:
      case NotificationType.DELIVERY_CONFIRMATION:
        return TrendingUp
      case NotificationType.PROMOTIONAL:
      case NotificationType.NEWSLETTER:
        return Bell
      case NotificationType.PASSWORD_RESET:
        return AlertTriangle
      case NotificationType.CUSTOM:
        return CheckCircle
      default:
        return Bell
    }
  }

  // Map priority to color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case NotificationPriority.URGENT:
        return 'text-red-600'
      case NotificationPriority.HIGH:
        return 'text-orange-600'
      case NotificationPriority.NORMAL:
        return 'text-blue-600'
      case NotificationPriority.LOW:
        return 'text-gray-600'
      default:
        return 'text-gray-600'
    }
  }

  // Map priority to badge
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case NotificationPriority.URGENT:
        return <Badge variant="destructive" className="text-xs">Urgent</Badge>
      case NotificationPriority.HIGH:
        return <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800">High</Badge>
      case NotificationPriority.NORMAL:
        return <Badge variant="outline" className="text-xs">Normal</Badge>
      case NotificationPriority.LOW:
        return null
      default:
        return null
    }
  }

  // Format timestamp to relative time
  const formatTimestamp = (timestamp: string) => {
    const now = new Date()
    const notificationTime = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - notificationTime.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    
    return notificationTime.toLocaleDateString('en-ZA', {
      month: 'short',
      day: 'numeric',
      year: notificationTime.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    })
  }

  // Handle notification click
  const handleNotificationClick = async (notification: NotificationRecord) => {
    if (!notification.readAt) {
      await markAsRead(notification.id)
    }
    
    // Handle action URL if present in metadata
    const actionUrl = notification.metadata?.actionUrl
    if (actionUrl) {
      if (actionUrl.startsWith('http')) {
        window.open(actionUrl, '_blank')
      } else {
        router.push(actionUrl)
      }
    }
    
    close()
  }

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value
    setSearchQuery(query)
    setSearchFilter(query)
  }

  // Toggle search input visibility
  const toggleSearch = () => {
    setIsSearchOpen(!isSearchOpen)
    if (isSearchOpen) {
      setSearchQuery('')
      setSearchFilter('')
    }
  }

  // Handle filter toggle
  const toggleUnreadFilter = () => {
    setUnreadOnlyFilter(!activeFilters.unreadOnly)
  }

  // Handle refresh
  const handleRefresh = async () => {
    await refresh()
  }

  // Handle view all
  const handleViewAll = () => {
    router.push('/admin/notifications')
    close()
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={toggle}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className={cn("relative", className)}>
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80">
        <DropdownMenuLabel className="flex items-center justify-between">
          <span>Notifications</span>
          <div className="flex items-center space-x-1">
            {unreadCount > 0 && (
              <Button variant="ghost" size="sm" onClick={() => markAllAsRead()}>
                Mark all read
              </Button>
            )}
          </div>
        </DropdownMenuLabel>
        
        {/* Search and filter bar */}
        <div className="px-2 py-1 flex items-center justify-between border-b">
          <div className="flex items-center space-x-1">
            <Button 
              variant="ghost" 
              size="sm" 
              className={cn("h-8 w-8 p-0", activeFilters.unreadOnly && "bg-blue-50")}
              onClick={toggleUnreadFilter}
              title="Show unread only"
            >
              <Filter className="h-4 w-4" />
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-8 w-8 p-0"
              onClick={handleRefresh}
              title="Refresh notifications"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="flex items-center space-x-1">
            {isSearchOpen ? (
              <Input
                type="text"
                placeholder="Search..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="h-8 text-sm w-40"
              />
            ) : null}
            <Button 
              variant="ghost" 
              size="sm" 
              className={cn("h-8 w-8 p-0", isSearchOpen && "bg-blue-50")}
              onClick={toggleSearch}
              title="Search notifications"
            >
              <Search className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        {/* Notification list */}
        {loading ? (
          <div className="p-4 space-y-3">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-start space-x-3">
                <Skeleton className="h-4 w-4 mt-1" />
                <div className="flex-1 space-y-1">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-1/4" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="p-4 text-center text-sm text-red-500">
            Failed to load notifications. Please try again.
          </div>
        ) : notifications.length === 0 ? (
          <div className="p-4 text-center text-sm text-muted-foreground">
            {activeFilters.search || activeFilters.unreadOnly ? 
              'No notifications match your filters' : 
              'No notifications'}
          </div>
        ) : (
          <ScrollArea className="h-[calc(100vh-20rem)] max-h-96">
            {notifications.map((notification) => {
              const Icon = getNotificationIcon(notification.type)
              const isUnread = !notification.readAt
              
              return (
                <DropdownMenuItem
                  key={notification.id}
                  className={cn(
                    "flex items-start space-x-3 p-3 cursor-pointer",
                    isUnread && "bg-blue-50"
                  )}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className={cn("mt-0.5", getPriorityColor(notification.priority))}>
                    <Icon className="h-4 w-4" />
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium leading-none">
                        {notification.title}
                      </p>
                      <div className="flex items-center space-x-1">
                        {getPriorityBadge(notification.priority)}
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-4 w-4 p-0"
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteNotification(notification.id)
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {notification.content}
                    </p>
                    <div className="flex items-center justify-between">
                      <p className="text-xs text-muted-foreground">
                        {formatTimestamp(notification.createdAt)}
                      </p>
                      {notification.metadata?.actionUrl && (
                        <ExternalLink className="h-3 w-3 text-muted-foreground" />
                      )}
                    </div>
                  </div>
                  {isUnread && (
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2" />
                  )}
                </DropdownMenuItem>
              )
            })}
          </ScrollArea>
        )}
        
        <DropdownMenuSeparator />
        <DropdownMenuItem className="text-center justify-center" onClick={handleViewAll}>
          <Button variant="ghost" size="sm" className="w-full">
            View All Notifications
          </Button>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
