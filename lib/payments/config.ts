import type { 
  PaymentGatewayConfig, 
  SecurityConfig, 
  ComplianceData, 
  RateLimitConfig
} from './types'
import { PaymentGateway, PaymentMethod } from './types'

// Environment validation
const requiredEnvVars = [
  'PAYFAST_MERCHANT_ID',
  'PAYFAST_MERCHANT_KEY',
  'PAYFAST_PASSPHRASE',
] as const

// Payment gateway configurations
export const paymentConfig: PaymentGatewayConfig = {
  payfast: {
    merchantId: process.env.PAYFAST_MERCHANT_ID || '',
    merchantKey: process.env.PAYFAST_MERCHANT_KEY || '',
    passphrase: process.env.PAYFAST_PASSPHRASE || '',
    sandbox: process.env.NODE_ENV !== 'production',
  },
  ozow: {
    apiKey: process.env.OZOW_API_KEY || '',
    privateKey: process.env.OZOW_PRIVATE_KEY || '',
    siteCode: process.env.OZOW_SITE_CODE || '',
    sandbox: process.env.NODE_ENV !== 'production',
  },
  snapscan: {
    apiKey: process.env.SNAPSCAN_API_KEY || '',
    merchantId: process.env.SNAPSCAN_MERCHANT_ID || '',
    sandbox: process.env.NODE_ENV !== 'production',
  },
  yoco: {
    secretKey: process.env.YOCO_SECRET_KEY || '',
    publicKey: process.env.YOCO_PUBLIC_KEY || '',
    sandbox: process.env.NODE_ENV !== 'production',
  },
  payu: {
    apiKey: process.env.PAYU_API_KEY || '',
    safeKey: process.env.PAYU_SAFE_KEY || '',
    merchantId: process.env.PAYU_MERCHANT_ID || '',
    sandbox: process.env.NODE_ENV !== 'production',
  },
}

// Security configuration
export const securityConfig: SecurityConfig = {
  encryptionKey: process.env.PAYMENT_ENCRYPTION_KEY || 'default-key-change-in-production',
  hashAlgorithm: 'sha256',
  tokenExpiry: 3600, // 1 hour
  maxRetries: 3,
  rateLimitWindow: 15 * 60 * 1000, // 15 minutes
  rateLimitMax: 100,
}

// Compliance configuration for South Africa
export const complianceConfig: ComplianceData = {
  pciCompliant: true,
  popiaCompliant: true,
  vatNumber: process.env.SA_VAT_NUMBER || '',
  businessRegistration: process.env.SA_BUSINESS_REGISTRATION || '',
  auditTrail: true,
}

// Rate limiting configuration
export const rateLimitConfig: RateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many payment requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
}

// Gateway URLs
export const GATEWAY_URLS = {
  payfast: {
    sandbox: 'https://sandbox.payfast.co.za/eng/process',
    production: 'https://www.payfast.co.za/eng/process',
    api: {
      sandbox: 'https://api.payfast.co.za',
      production: 'https://api.payfast.co.za',
    },
  },
  ozow: {
    sandbox: 'https://api.ozow.com/postpaymentrequest',
    production: 'https://api.ozow.com/postpaymentrequest',
    api: {
      sandbox: 'https://api.ozow.com',
      production: 'https://api.ozow.com',
    },
  },
  snapscan: {
    sandbox: 'https://pos.snapscan.co.za/merchant/api/v1',
    production: 'https://pos.snapscan.co.za/merchant/api/v1',
  },
  yoco: {
    sandbox: 'https://online.yoco.co.za/v1',
    production: 'https://online.yoco.co.za/v1',
  },
  payu: {
    sandbox: 'https://staging.payu.co.za/api',
    production: 'https://secure.payu.co.za/api',
  },
} as const

// Supported currencies (South African focus)
export const SUPPORTED_CURRENCIES = ['ZAR', 'USD', 'EUR', 'GBP'] as const

// Payment method configurations
export const PAYMENT_METHOD_CONFIG = {
  [PaymentMethod.CARD]: {
    displayName: 'Credit/Debit Card',
    description: 'Pay securely with your credit or debit card',
    icon: 'credit-card',
    supportedGateways: [PaymentGateway.PAYFAST, PaymentGateway.YOCO, PaymentGateway.PAYU],
    processingTime: '1-2 minutes',
    fees: {
      percentage: 2.9,
      fixed: 0,
    },
  },
  [PaymentMethod.EFT]: {
    displayName: 'EFT/Bank Transfer',
    description: 'Electronic Funds Transfer from your bank account',
    icon: 'bank',
    supportedGateways: [PaymentGateway.PAYFAST, PaymentGateway.OZOW],
    processingTime: '1-3 business days',
    fees: {
      percentage: 1.5,
      fixed: 0,
    },
  },
  [PaymentMethod.INSTANT_EFT]: {
    displayName: 'Instant EFT',
    description: 'Instant payment directly from your bank account',
    icon: 'zap',
    supportedGateways: [PaymentGateway.OZOW],
    processingTime: 'Instant',
    fees: {
      percentage: 1.5,
      fixed: 0,
    },
  },
  [PaymentMethod.QR_CODE]: {
    displayName: 'QR Code Payment',
    description: 'Scan QR code with your banking app',
    icon: 'qr-code',
    supportedGateways: [PaymentGateway.SNAPSCAN],
    processingTime: 'Instant',
    fees: {
      percentage: 2.5,
      fixed: 0,
    },
  },
  [PaymentMethod.BANK_TRANSFER]: {
    displayName: 'Bank Transfer',
    description: 'Direct bank-to-bank transfer',
    icon: 'bank',
    supportedGateways: [PaymentGateway.PAYFAST],
    processingTime: '1-3 business days',
    fees: {
      percentage: 1.0,
      fixed: 0,
    },
  },
  [PaymentMethod.MOBILE_MONEY]: {
    displayName: 'Mobile Money',
    description: 'Pay using mobile money services',
    icon: 'smartphone',
    supportedGateways: [],
    processingTime: 'Instant',
    fees: {
      percentage: 2.0,
      fixed: 0,
    },
  },
  [PaymentMethod.CRYPTO]: {
    displayName: 'Cryptocurrency',
    description: 'Pay with Bitcoin or other cryptocurrencies',
    icon: 'bitcoin',
    supportedGateways: [],
    processingTime: '10-60 minutes',
    fees: {
      percentage: 1.0,
      fixed: 0,
    },
  },
} as const

// Gateway priority order (for fallback)
export const GATEWAY_PRIORITY = [
  PaymentGateway.PAYFAST,
  PaymentGateway.OZOW,
  PaymentGateway.YOCO,
  PaymentGateway.SNAPSCAN,
  PaymentGateway.PAYU,
] as const

// Minimum and maximum transaction amounts (in ZAR)
export const TRANSACTION_LIMITS = {
  min: 1.00,
  max: 50000.00,
  dailyLimit: 100000.00,
  monthlyLimit: 500000.00,
} as const

// Webhook endpoints
export const WEBHOOK_ENDPOINTS = {
  payfast: '/api/webhooks/payfast',
  ozow: '/api/webhooks/ozow',
  snapscan: '/api/webhooks/snapscan',
  yoco: '/api/webhooks/yoco',
  payu: '/api/webhooks/payu',
} as const

// Error messages
export const PAYMENT_ERROR_MESSAGES = {
  INVALID_AMOUNT: 'Invalid payment amount',
  INVALID_CURRENCY: 'Currency not supported',
  INVALID_CUSTOMER: 'Invalid customer information',
  GATEWAY_ERROR: 'Payment gateway error',
  NETWORK_ERROR: 'Network connection error',
  AUTHENTICATION_ERROR: 'Authentication failed',
  INSUFFICIENT_FUNDS: 'Insufficient funds',
  CARD_DECLINED: 'Card declined by bank',
  EXPIRED_CARD: 'Card has expired',
  INVALID_CARD: 'Invalid card details',
  FRAUD_DETECTED: 'Transaction flagged as potentially fraudulent',
  RATE_LIMITED: 'Too many requests, please try again later',
  MAINTENANCE: 'Payment system under maintenance',
  UNKNOWN_ERROR: 'An unexpected error occurred',
} as const

// Success messages
export const PAYMENT_SUCCESS_MESSAGES = {
  PAYMENT_INITIATED: 'Payment initiated successfully',
  PAYMENT_COMPLETED: 'Payment completed successfully',
  REFUND_INITIATED: 'Refund initiated successfully',
  REFUND_COMPLETED: 'Refund completed successfully',
} as const

// Timeout configurations (in milliseconds)
export const TIMEOUT_CONFIG = {
  paymentRequest: 30000, // 30 seconds
  webhookResponse: 5000, // 5 seconds
  statusCheck: 10000, // 10 seconds
  refundRequest: 30000, // 30 seconds
} as const

// Retry configurations
export const RETRY_CONFIG = {
  maxAttempts: 3,
  backoffMultiplier: 2,
  initialDelay: 1000, // 1 second
  maxDelay: 10000, // 10 seconds
} as const

// Logging configuration
export const LOGGING_CONFIG = {
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: 'json',
  maxFiles: 10,
  maxSize: '10m',
  datePattern: 'YYYY-MM-DD',
} as const

// South African banking details for EFT
export const SA_BANKING_CONFIG = {
  supportedBanks: [
    'ABSA',
    'Standard Bank',
    'FNB',
    'Nedbank',
    'Capitec',
    'African Bank',
    'Bidvest Bank',
    'Discovery Bank',
    'TymeBank',
    'Bank Zero',
  ],
  clearingCodes: {
    'ABSA': '632005',
    'Standard Bank': '051001',
    'FNB': '250655',
    'Nedbank': '198765',
    'Capitec': '470010',
  },
} as const

// VAT configuration for South Africa
export const VAT_CONFIG = {
  rate: 0.15, // 15% VAT
  inclusive: true, // Prices include VAT
  number: process.env.SA_VAT_NUMBER || '',
  registration: process.env.SA_VAT_REGISTRATION || '',
} as const

// Validation functions
export function validateEnvironmentVariables(): void {
  const missingVars = requiredEnvVars.filter(
    (varName) => !process.env[varName]
  )

  if (missingVars.length > 0) {
    console.warn(
      `Missing payment environment variables: ${missingVars.join(', ')}`
    )
  }
}

export function getGatewayUrl(gateway: PaymentGateway, endpoint?: string): string {
  const config = GATEWAY_URLS[gateway]
  const isSandbox = process.env.NODE_ENV !== 'production'
  
  if (endpoint && 'api' in config) {
    return `${config.api[isSandbox ? 'sandbox' : 'production']}${endpoint}`
  }
  
  return isSandbox ? config.sandbox : config.production
}

export function isGatewayEnabled(gateway: PaymentGateway): boolean {
  const config = paymentConfig[gateway]
  if (!config) return false
  
  // Check if required configuration is present
  switch (gateway) {
    case PaymentGateway.PAYFAST:
      const payfastConfig = config as any
      return !!(payfastConfig.merchantId && payfastConfig.merchantKey && payfastConfig.passphrase)
    case PaymentGateway.OZOW:
      const ozowConfig = config as any
      return !!(ozowConfig.apiKey && ozowConfig.privateKey && ozowConfig.siteCode)
    case PaymentGateway.SNAPSCAN:
      const snapscanConfig = config as any
      return !!(snapscanConfig.apiKey && snapscanConfig.merchantId)
    case PaymentGateway.YOCO:
      const yocoConfig = config as any
      return !!(yocoConfig.secretKey && yocoConfig.publicKey)
    case PaymentGateway.PAYU:
      const payuConfig = config as any
      return !!(payuConfig.apiKey && payuConfig.safeKey && payuConfig.merchantId)
    default:
      return false
  }
}

export function getEnabledGateways(): PaymentGateway[] {
  return GATEWAY_PRIORITY.filter(isGatewayEnabled)
}

export function calculateTransactionFee(
  amount: number, 
  method: PaymentMethod
): number {
  const config = PAYMENT_METHOD_CONFIG[method]
  if (!config) return 0
  
  const percentageFee = (amount * config.fees.percentage) / 100
  return percentageFee + config.fees.fixed
}

// All exports are already available above
