// Payment Logger
import { PaymentLog, PaymentGateway } from './types'

interface LogContext {
  gateway?: PaymentGateway
  transactionId?: string
  orderId?: string
  customerId?: string
  reference?: string
  metadata?: any
  [key: string]: any
}

class PaymentLogger {
  private logs: PaymentLog[] = []

  private createLog(
    level: 'info' | 'warn' | 'error' | 'debug',
    message: string,
    context?: LogContext
  ): PaymentLog {
    return {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      level,
      message,
      gateway: context?.gateway,
      transactionId: context?.transactionId,
      orderId: context?.orderId,
      customerId: context?.customerId,
      metadata: context,
      timestamp: new Date(),
    }
  }

  info(message: string, context?: LogContext) {
    const log = this.createLog('info', message, context)
    this.logs.push(log)
    
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[PAYMENT INFO] ${message}`, context || '')
    }
  }

  warn(message: string, context?: LogContext) {
    const log = this.createLog('warn', message, context)
    this.logs.push(log)
    
    console.warn(`[PAYMENT WARN] ${message}`, context || '')
  }

  error(message: string, context?: LogContext) {
    const log = this.createLog('error', message, context)
    this.logs.push(log)
    
    console.error(`[PAYMENT ERROR] ${message}`, context || '')
  }

  debug(message: string, context?: LogContext) {
    const log = this.createLog('debug', message, context)
    this.logs.push(log)
    
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[PAYMENT DEBUG] ${message}`, context || '')
    }
  }

  getLogs(filter?: {
    level?: 'info' | 'warn' | 'error' | 'debug'
    gateway?: PaymentGateway
    transactionId?: string
    orderId?: string
    limit?: number
  }): PaymentLog[] {
    let filteredLogs = [...this.logs]

    if (filter?.level) {
      filteredLogs = filteredLogs.filter(log => log.level === filter.level)
    }

    if (filter?.gateway) {
      filteredLogs = filteredLogs.filter(log => log.gateway === filter.gateway)
    }

    if (filter?.transactionId) {
      filteredLogs = filteredLogs.filter(log => log.transactionId === filter.transactionId)
    }

    if (filter?.orderId) {
      filteredLogs = filteredLogs.filter(log => log.orderId === filter.orderId)
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())

    if (filter?.limit) {
      filteredLogs = filteredLogs.slice(0, filter.limit)
    }

    return filteredLogs
  }

  clearLogs() {
    this.logs = []
  }
}

// Export singleton instance
export const logger = new PaymentLogger()

// Export class for testing
export { PaymentLogger }