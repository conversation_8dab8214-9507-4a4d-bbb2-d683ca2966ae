import crypto from 'crypto'
import { 
  IPaymentGateway, 
  PaymentGateway, 
  PaymentMethod, 
  PaymentRequest, 
  PaymentResponse, 
  PaymentStatus,
  PaymentValidationResult,
  WebhookPayload,
  OzowConfig
} from '../types'
import { paymentConfig, getGatewayUrl } from '../config'
import { logger } from '../logger'

export class OzowGateway implements IPaymentGateway {
  name = PaymentGateway.OZOW
  displayName = 'Ozow'
  supportedMethods = [PaymentMethod.INSTANT_EFT, PaymentMethod.EFT]
  supportedCurrencies = ['ZAR']

  private config: OzowConfig

  constructor() {
    this.config = paymentConfig.ozow as OzowConfig
    if (!this.validateConfig()) {
      logger.warn('Ozow configuration is incomplete', { gateway: this.name })
    }
  }

  validateConfig(): boolean {
    return !!(
      this.config?.apiKey &&
      this.config?.privateKey &&
      this.config?.siteCode
    )
  }

  validatePaymentRequest(request: PaymentRequest): PaymentValidationResult {
    const errors: string[] = []

    // Amount validation
    if (!request.amount || request.amount.amount <= 0) {
      errors.push('Invalid amount')
    }

    if (request.amount.currency !== 'ZAR') {
      errors.push('Ozow only supports ZAR currency')
    }

    // Customer validation
    if (!request.customer.email) {
      errors.push('Customer email is required')
    }

    if (!request.customer.firstName || !request.customer.lastName) {
      errors.push('Customer name is required')
    }

    // URLs validation
    if (!request.returnUrl || !request.cancelUrl) {
      errors.push('Return and cancel URLs are required')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      logger.info('Creating Ozow payment', {
        gateway: this.name,
        reference: request.reference,
        amount: request.amount.amount
      })

      // Validate request
      const validation = this.validatePaymentRequest(request)
      if (!validation.isValid) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: {
            code: 'VALIDATION_ERROR',
            message: validation.errors.join(', ')
          }
        }
      }

      // Create Ozow payment data
      const paymentData = this.createPaymentData(request)
      
      // Generate hash check
      const hashCheck = this.generateHashCheck(paymentData)
      paymentData.HashCheck = hashCheck

      // Create payment URL
      const baseUrl = getGatewayUrl(PaymentGateway.OZOW)
      const queryString = new URLSearchParams(paymentData).toString()
      const paymentUrl = `${baseUrl}?${queryString}`

      // Generate transaction ID
      const transactionId = `ozow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      logger.info('Ozow payment URL created', {
        gateway: this.name,
        reference: request.reference,
        transactionId,
        hasHashCheck: !!hashCheck
      })

      return {
        success: true,
        paymentUrl,
        reference: request.reference,
        transactionId,
        status: PaymentStatus.PENDING,
        message: 'Payment URL created successfully'
      }

    } catch (error) {
      logger.error('Ozow payment creation failed', {
        gateway: this.name,
        reference: request.reference,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: 'GATEWAY_ERROR',
          message: 'Failed to create payment'
        }
      }
    }
  }

  async getPaymentStatus(transactionId: string): Promise<PaymentStatus> {
    try {
      logger.info('Getting Ozow payment status', {
        gateway: this.name,
        transactionId
      })

      // In a real implementation, you would call Ozow API to get status
      // For now, return pending as we don't have the actual API implementation
      return PaymentStatus.PENDING
    } catch (error) {
      logger.error('Failed to get Ozow payment status', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return PaymentStatus.FAILED
    }
  }

  async refundPayment(transactionId: string, amount?: number): Promise<PaymentResponse> {
    try {
      logger.info('Processing Ozow refund', {
        gateway: this.name,
        transactionId,
        amount
      })

      // Ozow refunds would require API integration
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: 'Automated refunds not implemented. Please contact Ozow support.'
        }
      }
    } catch (error) {
      logger.error('Ozow refund failed', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: 'REFUND_ERROR',
          message: 'Failed to process refund'
        }
      }
    }
  }

  verifyWebhook(payload: any, signature: string): boolean {
    try {
      // Generate expected hash check
      const expectedHash = this.generateWebhookHash(payload)
      
      return expectedHash === signature
    } catch (error) {
      logger.error('Ozow webhook verification failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return false
    }
  }

  async processWebhook(payload: WebhookPayload): Promise<void> {
    try {
      logger.info('Processing Ozow webhook', {
        gateway: this.name,
        event: payload.event,
        transactionId: payload.data.TransactionId
      })

      // Here you would typically:
      // 1. Update payment status in database
      // 2. Send notifications
      // 3. Update order status
      // 4. Trigger business logic

      logger.info('Ozow webhook processed', {
        gateway: this.name,
        event: payload.event,
        status: payload.data.Status,
        transactionId: payload.data.TransactionId
      })

    } catch (error) {
      logger.error('Ozow webhook processing failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      throw error
    }
  }

  private createPaymentData(request: PaymentRequest): Record<string, string> {
    const amount = (request.amount.amount * 100).toString() // Ozow expects amount in cents

    return {
      // Site details
      SiteCode: this.config.siteCode,
      CountryCode: 'ZA',
      CurrencyCode: 'ZAR',
      
      // Payment details
      Amount: amount,
      TransactionReference: request.reference,
      BankReference: request.reference,
      
      // Customer details
      Customer: `${request.customer.firstName} ${request.customer.lastName}`,
      
      // URLs
      SuccessUrl: request.returnUrl,
      CancelUrl: request.cancelUrl,
      ErrorUrl: request.cancelUrl,
      NotifyUrl: request.notifyUrl,
      
      // Optional fields
      IsTest: this.config.sandbox ? 'true' : 'false',
    }
  }

  private generateHashCheck(data: Record<string, string>): string {
    // Create the string to hash according to Ozow specification
    const stringToHash = [
      data.SiteCode,
      data.CountryCode,
      data.CurrencyCode,
      data.Amount,
      data.TransactionReference,
      data.BankReference,
      this.config.privateKey
    ].join('')

    // Generate SHA512 hash
    return crypto.createHash('sha512').update(stringToHash, 'utf8').digest('hex').toLowerCase()
  }

  private generateWebhookHash(data: any): string {
    // Generate hash for webhook verification
    const stringToHash = [
      data.SiteCode,
      data.TransactionId,
      data.TransactionReference,
      data.Amount,
      data.Status,
      data.Optional1 || '',
      data.Optional2 || '',
      data.Optional3 || '',
      data.CurrencyCode,
      data.IsTest,
      data.StatusMessage,
      this.config.privateKey
    ].join('')

    return crypto.createHash('sha512').update(stringToHash, 'utf8').digest('hex').toLowerCase()
  }
}