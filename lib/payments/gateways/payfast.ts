import crypto from 'crypto'
import { 
  IPaymentGateway, 
  PaymentGateway, 
  PaymentMethod, 
  PaymentRequest, 
  PaymentResponse, 
  PaymentStatus,
  PaymentValidationResult,
  WebhookPayload,
  PayFastConfig
} from '../types'
import { paymentConfig, getGatewayUrl } from '../config'
import { logger } from '../logger'

export class PayFastGateway implements IPaymentGateway {
  name = PaymentGateway.PAYFAST
  displayName = 'PayFast'
  supportedMethods = [PaymentMethod.CARD, PaymentMethod.EFT]
  supportedCurrencies = ['ZAR']

  private config: PayFastConfig

  constructor() {
    this.config = paymentConfig.payfast as PayFastConfig
    if (!this.validateConfig()) {
      logger.warn('PayFast configuration is incomplete', { gateway: this.name })
    }
  }

  validateConfig(): boolean {
    return !!(
      this.config?.merchantId &&
      this.config?.merchantKey &&
      this.config?.passphrase
    )
  }

  validatePaymentRequest(request: PaymentRequest): PaymentValidationResult {
    const errors: string[] = []

    // Amount validation
    if (!request.amount || request.amount.amount <= 0) {
      errors.push('Invalid amount')
    }

    if (request.amount.currency !== 'ZAR') {
      errors.push('PayFast only supports ZAR currency')
    }

    // Customer validation
    if (!request.customer.email) {
      errors.push('Customer email is required')
    }

    if (!request.customer.firstName || !request.customer.lastName) {
      errors.push('Customer name is required')
    }

    // URLs validation
    if (!request.returnUrl || !request.cancelUrl) {
      errors.push('Return and cancel URLs are required')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  async createPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      logger.info('Creating PayFast payment', {
        gateway: this.name,
        reference: request.reference,
        amount: request.amount.amount
      })

      // Validate request
      const validation = this.validatePaymentRequest(request)
      if (!validation.isValid) {
        return {
          success: false,
          status: PaymentStatus.FAILED,
          error: {
            code: 'VALIDATION_ERROR',
            message: validation.errors.join(', ')
          }
        }
      }

      // Create PayFast payment data
      const paymentData = this.createPaymentData(request)
      
      // Generate signature
      const signature = this.generateSignature(paymentData)
      paymentData.signature = signature

      // Create payment URL
      const baseUrl = getGatewayUrl(PaymentGateway.PAYFAST)
      const queryString = new URLSearchParams(paymentData).toString()
      const paymentUrl = `${baseUrl}?${queryString}`

      logger.info('PayFast payment URL created', {
        gateway: this.name,
        reference: request.reference,
        hasSignature: !!signature
      })

      return {
        success: true,
        paymentUrl,
        reference: request.reference,
        status: PaymentStatus.PENDING,
        message: 'Payment URL created successfully'
      }

    } catch (error) {
      logger.error('PayFast payment creation failed', {
        gateway: this.name,
        reference: request.reference,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: 'GATEWAY_ERROR',
          message: 'Failed to create payment'
        }
      }
    }
  }

  async getPaymentStatus(transactionId: string): Promise<PaymentStatus> {
    try {
      // In a real implementation, you would call PayFast API to get status
      // For now, return pending as we don't have the actual API implementation
      logger.info('Getting PayFast payment status', {
        gateway: this.name,
        transactionId
      })

      return PaymentStatus.PENDING
    } catch (error) {
      logger.error('Failed to get PayFast payment status', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return PaymentStatus.FAILED
    }
  }

  async refundPayment(transactionId: string, amount?: number): Promise<PaymentResponse> {
    try {
      logger.info('Processing PayFast refund', {
        gateway: this.name,
        transactionId,
        amount
      })

      // PayFast refunds are typically handled through their merchant portal
      // This would require API integration for automated refunds
      
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: 'Automated refunds not implemented. Please process through PayFast merchant portal.'
        }
      }
    } catch (error) {
      logger.error('PayFast refund failed', {
        gateway: this.name,
        transactionId,
        error: error instanceof Error ? error.message : 'Unknown error'
      })

      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: 'REFUND_ERROR',
          message: 'Failed to process refund'
        }
      }
    }
  }

  verifyWebhook(payload: any, signature: string): boolean {
    try {
      // Remove signature from payload for verification
      const { signature: _, ...dataToVerify } = payload
      
      // Generate expected signature
      const expectedSignature = this.generateSignature(dataToVerify)
      
      return expectedSignature === signature
    } catch (error) {
      logger.error('PayFast webhook verification failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      return false
    }
  }

  async processWebhook(payload: WebhookPayload): Promise<void> {
    try {
      logger.info('Processing PayFast webhook', {
        gateway: this.name,
        event: payload.event,
        reference: payload.data.m_payment_id
      })

      // Here you would typically:
      // 1. Update payment status in database
      // 2. Send notifications
      // 3. Update order status
      // 4. Trigger business logic

      // For now, just log the webhook
      logger.info('PayFast webhook processed', {
        gateway: this.name,
        event: payload.event,
        status: payload.data.payment_status,
        reference: payload.data.m_payment_id
      })

    } catch (error) {
      logger.error('PayFast webhook processing failed', {
        gateway: this.name,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      
      throw error
    }
  }

  private createPaymentData(request: PaymentRequest): Record<string, string> {
    return {
      // Merchant details
      merchant_id: this.config.merchantId,
      merchant_key: this.config.merchantKey,
      
      // Payment details
      amount: request.amount.amount.toFixed(2),
      item_name: request.description,
      item_description: request.items.map(item => `${item.name} x${item.quantity}`).join(', '),
      
      // Customer details
      name_first: request.customer.firstName,
      name_last: request.customer.lastName,
      email_address: request.customer.email,
      
      // URLs
      return_url: request.returnUrl,
      cancel_url: request.cancelUrl,
      notify_url: request.notifyUrl,
      
      // Custom fields
      custom_str1: request.reference,
      custom_str2: request.metadata.orderId,
      custom_str3: request.metadata.customerId || '',
      
      // Payment reference
      m_payment_id: request.reference,
    }
  }

  private generateSignature(data: Record<string, string>): string {
    // Create parameter string
    const paramString = Object.keys(data)
      .sort()
      .map(key => `${key}=${encodeURIComponent(data[key]).replace(/%20/g, '+')}`)
      .join('&')

    // Add passphrase
    const stringToHash = `${paramString}&passphrase=${encodeURIComponent(this.config.passphrase)}`

    // Generate MD5 hash
    return crypto.createHash('md5').update(stringToHash).digest('hex')
  }
}