import { EmailBlockBase, EmailBlockType, TemplateVariable } from '../types';
import { get }  from 'lodash';

/**
 * Parses a string and replaces variables with their values
 * Variables are in the format {{variableName}}
 */
export function parseVariables(
  text: string,
  variables: TemplateVariable[],
  data: Record<string, any>
): string {
  if (!text) return '';
  
  // Find all variables in the text
  const regex = /\{\{([^}]+)\}\}/g;
  
  return text.replace(regex, (match, variablePath) => {
    // Find the variable definition
    const variable = variables.find(v => v.key === variablePath.trim());
    
    if (!variable) {
      // Variable not found, return the original match
      return match;
    }
    
    // Get the value from the data
    let value;
    
    if (variable.source && variable.path) {
      // Get from nested path in the data
      value = get(data[variable.source], variable.path);
    } else {
      // Direct access to the data
      value = data[variablePath.trim()];
    }
    
    // Use default value if the value is undefined
    if (value === undefined && variable.defaultValue !== undefined) {
      value = variable.defaultValue;
    }
    
    // Format the value based on its type
    if (value !== undefined) {
      if (variable.type === 'date' && value instanceof Date) {
        return value.toLocaleDateString();
      }
      
      if (variable.type === 'object' || variable.type === 'array') {
        try {
          return JSON.stringify(value);
        } catch (e) {
          return String(value);
        }
      }
      
      return String(value);
    }
    
    // Return empty string if value is undefined
    return '';
  });
}

/**
 * Processes an entire email template and replaces all variables
 */
export function processTemplateVariables(
  template: any,
  variables: TemplateVariable[],
  data: Record<string, any>
): any {
  if (!template) return template;
  
  // Deep clone the template to avoid modifying the original
  const processedTemplate = JSON.parse(JSON.stringify(template));
  
  // Process subject and previewText
  if (processedTemplate.subject) {
    processedTemplate.subject = parseVariables(processedTemplate.subject, variables, data);
  }
  
  if (processedTemplate.previewText) {
    processedTemplate.previewText = parseVariables(processedTemplate.previewText, variables, data);
  }
  
  // Process blocks
  if (processedTemplate.blocks && Array.isArray(processedTemplate.blocks)) {
    processedTemplate.blocks = processedTemplate.blocks.map((block: EmailBlockBase) => {
      // Process text blocks
      if (block.type === EmailBlockType.TEXT && block.content) {
        block.content = parseVariables(block.content, variables, data);
      }
      
      // Process button blocks
      if (block.type === EmailBlockType.BUTTON) {
        if (block.text) {
          block.text = parseVariables(block.text, variables, data);
        }
        if (block.link) {
          block.link = parseVariables(block.link, variables, data);
        }
      }
      
      // Process custom HTML blocks
      if (block.type === EmailBlockType.CUSTOM_HTML && block.html) {
        block.html = parseVariables(block.html, variables, data);
      }
      
      // Process header blocks
      if (block.type === EmailBlockType.HEADER && block.settings.tagline) {
        block.settings.tagline = parseVariables(block.settings.tagline, variables, data);
      }
      
      // Process footer blocks
      if (block.type === EmailBlockType.FOOTER) {
        if (block.settings.companyInfo) {
          block.settings.companyInfo = parseVariables(block.settings.companyInfo, variables, data);
        }
        if (block.settings.copyrightText) {
          block.settings.copyrightText = parseVariables(block.settings.copyrightText, variables, data);
        }
      }
      
      // Process columns block
      if (block.type === EmailBlockType.COLUMNS && block.columns) {
        block.columns = block.columns.map((column: any) => {
          if (column.blocks && Array.isArray(column.blocks)) {
            column.blocks = column.blocks.map((nestedBlock: any) => {
              // Recursively process nested blocks
              return processNestedBlock(nestedBlock, variables, data);
            });
          }
          return column;
        });
      }
      
      return block;
    });
  }
  
  return processedTemplate;
}

/**
 * Helper function to process nested blocks in columns
 */
function processNestedBlock(
  block: any,
  variables: TemplateVariable[],
  data: Record<string, any>
): any {
  if (block.type === EmailBlockType.TEXT && block.content) {
    block.content = parseVariables(block.content, variables, data);
  }
  
  if (block.type === EmailBlockType.BUTTON) {
    if (block.text) {
      block.text = parseVariables(block.text, variables, data);
    }
    if (block.link) {
      block.link = parseVariables(block.link, variables, data);
    }
  }
  
  if (block.type === EmailBlockType.CUSTOM_HTML && block.html) {
    block.html = parseVariables(block.html, variables, data);
  }
  
  return block;
}