import { v4 as uuidv4 } from 'uuid';
import {
  EmailBlockType,
  TextBlock,
  ImageBlock,
  ButtonBlock,
  DividerBlock,
  SpacerBlock,
  SocialBlock,
  ProductBlock,
  ProductsGridBlock,
  OrderDetailsBlock,
  HeaderBlock,
  FooterBlock,
  ColumnsBlock,
  CustomHtmlBlock,
} from '../types';

/**
 * Factory functions to create different types of email blocks
 * These functions provide default values for each block type
 */

export function createTextBlock(position: number, content = 'Add your text here'): TextBlock {
  return {
    id: uuidv4(),
    type: EmailBlockType.TEXT,
    position,
    content,
    settings: {
      fontSize: '16px',
      fontFamily: 'inherit',
      fontWeight: 'normal',
      lineHeight: '1.5',
      color: '#333333',
      textAlign: 'left',
      padding: '10px',
    },
  };
}

export function createImageBlock(position: number, src = '', alt = 'Image'): ImageBlock {
  return {
    id: uuidv4(),
    type: EmailBlockType.IMAGE,
    position,
    src,
    alt,
    settings: {
      maxWidth: '100%',
      align: 'center',
      padding: '10px',
    },
  };
}

export function createButtonBlock(
  position: number,
  text = 'Click Here',
  link = '#'
): ButtonBlock {
  return {
    id: uuidv4(),
    type: EmailBlockType.BUTTON,
    position,
    text,
    link,
    settings: {
      buttonColor: '#6C1411',
      textColor: '#ffffff',
      fontSize: '16px',
      fontWeight: 'normal',
      borderRadius: '4px',
      padding: '15px',
      align: 'center',
      fullWidth: false,
    },
  };
}

export function createDividerBlock(position: number): DividerBlock {
  return {
    id: uuidv4(),
    type: EmailBlockType.DIVIDER,
    position,
    settings: {
      color: '#e0e0e0',
      thickness: '1px',
      style: 'solid',
      padding: '15px 0',
    },
  };
}

export function createSpacerBlock(position: number): SpacerBlock {
  return {
    id: uuidv4(),
    type: EmailBlockType.SPACER,
    position,
    settings: {
      height: '20px',
    },
  };
}

export function createSocialBlock(position: number): SocialBlock {
  return {
    id: uuidv4(),
    type: EmailBlockType.SOCIAL,
    position,
    networks: [
      { type: 'facebook', url: 'https://facebook.com', enabled: true },
      { type: 'twitter', url: 'https://twitter.com', enabled: true },
      { type: 'instagram', url: 'https://instagram.com', enabled: true },
      { type: 'linkedin', url: 'https://linkedin.com', enabled: false },
      { type: 'youtube', url: 'https://youtube.com', enabled: false },
      { type: 'pinterest', url: 'https://pinterest.com', enabled: false },
    ],
    settings: {
      iconSize: '24px',
      iconSpacing: '10px',
      iconColor: '#333333',
      showLabels: false,
      align: 'center',
      padding: '10px',
    },
  };
}

export function createProductBlock(position: number, productId = ''): ProductBlock {
  return {
    id: uuidv4(),
    type: EmailBlockType.PRODUCT,
    position,
    productId,
    settings: {
      showImage: true,
      showTitle: true,
      showPrice: true,
      showDescription: true,
      imagePosition: 'top',
      buttonText: 'View Product',
      buttonColor: '#6C1411',
      buttonTextColor: '#ffffff',
      padding: '15px',
    },
  };
}

export function createProductsGridBlock(position: number, productIds: string[] = []): ProductsGridBlock {
  return {
    id: uuidv4(),
    type: EmailBlockType.PRODUCTS_GRID,
    position,
    productIds,
    settings: {
      columns: 2,
      showImage: true,
      showTitle: true,
      showPrice: true,
      showButton: true,
      buttonText: 'View Product',
      imageAspectRatio: '1:1',
      padding: '15px',
    },
  };
}

export function createOrderDetailsBlock(position: number): OrderDetailsBlock {
  return {
    id: uuidv4(),
    type: EmailBlockType.ORDER_DETAILS,
    position,
    settings: {
      showOrderNumber: true,
      showOrderDate: true,
      showItems: true,
      showPrices: true,
      showShipping: true,
      showTotals: true,
      showCustomerInfo: true,
      padding: '15px',
    },
  };
}

export function createHeaderBlock(position: number): HeaderBlock {
  return {
    id: uuidv4(),
    type: EmailBlockType.HEADER,
    position,
    settings: {
      showLogo: true,
      logoPosition: 'center',
      logoSize: '200px',
      showTagline: false,
      tagline: 'Children\'s Clothing and Accessories',
      backgroundColor: '#ffffff',
      padding: '20px',
    },
  };
}

export function createFooterBlock(position: number): FooterBlock {
  return {
    id: uuidv4(),
    type: EmailBlockType.FOOTER,
    position,
    settings: {
      showCompanyInfo: true,
      companyInfo: 'Coco Milk Kids, 123 Main St, Cape Town, South Africa',
      showCopyright: true,
      copyrightText: `© ${new Date().getFullYear()} Coco Milk Kids. All rights reserved.`,
      showUnsubscribe: true,
      unsubscribeText: 'Unsubscribe | View in browser',
      showSocialLinks: true,
      backgroundColor: '#f8f9fa',
      padding: '20px',
    },
  };
}

export function createColumnsBlock(position: number, columnsCount = 2): ColumnsBlock {
  const columns = Array.from({ length: columnsCount }).map((_, i) => ({
    id: uuidv4(),
    blocks: [],
    settings: {
      width: `${100 / columnsCount}%`,
      padding: '10px',
    },
  }));

  return {
    id: uuidv4(),
    type: EmailBlockType.COLUMNS,
    position,
    columns,
    settings: {
      columnsCount,
      columnGap: '20px',
      stackOnMobile: true,
      padding: '10px',
    },
  };
}

export function createCustomHtmlBlock(position: number, html = '<div>Custom HTML here</div>'): CustomHtmlBlock {
  return {
    id: uuidv4(),
    type: EmailBlockType.CUSTOM_HTML,
    position,
    html,
    settings: {
      padding: '10px',
    },
  };
}

/**
 * Creates a new block of the specified type
 */
export function createBlock(type: EmailBlockType, position: number) {
  switch (type) {
    case EmailBlockType.TEXT:
      return createTextBlock(position);
    case EmailBlockType.IMAGE:
      return createImageBlock(position);
    case EmailBlockType.BUTTON:
      return createButtonBlock(position);
    case EmailBlockType.DIVIDER:
      return createDividerBlock(position);
    case EmailBlockType.SPACER:
      return createSpacerBlock(position);
    case EmailBlockType.SOCIAL:
      return createSocialBlock(position);
    case EmailBlockType.PRODUCT:
      return createProductBlock(position);
    case EmailBlockType.PRODUCTS_GRID:
      return createProductsGridBlock(position);
    case EmailBlockType.ORDER_DETAILS:
      return createOrderDetailsBlock(position);
    case EmailBlockType.HEADER:
      return createHeaderBlock(position);
    case EmailBlockType.FOOTER:
      return createFooterBlock(position);
    case EmailBlockType.COLUMNS:
      return createColumnsBlock(position);
    case EmailBlockType.CUSTOM_HTML:
      return createCustomHtmlBlock(position);
    default:
      throw new Error(`Unknown block type: ${type}`);
  }
}