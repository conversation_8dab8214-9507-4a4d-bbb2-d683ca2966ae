import React from 'react';
import { renderToStaticMarkup } from 'react-dom/server';
import { EmailTemplate, EmailBlock, EmailBlockType } from '../types';
import { processTemplateVariables } from './variable-parser';

/**
 * Renders an email template to HTML
 */
export function renderEmailTemplate(
  template: EmailTemplate,
  variables: any[] = [],
  data: Record<string, any> = {}
): string {
  // Process variables in the template
  const processedTemplate = processTemplateVariables(template, variables, data);
  
  // Render the template to HTML
  const html = renderToStaticMarkup(
    <EmailTemplateRenderer template={processedTemplate} />
  );
  
  return html;
}

/**
 * React component to render an email template
 */
function EmailTemplateRenderer({ template }: { template: EmailTemplate }) {
  const { settings, blocks } = template;
  
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta httpEquiv="Content-Type" content="text/html; charset=UTF-8" />
        <title>{template.subject}</title>
        <style dangerouslySetInnerHTML={{ __html: getEmailStyles(settings) }} />
      </head>
      <body style={{ 
        margin: 0, 
        padding: 0, 
        backgroundColor: settings.backgroundColor || '#f8f9fa',
        fontFamily: settings.fontFamily || 'Arial, sans-serif',
        fontSize: settings.fontSize || '16px',
        lineHeight: settings.lineHeight || '1.5',
        color: settings.color || '#333333',
      }}>
        {/* Preview text */}
        {template.previewText && (
          <div style={{ 
            display: 'none', 
            fontSize: '1px', 
            lineHeight: '1px', 
            maxHeight: '0px', 
            maxWidth: '0px', 
            opacity: 0, 
            overflow: 'hidden' 
          }}>
            {template.previewText}
          </div>
        )}
        
        {/* Email container */}
        <table 
          border={0} 
          cellPadding={0} 
          cellSpacing={0} 
          width="100%" 
          style={{ backgroundColor: settings.backgroundColor || '#f8f9fa' }}
        >
          <tr>
            <td align="center" style={{ padding: '20px 0' }}>
              <table 
                className="email-container" 
                border={0} 
                cellPadding={0} 
                cellSpacing={0} 
                width="100%" 
                style={{ 
                  maxWidth: settings.width || '600px',
                  backgroundColor: '#ffffff',
                }}
              >
                {/* Render blocks */}
                {blocks.map(block => (
                  <tr key={block.id}>
                    <td style={{ padding: 0 }}>
                      <BlockRenderer block={block} />
                    </td>
                  </tr>
                ))}
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  );
}

/**
 * Renders an individual block
 */
function BlockRenderer({ block }: { block: EmailBlock }) {
  switch (block.type) {
    case EmailBlockType.TEXT:
      return <TextBlockRenderer block={block} />;
    case EmailBlockType.IMAGE:
      return <ImageBlockRenderer block={block} />;
    case EmailBlockType.BUTTON:
      return <ButtonBlockRenderer block={block} />;
    case EmailBlockType.DIVIDER:
      return <DividerBlockRenderer block={block} />;
    case EmailBlockType.SPACER:
      return <SpacerBlockRenderer block={block} />;
    case EmailBlockType.SOCIAL:
      return <SocialBlockRenderer block={block} />;
    case EmailBlockType.PRODUCT:
      return <ProductBlockRenderer block={block} />;
    case EmailBlockType.PRODUCTS_GRID:
      return <ProductsGridBlockRenderer block={block} />;
    case EmailBlockType.ORDER_DETAILS:
      return <OrderDetailsBlockRenderer block={block} />;
    case EmailBlockType.HEADER:
      return <HeaderBlockRenderer block={block} />;
    case EmailBlockType.FOOTER:
      return <FooterBlockRenderer block={block} />;
    case EmailBlockType.COLUMNS:
      return <ColumnsBlockRenderer block={block} />;
    case EmailBlockType.CUSTOM_HTML:
      return <CustomHtmlBlockRenderer block={block} />;
    default:
      return null;
  }
}

/**
 * Text block renderer
 */
function TextBlockRenderer({ block }: { block: any }) {
  const { content, settings } = block;
  
  return (
    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
      <tr>
        <td 
          style={{ 
            padding: settings.padding || '10px',
            backgroundColor: settings.backgroundColor,
            textAlign: settings.textAlign || 'left',
          }}
        >
          <div 
            style={{ 
              fontFamily: settings.fontFamily || 'inherit',
              fontSize: settings.fontSize || 'inherit',
              fontWeight: settings.fontWeight || 'normal',
              lineHeight: settings.lineHeight || 'inherit',
              color: settings.color || 'inherit',
            }}
            dangerouslySetInnerHTML={{ __html: content }}
          />
        </td>
      </tr>
    </table>
  );
}

/**
 * Image block renderer
 */
function ImageBlockRenderer({ block }: { block: any }) {
  const { src, alt, link, settings } = block;
  
  const imageContent = (
    <img 
      src={src} 
      alt={alt} 
      style={{ 
        maxWidth: settings.maxWidth || '100%',
        height: settings.height || 'auto',
        display: 'block',
        border: 0,
      }} 
    />
  );
  
  return (
    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
      <tr>
        <td 
          align={settings.align || 'center'} 
          style={{ 
            padding: settings.padding || '10px',
            backgroundColor: settings.backgroundColor,
          }}
        >
          {link ? (
            <a href={link} target="_blank" rel="noopener noreferrer">
              {imageContent}
            </a>
          ) : (
            imageContent
          )}
        </td>
      </tr>
    </table>
  );
}

/**
 * Button block renderer
 */
function ButtonBlockRenderer({ block }: { block: any }) {
  const { text, link, settings } = block;
  
  return (
    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
      <tr>
        <td 
          align={settings.align || 'center'} 
          style={{ 
            padding: settings.padding || '15px',
            backgroundColor: settings.backgroundColor,
          }}
        >
          <table 
            border={0} 
            cellPadding={0} 
            cellSpacing={0} 
            style={{ 
              width: settings.fullWidth ? '100%' : 'auto',
            }}
          >
            <tr>
              <td 
                style={{ 
                  backgroundColor: settings.buttonColor || '#6C1411',
                  borderRadius: settings.borderRadius || '4px',
                  textAlign: 'center',
                }}
              >
                <a 
                  href={link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  style={{ 
                    display: 'inline-block',
                    padding: '12px 24px',
                    color: settings.textColor || '#ffffff',
                    fontSize: settings.fontSize || '16px',
                    fontWeight: settings.fontWeight || 'normal',
                    textDecoration: 'none',
                    textAlign: 'center',
                  }}
                >
                  {text}
                </a>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  );
}

/**
 * Divider block renderer
 */
function DividerBlockRenderer({ block }: { block: any }) {
  const { settings } = block;
  
  return (
    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
      <tr>
        <td 
          style={{ 
            padding: settings.padding || '15px 0',
            backgroundColor: settings.backgroundColor,
          }}
        >
          <table border={0} cellPadding={0} cellSpacing={0} width="100%">
            <tr>
              <td 
                style={{ 
                  borderTop: `${settings.thickness || '1px'} ${settings.style || 'solid'} ${settings.color || '#e0e0e0'}`,
                }}
              >
                &nbsp;
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  );
}

/**
 * Spacer block renderer
 */
function SpacerBlockRenderer({ block }: { block: any }) {
  const { settings } = block;
  
  return (
    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
      <tr>
        <td 
          style={{ 
            fontSize: '0',
            lineHeight: '0',
            height: settings.height || '20px',
            backgroundColor: settings.backgroundColor,
          }}
        >
          &nbsp;
        </td>
      </tr>
    </table>
  );
}

/**
 * Social links block renderer
 */
function SocialBlockRenderer({ block }: { block: any }) {
  const { networks, settings } = block;
  const enabledNetworks = networks.filter(network => network.enabled);
  
  if (enabledNetworks.length === 0) return null;
  
  return (
    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
      <tr>
        <td 
          align={settings.align || 'center'} 
          style={{ 
            padding: settings.padding || '10px',
            backgroundColor: settings.backgroundColor,
          }}
        >
          <table border={0} cellPadding={0} cellSpacing={0}>
            <tr>
              {enabledNetworks.map((network, index) => (
                <td 
                  key={network.type} 
                  style={{ 
                    paddingRight: index < enabledNetworks.length - 1 ? settings.iconSpacing || '10px' : '0',
                  }}
                >
                  <a 
                    href={network.url} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    style={{ 
                      color: settings.iconColor || '#333333',
                      textDecoration: 'none',
                    }}
                  >
                    {/* Social icon would go here */}
                    <div 
                      style={{ 
                        width: settings.iconSize || '24px',
                        height: settings.iconSize || '24px',
                        backgroundColor: settings.iconColor || '#333333',
                        borderRadius: '50%',
                      }}
                    />
                    {settings.showLabels && (
                      <div 
                        style={{ 
                          fontSize: '12px',
                          marginTop: '5px',
                          textAlign: 'center',
                        }}
                      >
                        {network.type.charAt(0).toUpperCase() + network.type.slice(1)}
                      </div>
                    )}
                  </a>
                </td>
              ))}
            </tr>
          </table>
        </td>
      </tr>
    </table>
  );
}

/**
 * Product block renderer
 */
function ProductBlockRenderer({ block }: { block: any }) {
  // In a real implementation, you would fetch product data
  // For now, we'll use placeholder data
  const product = {
    id: block.productId || '123',
    name: 'Product Name',
    price: 'R299.99',
    description: 'Product description goes here.',
    imageUrl: 'https://via.placeholder.com/300x300',
    url: 'https://example.com/product',
  };
  
  const { settings } = block;
  
  return (
    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
      <tr>
        <td 
          style={{ 
            padding: settings.padding || '15px',
            backgroundColor: settings.backgroundColor,
          }}
        >
          <table border={0} cellPadding={0} cellSpacing={0} width="100%">
            {settings.showImage && settings.imagePosition === 'top' && (
              <tr>
                <td align="center" style={{ paddingBottom: '15px' }}>
                  <a href={product.url} target="_blank" rel="noopener noreferrer">
                    <img 
                      src={product.imageUrl} 
                      alt={product.name} 
                      style={{ 
                        maxWidth: '100%',
                        height: 'auto',
                        display: 'block',
                        border: 0,
                      }} 
                    />
                  </a>
                </td>
              </tr>
            )}
            
            <tr>
              {settings.showImage && (settings.imagePosition === 'left' || settings.imagePosition === 'right') && (
                <td 
                  width="40%" 
                  valign="top" 
                  style={{ 
                    paddingRight: settings.imagePosition === 'left' ? '15px' : '0',
                    paddingLeft: settings.imagePosition === 'right' ? '15px' : '0',
                  }}
                >
                  <a href={product.url} target="_blank" rel="noopener noreferrer">
                    <img 
                      src={product.imageUrl} 
                      alt={product.name} 
                      style={{ 
                        maxWidth: '100%',
                        height: 'auto',
                        display: 'block',
                        border: 0,
                      }} 
                    />
                  </a>
                </td>
              )}
              
              <td valign="top">
                {settings.showTitle && (
                  <h3 style={{ margin: '0 0 10px 0', fontSize: '18px' }}>
                    <a 
                      href={product.url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      style={{ 
                        color: '#333333',
                        textDecoration: 'none',
                      }}
                    >
                      {product.name}
                    </a>
                  </h3>
                )}
                
                {settings.showPrice && (
                  <p style={{ margin: '0 0 10px 0', fontSize: '16px', fontWeight: 'bold' }}>
                    {product.price}
                  </p>
                )}
                
                {settings.showDescription && (
                  <p style={{ margin: '0 0 15px 0', fontSize: '14px' }}>
                    {product.description}
                  </p>
                )}
                
                <table border={0} cellPadding={0} cellSpacing={0}>
                  <tr>
                    <td 
                      style={{ 
                        backgroundColor: settings.buttonColor || '#6C1411',
                        borderRadius: '4px',
                      }}
                    >
                      <a 
                        href={product.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        style={{ 
                          display: 'inline-block',
                          padding: '8px 16px',
                          color: settings.buttonTextColor || '#ffffff',
                          fontSize: '14px',
                          textDecoration: 'none',
                        }}
                      >
                        {settings.buttonText || 'View Product'}
                      </a>
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  );
}

/**
 * Products grid block renderer
 */
function ProductsGridBlockRenderer({ block }: { block: any }) {
  // In a real implementation, you would fetch product data
  // For now, we'll use placeholder data
  const products = block.productIds.map((id: string, index: number) => ({
    id,
    name: `Product ${index + 1}`,
    price: `R${(199 + index * 50).toFixed(2)}`,
    imageUrl: `https://via.placeholder.com/300x300?text=Product+${index + 1}`,
    url: `https://example.com/product/${id}`,
  }));
  
  const { settings } = block;
  const columns = settings.columns || 2;
  
  // Create rows of products
  const rows = [];
  for (let i = 0; i < products.length; i += columns) {
    rows.push(products.slice(i, i + columns));
  }
  
  return (
    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
      <tr>
        <td 
          style={{ 
            padding: settings.padding || '15px',
            backgroundColor: settings.backgroundColor,
          }}
        >
          {rows.map((row, rowIndex) => (
            <table 
              key={rowIndex} 
              border={0} 
              cellPadding={0} 
              cellSpacing={0} 
              width="100%" 
              style={{ marginBottom: rowIndex < rows.length - 1 ? '20px' : '0' }}
            >
              <tr>
                {row.map((product, colIndex) => (
                  <td 
                    key={product.id} 
                    width={`${100 / columns}%`} 
                    valign="top" 
                    style={{ 
                      paddingRight: colIndex < row.length - 1 ? '10px' : '0',
                    }}
                  >
                    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
                      {settings.showImage && (
                        <tr>
                          <td align="center" style={{ paddingBottom: '10px' }}>
                            <a href={product.url} target="_blank" rel="noopener noreferrer">
                              <img 
                                src={product.imageUrl} 
                                alt={product.name} 
                                style={{ 
                                  width: '100%',
                                  height: 'auto',
                                  display: 'block',
                                  border: 0,
                                }} 
                              />
                            </a>
                          </td>
                        </tr>
                      )}
                      
                      {settings.showTitle && (
                        <tr>
                          <td style={{ paddingBottom: '5px' }}>
                            <h3 style={{ margin: '0', fontSize: '16px' }}>
                              <a 
                                href={product.url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                style={{ 
                                  color: '#333333',
                                  textDecoration: 'none',
                                }}
                              >
                                {product.name}
                              </a>
                            </h3>
                          </td>
                        </tr>
                      )}
                      
                      {settings.showPrice && (
                        <tr>
                          <td style={{ paddingBottom: '10px' }}>
                            <p style={{ margin: '0', fontSize: '14px', fontWeight: 'bold' }}>
                              {product.price}
                            </p>
                          </td>
                        </tr>
                      )}
                      
                      {settings.showButton && (
                        <tr>
                          <td>
                            <table border={0} cellPadding={0} cellSpacing={0}>
                              <tr>
                                <td 
                                  style={{ 
                                    backgroundColor: '#6C1411',
                                    borderRadius: '4px',
                                  }}
                                >
                                  <a 
                                    href={product.url} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    style={{ 
                                      display: 'inline-block',
                                      padding: '6px 12px',
                                      color: '#ffffff',
                                      fontSize: '12px',
                                      textDecoration: 'none',
                                    }}
                                  >
                                    {settings.buttonText || 'View Product'}
                                  </a>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      )}
                    </table>
                  </td>
                ))}
                
                {/* Add empty cells if row is not full */}
                {Array.from({ length: columns - row.length }).map((_, index) => (
                  <td key={`empty-${index}`} width={`${100 / columns}%`}>&nbsp;</td>
                ))}
              </tr>
            </table>
          ))}
        </td>
      </tr>
    </table>
  );
}

/**
 * Order details block renderer
 */
function OrderDetailsBlockRenderer({ block }: { block: any }) {
  // In a real implementation, you would use dynamic order data
  // For now, we'll use placeholder data
  const order = {
    orderNumber: '12345',
    orderDate: new Date().toLocaleDateString(),
    items: [
      { name: 'Product 1', quantity: 1, price: 'R299.99', total: 'R299.99' },
      { name: 'Product 2', quantity: 2, price: 'R199.99', total: 'R399.98' },
    ],
    subtotal: 'R699.97',
    shipping: 'R50.00',
    tax: 'R105.00',
    total: 'R854.97',
    customer: {
      name: 'John Doe',
      email: '<EMAIL>',
      address: '123 Main St, Cape Town, 8001, South Africa',
    },
  };
  
  const { settings } = block;
  
  return (
    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
      <tr>
        <td 
          style={{ 
            padding: settings.padding || '15px',
            backgroundColor: settings.backgroundColor,
          }}
        >
          <table border={0} cellPadding={0} cellSpacing={0} width="100%">
            {(settings.showOrderNumber || settings.showOrderDate) && (
              <tr>
                <td style={{ paddingBottom: '15px' }}>
                  <table border={0} cellPadding={0} cellSpacing={0} width="100%">
                    <tr>
                      {settings.showOrderNumber && (
                        <td>
                          <p style={{ margin: '0', fontSize: '14px' }}>
                            <strong>Order Number:</strong> {order.orderNumber}
                          </p>
                        </td>
                      )}
                      
                      {settings.showOrderDate && (
                        <td align="right">
                          <p style={{ margin: '0', fontSize: '14px' }}>
                            <strong>Order Date:</strong> {order.orderDate}
                          </p>
                        </td>
                      )}
                    </tr>
                  </table>
                </td>
              </tr>
            )}
            
            {settings.showItems && (
              <tr>
                <td style={{ paddingBottom: '15px' }}>
                  <table 
                    border={0} 
                    cellPadding={0} 
                    cellSpacing={0} 
                    width="100%" 
                    style={{ 
                      borderCollapse: 'collapse',
                    }}
                  >
                    <tr>
                      <th 
                        align="left" 
                        style={{ 
                          padding: '8px',
                          borderBottom: '1px solid #e0e0e0',
                          fontSize: '14px',
                        }}
                      >
                        Product
                      </th>
                      <th 
                        align="center" 
                        style={{ 
                          padding: '8px',
                          borderBottom: '1px solid #e0e0e0',
                          fontSize: '14px',
                        }}
                      >
                        Qty
                      </th>
                      {settings.showPrices && (
                        <>
                          <th 
                            align="right" 
                            style={{ 
                              padding: '8px',
                              borderBottom: '1px solid #e0e0e0',
                              fontSize: '14px',
                            }}
                          >
                            Price
                          </th>
                          <th 
                            align="right" 
                            style={{ 
                              padding: '8px',
                              borderBottom: '1px solid #e0e0e0',
                              fontSize: '14px',
                            }}
                          >
                            Total
                          </th>
                        </>
                      )}
                    </tr>
                    
                    {order.items.map((item, index) => (
                      <tr key={index}>
                        <td 
                          style={{ 
                            padding: '8px',
                            borderBottom: '1px solid #e0e0e0',
                            fontSize: '14px',
                          }}
                        >
                          {item.name}
                        </td>
                        <td 
                          align="center" 
                          style={{ 
                            padding: '8px',
                            borderBottom: '1px solid #e0e0e0',
                            fontSize: '14px',
                          }}
                        >
                          {item.quantity}
                        </td>
                        {settings.showPrices && (
                          <>
                            <td 
                              align="right" 
                              style={{ 
                                padding: '8px',
                                borderBottom: '1px solid #e0e0e0',
                                fontSize: '14px',
                              }}
                            >
                              {item.price}
                            </td>
                            <td 
                              align="right" 
                              style={{ 
                                padding: '8px',
                                borderBottom: '1px solid #e0e0e0',
                                fontSize: '14px',
                              }}
                            >
                              {item.total}
                            </td>
                          </>
                        )}
                      </tr>
                    ))}
                  </table>
                </td>
              </tr>
            )}
            
            {settings.showTotals && (
              <tr>
                <td style={{ paddingBottom: '15px' }}>
                  <table 
                    border={0} 
                    cellPadding={0} 
                    cellSpacing={0} 
                    width="100%" 
                    style={{ 
                      borderCollapse: 'collapse',
                    }}
                  >
                    <tr>
                      <td 
                        align="right" 
                        style={{ 
                          padding: '4px 0',
                          fontSize: '14px',
                        }}
                      >
                        Subtotal:
                      </td>
                      <td 
                        align="right" 
                        style={{ 
                          padding: '4px 0',
                          fontSize: '14px',
                          width: '100px',
                        }}
                      >
                        {order.subtotal}
                      </td>
                    </tr>
                    
                    {settings.showShipping && (
                      <tr>
                        <td 
                          align="right" 
                          style={{ 
                            padding: '4px 0',
                            fontSize: '14px',
                          }}
                        >
                          Shipping:
                        </td>
                        <td 
                          align="right" 
                          style={{ 
                            padding: '4px 0',
                            fontSize: '14px',
                          }}
                        >
                          {order.shipping}
                        </td>
                      </tr>
                    )}
                    
                    <tr>
                      <td 
                        align="right" 
                        style={{ 
                          padding: '4px 0',
                          fontSize: '14px',
                        }}
                      >
                        Tax:
                      </td>
                      <td 
                        align="right" 
                        style={{ 
                          padding: '4px 0',
                          fontSize: '14px',
                        }}
                      >
                        {order.tax}
                      </td>
                    </tr>
                    
                    <tr>
                      <td 
                        align="right" 
                        style={{ 
                          padding: '8px 0',
                          fontSize: '16px',
                          fontWeight: 'bold',
                          borderTop: '1px solid #e0e0e0',
                        }}
                      >
                        Total:
                      </td>
                      <td 
                        align="right" 
                        style={{ 
                          padding: '8px 0',
                          fontSize: '16px',
                          fontWeight: 'bold',
                          borderTop: '1px solid #e0e0e0',
                        }}
                      >
                        {order.total}
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            )}
            
            {settings.showCustomerInfo && (
              <tr>
                <td>
                  <table border={0} cellPadding={0} cellSpacing={0} width="100%">
                    <tr>
                      <td 
                        style={{ 
                          backgroundColor: '#f8f9fa',
                          padding: '15px',
                          borderRadius: '4px',
                        }}
                      >
                        <h3 style={{ margin: '0 0 10px 0', fontSize: '16px' }}>
                          Customer Information
                        </h3>
                        <p style={{ margin: '0 0 5px 0', fontSize: '14px' }}>
                          <strong>Name:</strong> {order.customer.name}
                        </p>
                        <p style={{ margin: '0 0 5px 0', fontSize: '14px' }}>
                          <strong>Email:</strong> {order.customer.email}
                        </p>
                        <p style={{ margin: '0', fontSize: '14px' }}>
                          <strong>Shipping Address:</strong><br />
                          {order.customer.address}
                        </p>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            )}
          </table>
        </td>
      </tr>
    </table>
  );
}

/**
 * Header block renderer
 */
function HeaderBlockRenderer({ block }: { block: any }) {
  const { settings } = block;
  
  return (
    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
      <tr>
        <td 
          style={{ 
            padding: settings.padding || '20px',
            backgroundColor: settings.backgroundColor || '#ffffff',
          }}
        >
          <table border={0} cellPadding={0} cellSpacing={0} width="100%">
            {settings.showLogo && (
              <tr>
                <td align={settings.logoPosition || 'center'}>
                  {/* Logo SVG inline for email compatibility */}
                  <svg
                    width={settings.logoSize || '200'}
                    height={(parseInt(settings.logoSize || '200') / 4)}
                    viewBox="0 0 200 50"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    style={{ maxWidth: '100%', height: 'auto' }}
                  >
                    {/* Hanger */}
                    <path
                      d="M30 25C30 25 25 25 25 30L20 40H40L35 30C35 25 30 25 30 25Z"
                      stroke="#6C1411"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      fill="none"
                    />
                    <path
                      d="M30 25V20"
                      stroke="#6C1411"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />

                    {/* coco */}
                    <path
                      d="M55 30C55 27.2386 57.2386 25 60 25C62.7614 25 65 27.2386 65 30C65 32.7614 62.7614 35 60 35C57.2386 35 55 32.7614 55 30Z"
                      stroke="#6C1411"
                      strokeWidth="1.5"
                      fill="none"
                    />
                    <path
                      d="M70 30C70 27.2386 72.2386 25 75 25C77.7614 25 80 27.2386 80 30C80 32.7614 77.7614 35 75 35C72.2386 35 70 32.7614 70 30Z"
                      stroke="#6C1411"
                      strokeWidth="1.5"
                      fill="none"
                    />
                    <path
                      d="M85 30C85 27.2386 87.2386 25 90 25C92.7614 25 95 27.2386 95 30C95 32.7614 92.7614 35 90 35C87.2386 35 85 32.7614 85 30Z"
                      stroke="#6C1411"
                      strokeWidth="1.5"
                      fill="none"
                    />
                    <path
                      d="M100 30C100 27.2386 102.239 25 105 25C107.761 25 110 27.2386 110 30C110 32.7614 107.761 35 105 35C102.239 35 100 32.7614 100 30Z"
                      stroke="#6C1411"
                      strokeWidth="1.5"
                      fill="none"
                    />

                    {/* milk */}
                    <path
                      d="M125 25V30M125 30L120 25M125 30L130 25"
                      stroke="#6C1411"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M135 25V30"
                      stroke="#6C1411"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M145 25V30L150 25V30"
                      stroke="#6C1411"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M160 25V30M160 25L155 30M160 25L165 30"
                      stroke="#6C1411"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />

                    {/* KIDS */}
                    <path
                      d="M175 35L170 30L175 30L170 35"
                      stroke="#012169"
                      strokeWidth="1"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M180 30V35"
                      stroke="#012169"
                      strokeWidth="1"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M185 30H190V32.5H185V35H190"
                      stroke="#012169"
                      strokeWidth="1"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M195 30C195 30 200 30 200 32.5C200 35 195 35 195 35"
                      stroke="#012169"
                      strokeWidth="1"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </td>
              </tr>
            )}
            
            {settings.showTagline && settings.tagline && (
              <tr>
                <td 
                  align="center" 
                  style={{ 
                    paddingTop: '10px',
                    fontSize: '14px',
                    color: '#666666',
                  }}
                >
                  {settings.tagline}
                </td>
              </tr>
            )}
          </table>
        </td>
      </tr>
    </table>
  );
}

/**
 * Footer block renderer
 */
function FooterBlockRenderer({ block }: { block: any }) {
  const { settings } = block;
  
  return (
    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
      <tr>
        <td 
          style={{ 
            padding: settings.padding || '20px',
            backgroundColor: settings.backgroundColor || '#f8f9fa',
            color: '#666666',
            fontSize: '14px',
          }}
        >
          <table border={0} cellPadding={0} cellSpacing={0} width="100%">
            {settings.showCompanyInfo && settings.companyInfo && (
              <tr>
                <td align="center" style={{ paddingBottom: '10px' }}>
                  {settings.companyInfo}
                </td>
              </tr>
            )}
            
            {settings.showSocialLinks && (
              <tr>
                <td align="center" style={{ paddingBottom: '10px' }}>
                  <table border={0} cellPadding={0} cellSpacing={0}>
                    <tr>
                      {['facebook', 'twitter', 'instagram'].map((network, index) => (
                        <td 
                          key={network} 
                          style={{ 
                            paddingRight: index < 2 ? '10px' : '0',
                          }}
                        >
                          <a 
                            href={`https://${network}.com`} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            style={{ 
                              color: '#666666',
                              textDecoration: 'none',
                            }}
                          >
                            {/* Social icon would go here */}
                            <div 
                              style={{ 
                                width: '24px',
                                height: '24px',
                                backgroundColor: '#666666',
                                borderRadius: '50%',
                              }}
                            />
                          </a>
                        </td>
                      ))}
                    </tr>
                  </table>
                </td>
              </tr>
            )}
            
            {settings.showCopyright && settings.copyrightText && (
              <tr>
                <td align="center" style={{ paddingBottom: '10px' }}>
                  {settings.copyrightText}
                </td>
              </tr>
            )}
            
            {settings.showUnsubscribe && settings.unsubscribeText && (
              <tr>
                <td align="center">
                  <a 
                    href="#" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    style={{ 
                      color: '#666666',
                      textDecoration: 'underline',
                    }}
                  >
                    {settings.unsubscribeText}
                  </a>
                </td>
              </tr>
            )}
          </table>
        </td>
      </tr>
    </table>
  );
}

/**
 * Columns block renderer
 */
function ColumnsBlockRenderer({ block }: { block: any }) {
  const { columns, settings } = block;
  
  return (
    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
      <tr>
        <td 
          style={{ 
            padding: settings.padding || '10px',
            backgroundColor: settings.backgroundColor,
          }}
        >
          <table border={0} cellPadding={0} cellSpacing={0} width="100%">
            <tr>
              {columns.map((column: any, index: number) => (
                <td 
                  key={column.id} 
                  width={column.settings.width || `${100 / columns.length}%`} 
                  valign="top" 
                  style={{ 
                    paddingRight: index < columns.length - 1 ? settings.columnGap || '20px' : '0',
                  }}
                >
                  <table 
                    border={0} 
                    cellPadding={0} 
                    cellSpacing={0} 
                    width="100%" 
                    style={{ 
                      backgroundColor: column.settings.backgroundColor,
                    }}
                  >
                    {column.blocks.map((block: any) => (
                      <tr key={block.id}>
                        <td style={{ padding: 0 }}>
                          <BlockRenderer block={block} />
                        </td>
                      </tr>
                    ))}
                  </table>
                </td>
              ))}
            </tr>
          </table>
        </td>
      </tr>
    </table>
  );
}

/**
 * Custom HTML block renderer
 */
function CustomHtmlBlockRenderer({ block }: { block: any }) {
  const { html, settings } = block;
  
  return (
    <table border={0} cellPadding={0} cellSpacing={0} width="100%">
      <tr>
        <td 
          style={{ 
            padding: settings.padding || '10px',
            backgroundColor: settings.backgroundColor,
          }}
        >
          <div dangerouslySetInnerHTML={{ __html: html }} />
        </td>
      </tr>
    </table>
  );
}

/**
 * Generates CSS styles for the email
 */
function getEmailStyles(settings: any): string {
  return `
    /* Base styles */
    body {
      margin: 0;
      padding: 0;
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
      background-color: ${settings.backgroundColor || '#f8f9fa'};
      font-family: ${settings.fontFamily || 'Arial, sans-serif'};
      font-size: ${settings.fontSize || '16px'};
      line-height: ${settings.lineHeight || '1.5'};
      color: ${settings.color || '#333333'};
    }
    
    table {
      border-collapse: collapse;
      mso-table-lspace: 0pt;
      mso-table-rspace: 0pt;
    }
    
    img {
      -ms-interpolation-mode: bicubic;
      border: 0;
      height: auto;
      line-height: 100%;
      outline: none;
      text-decoration: none;
      max-width: 100%;
    }
    
    p {
      margin: 0 0 16px 0;
    }
    
    a {
      color: ${settings.linkColor || '#0066cc'};
    }
    
    /* Responsive styles */
    @media only screen and (max-width: 600px) {
      .email-container {
        width: 100% !important;
      }
      
      .stack-column,
      .stack-column-center {
        display: block !important;
        width: 100% !important;
        max-width: 100% !important;
        direction: ltr !important;
      }
      
      .stack-column-center {
        text-align: center !important;
      }
    }
  `;
}