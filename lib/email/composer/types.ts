/**
 * Types for the Email Composer system
 */

// Block types that can be used in the email composer
export enum EmailBlockType {
  TEXT = 'text',
  IMAGE = 'image',
  BUTTON = 'button',
  DIVIDER = 'divider',
  SPACER = 'spacer',
  SOCIAL = 'social',
  PRODUCT = 'product',
  PRODUCTS_GRID = 'products_grid',
  ORDER_DETAILS = 'order_details',
  HEADER = 'header',
  FOOTER = 'footer',
  COLUMNS = 'columns',
  CUSTOM_HTML = 'custom_html',
}

// Base interface for all email blocks
export interface EmailBlockBase {
  id: string;
  type: EmailBlockType;
  position: number;
  settings: {
    backgroundColor?: string;
    padding?: string;
    margin?: string;
    width?: string;
    align?: 'left' | 'center' | 'right';
    borderRadius?: string;
    borderWidth?: string;
    borderColor?: string;
    borderStyle?: 'solid' | 'dashed' | 'dotted' | 'none';
    [key: string]: any;
  };
  // Common properties for all blocks
  content?: string;
  link?: string;
  alt?: string;
  text?: string;
  src?: string;
  html?: string;
  attributes?: Record<string, any>;
  // For columns block
  columns?: ColumnsBlock['columns'];
}

// Text block
export interface TextBlock extends EmailBlockBase {
  type: EmailBlockType.TEXT;
  settings: EmailBlockBase['settings'] & {
    fontSize?: string;
    fontFamily?: string;
    fontWeight?: string;
    lineHeight?: string;
    color?: string;
    textAlign?: 'left' | 'center' | 'right' | 'justify';
  };
}

// Image block
export interface ImageBlock extends EmailBlockBase {
  type: EmailBlockType.IMAGE;
  src: string;
  alt: string;
  link?: string;
  settings: EmailBlockBase['settings'] & {
    maxWidth?: string;
    height?: string;
    objectFit?: 'contain' | 'cover' | 'fill' | 'none';
  };
}

// Button block
export interface ButtonBlock extends EmailBlockBase {
  type: EmailBlockType.BUTTON;
  text: string;
  link: string;
  settings: EmailBlockBase['settings'] & {
    buttonColor?: string;
    textColor?: string;
    fontSize?: string;
    fontWeight?: string;
    borderRadius?: string;
    fullWidth?: boolean;
  };
}

// Divider block
export interface DividerBlock extends EmailBlockBase {
  type: EmailBlockType.DIVIDER;
  settings: EmailBlockBase['settings'] & {
    color?: string;
    thickness?: string;
    style?: 'solid' | 'dashed' | 'dotted';
  };
}

// Spacer block
export interface SpacerBlock extends EmailBlockBase {
  type: EmailBlockType.SPACER;
  settings: EmailBlockBase['settings'] & {
    height?: string;
  };
}

// Social links block
export interface SocialBlock extends EmailBlockBase {
  type: EmailBlockType.SOCIAL;
  networks: Array<{
    type: 'facebook' | 'twitter' | 'instagram' | 'linkedin' | 'youtube' | 'pinterest';
    url: string;
    enabled: boolean;
  }>;
  settings: EmailBlockBase['settings'] & {
    iconSize?: string;
    iconSpacing?: string;
    iconColor?: string;
    showLabels?: boolean;
  };
}

// Product block
export interface ProductBlock extends EmailBlockBase {
  type: EmailBlockType.PRODUCT;
  productId: string;
  settings: EmailBlockBase['settings'] & {
    showImage?: boolean;
    showTitle?: boolean;
    showPrice?: boolean;
    showDescription?: boolean;
    imagePosition?: 'left' | 'right' | 'top';
    buttonText?: string;
    buttonColor?: string;
    buttonTextColor?: string;
  };
}

// Products grid block
export interface ProductsGridBlock extends EmailBlockBase {
  type: EmailBlockType.PRODUCTS_GRID;
  productIds: string[];
  settings: EmailBlockBase['settings'] & {
    columns?: number;
    showImage?: boolean;
    showTitle?: boolean;
    showPrice?: boolean;
    showButton?: boolean;
    buttonText?: string;
    imageAspectRatio?: string;
  };
}

// Order details block
export interface OrderDetailsBlock extends EmailBlockBase {
  type: EmailBlockType.ORDER_DETAILS;
  settings: EmailBlockBase['settings'] & {
    showOrderNumber?: boolean;
    showOrderDate?: boolean;
    showItems?: boolean;
    showPrices?: boolean;
    showShipping?: boolean;
    showTotals?: boolean;
    showCustomerInfo?: boolean;
  };
}

// Header block
export interface HeaderBlock extends EmailBlockBase {
  type: EmailBlockType.HEADER;
  settings: EmailBlockBase['settings'] & {
    showLogo?: boolean;
    logoPosition?: 'left' | 'center' | 'right';
    logoSize?: string;
    showTagline?: boolean;
    tagline?: string;
  };
}

// Footer block
export interface FooterBlock extends EmailBlockBase {
  type: EmailBlockType.FOOTER;
  settings: EmailBlockBase['settings'] & {
    showCompanyInfo?: boolean;
    companyInfo?: string;
    showCopyright?: boolean;
    copyrightText?: string;
    showUnsubscribe?: boolean;
    unsubscribeText?: string;
    showSocialLinks?: boolean;
  };
}

// Columns block
export interface ColumnsBlock extends EmailBlockBase {
  type: EmailBlockType.COLUMNS;
  columns: Array<{
    id: string;
    blocks: EmailBlock[];
    settings: {
      width?: string;
      backgroundColor?: string;
      padding?: string;
    };
  }>;
  settings: EmailBlockBase['settings'] & {
    columnsCount?: number;
    columnGap?: string;
    stackOnMobile?: boolean;
  };
}

// Custom HTML block
export interface CustomHtmlBlock extends EmailBlockBase {
  type: EmailBlockType.CUSTOM_HTML;
  html: string;
}

// Union type of all possible email blocks
export type EmailBlock = 
  | TextBlock
  | ImageBlock
  | ButtonBlock
  | DividerBlock
  | SpacerBlock
  | SocialBlock
  | ProductBlock
  | ProductsGridBlock
  | OrderDetailsBlock
  | HeaderBlock
  | FooterBlock
  | ColumnsBlock
  | CustomHtmlBlock;

// Theme color definition
export interface ThemeColor {
  id: string;
  name: string;
  value: string;
}

// Theme font definition
export interface ThemeFont {
  id: string;
  name: string;
  value: string;
}

// Theme definition
export interface Theme {
  id: string;
  name: string;
  colors: ThemeColor[];
  fonts: ThemeFont[];
}

// Email template structure
export interface EmailTemplate {
  id: string;
  name: string;
  description?: string;
  subject: string;
  previewText?: string;
  blocks: EmailBlock[];
  settings: {
    backgroundColor?: string;
    width?: string;
    fontFamily?: string;
    fontSize?: string;
    lineHeight?: string;
    color?: string;
    linkColor?: string;
    contentPadding?: string;
  };
  theme?: Theme;
  createdAt: string;
  updatedAt: string;
  isDefault?: boolean;
  category?: string;
  tags?: string[];
}

// Data source for dynamic content
export interface DataSource {
  id: string;
  name: string;
  type: 'products' | 'orders' | 'customers' | 'custom';
  endpoint?: string;
  query?: string;
  parameters?: Record<string, any>;
}

// Variable that can be used in templates
export interface TemplateVariable {
  name: string;
  key: string;
  description?: string;
  defaultValue?: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'object' | 'array';
  source?: string;
  path?: string;
}

// Email composer state
export interface EmailComposerState {
  currentTemplate: EmailTemplate | null;
  selectedBlockId: string | null;
  isDragging: boolean;
  availableVariables: TemplateVariable[];
  dataSources: DataSource[];
  previewMode: boolean;
  previewData: Record<string, any> | null;
  history: {
    past: EmailTemplate[];
    future: EmailTemplate[];
  };
  isSaving: boolean;
  lastSaved: string | null;
  errors: string[];
}

// Email composer context
export interface EmailComposerContextType {
  state: EmailComposerState;
  actions: {
    selectBlock: (blockId: string | null) => void;
    addBlock: (block: Omit<EmailBlock, 'id' | 'position'>, index?: number) => void;
    updateBlock: (blockId: string, updates: Partial<EmailBlock>) => void;
    removeBlock: (blockId: string) => void;
    moveBlock: (blockId: string, newIndex: number) => void;
    duplicateBlock: (blockId: string) => void;
    updateTemplate: (updates: Partial<EmailTemplate>) => void;
    saveTemplate: () => Promise<void>;
    loadTemplate: (templateId: string) => Promise<void>;
    createTemplate: (template: Partial<EmailTemplate>) => Promise<void>;
    previewTemplate: (withData?: Record<string, any>) => void;
    exitPreview: () => void;
    undo: () => void;
    redo: () => void;
    resetHistory: () => void;
  };
}