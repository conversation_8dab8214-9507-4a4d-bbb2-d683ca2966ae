# Email Composer System

A block-based email template editor with drag-and-drop functionality, resizable panels, and data querying capabilities.

## Features

- **Block-Based Editing**: Create emails by adding, removing, and rearranging content blocks
- **Drag and Drop**: Intuitive drag-and-drop interface using dnd-kit
- **Resizable Panels**: Flexible editor layout with resizable panels
- **Data Querying**: Connect to data sources to populate dynamic content
- **Variable Support**: Insert dynamic variables into templates
- **Preview Mode**: Preview emails with real data
- **Responsive Design**: Create emails that look great on all devices
- **Template Management**: Save, load, and manage email templates

## Architecture

The Email Composer system is built with a modular architecture:

```
lib/email/composer/
├── blocks/              # Block implementations
├── components/          # UI components
├── hooks/               # Custom React hooks
├── utils/               # Utility functions
├── context.tsx          # State management
├── index.ts             # Main exports
├── types.ts             # TypeScript types
└── README.md            # Documentation
```

## Block Types

The system supports the following block types:

- **Text**: Rich text content
- **Image**: Images with optional links
- **Button**: Call-to-action buttons
- **Divider**: Horizontal dividers
- **Spacer**: Vertical spacing
- **Social**: Social media links
- **Product**: Single product display
- **Products Grid**: Grid of products
- **Order Details**: Order summary information
- **Header**: Email header with logo
- **Footer**: Email footer with contact info
- **Columns**: Multi-column layout
- **Custom HTML**: Custom HTML code

## Usage

### Basic Usage

```tsx
import { EmailComposerProvider, EmailEditor } from '@/lib/email/composer';

export default function EmailComposerPage() {
  return (
    <EmailComposerProvider>
      <EmailEditor />
    </EmailComposerProvider>
  );
}
```

### Rendering Email Templates

```tsx
import { renderEmailTemplate } from '@/lib/email/composer';

// Render a template to HTML
const html = renderEmailTemplate(template, variables, data);
```

### Processing Variables

```tsx
import { processTemplateVariables } from '@/lib/email/composer';

// Process variables in a template
const processedTemplate = processTemplateVariables(template, variables, data);
```

## Data Querying

The system includes a data querying hook that can be used to fetch data from various sources:

```tsx
import { useDataQuery } from '@/lib/email/composer';

// Define a data source
const dataSource = {
  id: 'products',
  name: 'Products',
  type: 'products',
};

// Use the hook to fetch data
const { data, isLoading, error, refetch } = useDataQuery(dataSource);
```

## Dependencies

- **@dnd-kit/core**: Core drag-and-drop functionality
- **@dnd-kit/sortable**: Sortable lists
- **@dnd-kit/modifiers**: Modifiers for drag behavior
- **@dnd-kit/utilities**: Utility functions
- **react**: React library
- **react-dom**: React DOM utilities

## Customization

The Email Composer can be customized in several ways:

### Custom Blocks

You can create custom blocks by extending the `EmailBlockBase` interface and implementing the necessary components.

### Custom Styling

The system uses Tailwind CSS for styling, which can be customized through your project's Tailwind configuration.

### Custom Data Sources

You can implement custom data sources by extending the data querying functionality in the `hooks/use-data-query.ts` file.

## Best Practices

- **Performance**: For large templates, consider implementing virtualization for the block list
- **Accessibility**: Ensure all UI components are accessible
- **Testing**: Test templates across different email clients
- **Security**: Sanitize HTML content to prevent XSS attacks

## Future Enhancements

- Rich text editor for text blocks
- Image upload and management
- Template categories and tags
- A/B testing capabilities
- Analytics integration
- Export to various email service providers