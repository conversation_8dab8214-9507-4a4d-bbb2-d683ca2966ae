'use client';

import React from 'react';
import { EmailBlockType } from '../types';
import { createBlock } from '../utils/block-factory';
import { useEmailComposer } from '../context';

interface BlockPaletteProps {
  className?: string;
}

interface BlockTypeItem {
  type: EmailBlockType;
  label: string;
  icon: React.ReactNode;
  description: string;
}

/**
 * Component that displays a palette of available blocks that can be added to the email
 */
export function BlockPalette({ className = '' }: BlockPaletteProps) {
  const { actions } = useEmailComposer();
  
  const blockTypes: BlockTypeItem[] = [
    {
      type: EmailBlockType.HEADER,
      label: 'Header',
      icon: <HeaderIcon />,
      description: 'Add a header with logo and tagline',
    },
    {
      type: EmailBlockType.TEXT,
      label: 'Text',
      icon: <TextIcon />,
      description: 'Add a text block',
    },
    {
      type: EmailBlockType.IMAGE,
      label: 'Image',
      icon: <ImageIcon />,
      description: 'Add an image',
    },
    {
      type: EmailBlockType.BUTTON,
      label: 'Button',
      icon: <ButtonIcon />,
      description: 'Add a call-to-action button',
    },
    {
      type: EmailBlockType.DIVIDER,
      label: 'Divider',
      icon: <DividerIcon />,
      description: 'Add a horizontal divider',
    },
    {
      type: EmailBlockType.SPACER,
      label: 'Spacer',
      icon: <SpacerIcon />,
      description: 'Add vertical space',
    },
    {
      type: EmailBlockType.SOCIAL,
      label: 'Social',
      icon: <SocialIcon />,
      description: 'Add social media links',
    },
    {
      type: EmailBlockType.PRODUCT,
      label: 'Product',
      icon: <ProductIcon />,
      description: 'Add a single product',
    },
    {
      type: EmailBlockType.PRODUCTS_GRID,
      label: 'Products Grid',
      icon: <ProductsGridIcon />,
      description: 'Add a grid of products',
    },
    {
      type: EmailBlockType.ORDER_DETAILS,
      label: 'Order Details',
      icon: <OrderDetailsIcon />,
      description: 'Add order details',
    },
    {
      type: EmailBlockType.COLUMNS,
      label: 'Columns',
      icon: <ColumnsIcon />,
      description: 'Add a multi-column layout',
    },
    {
      type: EmailBlockType.CUSTOM_HTML,
      label: 'Custom HTML',
      icon: <CustomHtmlIcon />,
      description: 'Add custom HTML code',
    },
    {
      type: EmailBlockType.FOOTER,
      label: 'Footer',
      icon: <FooterIcon />,
      description: 'Add a footer with contact info',
    },
  ];
  
  const handleAddBlock = (type: EmailBlockType) => {
    const position = 999; // Will be reindexed by the context
    const newBlock = createBlock(type, position);
    actions.addBlock(newBlock);
  };
  
  return (
    <div className={`p-4 ${className}`}>
      <h3 className="text-lg font-medium mb-4">Content Blocks</h3>
      
      <div className="grid grid-cols-2 gap-2">
        {blockTypes.map((blockType) => (
          <button
            key={blockType.type}
            className="flex flex-col items-center justify-center p-3 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors"
            onClick={() => handleAddBlock(blockType.type)}
            title={blockType.description}
          >
            <div className="w-8 h-8 mb-2 flex items-center justify-center text-gray-600">
              {blockType.icon}
            </div>
            <span className="text-xs text-center">{blockType.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
}

// Simple icon components
function HeaderIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <rect x="3" y="4" width="18" height="8" rx="1" ry="1"></rect>
      <path d="M6 8h.01"></path>
      <path d="M9 8h.01"></path>
    </svg>
  );
}

function TextIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <polyline points="4 7 4 4 20 4 20 7"></polyline>
      <line x1="9" y1="20" x2="15" y2="20"></line>
      <line x1="12" y1="4" x2="12" y2="20"></line>
    </svg>
  );
}

function ImageIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
      <circle cx="8.5" cy="8.5" r="1.5"></circle>
      <polyline points="21 15 16 10 5 21"></polyline>
    </svg>
  );
}

function ButtonIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <rect x="3" y="8" width="18" height="8" rx="1" ry="1"></rect>
      <line x1="7" y1="12" x2="17" y2="12"></line>
    </svg>
  );
}

function DividerIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <line x1="3" y1="12" x2="21" y2="12"></line>
    </svg>
  );
}

function SpacerIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <polyline points="7 8 3 12 7 16"></polyline>
      <polyline points="17 8 21 12 17 16"></polyline>
      <line x1="14" y1="4" x2="10" y2="20"></line>
    </svg>
  );
}

function SocialIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
    </svg>
  );
}

function ProductIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <path d="M16 10a4 4 0 0 1-8 0"></path>
    </svg>
  );
}

function ProductsGridIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <rect x="3" y="3" width="7" height="7"></rect>
      <rect x="14" y="3" width="7" height="7"></rect>
      <rect x="14" y="14" width="7" height="7"></rect>
      <rect x="3" y="14" width="7" height="7"></rect>
    </svg>
  );
}

function OrderDetailsIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
      <polyline points="14 2 14 8 20 8"></polyline>
      <line x1="16" y1="13" x2="8" y2="13"></line>
      <line x1="16" y1="17" x2="8" y2="17"></line>
      <polyline points="10 9 9 9 8 9"></polyline>
    </svg>
  );
}

function ColumnsIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <line x1="12" y1="3" x2="12" y2="21"></line>
      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
    </svg>
  );
}

function CustomHtmlIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <polyline points="16 18 22 12 16 6"></polyline>
      <polyline points="8 6 2 12 8 18"></polyline>
    </svg>
  );
}

function FooterIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <rect x="3" y="12" width="18" height="8" rx="1" ry="1"></rect>
      <path d="M6 16h.01"></path>
      <path d="M9 16h.01"></path>
    </svg>
  );
}