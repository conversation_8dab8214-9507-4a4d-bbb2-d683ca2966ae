'use client';

import React, { useState } from 'react';
import { EmailBlock, EmailBlockType } from '../types';
import { StyleManagerFactory } from './StyleManagerFactory';

interface PropertiesPanelProps {
  block: EmailBlock;
  onUpdate: (updates: Partial<EmailBlock>) => void;
}

/**
 * Panel for editing block properties
 */
export function PropertiesPanel({ block, onUpdate }: PropertiesPanelProps) {
  const [activeTab, setActiveTab] = useState<'content' | 'style'>('content');
  
  return (
    <div className="p-4">
      <h3 className="text-lg font-medium mb-4">Block Properties</h3>
      
      {/* Tab navigation */}
      <div className="flex border-b border-gray-200 mb-4">
        <button
          className={`py-2 px-4 text-sm font-medium ${activeTab === 'content' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
          onClick={() => setActiveTab('content')}
        >
          Content
        </button>
        <button
          className={`py-2 px-4 text-sm font-medium ${activeTab === 'style' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
          onClick={() => setActiveTab('style')}
        >
          Style
        </button>
      </div>
      
      {/* Content tab */}
      {activeTab === 'content' && (
        <div className="space-y-4">
          {/* Text block content */}
          {block.type === EmailBlockType.TEXT && (
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Content
              </label>
              <textarea
                value={(block as any).content || ''}
                onChange={(e) => onUpdate({
                  content: e.target.value,
                } as any)}
                rows={8}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
          )}
          
          {/* Button block content */}
          {block.type === EmailBlockType.BUTTON && (
            <div className="space-y-3">
              <div>
                <label className="block text-sm text-gray-700 mb-1">
                  Button Text
                </label>
                <input
                  type="text"
                  value={(block as any).text || ''}
                  onChange={(e) => onUpdate({
                    text: e.target.value,
                  } as any)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-1">
                  Link URL
                </label>
                <input
                  type="text"
                  value={(block as any).link || ''}
                  onChange={(e) => onUpdate({
                    link: e.target.value,
                  } as any)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="fullWidth"
                  checked={(block as any).settings.fullWidth || false}
                  onChange={(e) => onUpdate({
                    settings: {
                      ...block.settings,
                      fullWidth: e.target.checked,
                    },
                  })}
                  className="mr-2"
                />
                <label htmlFor="fullWidth" className="text-sm text-gray-700">
                  Full Width
                </label>
              </div>
            </div>
          )}
          
          {/* Image block content */}
          {block.type === EmailBlockType.IMAGE && (
            <div className="space-y-3">
              <div>
                <label className="block text-sm text-gray-700 mb-1">
                  Image URL
                </label>
                <input
                  type="text"
                  value={(block as any).src || ''}
                  onChange={(e) => onUpdate({
                    src: e.target.value,
                  } as any)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-1">
                  Alt Text
                </label>
                <input
                  type="text"
                  value={(block as any).alt || ''}
                  onChange={(e) => onUpdate({
                    alt: e.target.value,
                  } as any)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-1">
                  Link URL (optional)
                </label>
                <input
                  type="text"
                  value={(block as any).link || ''}
                  onChange={(e) => onUpdate({
                    link: e.target.value,
                  } as any)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>
          )}
          
          {/* Divider block content */}
          {block.type === EmailBlockType.DIVIDER && (
            <div>
              <p className="text-sm text-gray-500">
                Divider block has no content properties. Use the Style tab to customize its appearance.
              </p>
            </div>
          )}
          
          {/* Spacer block content */}
          {block.type === EmailBlockType.SPACER && (
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Height
              </label>
              <input
                type="text"
                value={(block as any).settings.height || ''}
                onChange={(e) => onUpdate({
                  settings: {
                    ...block.settings,
                    height: e.target.value,
                  },
                })}
                placeholder="e.g., 20px"
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
          )}
          
          {/* Custom HTML block content */}
          {block.type === EmailBlockType.CUSTOM_HTML && (
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                HTML Content
              </label>
              <textarea
                value={(block as any).html || ''}
                onChange={(e) => onUpdate({
                  html: e.target.value,
                } as any)}
                rows={12}
                className="w-full p-2 border border-gray-300 rounded-md font-mono text-sm"
              />
            </div>
          )}
          
          {/* Social block content */}
          {block.type === EmailBlockType.SOCIAL && (
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Social Networks</h4>
              {(block as any).networks?.map((network: any, index: number) => (
                <div key={index} className="flex items-center space-x-2 p-2 border border-gray-200 rounded-md">
                  <input
                    type="checkbox"
                    checked={network.enabled}
                    onChange={(e) => {
                      const updatedNetworks = [...(block as any).networks];
                      updatedNetworks[index] = {
                        ...updatedNetworks[index],
                        enabled: e.target.checked,
                      };
                      onUpdate({
                        networks: updatedNetworks,
                      } as any);
                    }}
                    className="mr-1"
                  />
                  <span className="text-sm capitalize">{network.type}:</span>
                  <input
                    type="text"
                    value={network.url}
                    onChange={(e) => {
                      const updatedNetworks = [...(block as any).networks];
                      updatedNetworks[index] = {
                        ...updatedNetworks[index],
                        url: e.target.value,
                      };
                      onUpdate({
                        networks: updatedNetworks,
                      } as any);
                    }}
                    className="flex-1 p-1 text-sm border border-gray-300 rounded-md"
                    placeholder={`${network.type} URL`}
                  />
                </div>
              ))}
            </div>
          )}
          
          {/* Add more block-specific content settings here */}
        </div>
      )}
      
      {/* Style tab */}
      {activeTab === 'style' && (
        <StyleManagerFactory block={block} onUpdate={onUpdate} />
      )}
      
      <div className="pt-4 mt-4 border-t border-gray-200">
        <button
          className="w-full py-2 px-4 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
          onClick={() => {
            // This would be handled by the parent component
            // We're just showing the button here
          }}
        >
          Delete Block
        </button>
      </div>
    </div>
  );
}