'use client';

import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { EmailBlock, EmailBlockType } from '../types';

interface SortableBlockItemProps {
  block: EmailBlock;
  isSelected: boolean;
  onSelect: () => void;
  onDelete: () => void;
  onDuplicate: () => void;
}

/**
 * A sortable block item that can be dragged and dropped
 */
export function SortableBlockItem({
  block,
  isSelected,
  onSelect,
  onDelete,
  onDuplicate,
}: SortableBlockItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: block.id });
  
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    position: 'relative' as const,
    zIndex: isDragging ? 1 : 'auto',
  };
  
  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`mb-2 ${isSelected ? 'ring-2 ring-blue-500' : ''}`}
      onClick={onSelect}
    >
      <div className="group relative">
        {/* Block content preview */}
        <div className="border border-gray-200 bg-white">
          <BlockPreview block={block} />
        </div>
        
        {/* Block controls */}
        <div className={`absolute top-0 right-0 p-1 flex space-x-1 ${isSelected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'} transition-opacity`}>
          <button
            className="w-6 h-6 flex items-center justify-center bg-white border border-gray-200 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            onClick={(e) => {
              e.stopPropagation();
              onDuplicate();
            }}
            title="Duplicate"
          >
            <DuplicateIcon />
          </button>
          <button
            className="w-6 h-6 flex items-center justify-center bg-white border border-gray-200 rounded-md text-gray-500 hover:text-red-500 hover:bg-gray-50"
            onClick={(e) => {
              e.stopPropagation();
              onDelete();
            }}
            title="Delete"
          >
            <DeleteIcon />
          </button>
          <button
            className="w-6 h-6 flex items-center justify-center bg-white border border-gray-200 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-50 cursor-move"
            {...attributes}
            {...listeners}
            title="Drag to reorder"
          >
            <DragIcon />
          </button>
        </div>
        
        {/* Block type label */}
        <div className="absolute top-0 left-0 p-1">
          <div className="text-xs font-medium px-1.5 py-0.5 bg-gray-100 text-gray-700 rounded">
            {getBlockTypeLabel(block.type)}
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Renders a preview of the block content
 */
function BlockPreview({ block }: { block: EmailBlock }) {
  switch (block.type) {
    case EmailBlockType.TEXT:
      return (
        <div
          className="p-4"
          style={{
            backgroundColor: block.settings.backgroundColor,
            padding: block.settings.padding,
            color: (block as any).settings.color,
            fontFamily: (block as any).settings.fontFamily,
            fontSize: (block as any).settings.fontSize,
            fontWeight: (block as any).settings.fontWeight,
            lineHeight: (block as any).settings.lineHeight,
            textAlign: (block as any).settings.textAlign as any,
          }}
          dangerouslySetInnerHTML={{ __html: (block as any).content }}
        />
      );
    
    case EmailBlockType.IMAGE:
      return (
        <div
          className="p-4 flex justify-center"
          style={{
            backgroundColor: block.settings.backgroundColor,
            padding: block.settings.padding,
          }}
        >
          {(block as any).src ? (
            <img
              src={(block as any).src}
              alt={(block as any).alt || ''}
              style={{
                maxWidth: (block as any).settings.maxWidth || '100%',
                height: (block as any).settings.height || 'auto',
              }}
            />
          ) : (
            <div className="w-full h-32 bg-gray-200 flex items-center justify-center text-gray-500">
              Image Placeholder
            </div>
          )}
        </div>
      );
    
    case EmailBlockType.BUTTON:
      return (
        <div
          className="p-4 flex justify-center"
          style={{
            backgroundColor: block.settings.backgroundColor,
            padding: block.settings.padding,
          }}
        >
          <button
            className="px-4 py-2 rounded"
            style={{
              backgroundColor: (block as any).settings.buttonColor || '#6C1411',
              color: (block as any).settings.textColor || '#ffffff',
              fontSize: (block as any).settings.fontSize,
              fontWeight: (block as any).settings.fontWeight,
              borderRadius: (block as any).settings.borderRadius,
              width: (block as any).settings.fullWidth ? '100%' : 'auto',
            }}
          >
            {(block as any).text || 'Button Text'}
          </button>
        </div>
      );
    
    case EmailBlockType.DIVIDER:
      return (
        <div
          className="p-4"
          style={{
            backgroundColor: block.settings.backgroundColor,
            padding: block.settings.padding,
          }}
        >
          <hr
            style={{
              borderTop: `${(block as any).settings.thickness || '1px'} ${(block as any).settings.style || 'solid'} ${(block as any).settings.color || '#e0e0e0'}`,
              margin: 0,
            }}
          />
        </div>
      );
    
    case EmailBlockType.SPACER:
      return (
        <div
          style={{
            height: (block as any).settings.height || '20px',
            backgroundColor: block.settings.backgroundColor,
          }}
        />
      );
    
    case EmailBlockType.SOCIAL:
      return (
        <div
          className="p-4 flex justify-center"
          style={{
            backgroundColor: block.settings.backgroundColor,
            padding: block.settings.padding,
          }}
        >
          <div className="flex space-x-2">
            {(block as any).networks && (block as any).networks.filter((n: any) => n.enabled).map((network: any, index: number) => (
              <div
                key={index}
                className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center"
                style={{
                  backgroundColor: (block as any).settings.iconColor || '#333333',
                }}
              />
            ))}
          </div>
        </div>
      );
    
    case EmailBlockType.PRODUCT:
      return (
        <div
          className="p-4"
          style={{
            backgroundColor: block.settings.backgroundColor,
            padding: block.settings.padding,
          }}
        >
          <div className="flex">
            {(block as any).settings.showImage && (
              <div className="w-1/3 bg-gray-200 h-24 flex items-center justify-center text-gray-500">
                Product Image
              </div>
            )}
            <div className={`${(block as any).settings.showImage ? 'w-2/3 pl-4' : 'w-full'}`}>
              {(block as any).settings.showTitle && (
                <div className="font-medium mb-1">Product Title</div>
              )}
              {(block as any).settings.showPrice && (
                <div className="text-sm font-bold mb-2">R299.99</div>
              )}
              {(block as any).settings.showDescription && (
                <div className="text-sm text-gray-600 mb-2">Product description goes here...</div>
              )}
              <button
                className="px-3 py-1 text-sm rounded"
                style={{
                  backgroundColor: (block as any).settings.buttonColor || '#6C1411',
                  color: (block as any).settings.buttonTextColor || '#ffffff',
                }}
              >
                {(block as any).settings.buttonText || 'View Product'}
              </button>
            </div>
          </div>
        </div>
      );
    
    case EmailBlockType.PRODUCTS_GRID:
      return (
        <div
          className="p-4"
          style={{
            backgroundColor: block.settings.backgroundColor,
            padding: block.settings.padding,
          }}
        >
          <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${(block as any).settings.columns || 2}, 1fr)` }}>
            {Array.from({ length: Math.min((block as any).productIds?.length || 0, 4) || 2 }).map((_, index) => (
              <div key={index} className="text-center">
                <div className="bg-gray-200 h-20 mb-2 flex items-center justify-center text-gray-500">
                  Product {index + 1}
                </div>
                <div className="text-sm font-medium mb-1">Product Title</div>
                <div className="text-xs font-bold mb-2">R299.99</div>
              </div>
            ))}
          </div>
        </div>
      );
    
    case EmailBlockType.ORDER_DETAILS:
      return (
        <div
          className="p-4"
          style={{
            backgroundColor: block.settings.backgroundColor,
            padding: block.settings.padding,
          }}
        >
          <div className="text-sm">
            <div className="flex justify-between mb-2">
              <div><strong>Order #:</strong> 12345</div>
              <div><strong>Date:</strong> {new Date().toLocaleDateString()}</div>
            </div>
            <table className="w-full mb-2 text-xs">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-1">Product</th>
                  <th className="text-center py-1">Qty</th>
                  <th className="text-right py-1">Price</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b">
                  <td className="py-1">Product 1</td>
                  <td className="text-center py-1">1</td>
                  <td className="text-right py-1">R299.99</td>
                </tr>
                <tr className="border-b">
                  <td className="py-1">Product 2</td>
                  <td className="text-center py-1">2</td>
                  <td className="text-right py-1">R199.98</td>
                </tr>
              </tbody>
            </table>
            <div className="flex justify-between border-t pt-1">
              <div><strong>Total:</strong></div>
              <div>R499.97</div>
            </div>
          </div>
        </div>
      );
    
    case EmailBlockType.HEADER:
      
    
    case EmailBlockType.FOOTER:
      return (
        <div
          className="p-4 text-center text-sm text-gray-600"
          style={{
            backgroundColor: block.settings.backgroundColor || '#f8f9fa',
            padding: block.settings.padding,
          }}
        >
          {(block as any).settings.showCompanyInfo && (
            <div className="mb-2">
              {(block as any).settings.companyInfo || 'Coco Milk Kids, 123 Main St, Cape Town, South Africa'}
            </div>
          )}
          {(block as any).settings.showSocialLinks && (
            <div className="flex justify-center space-x-2 mb-2">
              <div className="w-6 h-6 rounded-full bg-gray-400"></div>
              <div className="w-6 h-6 rounded-full bg-gray-400"></div>
              <div className="w-6 h-6 rounded-full bg-gray-400"></div>
            </div>
          )}
          {(block as any).settings.showCopyright && (
            <div className="mb-2">
              {(block as any).settings.copyrightText || `© ${new Date().getFullYear()} Coco Milk Kids. All rights reserved.`}
            </div>
          )}
          {(block as any).settings.showUnsubscribe && (
            <div>
              {(block as any).settings.unsubscribeText || 'Unsubscribe | View in browser'}
            </div>
          )}
        </div>
      );
    
    case EmailBlockType.COLUMNS:
      return (
        <div
          style={{
            backgroundColor: block.settings.backgroundColor,
            padding: block.settings.padding,
          }}
        >
          <div 
            className="flex"
            style={{ gap: (block as any).settings.columnGap || '20px' }}
          >
            {(block as any).columns.map((column: any, index: number) => (
              <div 
                key={index} 
                style={{ 
                  width: column.settings.width || `${100 / (block as any).columns.length}%`,
                  backgroundColor: column.settings.backgroundColor,
                  padding: column.settings.padding,
                }}
                className="border border-dashed border-gray-300"
              >
                <div className="h-20 flex items-center justify-center text-gray-500 text-sm">
                  Column {index + 1}
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    
    case EmailBlockType.CUSTOM_HTML:
      return (
        <div
          className="p-4"
          style={{
            backgroundColor: block.settings.backgroundColor,
            padding: block.settings.padding,
          }}
        >
          <div className="text-xs font-mono bg-gray-100 p-2 rounded overflow-hidden">
            {(block as any).html ? (
              <code>{(block as any).html.substring(0, 100)}{(block as any).html.length > 100 ? '...' : ''}</code>
            ) : (
              <code>&lt;div&gt;Custom HTML here&lt;/div&gt;</code>
            )}
          </div>
        </div>
      );
    
    default:
      return (
        <div className="p-4 text-center text-gray-500">
          Unknown block type: {block.type}
        </div>
      );
  }
}

/**
 * Get a human-readable label for a block type
 */
function getBlockTypeLabel(type: EmailBlockType): string {
  switch (type) {
    case EmailBlockType.TEXT:
      return 'Text';
    case EmailBlockType.IMAGE:
      return 'Image';
    case EmailBlockType.BUTTON:
      return 'Button';
    case EmailBlockType.DIVIDER:
      return 'Divider';
    case EmailBlockType.SPACER:
      return 'Spacer';
    case EmailBlockType.SOCIAL:
      return 'Social';
    case EmailBlockType.PRODUCT:
      return 'Product';
    case EmailBlockType.PRODUCTS_GRID:
      return 'Products Grid';
    case EmailBlockType.ORDER_DETAILS:
      return 'Order Details';
    case EmailBlockType.HEADER:
      return 'Header';
    case EmailBlockType.FOOTER:
      return 'Footer';
    case EmailBlockType.COLUMNS:
      return 'Columns';
    case EmailBlockType.CUSTOM_HTML:
      return 'Custom HTML';
    default:
      return 'Unknown';
  }
}

// Simple icon components
function DuplicateIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
      <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
    </svg>
  );
}

function DeleteIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <polyline points="3 6 5 6 21 6"></polyline>
      <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
    </svg>
  );
}

function DragIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
      <circle cx="9" cy="12" r="1"></circle>
      <circle cx="9" cy="5" r="1"></circle>
      <circle cx="9" cy="19" r="1"></circle>
      <circle cx="15" cy="12" r="1"></circle>
      <circle cx="15" cy="5" r="1"></circle>
      <circle cx="15" cy="19" r="1"></circle>
    </svg>
  );
}