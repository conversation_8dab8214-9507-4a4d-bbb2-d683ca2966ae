'use client';

import React, { useState } from 'react';
import { EmailTemplate } from '../types';
import { useEmailComposer } from '../context';
import { ThemeManager } from './ThemeManager';
import { StyleSection } from './StyleSection';
import { ColorPicker } from './ColorPicker';

interface TemplateSettingsProps {
  template: EmailTemplate;
}

/**
 * Component for editing template-wide settings
 */
export function TemplateSettings({ template }: TemplateSettingsProps) {
  const { actions } = useEmailComposer();
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    theme: true,
    layout: false,
    typography: false,
    colors: false,
    spacing: false,
  });
  
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };
  
  const handleSettingChange = (key: string, value: string) => {
    actions.updateTemplate({
      settings: {
        ...template.settings,
        [key]: value,
      },
    });
  };
  
  return (
    <div className="p-4">
      <h3 className="text-lg font-medium mb-4">Template Settings</h3>
      
      <div className="space-y-4">
        {/* Theme Manager */}
        <StyleSection
          title="Theme"
          expanded={expandedSections.theme}
          onToggle={() => toggleSection('theme')}
        >
          <ThemeManager />
        </StyleSection>
        
        {/* Layout Settings */}
        <StyleSection
          title="Layout"
          expanded={expandedSections.layout}
          onToggle={() => toggleSection('layout')}
        >
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Width
              </label>
              <input
                type="text"
                value={template.settings.width || '600px'}
                onChange={(e) => handleSettingChange('width', e.target.value)}
                placeholder="e.g., 600px"
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Content Padding
              </label>
              <input
                type="text"
                value={template.settings.contentPadding || '20px'}
                onChange={(e) => handleSettingChange('contentPadding', e.target.value)}
                placeholder="e.g., 20px"
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              />
            </div>
          </div>
        </StyleSection>
        
        {/* Typography Settings */}
        <StyleSection
          title="Typography"
          expanded={expandedSections.typography}
          onToggle={() => toggleSection('typography')}
        >
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Font Family
              </label>
              <select
                value={template.settings.fontFamily || 'Arial, sans-serif'}
                onChange={(e) => handleSettingChange('fontFamily', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="Arial, sans-serif">Arial</option>
                <option value="'Helvetica Neue', Helvetica, Arial, sans-serif">Helvetica</option>
                <option value="Georgia, serif">Georgia</option>
                <option value="'Times New Roman', Times, serif">Times New Roman</option>
                <option value="Verdana, sans-serif">Verdana</option>
                <option value="Tahoma, sans-serif">Tahoma</option>
                <option value="'Trebuchet MS', sans-serif">Trebuchet MS</option>
                <option value="'Courier New', Courier, monospace">Courier New</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Font Size
              </label>
              <input
                type="text"
                value={template.settings.fontSize || '16px'}
                onChange={(e) => handleSettingChange('fontSize', e.target.value)}
                placeholder="e.g., 16px"
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Line Height
              </label>
              <input
                type="text"
                value={template.settings.lineHeight || '1.5'}
                onChange={(e) => handleSettingChange('lineHeight', e.target.value)}
                placeholder="e.g., 1.5"
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              />
            </div>
          </div>
        </StyleSection>
        
        {/* Colors Settings */}
        <StyleSection
          title="Colors"
          expanded={expandedSections.colors}
          onToggle={() => toggleSection('colors')}
        >
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Background Color
              </label>
              <ColorPicker
                color={template.settings.backgroundColor || '#f8f9fa'}
                onChange={(color) => handleSettingChange('backgroundColor', color)}
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Text Color
              </label>
              <ColorPicker
                color={template.settings.color || '#333333'}
                onChange={(color) => handleSettingChange('color', color)}
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-1">
                Link Color
              </label>
              <ColorPicker
                color={template.settings.linkColor || '#0066cc'}
                onChange={(color) => handleSettingChange('linkColor', color)}
              />
            </div>
          </div>
        </StyleSection>
      </div>
    </div>
  );
}