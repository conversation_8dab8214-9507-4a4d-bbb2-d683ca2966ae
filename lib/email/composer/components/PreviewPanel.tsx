'use client';

import React, { useState, useEffect } from 'react';
import { useEmailComposer } from '../context';
import { renderEmailTemplate } from '../utils/template-renderer';

/**
 * Panel for previewing the email template
 */
export function PreviewPanel() {
  const { state } = useEmailComposer();
  const [html, setHtml] = useState<string>('');
  const [viewMode, setViewMode] = useState<'desktop' | 'mobile'>('desktop');
  const [loading, setLoading] = useState<boolean>(true);
  
  useEffect(() => {
    if (state.currentTemplate) {
      try {
        // Render the template to HTML
        const renderedHtml = renderEmailTemplate(
          state.currentTemplate,
          state.availableVariables,
          state.previewData || {}
        );
        
        setHtml(renderedHtml);
      } catch (error) {
        console.error('Error rendering template:', error);
      } finally {
        setLoading(false);
      }
    }
  }, [state.currentTemplate, state.availableVariables, state.previewData]);
  
  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        <h3 className="text-lg font-medium">Preview</h3>
        
        <div className="flex items-center space-x-2">
          <button
            className={`py-1 px-3 text-sm border rounded-md transition-colors ${viewMode === 'desktop' ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300 hover:bg-gray-50'}`}
            onClick={() => setViewMode('desktop')}
          >
            Desktop
          </button>
          <button
            className={`py-1 px-3 text-sm border rounded-md transition-colors ${viewMode === 'mobile' ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300 hover:bg-gray-50'}`}
            onClick={() => setViewMode('mobile')}
          >
            Mobile
          </button>
        </div>
      </div>
      
      <div className="flex-1 overflow-auto p-4 bg-gray-100">
        <div 
          className={`mx-auto bg-white shadow-md ${viewMode === 'mobile' ? 'max-w-[375px]' : ''}`}
          style={{
            maxWidth: viewMode === 'mobile' ? '375px' : state.currentTemplate?.settings?.width || '600px',
          }}
        >
          {loading ? (
            <div className="p-8 text-center text-gray-500">
              <p>Loading preview...</p>
            </div>
          ) : (
            <iframe
              srcDoc={html}
              title="Email Preview"
              className="w-full border-0"
              style={{ height: '800px' }}
            />
          )}
        </div>
      </div>
    </div>
  );
}