'use client';

import React from 'react';
import { EmailBlock, EmailBlockType } from '../types';

interface StylePreviewProps {
  block: EmailBlock;
  settings: Record<string, any>;
}

/**
 * Component for previewing style changes in real-time
 */
export function StylePreview({ block, settings }: StylePreviewProps) {
  // Generate inline styles based on settings
  const generateStyles = () => {
    const styles: Record<string, any> = {};
    
    // Apply common styles
    if (settings.width) styles.width = settings.width;
    if (settings.backgroundColor) styles.backgroundColor = settings.backgroundColor;
    if (settings.color) styles.color = settings.color;
    if (settings.fontFamily) styles.fontFamily = settings.fontFamily;
    if (settings.fontSize) styles.fontSize = settings.fontSize;
    if (settings.fontWeight) styles.fontWeight = settings.fontWeight;
    if (settings.lineHeight) styles.lineHeight = settings.lineHeight;
    if (settings.textAlign) styles.textAlign = settings.textAlign;
    if (settings.padding) styles.padding = settings.padding;
    if (settings.margin) styles.margin = settings.margin;
    
    // Apply border styles
    if (settings.borderStyle && settings.borderStyle !== 'none') {
      styles.borderStyle = settings.borderStyle;
      if (settings.borderWidth) styles.borderWidth = settings.borderWidth;
      if (settings.borderColor) styles.borderColor = settings.borderColor;
      if (settings.borderRadius) styles.borderRadius = settings.borderRadius;
    }
    
    // Button-specific styles
    if (block.type === EmailBlockType.BUTTON) {
      if (settings.buttonColor) styles.backgroundColor = settings.buttonColor;
      if (settings.textColor) styles.color = settings.textColor;
      styles.display = 'inline-block';
      styles.textDecoration = 'none';
      styles.textAlign = 'center';
      styles.cursor = 'pointer';
    }
    
    return styles;
  };
  
  // Render preview content based on block type
  const renderPreviewContent = () => {
    switch (block.type) {
      case EmailBlockType.TEXT:
        return <p>Sample text content</p>;
        
      case EmailBlockType.BUTTON:
        return <div>Button Text</div>;
        
      case EmailBlockType.IMAGE:
        return (
          <div className="bg-gray-200 flex items-center justify-center" style={{ height: '100px' }}>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
        );
        
      case EmailBlockType.DIVIDER:
        return <hr style={{ borderTopWidth: settings.thickness || '1px', borderTopStyle: settings.style || 'solid', borderTopColor: settings.color || '#dddddd' }} />;
        
      default:
        return <div>Preview not available</div>;
    }
  };
  
  return (
    <div className="border border-gray-300 rounded-md overflow-hidden">
      <div className="bg-gray-100 p-2 text-xs font-medium text-gray-700 border-b border-gray-300">
        Live Preview
      </div>
      <div className="p-4 bg-white">
        <div style={generateStyles()}>
          {renderPreviewContent()}
        </div>
      </div>
    </div>
  );
}