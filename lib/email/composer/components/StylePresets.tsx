'use client';

import React, { useState } from 'react';
import { EmailBlock, EmailBlockType } from '../types';

// Define preset types
export interface StylePreset {
  id: string;
  name: string;
  description?: string;
  blockType: EmailBlockType;
  settings: Record<string, any>;
  thumbnail?: string;
}

interface StylePresetsProps {
  blockType: EmailBlockType;
  onApply: (settings: Record<string, any>) => void;
}

/**
 * Component for displaying and applying style presets
 */
export function StylePresets({ blockType, onApply }: StylePresetsProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Filter presets by block type
  const filteredPresets = STYLE_PRESETS.filter(preset => 
    preset.blockType === blockType || preset.blockType === 'all'
  );

  if (filteredPresets.length === 0) {
    return null;
  }

  return (
    <div className="mb-4">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full p-2 text-sm font-medium text-left text-gray-700 bg-gray-50 hover:bg-gray-100 rounded-md border border-gray-200"
      >
        <span>Style Presets</span>
        <span>{isOpen ? '−' : '+'}</span>
      </button>
      
      {isOpen && (
        <div className="mt-2 p-2 border border-gray-200 rounded-md bg-white">
          <div className="grid grid-cols-2 gap-2">
            {filteredPresets.map(preset => (
              <button
                key={preset.id}
                type="button"
                onClick={() => {
                  onApply(preset.settings);
                  setIsOpen(false);
                }}
                className="p-2 border border-gray-200 rounded-md hover:bg-gray-50 text-left"
              >
                <div className="h-12 mb-2 bg-gray-100 rounded flex items-center justify-center overflow-hidden">
                  {preset.thumbnail ? (
                    <img 
                      src={preset.thumbnail} 
                      alt={preset.name} 
                      className="w-full h-full object-cover" 
                    />
                  ) : (
                    <div 
                      className="w-full h-full" 
                      style={{
                        backgroundColor: preset.settings.backgroundColor || '#ffffff',
                        border: preset.settings.borderWidth ? 
                          `${preset.settings.borderWidth} ${preset.settings.borderStyle || 'solid'} ${preset.settings.borderColor || '#000000'}` : 
                          'none',
                        borderRadius: preset.settings.borderRadius || '0',
                      }}
                    />
                  )}
                </div>
                <div className="text-xs font-medium">{preset.name}</div>
                {preset.description && (
                  <div className="text-xs text-gray-500 truncate">{preset.description}</div>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// Define some default style presets
const STYLE_PRESETS: StylePreset[] = [
  // Button presets
  {
    id: 'button-primary',
    name: 'Primary Button',
    description: 'Main call-to-action button',
    blockType: EmailBlockType.BUTTON,
    settings: {
      buttonColor: '#6C1411',
      textColor: '#ffffff',
      padding: '12px 24px',
      borderRadius: '4px',
      fontWeight: 'bold',
      fontSize: '16px',
      align: 'center',
    },
  },
  {
    id: 'button-secondary',
    name: 'Secondary Button',
    description: 'Alternative action button',
    blockType: EmailBlockType.BUTTON,
    settings: {
      buttonColor: '#ffffff',
      textColor: '#6C1411',
      padding: '10px 20px',
      borderRadius: '4px',
      borderWidth: '2px',
      borderStyle: 'solid',
      borderColor: '#6C1411',
      fontWeight: 'bold',
      fontSize: '16px',
      align: 'center',
    },
  },
  {
    id: 'button-ghost',
    name: 'Ghost Button',
    description: 'Subtle button style',
    blockType: EmailBlockType.BUTTON,
    settings: {
      buttonColor: 'transparent',
      textColor: '#6C1411',
      padding: '10px 20px',
      borderRadius: '4px',
      borderWidth: '1px',
      borderStyle: 'solid',
      borderColor: '#6C1411',
      fontWeight: 'normal',
      fontSize: '16px',
      align: 'center',
    },
  },
  {
    id: 'button-rounded',
    name: 'Rounded Button',
    description: 'Button with rounded corners',
    blockType: EmailBlockType.BUTTON,
    settings: {
      buttonColor: '#6C1411',
      textColor: '#ffffff',
      padding: '12px 24px',
      borderRadius: '50px',
      fontWeight: 'bold',
      fontSize: '16px',
      align: 'center',
    },
  },
  
  // Text presets
  {
    id: 'text-heading',
    name: 'Heading',
    description: 'Large heading text',
    blockType: EmailBlockType.TEXT,
    settings: {
      fontSize: '24px',
      fontWeight: 'bold',
      lineHeight: '1.2',
      color: '#333333',
      textAlign: 'left',
      margin: '0 0 16px 0',
    },
  },
  {
    id: 'text-subheading',
    name: 'Subheading',
    description: 'Secondary heading text',
    blockType: EmailBlockType.TEXT,
    settings: {
      fontSize: '18px',
      fontWeight: '600',
      lineHeight: '1.3',
      color: '#555555',
      textAlign: 'left',
      margin: '0 0 12px 0',
    },
  },
  {
    id: 'text-body',
    name: 'Body Text',
    description: 'Regular paragraph text',
    blockType: EmailBlockType.TEXT,
    settings: {
      fontSize: '16px',
      fontWeight: 'normal',
      lineHeight: '1.5',
      color: '#333333',
      textAlign: 'left',
      margin: '0 0 16px 0',
    },
  },
  {
    id: 'text-quote',
    name: 'Quote',
    description: 'Styled quote or testimonial',
    blockType: EmailBlockType.TEXT,
    settings: {
      fontSize: '18px',
      fontWeight: 'normal',
      fontStyle: 'italic',
      lineHeight: '1.5',
      color: '#555555',
      textAlign: 'center',
      padding: '16px 24px',
      borderLeft: '4px solid #6C1411',
      backgroundColor: '#f9f9f9',
      margin: '16px 0',
    },
  },
  
  // Divider presets
  {
    id: 'divider-solid',
    name: 'Solid Divider',
    description: 'Standard horizontal line',
    blockType: EmailBlockType.DIVIDER,
    settings: {
      color: '#dddddd',
      thickness: '1px',
      style: 'solid',
      margin: '24px 0',
    },
  },
  {
    id: 'divider-dashed',
    name: 'Dashed Divider',
    description: 'Dashed horizontal line',
    blockType: EmailBlockType.DIVIDER,
    settings: {
      color: '#dddddd',
      thickness: '1px',
      style: 'dashed',
      margin: '24px 0',
    },
  },
  {
    id: 'divider-thick',
    name: 'Thick Divider',
    description: 'Prominent divider line',
    blockType: EmailBlockType.DIVIDER,
    settings: {
      color: '#6C1411',
      thickness: '3px',
      style: 'solid',
      margin: '32px 0',
      width: '100px',
      align: 'center',
    },
  },
  
  // Image presets
  {
    id: 'image-full',
    name: 'Full Width Image',
    description: 'Image that spans full width',
    blockType: EmailBlockType.IMAGE,
    settings: {
      width: '100%',
      align: 'center',
      margin: '16px 0',
    },
  },
  {
    id: 'image-rounded',
    name: 'Rounded Image',
    description: 'Image with rounded corners',
    blockType: EmailBlockType.IMAGE,
    settings: {
      width: '100%',
      align: 'center',
      margin: '16px 0',
      borderRadius: '8px',
    },
  },
  {
    id: 'image-bordered',
    name: 'Bordered Image',
    description: 'Image with a border',
    blockType: EmailBlockType.IMAGE,
    settings: {
      width: '100%',
      align: 'center',
      margin: '16px 0',
      borderWidth: '1px',
      borderStyle: 'solid',
      borderColor: '#dddddd',
      padding: '4px',
      backgroundColor: '#ffffff',
    },
  },
];