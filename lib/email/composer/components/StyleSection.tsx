'use client';

import React from 'react';

interface StyleSectionProps {
  title: string;
  expanded: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

/**
 * Collapsible section component for style managers
 */
export function StyleSection({ title, expanded, onToggle, children }: StyleSectionProps) {
  return (
    <div className="border border-gray-200 rounded-md overflow-hidden">
      <button
        type="button"
        onClick={onToggle}
        className="flex items-center justify-between w-full p-3 text-sm font-medium text-left text-gray-700 bg-gray-50 hover:bg-gray-100"
      >
        <span>{title}</span>
        <span>{expanded ? '−' : '+'}</span>
      </button>
      
      {expanded && (
        <div className="p-3 border-t border-gray-200 bg-white">
          {children}
        </div>
      )}
    </div>
  );
}