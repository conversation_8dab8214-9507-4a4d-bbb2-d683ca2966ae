'use client';

import React, { useState, useEffect } from 'react';

interface StyleProperty {
  key: string;
  name: string;
  category: string;
  description?: string;
}

interface StyleSearchProps {
  onSelectProperty: (property: string) => void;
}

// Define common style properties
const STYLE_PROPERTIES: StyleProperty[] = [
  // Layout properties
  { key: 'width', name: 'Width', category: 'layout', description: 'Sets the width of an element' },
  { key: 'height', name: 'Height', category: 'layout', description: 'Sets the height of an element' },
  { key: 'align', name: 'Alignment', category: 'layout', description: 'Sets the alignment of an element' },
  { key: 'display', name: 'Display', category: 'layout', description: 'Sets how an element is displayed' },
  
  // Typography properties
  { key: 'fontFamily', name: 'Font Family', category: 'typography', description: 'Sets the font family for text' },
  { key: 'fontSize', name: '<PERSON>ont Si<PERSON>', category: 'typography', description: 'Sets the size of the font' },
  { key: 'fontWeight', name: 'Font Weight', category: 'typography', description: 'Sets the thickness of characters in text' },
  { key: 'lineHeight', name: 'Line Height', category: 'typography', description: 'Sets the height of a line of text' },
  { key: 'color', name: 'Text Color', category: 'typography', description: 'Sets the color of text' },
  { key: 'textAlign', name: 'Text Align', category: 'typography', description: 'Sets the horizontal alignment of text' },
  { key: 'textDecoration', name: 'Text Decoration', category: 'typography', description: 'Sets the decoration added to text' },
  
  // Spacing properties
  { key: 'padding', name: 'Padding', category: 'spacing', description: 'Sets the padding area on all sides of an element' },
  { key: 'paddingTop', name: 'Padding Top', category: 'spacing', description: 'Sets the top padding of an element' },
  { key: 'paddingRight', name: 'Padding Right', category: 'spacing', description: 'Sets the right padding of an element' },
  { key: 'paddingBottom', name: 'Padding Bottom', category: 'spacing', description: 'Sets the bottom padding of an element' },
  { key: 'paddingLeft', name: 'Padding Left', category: 'spacing', description: 'Sets the left padding of an element' },
  { key: 'margin', name: 'Margin', category: 'spacing', description: 'Sets the margin area on all sides of an element' },
  { key: 'marginTop', name: 'Margin Top', category: 'spacing', description: 'Sets the top margin of an element' },
  { key: 'marginRight', name: 'Margin Right', category: 'spacing', description: 'Sets the right margin of an element' },
  { key: 'marginBottom', name: 'Margin Bottom', category: 'spacing', description: 'Sets the bottom margin of an element' },
  { key: 'marginLeft', name: 'Margin Left', category: 'spacing', description: 'Sets the left margin of an element' },
  
  // Border properties
  { key: 'borderStyle', name: 'Border Style', category: 'borders', description: 'Sets the style of an element\'s border' },
  { key: 'borderWidth', name: 'Border Width', category: 'borders', description: 'Sets the width of an element\'s border' },
  { key: 'borderColor', name: 'Border Color', category: 'borders', description: 'Sets the color of an element\'s border' },
  { key: 'borderRadius', name: 'Border Radius', category: 'borders', description: 'Sets the radius of the element\'s corners' },
  
  // Background properties
  { key: 'backgroundColor', name: 'Background Color', category: 'background', description: 'Sets the background color of an element' },
  
  // Button-specific properties
  { key: 'buttonColor', name: 'Button Color', category: 'button', description: 'Sets the background color of a button' },
  { key: 'textColor', name: 'Button Text Color', category: 'button', description: 'Sets the text color of a button' },
  { key: 'fullWidth', name: 'Full Width', category: 'button', description: 'Sets whether a button should take up the full width' },
];

/**
 * Component for searching and finding style properties
 */
export function StyleSearch({ onSelectProperty }: StyleSearchProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<StyleProperty[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  
  // Filter properties based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }
    
    const filteredResults = STYLE_PROPERTIES.filter(property => 
      property.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      property.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
      property.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (property.description && property.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    
    setSearchResults(filteredResults);
  }, [searchTerm]);
  
  // Handle property selection
  const handleSelectProperty = (property: StyleProperty) => {
    onSelectProperty(property.key);
    setSearchTerm('');
    setIsOpen(false);
  };
  
  return (
    <div className="relative">
      <div className="flex items-center">
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value);
            setIsOpen(true);
          }}
          onFocus={() => setIsOpen(true)}
          placeholder="Search for style properties..."
          className="w-full p-2 border border-gray-300 rounded-md text-sm"
        />
        {searchTerm && (
          <button
            type="button"
            onClick={() => {
              setSearchTerm('');
              setIsOpen(false);
            }}
            className="absolute right-2 text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        )}
      </div>
      
      {isOpen && searchResults.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
          {searchResults.map((property) => (
            <button
              key={property.key}
              type="button"
              onClick={() => handleSelectProperty(property)}
              className="w-full text-left px-3 py-2 hover:bg-gray-100 border-b border-gray-200 last:border-b-0"
            >
              <div className="font-medium text-sm">{property.name}</div>
              {property.description && (
                <div className="text-xs text-gray-500">{property.description}</div>
              )}
              <div className="text-xs text-gray-400 mt-1">Category: {property.category}</div>
            </button>
          ))}
        </div>
      )}
      
      {isOpen && searchTerm && searchResults.length === 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg p-3 text-sm text-gray-500">
          No style properties found matching "{searchTerm}"
        </div>
      )}
    </div>
  );
}