'use client';

import React, { useState, useEffect } from 'react';
import { EmailBlockType } from '../types';

// Storage key for localStorage
const STORAGE_KEY_STYLES = 'emailComposer.copiedStyles';
const STORAGE_KEY_TYPE = 'emailComposer.copiedBlockType';

interface StyleCopyPasteProps {
  blockType: EmailBlockType;
  settings: Record<string, any>;
  onPaste: (settings: Record<string, any>) => void;
}

/**
 * Component for copying and pasting styles between blocks
 */
export function StyleCopyPaste({ blockType, settings, onPaste }: StyleCopyPasteProps) {
  const [canPaste, setCanPaste] = useState(false);
  const [copiedStyles, setCopiedStyles] = useState<Record<string, any> | null>(null);
  const [copiedBlockType, setCopiedBlockType] = useState<EmailBlockType | null>(null);
  
  // Load saved styles from localStorage on mount
  useEffect(() => {
    try {
      const savedStyles = localStorage.getItem(STORAGE_KEY_STYLES);
      const savedType = localStorage.getItem(STORAGE_KEY_TYPE);
      
      if (savedStyles) {
        setCopiedStyles(JSON.parse(savedStyles));
      }
      
      if (savedType) {
        setCopiedBlockType(savedType as EmailBlockType);
      }
    } catch (error) {
      console.error('Error loading saved styles:', error);
    }
  }, []);
  
  // Check if we can paste styles (if we have copied styles and they're compatible)
  useEffect(() => {
    setCanPaste(
      copiedStyles !== null && 
      (copiedBlockType === blockType || copiedBlockType === 'all')
    );
  }, [blockType, copiedStyles, copiedBlockType]);

  // Copy current styles
  const handleCopy = () => {
    try {
      // Save to state
      setCopiedStyles({ ...settings });
      setCopiedBlockType(blockType);
      
      // Save to localStorage
      localStorage.setItem(STORAGE_KEY_STYLES, JSON.stringify(settings));
      localStorage.setItem(STORAGE_KEY_TYPE, blockType);
    } catch (error) {
      console.error('Error copying styles:', error);
    }
  };

  // Paste copied styles
  const handlePaste = () => {
    if (copiedStyles && canPaste) {
      onPaste(copiedStyles);
    }
  };

  return (
    <div className="flex flex-col space-y-2 mb-4">
      <div className="flex space-x-2">
        <button
          type="button"
          onClick={handleCopy}
          className="flex-1 py-1 px-3 text-xs bg-gray-100 hover:bg-gray-200 border border-gray-300 rounded-md transition-colors"
        >
          Copy Style
        </button>
        <button
          type="button"
          onClick={handlePaste}
          disabled={!canPaste}
          className={`flex-1 py-1 px-3 text-xs border rounded-md transition-colors ${
            canPaste 
              ? 'bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-300' 
              : 'bg-gray-50 text-gray-400 border-gray-200 cursor-not-allowed'
          }`}
        >
          Paste Style
        </button>
      </div>
      
      {copiedStyles && (
        <div className="text-xs text-gray-500 px-1">
          {copiedBlockType === blockType 
            ? 'Styles from same block type available' 
            : `Styles from ${copiedBlockType} block available`}
        </div>
      )}
    </div>
  );
}