'use client';

import React, { useState } from 'react';
import { ColorPicker } from './ColorPicker';
import { StyleSection } from './StyleSection';
import { useEmailComposer } from '../context';

interface ThemeColor {
  id: string;
  name: string;
  value: string;
}

interface ThemeFont {
  id: string;
  name: string;
  value: string;
}

interface Theme {
  id: string;
  name: string;
  colors: ThemeColor[];
  fonts: ThemeFont[];
}

const DEFAULT_THEMES: Theme[] = [
  {
    id: 'default',
    name: 'Default Theme',
    colors: [
      { id: 'primary', name: 'Primary', value: '#6C1411' },
      { id: 'secondary', name: 'Secondary', value: '#333333' },
      { id: 'accent', name: 'Accent', value: '#f3f4f6' },
      { id: 'background', name: 'Background', value: '#ffffff' },
      { id: 'text', name: 'Text', value: '#333333' },
    ],
    fonts: [
      { id: 'heading', name: 'Heading', value: 'Arial, sans-serif' },
      { id: 'body', name: 'Body', value: 'Arial, sans-serif' },
    ],
  },
  {
    id: 'modern',
    name: 'Modern Theme',
    colors: [
      { id: 'primary', name: 'Primary', value: '#3b82f6' },
      { id: 'secondary', name: 'Secondary', value: '#1e40af' },
      { id: 'accent', name: 'Accent', value: '#dbeafe' },
      { id: 'background', name: 'Background', value: '#ffffff' },
      { id: 'text', name: 'Text', value: '#1f2937' },
    ],
    fonts: [
      { id: 'heading', name: 'Heading', value: "'Helvetica Neue', Helvetica, sans-serif" },
      { id: 'body', name: 'Body', value: "'Helvetica Neue', Helvetica, sans-serif" },
    ],
  },
  {
    id: 'elegant',
    name: 'Elegant Theme',
    colors: [
      { id: 'primary', name: 'Primary', value: '#4b5563' },
      { id: 'secondary', name: 'Secondary', value: '#111827' },
      { id: 'accent', name: 'Accent', value: '#f3f4f6' },
      { id: 'background', name: 'Background', value: '#ffffff' },
      { id: 'text', name: 'Text', value: '#374151' },
    ],
    fonts: [
      { id: 'heading', name: 'Heading', value: 'Georgia, serif' },
      { id: 'body', name: 'Body', value: 'Georgia, serif' },
    ],
  },
];

/**
 * Theme manager component for managing global theme settings
 */
export function ThemeManager() {
  const { state, actions } = useEmailComposer();
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    themes: true,
    colors: false,
    fonts: false,
  });
  const [selectedTheme, setSelectedTheme] = useState<string>(state.currentTemplate?.theme?.id || 'default');
  
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };
  
  const applyTheme = (themeId: string) => {
    const theme = DEFAULT_THEMES.find(t => t.id === themeId);
    if (!theme) return;
    
    setSelectedTheme(themeId);
    
    // Update template theme
    actions.updateTemplate({
      theme: theme,
    });
    
    // Apply theme colors to template settings
    actions.updateTemplate({
      settings: {
        ...state.currentTemplate?.settings,
        backgroundColor: theme.colors.find(c => c.id === 'background')?.value || '#ffffff',
        textColor: theme.colors.find(c => c.id === 'text')?.value || '#333333',
      }
    });
  };
  
  const updateThemeColor = (colorId: string, value: string) => {
    const currentTheme = state.currentTemplate?.theme || DEFAULT_THEMES[0];
    const updatedColors = currentTheme.colors.map(color => 
      color.id === colorId ? { ...color, value } : color
    );
    
    actions.updateTemplate({
      theme: {
        ...currentTheme,
        colors: updatedColors,
      }
    });
    
    // If updating background or text color, also update template settings
    if (colorId === 'background') {
      actions.updateTemplate({
        settings: {
          ...state.currentTemplate?.settings,
          backgroundColor: value,
        }
      });
    } else if (colorId === 'text') {
      actions.updateTemplate({
        settings: {
          ...state.currentTemplate?.settings,
          textColor: value,
        }
      });
    }
  };
  
  const updateThemeFont = (fontId: string, value: string) => {
    const currentTheme = state.currentTemplate?.theme || DEFAULT_THEMES[0];
    const updatedFonts = currentTheme.fonts.map(font => 
      font.id === fontId ? { ...font, value } : font
    );
    
    actions.updateTemplate({
      theme: {
        ...currentTheme,
        fonts: updatedFonts,
      }
    });
  };
  
  const currentTheme = state.currentTemplate?.theme || DEFAULT_THEMES.find(t => t.id === selectedTheme) || DEFAULT_THEMES[0];
  
  return (
    <div className="space-y-4">
      <StyleSection
        title="Theme Selection"
        expanded={expandedSections.themes}
        onToggle={() => toggleSection('themes')}
      >
        <div className="space-y-3">
          <div>
            <label className="block text-sm text-gray-700 mb-2">Select Theme</label>
            <div className="grid grid-cols-1 gap-2">
              {DEFAULT_THEMES.map(theme => (
                <button
                  key={theme.id}
                  type="button"
                  onClick={() => applyTheme(theme.id)}
                  className={`p-2 text-left border rounded-md transition-colors ${
                    selectedTheme === theme.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="font-medium text-sm">{theme.name}</div>
                  <div className="flex mt-2 space-x-1">
                    {theme.colors.map(color => (
                      <div 
                        key={color.id}
                        className="w-4 h-4 rounded-full border border-gray-300"
                        style={{ backgroundColor: color.value }}
                        title={color.name}
                      />
                    ))}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
      </StyleSection>
      
      <StyleSection
        title="Theme Colors"
        expanded={expandedSections.colors}
        onToggle={() => toggleSection('colors')}
      >
        <div className="space-y-3">
          {currentTheme.colors.map(color => (
            <div key={color.id}>
              <label className="block text-sm text-gray-700 mb-1">{color.name}</label>
              <ColorPicker
                color={color.value}
                onChange={(value) => updateThemeColor(color.id, value)}
              />
            </div>
          ))}
        </div>
      </StyleSection>
      
      <StyleSection
        title="Theme Fonts"
        expanded={expandedSections.fonts}
        onToggle={() => toggleSection('fonts')}
      >
        <div className="space-y-3">
          {currentTheme.fonts.map(font => (
            <div key={font.id}>
              <label className="block text-sm text-gray-700 mb-1">{font.name} Font</label>
              <select
                value={font.value}
                onChange={(e) => updateThemeFont(font.id, e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="Arial, sans-serif">Arial</option>
                <option value="'Helvetica Neue', Helvetica, sans-serif">Helvetica</option>
                <option value="'Times New Roman', Times, serif">Times New Roman</option>
                <option value="Georgia, serif">Georgia</option>
                <option value="'Courier New', Courier, monospace">Courier New</option>
                <option value="Verdana, sans-serif">Verdana</option>
                <option value="Tahoma, sans-serif">Tahoma</option>
              </select>
              <div 
                className="mt-1 p-2 border border-gray-200 rounded text-sm"
                style={{ fontFamily: font.value }}
              >
                Sample text in {font.name.toLowerCase()} font
              </div>
            </div>
          ))}
        </div>
      </StyleSection>
    </div>
  );
}