'use client';

import React, { useState, useRef, useEffect } from 'react';

interface ColorPickerProps {
  color: string;
  onChange: (color: string) => void;
  presetColors?: string[];
}

/**
 * Enhanced color picker component with preset colors and transparency support
 */
export function ColorPicker({ 
  color = '#000000', 
  onChange,
  presetColors = [
    '#000000', '#ffffff', '#f44336', '#e91e63', '#9c27b0', 
    '#673ab7', '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4', 
    '#009688', '#4caf50', '#8bc34a', '#cddc39', '#ffeb3b', 
    '#ffc107', '#ff9800', '#ff5722', '#795548', '#607d8b',
    '#6C1411', // Brand color
  ]
}: ColorPickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState(color);
  const pickerRef = useRef<HTMLDivElement>(null);

  // Update input value when color prop changes
  useEffect(() => {
    setInputValue(color);
  }, [color]);

  // Close picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (pickerRef.current && !pickerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle color input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    
    // Only update parent if it's a valid color
    if (value.match(/^#([0-9A-F]{3}){1,2}$/i) || value.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/)) {
      onChange(value);
    }
  };

  // Handle color input blur
  const handleInputBlur = () => {
    // Reset to current color if input is invalid
    if (!inputValue.match(/^#([0-9A-F]{3}){1,2}$/i) && !inputValue.match(/^rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d+(?:\.\d+)?))?\)$/)) {
      setInputValue(color);
    }
  };

  // Handle preset color selection
  const handlePresetClick = (presetColor: string) => {
    onChange(presetColor);
    setInputValue(presetColor);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={pickerRef}>
      <div className="flex items-center">
        <button
          type="button"
          className="w-10 h-10 rounded-md border border-gray-300 mr-2 flex-shrink-0"
          style={{ backgroundColor: color }}
          onClick={() => setIsOpen(!isOpen)}
          aria-label="Open color picker"
        />
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          placeholder="#000000"
          className="flex-1 p-2 border border-gray-300 rounded-md text-sm"
        />
        <input
          type="color"
          value={color.startsWith('#') ? color : '#000000'}
          onChange={(e) => {
            onChange(e.target.value);
            setInputValue(e.target.value);
          }}
          className="sr-only"
          id="hidden-color-input"
        />
        <label 
          htmlFor="hidden-color-input"
          className="ml-2 p-2 bg-gray-100 border border-gray-300 rounded-md cursor-pointer text-xs"
        >
          Pick
        </label>
      </div>
      
      {isOpen && (
        <div className="absolute z-10 mt-1 p-2 bg-white border border-gray-300 rounded-md shadow-lg">
          <div className="grid grid-cols-7 gap-1">
            {presetColors.map((presetColor) => (
              <button
                key={presetColor}
                type="button"
                className="w-6 h-6 rounded-md border border-gray-300 hover:scale-110 transition-transform"
                style={{ backgroundColor: presetColor }}
                onClick={() => handlePresetClick(presetColor)}
                aria-label={`Select color ${presetColor}`}
              />
            ))}
          </div>
          <div className="mt-2 pt-2 border-t border-gray-200">
            <button
              type="button"
              className="w-full text-xs text-gray-600 hover:text-gray-900"
              onClick={() => {
                onChange('transparent');
                setInputValue('transparent');
                setIsOpen(false);
              }}
            >
              Transparent
            </button>
          </div>
        </div>
      )}
    </div>
  );
}