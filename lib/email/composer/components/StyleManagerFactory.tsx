'use client';

import React from 'react';
import { EmailBlock, EmailBlockType, ButtonBlock } from '../types';
import { StyleManager } from './StyleManager';
import { ButtonStyleManager } from './ButtonStyleManager';

interface StyleManagerFactoryProps {
  block: EmailBlock;
  onUpdate: (updates: Partial<EmailBlock>) => void;
}

/**
 * Factory component that returns the appropriate style manager based on block type
 * This makes it easy to add specialized style managers for different block types
 */
export function StyleManagerFactory({ block, onUpdate }: StyleManagerFactoryProps) {
  // Return the appropriate style manager based on block type
  switch (block.type) {
    case EmailBlockType.BUTTON:
      return <ButtonStyleManager block={block as ButtonBlock} onUpdate={onUpdate} />;
    
    // Add more specialized style managers here as they are created
    // case EmailBlockType.IMAGE:
    //   return <ImageStyleManager block={block as ImageBlock} onUpdate={onUpdate} />;
    
    // Default style manager for all other block types
    default:
      return <StyleManager block={block} onUpdate={onUpdate} />;
  }
}