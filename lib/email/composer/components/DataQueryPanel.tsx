'use client';

import React, { useState } from 'react';
import { useEmailComposer } from '../context';
import { DataSource } from '../types';
import { useDataQuery } from '../hooks/use-data-query';

/**
 * Panel for querying data sources
 */
export function DataQueryPanel() {
  const { state } = useEmailComposer();
  const [selectedSource, setSelectedSource] = useState<DataSource | null>(null);
  const [queryParams, setQueryParams] = useState<Record<string, string>>({});
  
  const { data, isLoading, error, refetch } = useDataQuery(selectedSource);
  
  // Example data sources
  const dataSources: DataSource[] = [
    {
      id: 'products',
      name: 'Products',
      type: 'products',
    },
    {
      id: 'orders',
      name: 'Orders',
      type: 'orders',
    },
    {
      id: 'customers',
      name: 'Customers',
      type: 'customers',
    },
    {
      id: 'custom',
      name: 'Custom API',
      type: 'custom',
      endpoint: 'https://api.example.com/data',
    },
  ];
  
  const handleSourceChange = (sourceId: string) => {
    const source = dataSources.find(s => s.id === sourceId) || null;
    setSelectedSource(source);
    setQueryParams({});
  };
  
  const handleParamChange = (key: string, value: string) => {
    setQueryParams(prev => ({
      ...prev,
      [key]: value,
    }));
  };
  
  const handleQuery = () => {
    if (selectedSource) {
      refetch();
    }
  };
  
  return (
    <div className="p-4">
      <h3 className="text-lg font-medium mb-4">Data Query</h3>
      
      <div className="space-y-4">
        <div>
          <label className="block text-sm text-gray-700 mb-1">
            Data Source
          </label>
          <select
            value={selectedSource?.id || ''}
            onChange={(e) => handleSourceChange(e.target.value)}
            className="w-full p-2 border border-gray-300 rounded-md"
          >
            <option value="">Select a data source</option>
            {dataSources.map(source => (
              <option key={source.id} value={source.id}>
                {source.name}
              </option>
            ))}
          </select>
        </div>
        
        {selectedSource && (
          <>
            {selectedSource.type === 'products' && (
              <div className="space-y-2">
                <div>
                  <label className="block text-sm text-gray-700 mb-1">
                    Category
                  </label>
                  <input
                    type="text"
                    value={queryParams.category || ''}
                    onChange={(e) => handleParamChange('category', e.target.value)}
                    placeholder="e.g., clothing"
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>
                
                <div>
                  <label className="block text-sm text-gray-700 mb-1">
                    Limit
                  </label>
                  <input
                    type="number"
                    value={queryParams.limit || ''}
                    onChange={(e) => handleParamChange('limit', e.target.value)}
                    placeholder="e.g., 10"
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>
            )}
            
            {selectedSource.type === 'orders' && (
              <div className="space-y-2">
                <div>
                  <label className="block text-sm text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={queryParams.status || ''}
                    onChange={(e) => handleParamChange('status', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  >
                    <option value="">All Statuses</option>
                    <option value="processing">Processing</option>
                    <option value="shipped">Shipped</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm text-gray-700 mb-1">
                    Date Range
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="date"
                      value={queryParams.startDate || ''}
                      onChange={(e) => handleParamChange('startDate', e.target.value)}
                      className="flex-1 p-2 border border-gray-300 rounded-md"
                    />
                    <input
                      type="date"
                      value={queryParams.endDate || ''}
                      onChange={(e) => handleParamChange('endDate', e.target.value)}
                      className="flex-1 p-2 border border-gray-300 rounded-md"
                    />
                  </div>
                </div>
              </div>
            )}
            
            {selectedSource.type === 'customers' && (
              <div className="space-y-2">
                <div>
                  <label className="block text-sm text-gray-700 mb-1">
                    Search
                  </label>
                  <input
                    type="text"
                    value={queryParams.search || ''}
                    onChange={(e) => handleParamChange('search', e.target.value)}
                    placeholder="Search by name or email"
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>
              </div>
            )}
            
            {selectedSource.type === 'custom' && (
              <div className="space-y-2">
                <div>
                  <label className="block text-sm text-gray-700 mb-1">
                    Endpoint URL
                  </label>
                  <input
                    type="text"
                    value={selectedSource.endpoint || ''}
                    disabled
                    className="w-full p-2 border border-gray-300 rounded-md bg-gray-50"
                  />
                </div>
                
                <div>
                  <label className="block text-sm text-gray-700 mb-1">
                    Custom Parameters (JSON)
                  </label>
                  <textarea
                    value={queryParams.params || '{}'}
                    onChange={(e) => handleParamChange('params', e.target.value)}
                    rows={4}
                    className="w-full p-2 border border-gray-300 rounded-md font-mono text-sm"
                    placeholder='{"key": "value"}'
                  />
                </div>
              </div>
            )}
            
            <button
              className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              onClick={handleQuery}
              disabled={isLoading}
            >
              {isLoading ? 'Loading...' : 'Query Data'}
            </button>
          </>
        )}
        
        {error && (
          <div className="p-3 bg-red-50 text-red-700 rounded-md text-sm">
            {error.message}
          </div>
        )}
        
        {data && (
          <div>
            <h4 className="text-sm font-medium mb-2">Query Results</h4>
            <div className="border border-gray-200 rounded-md p-3 bg-gray-50 max-h-60 overflow-auto">
              <pre className="text-xs">{JSON.stringify(data, null, 2)}</pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}