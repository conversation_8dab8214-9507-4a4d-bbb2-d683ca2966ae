'use client';

import React, { useState, useEffect, useRef } from 'react';
import { EmailBlock } from '../types';
import { ColorPicker } from './ColorPicker';
import { StylePresets } from './StylePresets';
import { StyleCopyPaste } from './StyleCopyPaste';
import { StyleSection } from './StyleSection';
import { StylePreview } from './StylePreview';
import { StyleSearch } from './StyleSearch';
import { useStyleHistory } from '../hooks/useStyleHistory';

interface StyleManagerProps {
  block: EmailBlock;
  onUpdate: (updates: Partial<EmailBlock>) => void;
}

/**
 * StyleManager component for managing block styles
 * This component provides a unified interface for editing style properties
 * of email blocks with collapsible sections for better organization
 */
export function StyleManager({ block, onUpdate }: StyleManagerProps) {
  // Track expanded sections
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    layout: true,
    typography: false,
    spacing: false,
    borders: false,
    background: false,
  });

  // Use style history for undo/redo
  const {
    state: styleState,
    setState: setStyleState,
    undo,
    redo,
    canUndo,
    canRedo,
  } = useStyleHistory<Record<string, any>>(block.settings);

  // Update style history when block settings change from outside
  useEffect(() => {
    if (JSON.stringify(styleState) !== JSON.stringify(block.settings)) {
      setStyleState(block.settings);
    }
  }, [block.settings]);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const updateStyle = (key: string, value: any) => {
    const newSettings = {
      ...styleState,
      [key]: value,
    };
    
    setStyleState(newSettings);
    
    onUpdate({
      settings: newSettings,
    });
  };
  
  // Apply multiple style settings at once (for presets)
  const applyStyles = (newStyles: Record<string, any>) => {
    const newSettings = {
      ...styleState,
      ...newStyles,
    };
    
    setStyleState(newSettings);
    
    onUpdate({
      settings: newSettings,
    });
  };

  // Handle scrolling to a specific section when a property is selected from search
  const sectionRefs = {
    layout: useRef<HTMLDivElement>(null),
    typography: useRef<HTMLDivElement>(null),
    spacing: useRef<HTMLDivElement>(null),
    borders: useRef<HTMLDivElement>(null),
    background: useRef<HTMLDivElement>(null),
  };
  
  // Map property keys to their sections
  const propertySectionMap: Record<string, string> = {
    // Layout properties
    width: 'layout',
    height: 'layout',
    align: 'layout',
    display: 'layout',
    
    // Typography properties
    fontFamily: 'typography',
    fontSize: 'typography',
    fontWeight: 'typography',
    lineHeight: 'typography',
    color: 'typography',
    textAlign: 'typography',
    textDecoration: 'typography',
    
    // Spacing properties
    padding: 'spacing',
    paddingTop: 'spacing',
    paddingRight: 'spacing',
    paddingBottom: 'spacing',
    paddingLeft: 'spacing',
    margin: 'spacing',
    marginTop: 'spacing',
    marginRight: 'spacing',
    marginBottom: 'spacing',
    marginLeft: 'spacing',
    
    // Border properties
    borderStyle: 'borders',
    borderWidth: 'borders',
    borderColor: 'borders',
    borderRadius: 'borders',
    
    // Background properties
    backgroundColor: 'background',
  };
  
  // Handle property selection from search
  const handlePropertySelect = (property: string) => {
    const section = propertySectionMap[property];
    if (section) {
      // Expand the section if it's not already expanded
      if (!expandedSections[section]) {
        setExpandedSections(prev => ({
          ...prev,
          [section]: true,
        }));
      }
      
      // Scroll to the section
      setTimeout(() => {
        sectionRefs[section as keyof typeof sectionRefs]?.current?.scrollIntoView({ 
          behavior: 'smooth',
          block: 'start',
        });
      }, 100);
    }
  };

  return (
    <div className="space-y-4">
      {/* Style search */}
      <div className="mb-4">
        <StyleSearch onSelectProperty={handlePropertySelect} />
      </div>
      
      {/* Style preview */}
      <div className="mb-4">
        <StylePreview block={block} settings={styleState} />
      </div>
      
      {/* Style presets and copy/paste */}
      <div className="mb-4">
        {/* Style history controls */}
        <div className="flex space-x-2 mb-4">
          <button
            type="button"
            onClick={undo}
            disabled={!canUndo}
            className={`flex-1 py-1 px-3 text-xs border rounded-md transition-colors ${
              canUndo 
                ? 'bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300' 
                : 'bg-gray-50 text-gray-400 border-gray-200 cursor-not-allowed'
            }`}
          >
            Undo
          </button>
          <button
            type="button"
            onClick={redo}
            disabled={!canRedo}
            className={`flex-1 py-1 px-3 text-xs border rounded-md transition-colors ${
              canRedo 
                ? 'bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300' 
                : 'bg-gray-50 text-gray-400 border-gray-200 cursor-not-allowed'
            }`}
          >
            Redo
          </button>
        </div>
        
        {/* Style presets */}
        <StylePresets 
          blockType={block.type} 
          onApply={applyStyles} 
        />
        
        {/* Style copy/paste */}
        <StyleCopyPaste 
          blockType={block.type}
          settings={styleState}
          onPaste={applyStyles}
        />
      </div>
      
      {/* Layout Section */}
      <div ref={sectionRefs.layout}>
        <StyleSection 
          title="Layout" 
          expanded={expandedSections.layout}
          onToggle={() => toggleSection('layout')}
        >
        <div className="space-y-3">
          <div>
            <label className="block text-sm text-gray-700 mb-1">Width</label>
            <input
              type="text"
              value={styleState.width || ''}
              onChange={(e) => updateStyle('width', e.target.value)}
              placeholder="e.g., 100% or 300px"
              className="w-full p-2 border border-gray-300 rounded-md text-sm"
            />
          </div>
          
          <div>
            <label className="block text-sm text-gray-700 mb-1">Alignment</label>
            <div className="flex border border-gray-300 rounded-md overflow-hidden">
              <button
                type="button"
                className={`flex-1 py-1 px-2 text-sm ${styleState.align === 'left' ? 'bg-blue-100 text-blue-700' : 'bg-white text-gray-700'}`}
                onClick={() => updateStyle('align', 'left')}
              >
                Left
              </button>
              <button
                type="button"
                className={`flex-1 py-1 px-2 text-sm ${styleState.align === 'center' ? 'bg-blue-100 text-blue-700' : 'bg-white text-gray-700'}`}
                onClick={() => updateStyle('align', 'center')}
              >
                Center
              </button>
              <button
                type="button"
                className={`flex-1 py-1 px-2 text-sm ${styleState.align === 'right' ? 'bg-blue-100 text-blue-700' : 'bg-white text-gray-700'}`}
                onClick={() => updateStyle('align', 'right')}
              >
                Right
              </button>
            </div>
          </div>
        </div>
        </StyleSection>
      </div>

      {/* Typography Section - Only show for blocks that have text */}
      {(block.type === 'text' || block.type === 'button' || block.type === 'header' || block.type === 'footer') && (
        <div ref={sectionRefs.typography}>
          <StyleSection 
            title="Typography" 
            expanded={expandedSections.typography}
            onToggle={() => toggleSection('typography')}
          >
          <div className="space-y-3">
            <div>
              <label className="block text-sm text-gray-700 mb-1">Font Family</label>
              <select
                value={styleState.fontFamily || ''}
                onChange={(e) => updateStyle('fontFamily', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="">Default</option>
                <option value="Arial, sans-serif">Arial</option>
                <option value="'Helvetica Neue', Helvetica, sans-serif">Helvetica</option>
                <option value="'Times New Roman', Times, serif">Times New Roman</option>
                <option value="Georgia, serif">Georgia</option>
                <option value="'Courier New', Courier, monospace">Courier New</option>
                <option value="Verdana, sans-serif">Verdana</option>
                <option value="Tahoma, sans-serif">Tahoma</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-1">Font Size</label>
              <div className="flex items-center">
                <input
                  type="text"
                  value={styleState.fontSize || ''}
                  onChange={(e) => updateStyle('fontSize', e.target.value)}
                  placeholder="e.g., 16px"
                  className="flex-1 p-2 border border-gray-300 rounded-md text-sm"
                />
                <div className="ml-2 flex border border-gray-300 rounded-md overflow-hidden">
                  <button
                    type="button"
                    className="py-1 px-2 text-sm bg-white text-gray-700 hover:bg-gray-100"
                    onClick={() => {
                      const currentSize = parseInt(styleState.fontSize || '16px');
                      if (!isNaN(currentSize)) {
                        updateStyle('fontSize', `${currentSize - 1}px`);
                      }
                    }}
                  >
                    -
                  </button>
                  <button
                    type="button"
                    className="py-1 px-2 text-sm bg-white text-gray-700 hover:bg-gray-100"
                    onClick={() => {
                      const currentSize = parseInt(styleState.fontSize || '16px');
                      if (!isNaN(currentSize)) {
                        updateStyle('fontSize', `${currentSize + 1}px`);
                      }
                    }}
                  >
                    +
                  </button>
                </div>
              </div>
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-1">Font Weight</label>
              <select
                value={styleState.fontWeight || ''}
                onChange={(e) => updateStyle('fontWeight', e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="">Default</option>
                <option value="normal">Normal</option>
                <option value="bold">Bold</option>
                <option value="300">Light (300)</option>
                <option value="400">Regular (400)</option>
                <option value="500">Medium (500)</option>
                <option value="600">Semi-Bold (600)</option>
                <option value="700">Bold (700)</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-1">Line Height</label>
              <input
                type="text"
                value={styleState.lineHeight || ''}
                onChange={(e) => updateStyle('lineHeight', e.target.value)}
                placeholder="e.g., 1.5 or 24px"
                className="w-full p-2 border border-gray-300 rounded-md text-sm"
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-1">Text Color</label>
              <ColorPicker
                color={styleState.color || '#333333'}
                onChange={(color) => updateStyle('color', color)}
              />
            </div>
            
            <div>
              <label className="block text-sm text-gray-700 mb-1">Text Align</label>
              <div className="flex border border-gray-300 rounded-md overflow-hidden">
                <button
                  type="button"
                  className={`flex-1 py-1 px-2 text-sm ${styleState.textAlign === 'left' ? 'bg-blue-100 text-blue-700' : 'bg-white text-gray-700'}`}
                  onClick={() => updateStyle('textAlign', 'left')}
                >
                  Left
                </button>
                <button
                  type="button"
                  className={`flex-1 py-1 px-2 text-sm ${styleState.textAlign === 'center' ? 'bg-blue-100 text-blue-700' : 'bg-white text-gray-700'}`}
                  onClick={() => updateStyle('textAlign', 'center')}
                >
                  Center
                </button>
                <button
                  type="button"
                  className={`flex-1 py-1 px-2 text-sm ${styleState.textAlign === 'right' ? 'bg-blue-100 text-blue-700' : 'bg-white text-gray-700'}`}
                  onClick={() => updateStyle('textAlign', 'right')}
                >
                  Right
                </button>
                <button
                  type="button"
                  className={`flex-1 py-1 px-2 text-sm ${styleState.textAlign === 'justify' ? 'bg-blue-100 text-blue-700' : 'bg-white text-gray-700'}`}
                  onClick={() => updateStyle('textAlign', 'justify')}
                >
                  Justify
                </button>
              </div>
            </div>
          </div>
          </StyleSection>
        </div>
      )}

      {/* Spacing Section */}
      <div ref={sectionRefs.spacing}>
        <StyleSection 
          title="Spacing" 
          expanded={expandedSections.spacing}
          onToggle={() => toggleSection('spacing')}
        >
        <div className="space-y-3">
          <div>
            <label className="block text-sm text-gray-700 mb-1">Padding</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Top</label>
                <input
                  type="text"
                  value={getPaddingValue(styleState.padding, 'top')}
                  onChange={(e) => updatePadding('top', e.target.value)}
                  placeholder="px"
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Right</label>
                <input
                  type="text"
                  value={getPaddingValue(styleState.padding, 'right')}
                  onChange={(e) => updatePadding('right', e.target.value)}
                  placeholder="px"
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Bottom</label>
                <input
                  type="text"
                  value={getPaddingValue(styleState.padding, 'bottom')}
                  onChange={(e) => updatePadding('bottom', e.target.value)}
                  placeholder="px"
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Left</label>
                <input
                  type="text"
                  value={getPaddingValue(styleState.padding, 'left')}
                  onChange={(e) => updatePadding('left', e.target.value)}
                  placeholder="px"
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>
          </div>
          
          <div>
            <label className="block text-sm text-gray-700 mb-1">Margin</label>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs text-gray-500 mb-1">Top</label>
                <input
                  type="text"
                  value={getMarginValue(styleState.margin, 'top')}
                  onChange={(e) => updateMargin('top', e.target.value)}
                  placeholder="px"
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Right</label>
                <input
                  type="text"
                  value={getMarginValue(styleState.margin, 'right')}
                  onChange={(e) => updateMargin('right', e.target.value)}
                  placeholder="px"
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Bottom</label>
                <input
                  type="text"
                  value={getMarginValue(styleState.margin, 'bottom')}
                  onChange={(e) => updateMargin('bottom', e.target.value)}
                  placeholder="px"
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-500 mb-1">Left</label>
                <input
                  type="text"
                  value={getMarginValue(styleState.margin, 'left')}
                  onChange={(e) => updateMargin('left', e.target.value)}
                  placeholder="px"
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </div>
          </div>
        </div>
        </StyleSection>
      </div>

      {/* Borders Section */}
      <div ref={sectionRefs.borders}>
        <StyleSection 
          title="Borders" 
          expanded={expandedSections.borders}
          onToggle={() => toggleSection('borders')}
        >
        <div className="space-y-3">
          <div>
            <label className="block text-sm text-gray-700 mb-1">Border Style</label>
            <select
              value={styleState.borderStyle || 'none'}
              onChange={(e) => updateStyle('borderStyle', e.target.value)}
              className="w-full p-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="none">None</option>
              <option value="solid">Solid</option>
              <option value="dashed">Dashed</option>
              <option value="dotted">Dotted</option>
            </select>
          </div>
          
          {styleState.borderStyle && styleState.borderStyle !== 'none' && (
            <>
              <div>
                <label className="block text-sm text-gray-700 mb-1">Border Width</label>
                <input
                  type="text"
                  value={styleState.borderWidth || ''}
                  onChange={(e) => updateStyle('borderWidth', e.target.value)}
                  placeholder="e.g., 1px"
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-1">Border Color</label>
                <ColorPicker
                  color={styleState.borderColor || '#cccccc'}
                  onChange={(color) => updateStyle('borderColor', color)}
                />
              </div>
              
              <div>
                <label className="block text-sm text-gray-700 mb-1">Border Radius</label>
                <input
                  type="text"
                  value={styleState.borderRadius || ''}
                  onChange={(e) => updateStyle('borderRadius', e.target.value)}
                  placeholder="e.g., 4px"
                  className="w-full p-2 border border-gray-300 rounded-md text-sm"
                />
              </div>
            </>
          )}
        </div>
        </StyleSection>
      </div>

      {/* Background Section */}
      <div ref={sectionRefs.background}>
        <StyleSection 
          title="Background" 
          expanded={expandedSections.background}
          onToggle={() => toggleSection('background')}
        >
        <div className="space-y-3">
          <div>
            <label className="block text-sm text-gray-700 mb-1">Background Color</label>
            <ColorPicker
              color={styleState.backgroundColor || '#ffffff'}
              onChange={(color) => updateStyle('backgroundColor', color)}
            />
          </div>
        </div>
        </StyleSection>
      </div>
    </div>
  );

  // Helper function to parse padding values
  function getPaddingValue(padding: string | undefined, side: 'top' | 'right' | 'bottom' | 'left'): string {
    if (!padding) return '';
    
    const values = padding.split(' ').map(v => v.trim());
    
    if (values.length === 1) {
      return values[0]; // Same padding on all sides
    } else if (values.length === 2) {
      // top/bottom right/left
      return side === 'top' || side === 'bottom' ? values[0] : values[1];
    } else if (values.length === 4) {
      // top right bottom left
      switch (side) {
        case 'top': return values[0];
        case 'right': return values[1];
        case 'bottom': return values[2];
        case 'left': return values[3];
      }
    }
    
    return '';
  }

  // Helper function to update padding
  function updatePadding(side: 'top' | 'right' | 'bottom' | 'left', value: string) {
    const currentPadding = block.settings.padding || '';
    const values = currentPadding.split(' ').map(v => v.trim());
    
    let newPadding: string;
    
    if (values.length === 0 || values.length === 1) {
      // If no padding or single value, create a 4-value padding
      const defaultValue = values.length === 1 ? values[0] : '0px';
      const paddingValues = [defaultValue, defaultValue, defaultValue, defaultValue];
      
      switch (side) {
        case 'top': paddingValues[0] = value; break;
        case 'right': paddingValues[1] = value; break;
        case 'bottom': paddingValues[2] = value; break;
        case 'left': paddingValues[3] = value; break;
      }
      
      newPadding = paddingValues.join(' ');
    } else if (values.length === 2) {
      // Convert 2-value padding to 4-value
      const paddingValues = [values[0], values[1], values[0], values[1]];
      
      switch (side) {
        case 'top': paddingValues[0] = value; break;
        case 'right': paddingValues[1] = value; break;
        case 'bottom': paddingValues[2] = value; break;
        case 'left': paddingValues[3] = value; break;
      }
      
      newPadding = paddingValues.join(' ');
    } else if (values.length === 4) {
      // Update the specific side in 4-value padding
      switch (side) {
        case 'top': values[0] = value; break;
        case 'right': values[1] = value; break;
        case 'bottom': values[2] = value; break;
        case 'left': values[3] = value; break;
      }
      
      newPadding = values.join(' ');
    } else {
      // Unexpected format, reset to 4-value padding
      newPadding = `${side === 'top' ? value : '0px'} ${side === 'right' ? value : '0px'} ${side === 'bottom' ? value : '0px'} ${side === 'left' ? value : '0px'}`;
    }
    
    updateStyle('padding', newPadding);
  }

  // Helper function to parse margin values
  function getMarginValue(margin: string | undefined, side: 'top' | 'right' | 'bottom' | 'left'): string {
    if (!margin) return '';
    
    const values = margin.split(' ').map(v => v.trim());
    
    if (values.length === 1) {
      return values[0]; // Same margin on all sides
    } else if (values.length === 2) {
      // top/bottom right/left
      return side === 'top' || side === 'bottom' ? values[0] : values[1];
    } else if (values.length === 4) {
      // top right bottom left
      switch (side) {
        case 'top': return values[0];
        case 'right': return values[1];
        case 'bottom': return values[2];
        case 'left': return values[3];
      }
    }
    
    return '';
  }

  // Helper function to update margin
  function updateMargin(side: 'top' | 'right' | 'bottom' | 'left', value: string) {
    const currentMargin = block.settings.margin || '';
    const values = currentMargin.split(' ').map(v => v.trim());
    
    let newMargin: string;
    
    if (values.length === 0 || values.length === 1) {
      // If no margin or single value, create a 4-value margin
      const defaultValue = values.length === 1 ? values[0] : '0px';
      const marginValues = [defaultValue, defaultValue, defaultValue, defaultValue];
      
      switch (side) {
        case 'top': marginValues[0] = value; break;
        case 'right': marginValues[1] = value; break;
        case 'bottom': marginValues[2] = value; break;
        case 'left': marginValues[3] = value; break;
      }
      
      newMargin = marginValues.join(' ');
    } else if (values.length === 2) {
      // Convert 2-value margin to 4-value
      const marginValues = [values[0], values[1], values[0], values[1]];
      
      switch (side) {
        case 'top': marginValues[0] = value; break;
        case 'right': marginValues[1] = value; break;
        case 'bottom': marginValues[2] = value; break;
        case 'left': marginValues[3] = value; break;
      }
      
      newMargin = marginValues.join(' ');
    } else if (values.length === 4) {
      // Update the specific side in 4-value margin
      switch (side) {
        case 'top': values[0] = value; break;
        case 'right': values[1] = value; break;
        case 'bottom': values[2] = value; break;
        case 'left': values[3] = value; break;
      }
      
      newMargin = values.join(' ');
    } else {
      // Unexpected format, reset to 4-value margin
      newMargin = `${side === 'top' ? value : '0px'} ${side === 'right' ? value : '0px'} ${side === 'bottom' ? value : '0px'} ${side === 'left' ? value : '0px'}`;
    }
    
    updateStyle('margin', newMargin);
  }
}

// StyleSection component for collapsible sections
interface StyleSectionProps {
  title: string;
  expanded: boolean;
  onToggle: () => void;
  children: React.ReactNode;
}

function StyleSection({ title, expanded, onToggle, children }: StyleSectionProps) {
  return (
    <div className="border border-gray-200 rounded-md overflow-hidden">
      <button
        type="button"
        className="w-full flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 transition-colors text-left"
        onClick={onToggle}
      >
        <span className="font-medium text-sm">{title}</span>
        <span className="text-gray-500">
          {expanded ? '−' : '+'}
        </span>
      </button>
      
      {expanded && (
        <div className="p-3 border-t border-gray-200">
          {children}
        </div>
      )}
    </div>
  );
}