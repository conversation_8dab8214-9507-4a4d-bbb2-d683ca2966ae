'use client';

import React, { useState, useEffect } from 'react';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors, DragEndEvent } from '@dnd-kit/core';
import { SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { restrictToVerticalAxis, restrictToWindowEdges } from '@dnd-kit/modifiers';
import { useEmailComposer } from '../context';
import { EmailBlock } from '../types';
import { SortableBlockItem } from './SortableBlockItem';
import { BlockPalette } from './BlockPalette';
import { PropertiesPanel } from './PropertiesPanel';
import { TemplateSettings } from './TemplateSettings';
import { VariablesPanel } from './VariablesPanel';
import { PreviewPanel } from './PreviewPanel';
import { DataQueryPanel } from './DataQueryPanel';
import { ResponsivePreview, DeviceType, getDeviceWidth } from './ResponsivePreview';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { SidebarTrigger } from '@/components/ui/sidebar';

interface EmailEditorProps {
  className?: string;
}

/**
 * Main email editor component that combines all the pieces
 */
export function EmailEditor({ className = '' }: EmailEditorProps) {
  const { state, actions } = useEmailComposer();
  const [activeTab, setActiveTab] = useState<'blocks' | 'settings' | 'variables' | 'data'>('blocks');
  const [previewMode, setPreviewMode] = useState(false);
  const [currentDevice, setCurrentDevice] = useState<DeviceType>('desktop');
  
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  
  // Handle drag end event
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      const oldIndex = state.currentTemplate?.blocks.findIndex(block => block.id === active.id);
      const newIndex = state.currentTemplate?.blocks.findIndex(block => block.id === over.id);
      
      if (oldIndex !== undefined && newIndex !== undefined && oldIndex !== -1 && newIndex !== -1) {
        actions.moveBlock(active.id as string, newIndex);
      }
    }
  };
  
  // Load a template when the component mounts
  useEffect(() => {
    if (!state.currentTemplate) {
      actions.createTemplate({
        name: 'New Email Template',
        subject: 'New Email',
        blocks: [],
      });
    }
  }, [actions, state.currentTemplate]);
  
  if (!state.currentTemplate) {
    return <div className="flex items-center justify-center h-full">Loading...</div>;
  }
  
  return (
    <div className={`h-full ${className}`}>
      <ResizablePanelGroup direction="horizontal" className="h-full">
        {/* Left sidebar */}
        <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
          <div className="flex flex-col h-full border-r border-gray-200">
            <div className="p-4 border-b border-gray-200">
              <h2 className="text-lg font-medium">Email Composer</h2>
              <p className="text-sm text-gray-500">Build your email template</p>
            </div>
            
            <div className="flex border-b border-gray-200">
              <button
                className={`flex-1 py-2 text-sm font-medium ${activeTab === 'blocks' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
                onClick={() => setActiveTab('blocks')}
              >
                Blocks
              </button>
              <button
                className={`flex-1 py-2 text-sm font-medium ${activeTab === 'settings' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
                onClick={() => setActiveTab('settings')}
              >
                Settings
              </button>
              <button
                className={`flex-1 py-2 text-sm font-medium ${activeTab === 'variables' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
                onClick={() => setActiveTab('variables')}
              >
                Variables
              </button>
              <button
                className={`flex-1 py-2 text-sm font-medium ${activeTab === 'data' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-500'}`}
                onClick={() => setActiveTab('data')}
              >
                Data
              </button>
            </div>
            
            <div className="flex-1 overflow-y-auto">
              {activeTab === 'blocks' && <BlockPalette />}
              {activeTab === 'settings' && <TemplateSettings template={state.currentTemplate} />}
              {activeTab === 'variables' && <VariablesPanel />}
              {activeTab === 'data' && <DataQueryPanel />}
            </div>
            
            <div className="p-4 border-t border-gray-200">
              <button
                className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                onClick={actions.saveTemplate}
                disabled={state.isSaving}
              >
                {state.isSaving ? 'Saving...' : 'Save Template'}
              </button>
              
              <div className="mt-2 text-xs text-gray-500 text-center">
                {state.lastSaved && `Last saved: ${new Date(state.lastSaved).toLocaleTimeString()}`}
              </div>
            </div>
          </div>
        </ResizablePanel>
        
        <ResizableHandle withHandle />
        
        {/* Main content area */}
        <ResizablePanel defaultSize={state.selectedBlockId && !previewMode ? 55 : 80}>
          <div className="flex flex-col h-full">
            <div className="p-4 border-b border-gray-200 flex items-center justify-between">
              <div>
                <SidebarTrigger 
                  className="text-gray-500 hover:text-gray-700 transition-colors"
                />
                <input
                  type="text"
                  value={state.currentTemplate.name}
                  onChange={(e) => actions.updateTemplate({ name: e.target.value })}
                  className="text-lg font-medium border-none focus:outline-none focus:ring-1 focus:ring-blue-500 rounded-md px-2 py-1"
                  placeholder="Template Name"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <button
                  className="py-1 px-3 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                  onClick={actions.undo}
                  disabled={state.history.past.length === 0}
                >
                  Undo
                </button>
                <button
                  className="py-1 px-3 text-sm border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                  onClick={actions.redo}
                  disabled={state.history.future.length === 0}
                >
                  Redo
                </button>
                <button
                  className={`py-1 px-3 text-sm border rounded-md transition-colors ${previewMode ? 'bg-blue-600 text-white border-blue-600' : 'border-gray-300 hover:bg-gray-50'}`}
                  onClick={() => setPreviewMode(!previewMode)}
                >
                  {previewMode ? 'Exit Preview' : 'Preview'}
                </button>
                {!previewMode && (
                  <ResponsivePreview 
                    currentDevice={currentDevice} 
                    onDeviceChange={setCurrentDevice} 
                  />
                )}
              </div>
            </div>
            
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
                    Subject Line
                  </label>
                  <input
                    id="subject"
                    type="text"
                    value={state.currentTemplate.subject}
                    onChange={(e) => actions.updateTemplate({ subject: e.target.value })}
                    className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    placeholder="Enter email subject"
                  />
                </div>
                
                <div className="flex-1">
                  <label htmlFor="previewText" className="block text-sm font-medium text-gray-700 mb-1">
                    Preview Text
                  </label>
                  <input
                    id="previewText"
                    type="text"
                    value={state.currentTemplate.previewText || ''}
                    onChange={(e) => actions.updateTemplate({ previewText: e.target.value })}
                    className="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    placeholder="Enter preview text"
                  />
                </div>
              </div>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4 bg-gray-100">
              {previewMode ? (
                <PreviewPanel />
              ) : (
                <div
                  className="mx-auto transition-all duration-300"
                  style={{
                    maxWidth: getDeviceWidth(currentDevice),
                    backgroundColor: '#ffffff',
                    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
                  }}
                >
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={handleDragEnd}
                    modifiers={[restrictToVerticalAxis, restrictToWindowEdges]}
                  >
                    <SortableContext
                      items={state.currentTemplate.blocks.map((block: EmailBlock) => block.id)}
                      strategy={verticalListSortingStrategy}
                    >
                      {state.currentTemplate.blocks.length === 0 ? (
                        <div className="p-8 text-center text-gray-500 border-2 border-dashed border-gray-300 rounded-md">
                          <p>Drag and drop blocks here to build your email</p>
                        </div>
                      ) : (
                        state.currentTemplate.blocks.map((block: EmailBlock) => (
                          <SortableBlockItem
                            key={block.id}
                            block={block}
                            isSelected={block.id === state.selectedBlockId}
                            onSelect={() => actions.selectBlock(block.id)}
                            onDelete={() => actions.removeBlock(block.id)}
                            onDuplicate={() => actions.duplicateBlock(block.id)}
                          />
                        ))
                      )}
                    </SortableContext>
                  </DndContext>
                </div>
              )}
            </div>
          </div>
        </ResizablePanel>
        
        {/* Properties panel - only show when a block is selected and not in preview mode */}
        {state.selectedBlockId && !previewMode && (
          <>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={25} minSize={20} maxSize={40}>
              <div className="h-full overflow-y-auto border-l border-gray-200">
                <PropertiesPanel
                  block={state.currentTemplate.blocks.find(block => block.id === state.selectedBlockId)!}
                  onUpdate={(updates) => actions.updateBlock(state.selectedBlockId!, updates)}
                />
              </div>
            </ResizablePanel>
          </>
        )}
      </ResizablePanelGroup>
    </div>
  );
}