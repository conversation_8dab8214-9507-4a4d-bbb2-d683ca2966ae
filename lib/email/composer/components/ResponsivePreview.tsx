'use client';

import React, { useState } from 'react';

export type DeviceType = 'desktop' | 'tablet' | 'mobile';

interface ResponsivePreviewProps {
  onDeviceChange: (device: DeviceType) => void;
  currentDevice: DeviceType;
}

/**
 * Component for toggling between different device preview modes
 */
export function ResponsivePreview({ onDeviceChange, currentDevice }: ResponsivePreviewProps) {
  return (
    <div className="flex items-center border border-gray-300 rounded-md overflow-hidden">
      <button
        type="button"
        className={`flex-1 py-1 px-2 text-xs flex items-center justify-center ${
          currentDevice === 'desktop' ? 'bg-blue-100 text-blue-700' : 'bg-white text-gray-700 hover:bg-gray-50'
        }`}
        onClick={() => onDeviceChange('desktop')}
        title="Desktop view (600px)"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
        Desktop
      </button>
      <button
        type="button"
        className={`flex-1 py-1 px-2 text-xs flex items-center justify-center ${
          currentDevice === 'tablet' ? 'bg-blue-100 text-blue-700' : 'bg-white text-gray-700 hover:bg-gray-50'
        }`}
        onClick={() => onDeviceChange('tablet')}
        title="Tablet view (480px)"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
        Tablet
      </button>
      <button
        type="button"
        className={`flex-1 py-1 px-2 text-xs flex items-center justify-center ${
          currentDevice === 'mobile' ? 'bg-blue-100 text-blue-700' : 'bg-white text-gray-700 hover:bg-gray-50'
        }`}
        onClick={() => onDeviceChange('mobile')}
        title="Mobile view (320px)"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
        </svg>
        Mobile
      </button>
    </div>
  );
}

// Helper function to get width based on device type
export function getDeviceWidth(device: DeviceType): string {
  switch (device) {
    case 'desktop':
      return '600px';
    case 'tablet':
      return '480px';
    case 'mobile':
      return '320px';
    default:
      return '600px';
  }
}