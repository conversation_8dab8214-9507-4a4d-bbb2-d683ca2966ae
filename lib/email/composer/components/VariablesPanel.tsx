'use client';

import React, { useState } from 'react';
import { useEmailComposer } from '../context';
import { TemplateVariable } from '../types';

/**
 * Panel for managing template variables
 */
export function VariablesPanel() {
  const { state, actions } = useEmailComposer();
  const [newVariable, setNewVariable] = useState<Partial<TemplateVariable>>({
    name: '',
    key: '',
    type: 'string',
    defaultValue: '',
  });
  
  const handleAddVariable = () => {
    if (!newVariable.name || !newVariable.key) return;
    
    // Add the variable to the state
    const variable: TemplateVariable = {
      name: newVariable.name,
      key: newVariable.key,
      description: newVariable.description,
      defaultValue: newVariable.defaultValue,
      type: newVariable.type as 'string' | 'number' | 'boolean' | 'date' | 'object' | 'array',
      source: newVariable.source,
      path: newVariable.path,
    };
    
    // This is a placeholder implementation
    // In a real implementation, you would dispatch an action to add the variable
    console.log('Adding variable:', variable);
    
    // Reset the form
    setNewVariable({
      name: '',
      key: '',
      type: 'string',
      defaultValue: '',
    });
  };
  
  // Example variables
  const exampleVariables: TemplateVariable[] = [
    {
      name: 'First Name',
      key: 'firstName',
      description: 'Customer\'s first name',
      defaultValue: 'Friend',
      type: 'string',
    },
    {
      name: 'Order Number',
      key: 'orderNumber',
      description: 'Order reference number',
      type: 'string',
    },
    {
      name: 'Order Date',
      key: 'orderDate',
      description: 'Date when the order was placed',
      type: 'date',
    },
    {
      name: 'Total Amount',
      key: 'totalAmount',
      description: 'Total order amount',
      type: 'number',
    },
  ];
  
  return (
    <div className="p-4">
      <h3 className="text-lg font-medium mb-4">Template Variables</h3>
      
      <div className="mb-6">
        <p className="text-sm text-gray-600 mb-2">
          Variables can be used in your template by using the format <code className="bg-gray-100 px-1 py-0.5 rounded">{'{{variableName}}'}</code>
        </p>
        
        <div className="space-y-2">
          <div>
            <label className="block text-sm text-gray-700 mb-1">
              Variable Name
            </label>
            <input
              type="text"
              value={newVariable.name}
              onChange={(e) => setNewVariable({ ...newVariable, name: e.target.value })}
              placeholder="e.g., First Name"
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm text-gray-700 mb-1">
              Variable Key
            </label>
            <input
              type="text"
              value={newVariable.key}
              onChange={(e) => setNewVariable({ ...newVariable, key: e.target.value })}
              placeholder="e.g., firstName"
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm text-gray-700 mb-1">
              Type
            </label>
            <select
              value={newVariable.type}
              onChange={(e) => setNewVariable({ ...newVariable, type: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-md"
            >
              <option value="string">String</option>
              <option value="number">Number</option>
              <option value="boolean">Boolean</option>
              <option value="date">Date</option>
              <option value="object">Object</option>
              <option value="array">Array</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm text-gray-700 mb-1">
              Default Value (Optional)
            </label>
            <input
              type="text"
              value={newVariable.defaultValue || ''}
              onChange={(e) => setNewVariable({ ...newVariable, defaultValue: e.target.value })}
              placeholder="e.g., Friend"
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <div>
            <label className="block text-sm text-gray-700 mb-1">
              Description (Optional)
            </label>
            <input
              type="text"
              value={newVariable.description || ''}
              onChange={(e) => setNewVariable({ ...newVariable, description: e.target.value })}
              placeholder="e.g., Customer's first name"
              className="w-full p-2 border border-gray-300 rounded-md"
            />
          </div>
          
          <button
            className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors mt-2"
            onClick={handleAddVariable}
            disabled={!newVariable.name || !newVariable.key}
          >
            Add Variable
          </button>
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">Available Variables</h4>
        
        <div className="border border-gray-200 rounded-md overflow-hidden">
          <table className="w-full text-sm">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-2 text-left font-medium text-gray-500">Name</th>
                <th className="px-4 py-2 text-left font-medium text-gray-500">Key</th>
                <th className="px-4 py-2 text-left font-medium text-gray-500">Type</th>
                <th className="px-4 py-2 text-left font-medium text-gray-500">Default</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {exampleVariables.map((variable, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-4 py-2">{variable.name}</td>
                  <td className="px-4 py-2">
                    <code className="bg-gray-100 px-1 py-0.5 rounded">
                      {`{{${variable.key}}}`}
                    </code>
                  </td>
                  <td className="px-4 py-2">{variable.type}</td>
                  <td className="px-4 py-2">{variable.defaultValue || '-'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}