'use client';

import React, { createContext, useContext, useReducer, useCallback, ReactNode } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { 
  EmailComposerState, 
  EmailComposerContextType, 
  EmailTemplate, 
  EmailBlock 
} from './types';

// Initial state for the email composer
const initialState: EmailComposerState = {
  currentTemplate: null,
  selectedBlockId: null,
  isDragging: false,
  availableVariables: [],
  dataSources: [],
  previewMode: false,
  previewData: null,
  history: {
    past: [],
    future: [],
  },
  isSaving: false,
  lastSaved: null,
  errors: [],
};

// Action types for the reducer
type Action =
  | { type: 'SET_TEMPLATE'; payload: EmailTemplate }
  | { type: 'UPDATE_TEMPLATE'; payload: Partial<EmailTemplate> }
  | { type: 'SELECT_BLOCK'; payload: string | null }
  | { type: 'ADD_BLOCK'; payload: { block: EmailBlock; index?: number } }
  | { type: 'UPDATE_BLOCK'; payload: { blockId: string; updates: Partial<EmailBlock> } }
  | { type: 'REMOVE_BLOCK'; payload: string }
  | { type: 'MOVE_BLOCK'; payload: { blockId: string; newIndex: number } }
  | { type: 'DUPLICATE_BLOCK'; payload: string }
  | { type: 'SET_DRAGGING'; payload: boolean }
  | { type: 'SET_PREVIEW_MODE'; payload: boolean }
  | { type: 'SET_PREVIEW_DATA'; payload: Record<string, any> | null }
  | { type: 'SET_VARIABLES'; payload: EmailComposerState['availableVariables'] }
  | { type: 'SET_DATA_SOURCES'; payload: EmailComposerState['dataSources'] }
  | { type: 'SET_SAVING'; payload: boolean }
  | { type: 'SET_LAST_SAVED'; payload: string | null }
  | { type: 'ADD_ERROR'; payload: string }
  | { type: 'CLEAR_ERRORS' }
  | { type: 'UNDO' }
  | { type: 'REDO' }
  | { type: 'RESET_HISTORY' };

// Reducer function for the email composer
function reducer(state: EmailComposerState, action: Action): EmailComposerState {
  switch (action.type) {
    case 'SET_TEMPLATE':
      return {
        ...state,
        currentTemplate: action.payload,
        selectedBlockId: null,
        history: {
          past: [],
          future: [],
        },
      };

    case 'UPDATE_TEMPLATE':
      if (!state.currentTemplate) return state;
      
      const updatedTemplate = {
        ...state.currentTemplate,
        ...action.payload,
        updatedAt: new Date().toISOString(),
      };
      
      return {
        ...state,
        currentTemplate: updatedTemplate,
        history: {
          past: [...state.history.past, state.currentTemplate],
          future: [],
        },
      };

    case 'SELECT_BLOCK':
      return {
        ...state,
        selectedBlockId: action.payload,
      };

    case 'ADD_BLOCK': {
      if (!state.currentTemplate) return state;
      
      const { block, index } = action.payload;
      const newBlocks = [...state.currentTemplate.blocks];
      
      if (index !== undefined) {
        newBlocks.splice(index, 0, block);
      } else {
        newBlocks.push(block);
      }
      
      // Update positions
      const updatedBlocks = newBlocks.map((block, idx) => ({
        ...block,
        position: idx,
      }));
      
      const updatedTemplate = {
        ...state.currentTemplate,
        blocks: updatedBlocks,
        updatedAt: new Date().toISOString(),
      };
      
      return {
        ...state,
        currentTemplate: updatedTemplate,
        selectedBlockId: block.id,
        history: {
          past: [...state.history.past, state.currentTemplate],
          future: [],
        },
      };
    }

    case 'UPDATE_BLOCK': {
      if (!state.currentTemplate) return state;
      
      const { blockId, updates } = action.payload;
      const updatedBlocks = state.currentTemplate.blocks.map(block => 
        block.id === blockId ? { ...block, ...updates } : block
      );
      
      const updatedTemplate = {
        ...state.currentTemplate,
        blocks: updatedBlocks,
        updatedAt: new Date().toISOString(),
      };
      
      return {
        ...state,
        currentTemplate: updatedTemplate,
        history: {
          past: [...state.history.past, state.currentTemplate],
          future: [],
        },
      };
    }

    case 'REMOVE_BLOCK': {
      if (!state.currentTemplate) return state;
      
      const blockId = action.payload;
      const updatedBlocks = state.currentTemplate.blocks
        .filter(block => block.id !== blockId)
        .map((block, idx) => ({
          ...block,
          position: idx,
        }));
      
      const updatedTemplate = {
        ...state.currentTemplate,
        blocks: updatedBlocks,
        updatedAt: new Date().toISOString(),
      };
      
      return {
        ...state,
        currentTemplate: updatedTemplate,
        selectedBlockId: null,
        history: {
          past: [...state.history.past, state.currentTemplate],
          future: [],
        },
      };
    }

    case 'MOVE_BLOCK': {
      if (!state.currentTemplate) return state;
      
      const { blockId, newIndex } = action.payload;
      const blocks = [...state.currentTemplate.blocks];
      const currentIndex = blocks.findIndex(block => block.id === blockId);
      
      if (currentIndex === -1) return state;
      
      const [movedBlock] = blocks.splice(currentIndex, 1);
      blocks.splice(newIndex, 0, movedBlock);
      
      // Update positions
      const updatedBlocks = blocks.map((block, idx) => ({
        ...block,
        position: idx,
      }));
      
      const updatedTemplate = {
        ...state.currentTemplate,
        blocks: updatedBlocks,
        updatedAt: new Date().toISOString(),
      };
      
      return {
        ...state,
        currentTemplate: updatedTemplate,
        history: {
          past: [...state.history.past, state.currentTemplate],
          future: [],
        },
      };
    }

    case 'DUPLICATE_BLOCK': {
      if (!state.currentTemplate) return state;
      
      const blockId = action.payload;
      const blockToDuplicate = state.currentTemplate.blocks.find(block => block.id === blockId);
      
      if (!blockToDuplicate) return state;
      
      const duplicatedBlock = {
        ...blockToDuplicate,
        id: uuidv4(),
        position: blockToDuplicate.position + 1,
      };
      
      const updatedBlocks = [...state.currentTemplate.blocks];
      updatedBlocks.splice(duplicatedBlock.position, 0, duplicatedBlock);
      
      // Update positions
      const reindexedBlocks = updatedBlocks.map((block, idx) => ({
        ...block,
        position: idx,
      }));
      
      const updatedTemplate = {
        ...state.currentTemplate,
        blocks: reindexedBlocks,
        updatedAt: new Date().toISOString(),
      };
      
      return {
        ...state,
        currentTemplate: updatedTemplate,
        selectedBlockId: duplicatedBlock.id,
        history: {
          past: [...state.history.past, state.currentTemplate],
          future: [],
        },
      };
    }

    case 'SET_DRAGGING':
      return {
        ...state,
        isDragging: action.payload,
      };

    case 'SET_PREVIEW_MODE':
      return {
        ...state,
        previewMode: action.payload,
        selectedBlockId: action.payload ? null : state.selectedBlockId,
      };

    case 'SET_PREVIEW_DATA':
      return {
        ...state,
        previewData: action.payload,
      };

    case 'SET_VARIABLES':
      return {
        ...state,
        availableVariables: action.payload,
      };

    case 'SET_DATA_SOURCES':
      return {
        ...state,
        dataSources: action.payload,
      };

    case 'SET_SAVING':
      return {
        ...state,
        isSaving: action.payload,
      };

    case 'SET_LAST_SAVED':
      return {
        ...state,
        lastSaved: action.payload,
      };

    case 'ADD_ERROR':
      return {
        ...state,
        errors: [...state.errors, action.payload],
      };

    case 'CLEAR_ERRORS':
      return {
        ...state,
        errors: [],
      };

    case 'UNDO': {
      if (state.history.past.length === 0) return state;
      
      const previous = state.history.past[state.history.past.length - 1];
      const newPast = state.history.past.slice(0, state.history.past.length - 1);
      
      return {
        ...state,
        currentTemplate: previous,
        history: {
          past: newPast,
          future: [state.currentTemplate!, ...state.history.future],
        },
      };
    }

    case 'REDO': {
      if (state.history.future.length === 0) return state;
      
      const next = state.history.future[0];
      const newFuture = state.history.future.slice(1);
      
      return {
        ...state,
        currentTemplate: next,
        history: {
          past: [...state.history.past, state.currentTemplate!],
          future: newFuture,
        },
      };
    }

    case 'RESET_HISTORY':
      return {
        ...state,
        history: {
          past: [],
          future: [],
        },
      };

    default:
      return state;
  }
}

// Create the context
const EmailComposerContext = createContext<EmailComposerContextType | undefined>(undefined);

// Provider component
interface EmailComposerProviderProps {
  children: ReactNode;
}

export function EmailComposerProvider({ children }: EmailComposerProviderProps) {
  const [state, dispatch] = useReducer(reducer, initialState);

  // Actions
  const selectBlock = useCallback((blockId: string | null) => {
    dispatch({ type: 'SELECT_BLOCK', payload: blockId });
  }, []);

  const addBlock = useCallback((block: Omit<EmailBlock, 'id' | 'position'>, index?: number) => {
    const newBlock: EmailBlock = {
      ...block as any,
      id: uuidv4(),
      position: index ?? (state.currentTemplate?.blocks.length || 0),
    };
    
    dispatch({ 
      type: 'ADD_BLOCK', 
      payload: { block: newBlock, index } 
    });
  }, [state.currentTemplate?.blocks.length]);

  const updateBlock = useCallback((blockId: string, updates: Partial<EmailBlock>) => {
    dispatch({ 
      type: 'UPDATE_BLOCK', 
      payload: { blockId, updates } 
    });
  }, []);

  const removeBlock = useCallback((blockId: string) => {
    dispatch({ type: 'REMOVE_BLOCK', payload: blockId });
  }, []);

  const moveBlock = useCallback((blockId: string, newIndex: number) => {
    dispatch({ 
      type: 'MOVE_BLOCK', 
      payload: { blockId, newIndex } 
    });
  }, []);

  const duplicateBlock = useCallback((blockId: string) => {
    dispatch({ type: 'DUPLICATE_BLOCK', payload: blockId });
  }, []);

  const updateTemplate = useCallback((updates: Partial<EmailTemplate>) => {
    dispatch({ type: 'UPDATE_TEMPLATE', payload: updates });
  }, []);

  const saveTemplate = useCallback(async () => {
    if (!state.currentTemplate) return;
    
    dispatch({ type: 'SET_SAVING', payload: true });
    
    try {
      // API call to save template would go here
      // const response = await fetch('/api/email-templates', {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(state.currentTemplate),
      // });
      
      // if (!response.ok) throw new Error('Failed to save template');
      
      // Mock successful save
      await new Promise(resolve => setTimeout(resolve, 500));
      
      dispatch({ 
        type: 'SET_LAST_SAVED', 
        payload: new Date().toISOString() 
      });
    } catch (error) {
      dispatch({ 
        type: 'ADD_ERROR', 
        payload: 'Failed to save template' 
      });
    } finally {
      dispatch({ type: 'SET_SAVING', payload: false });
    }
  }, [state.currentTemplate]);

  const loadTemplate = useCallback(async (templateId: string) => {
    dispatch({ type: 'SET_SAVING', payload: true });
    
    try {
      // API call to load template would go here
      // const response = await fetch(`/api/email-templates/${templateId}`);
      // if (!response.ok) throw new Error('Failed to load template');
      // const template = await response.json();
      
      // Mock template loading
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Example template data
      const template: EmailTemplate = {
        id: templateId,
        name: 'Example Template',
        description: 'An example email template',
        subject: 'Example Subject',
        previewText: 'This is a preview text for the email',
        blocks: [],
        settings: {
          backgroundColor: '#ffffff',
          width: '600px',
          fontFamily: 'Arial, sans-serif',
          fontSize: '16px',
          lineHeight: '1.5',
          color: '#333333',
          linkColor: '#0066cc',
          contentPadding: '20px',
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      dispatch({ type: 'SET_TEMPLATE', payload: template });
    } catch (error) {
      dispatch({ 
        type: 'ADD_ERROR', 
        payload: 'Failed to load template' 
      });
    } finally {
      dispatch({ type: 'SET_SAVING', payload: false });
    }
  }, []);

  const createTemplate = useCallback(async (template: Partial<EmailTemplate>) => {
    dispatch({ type: 'SET_SAVING', payload: true });
    
    try {
      // API call to create template would go here
      // const response = await fetch('/api/email-templates', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(template),
      // });
      
      // if (!response.ok) throw new Error('Failed to create template');
      // const newTemplate = await response.json();
      
      // Mock template creation
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const newTemplate: EmailTemplate = {
        id: uuidv4(),
        name: template.name || 'New Template',
        description: template.description || '',
        subject: template.subject || 'New Email',
        previewText: template.previewText || '',
        blocks: template.blocks || [],
        settings: template.settings || {
          backgroundColor: '#ffffff',
          width: '600px',
          fontFamily: 'Arial, sans-serif',
          fontSize: '16px',
          lineHeight: '1.5',
          color: '#333333',
          linkColor: '#0066cc',
          contentPadding: '20px',
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      dispatch({ type: 'SET_TEMPLATE', payload: newTemplate });
    } catch (error) {
      dispatch({ 
        type: 'ADD_ERROR', 
        payload: 'Failed to create template' 
      });
    } finally {
      dispatch({ type: 'SET_SAVING', payload: false });
    }
  }, []);

  const previewTemplate = useCallback((withData?: Record<string, any>) => {
    if (withData) {
      dispatch({ type: 'SET_PREVIEW_DATA', payload: withData });
    }
    dispatch({ type: 'SET_PREVIEW_MODE', payload: true });
  }, []);

  const exitPreview = useCallback(() => {
    dispatch({ type: 'SET_PREVIEW_MODE', payload: false });
  }, []);

  const undo = useCallback(() => {
    dispatch({ type: 'UNDO' });
  }, []);

  const redo = useCallback(() => {
    dispatch({ type: 'REDO' });
  }, []);

  const resetHistory = useCallback(() => {
    dispatch({ type: 'RESET_HISTORY' });
  }, []);

  const contextValue: EmailComposerContextType = {
    state,
    actions: {
      selectBlock,
      addBlock,
      updateBlock,
      removeBlock,
      moveBlock,
      duplicateBlock,
      updateTemplate,
      saveTemplate,
      loadTemplate,
      createTemplate,
      previewTemplate,
      exitPreview,
      undo,
      redo,
      resetHistory,
    },
  };

  return (
    <EmailComposerContext.Provider value={contextValue}>
      {children}
    </EmailComposerContext.Provider>
  );
}

// Custom hook to use the email composer context
export function useEmailComposer() {
  const context = useContext(EmailComposerContext);
  
  if (context === undefined) {
    throw new Error('useEmailComposer must be used within an EmailComposerProvider');
  }
  
  return context;
}