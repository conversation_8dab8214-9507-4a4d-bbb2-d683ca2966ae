'use client';

import { useState, useEffect } from 'react';
import { DataSource } from '../types';

interface UseDataQueryOptions {
  enabled?: boolean;
  refetchInterval?: number;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}

interface UseDataQueryResult<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

/**
 * Hook for querying data from various sources
 */
export function useDataQuery<T = any>(
  dataSource: DataSource | null,
  options: UseDataQueryOptions = {}
): UseDataQueryResult<T> {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  
  const { enabled = true, refetchInterval, onSuccess, onError } = options;
  
  const fetchData = async () => {
    if (!dataSource || !enabled) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      let result;
      
      switch (dataSource.type) {
        case 'products':
          result = await fetchProducts(dataSource);
          break;
        case 'orders':
          result = await fetchOrders(dataSource);
          break;
        case 'customers':
          result = await fetchCustomers(dataSource);
          break;
        case 'custom':
          result = await fetchCustomData(dataSource);
          break;
        default:
          throw new Error(`Unsupported data source type: ${dataSource.type}`);
      }
      
      setData(result);
      onSuccess?.(result);
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      onError?.(error);
    } finally {
      setIsLoading(false);
    }
  };
  
  useEffect(() => {
    if (enabled) {
      fetchData();
    }
    
    if (refetchInterval && enabled) {
      const intervalId = setInterval(fetchData, refetchInterval);
      return () => clearInterval(intervalId);
    }
  }, [dataSource, enabled, refetchInterval]);
  
  const refetch = async () => {
    await fetchData();
  };
  
  return { data, isLoading, error, refetch };
}

/**
 * Fetch products data
 */
async function fetchProducts(dataSource: DataSource): Promise<any> {
  // In a real implementation, you would fetch from your API
  // For now, we'll return mock data
  
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return {
    products: [
      {
        id: '1',
        name: 'Kids T-Shirt',
        price: 'R199.99',
        description: 'Comfortable cotton t-shirt for kids.',
        imageUrl: 'https://via.placeholder.com/300x300?text=Kids+T-Shirt',
        url: 'https://example.com/product/1',
      },
      {
        id: '2',
        name: 'Kids Jeans',
        price: 'R299.99',
        description: 'Durable denim jeans for kids.',
        imageUrl: 'https://via.placeholder.com/300x300?text=Kids+Jeans',
        url: 'https://example.com/product/2',
      },
      {
        id: '3',
        name: 'Kids Sneakers',
        price: 'R399.99',
        description: 'Comfortable sneakers for kids.',
        imageUrl: 'https://via.placeholder.com/300x300?text=Kids+Sneakers',
        url: 'https://example.com/product/3',
      },
    ],
  };
}

/**
 * Fetch orders data
 */
async function fetchOrders(dataSource: DataSource): Promise<any> {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return {
    orders: [
      {
        id: '1',
        orderNumber: '12345',
        orderDate: new Date().toLocaleDateString(),
        status: 'Processing',
        total: 'R699.97',
        items: [
          { name: 'Kids T-Shirt', quantity: 1, price: 'R199.99', total: 'R199.99' },
          { name: 'Kids Jeans', quantity: 1, price: 'R299.99', total: 'R299.99' },
          { name: 'Kids Socks', quantity: 2, price: 'R99.99', total: 'R199.98' },
        ],
        customer: {
          name: 'John Doe',
          email: '<EMAIL>',
        },
      },
      {
        id: '2',
        orderNumber: '12346',
        orderDate: new Date().toLocaleDateString(),
        status: 'Shipped',
        total: 'R399.99',
        items: [
          { name: 'Kids Sneakers', quantity: 1, price: 'R399.99', total: 'R399.99' },
        ],
        customer: {
          name: 'Jane Smith',
          email: '<EMAIL>',
        },
      },
    ],
  };
}

/**
 * Fetch customers data
 */
async function fetchCustomers(dataSource: DataSource): Promise<any> {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return {
    customers: [
      {
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+27 ************',
        address: '123 Main St, Cape Town, 8001, South Africa',
        orderCount: 3,
        totalSpent: 'R1,299.95',
      },
      {
        id: '2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        phone: '+27 ************',
        address: '456 Oak Ave, Johannesburg, 2000, South Africa',
        orderCount: 1,
        totalSpent: 'R399.99',
      },
    ],
  };
}

/**
 * Fetch custom data from a custom endpoint
 */
async function fetchCustomData(dataSource: DataSource): Promise<any> {
  if (!dataSource.endpoint) {
    throw new Error('Endpoint is required for custom data source');
  }
  
  // In a real implementation, you would fetch from the provided endpoint
  // For now, we'll simulate an API call
  
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Return mock data based on the endpoint
  if (dataSource.endpoint.includes('products')) {
    return await fetchProducts(dataSource);
  } else if (dataSource.endpoint.includes('orders')) {
    return await fetchOrders(dataSource);
  } else if (dataSource.endpoint.includes('customers')) {
    return await fetchCustomers(dataSource);
  }
  
  // Default mock data
  return {
    data: [
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' },
      { id: 3, name: 'Item 3' },
    ],
  };
}