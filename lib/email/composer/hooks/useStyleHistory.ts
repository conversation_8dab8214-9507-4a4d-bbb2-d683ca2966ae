'use client';

import { useState, useCallback } from 'react';

interface StyleHistoryState<T> {
  past: T[];
  present: T;
  future: T[];
}

interface UseStyleHistoryReturn<T> {
  state: T;
  setState: (newState: T) => void;
  undo: () => void;
  redo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  history: StyleHistoryState<T>;
  clearHistory: () => void;
}

/**
 * Custom hook for managing style history with undo/redo functionality
 */
export function useStyleHistory<T>(initialState: T): UseStyleHistoryReturn<T> {
  const [history, setHistory] = useState<StyleHistoryState<T>>({
    past: [],
    present: initialState,
    future: [],
  });

  const { past, present, future } = history;

  const setState = useCallback((newState: T) => {
    setHistory(prevHistory => ({
      past: [...prevHistory.past, prevHistory.present],
      present: newState,
      future: [],
    }));
  }, []);

  const undo = useCallback(() => {
    if (past.length === 0) return;

    setHistory(prevHistory => {
      const previous = prevHistory.past[prevHistory.past.length - 1];
      const newPast = prevHistory.past.slice(0, prevHistory.past.length - 1);

      return {
        past: newPast,
        present: previous,
        future: [prevHistory.present, ...prevHistory.future],
      };
    });
  }, [past]);

  const redo = useCallback(() => {
    if (future.length === 0) return;

    setHistory(prevHistory => {
      const next = prevHistory.future[0];
      const newFuture = prevHistory.future.slice(1);

      return {
        past: [...prevHistory.past, prevHistory.present],
        present: next,
        future: newFuture,
      };
    });
  }, [future]);

  const clearHistory = useCallback(() => {
    setHistory({
      past: [],
      present,
      future: [],
    });
  }, [present]);

  return {
    state: present,
    setState,
    undo,
    redo,
    canUndo: past.length > 0,
    canRedo: future.length > 0,
    history,
    clearHistory,
  };
}