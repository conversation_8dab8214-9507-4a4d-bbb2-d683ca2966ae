import React from 'react';
import { BaseEmailTemplate } from './base-template';
import { renderEmailTemplate } from '../server/render-email';

interface OrderItem {
  name: string;
  quantity: number;
  price: string;
  total: string;
  imageUrl?: string;
  options?: { [key: string]: string };
}

interface OrderAddress {
  name: string;
  street: string;
  city: string;
  state?: string;
  postalCode: string;
  country: string;
}

interface OrderConfirmationEmailProps {
  customerName: string;
  orderNumber: string;
  orderDate: string;
  items: OrderItem[];
  subtotal: string;
  shipping: string;
  tax?: string;
  discount?: string;
  total: string;
  shippingAddress: OrderAddress;
  billingAddress?: OrderAddress;
  paymentMethod: string;
  estimatedDelivery?: string;
  trackingUrl?: string;
  trackingNumber?: string;
  customMessage?: string;
  orderUrl?: string;
}

/**
 * Order confirmation email template sent after a successful purchase
 */
export const OrderConfirmationEmail: React.FC<OrderConfirmationEmailProps> = ({
  customerName,
  orderNumber,
  orderDate,
  items,
  subtotal,
  shipping,
  tax,
  discount,
  total,
  shippingAddress,
  billingAddress,
  paymentMethod,
  estimatedDelivery,
  trackingUrl,
  trackingNumber,
  customMessage,
  orderUrl,
}) => {
  return (
    <BaseEmailTemplate
      previewText={`Order Confirmation #${orderNumber} - Thank you for your purchase!`}
    >
      <h1 style={{ fontSize: '24px', marginBottom: '10px' }}>Thank You for Your Order!</h1>
      
      <p>Hello {customerName},</p>
      
      <p>We're pleased to confirm that we've received your order. Here's a summary of your purchase:</p>
      
      <div style={{ backgroundColor: '#f8f9fa', padding: '15px', borderRadius: '4px', marginBottom: '20px' }}>
        <p style={{ margin: '5px 0' }}><strong>Order Number:</strong> #{orderNumber}</p>
        <p style={{ margin: '5px 0' }}><strong>Order Date:</strong> {orderDate}</p>
        {estimatedDelivery && (
          <p style={{ margin: '5px 0' }}><strong>Estimated Delivery:</strong> {estimatedDelivery}</p>
        )}
        {orderUrl && (
          <p style={{ margin: '10px 0 5px' }}>
            <a 
              href={orderUrl} 
              style={{ 
                color: '#6C1411', 
                textDecoration: 'underline' 
              }}
            >
              View Order Details
            </a>
          </p>
        )}
      </div>
      
      {customMessage && (
        <p style={{ marginBottom: '20px' }}>{customMessage}</p>
      )}
      
      <h2 style={{ fontSize: '20px', marginBottom: '15px', borderBottom: '1px solid #f0f0f0', paddingBottom: '10px' }}>Order Summary</h2>
      
      <table border={0} cellPadding={0} cellSpacing={0} width="100%" style={{ marginBottom: '20px' }}>
        <thead>
          <tr>
            <th style={{ textAlign: 'left', padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>Product</th>
            <th style={{ textAlign: 'center', padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>Qty</th>
            <th style={{ textAlign: 'right', padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>Price</th>
            <th style={{ textAlign: 'right', padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>Total</th>
          </tr>
        </thead>
        <tbody>
          {items.map((item, index) => (
            <tr key={index}>
              <td style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0', verticalAlign: 'top' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  {item.imageUrl && (
                    <img 
                      src={item.imageUrl} 
                      alt={item.name} 
                      width="50" 
                      height="50" 
                      style={{ marginRight: '10px', objectFit: 'cover' }} 
                    />
                  )}
                  <div>
                    <p style={{ margin: '0 0 5px 0', fontWeight: 'bold' }}>{item.name}</p>
                    {item.options && Object.entries(item.options).map(([key, value]) => (
                      <p key={key} style={{ margin: '0', fontSize: '12px', color: '#6c757d' }}>
                        {key}: {value}
                      </p>
                    ))}
                  </div>
                </div>
              </td>
              <td style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0', textAlign: 'center' }}>{item.quantity}</td>
              <td style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0', textAlign: 'right' }}>{item.price}</td>
              <td style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0', textAlign: 'right' }}>{item.total}</td>
            </tr>
          ))}
        </tbody>
      </table>
      
      <table border={0} cellPadding={0} cellSpacing={0} width="100%" style={{ marginBottom: '30px' }}>
        <tr>
          <td style={{ padding: '5px 0' }}>Subtotal:</td>
          <td style={{ padding: '5px 0', textAlign: 'right' }}>{subtotal}</td>
        </tr>
        <tr>
          <td style={{ padding: '5px 0' }}>Shipping:</td>
          <td style={{ padding: '5px 0', textAlign: 'right' }}>{shipping}</td>
        </tr>
        {tax && (
          <tr>
            <td style={{ padding: '5px 0' }}>Tax:</td>
            <td style={{ padding: '5px 0', textAlign: 'right' }}>{tax}</td>
          </tr>
        )}
        {discount && (
          <tr>
            <td style={{ padding: '5px 0' }}>Discount:</td>
            <td style={{ padding: '5px 0', textAlign: 'right', color: '#28a745' }}>-{discount}</td>
          </tr>
        )}
        <tr>
          <td style={{ padding: '10px 0', fontWeight: 'bold', borderTop: '1px solid #f0f0f0' }}>Total:</td>
          <td style={{ padding: '10px 0', textAlign: 'right', fontWeight: 'bold', borderTop: '1px solid #f0f0f0' }}>{total}</td>
        </tr>
      </table>
      
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '30px' }}>
        <div style={{ width: '48%' }}>
          <h3 style={{ fontSize: '16px', marginBottom: '10px' }}>Shipping Address</h3>
          <p style={{ margin: '0 0 5px 0' }}>{shippingAddress.name}</p>
          <p style={{ margin: '0 0 5px 0' }}>{shippingAddress.street}</p>
          <p style={{ margin: '0 0 5px 0' }}>
            {shippingAddress.city}{shippingAddress.state ? `, ${shippingAddress.state}` : ''} {shippingAddress.postalCode}
          </p>
          <p style={{ margin: '0' }}>{shippingAddress.country}</p>
        </div>
        
        <div style={{ width: '48%' }}>
          <h3 style={{ fontSize: '16px', marginBottom: '10px' }}>Payment Information</h3>
          <p style={{ margin: '0 0 5px 0' }}><strong>Method:</strong> {paymentMethod}</p>
          
          {billingAddress && (
            <>
              <p style={{ margin: '10px 0 5px 0' }}><strong>Billing Address:</strong></p>
              <p style={{ margin: '0 0 5px 0' }}>{billingAddress.name}</p>
              <p style={{ margin: '0 0 5px 0' }}>{billingAddress.street}</p>
              <p style={{ margin: '0 0 5px 0' }}>
                {billingAddress.city}{billingAddress.state ? `, ${billingAddress.state}` : ''} {billingAddress.postalCode}
              </p>
              <p style={{ margin: '0' }}>{billingAddress.country}</p>
            </>
          )}
        </div>
      </div>
      
      {trackingNumber && (
        <div style={{ backgroundColor: '#f8f9fa', padding: '15px', borderRadius: '4px', marginBottom: '20px' }}>
          <h3 style={{ fontSize: '16px', marginTop: '0', marginBottom: '10px' }}>Tracking Information</h3>
          <p style={{ margin: '0 0 5px 0' }}><strong>Tracking Number:</strong> {trackingNumber}</p>
          {trackingUrl && (
            <a 
              href={trackingUrl} 
              className="button button-secondary" 
              style={{ 
                backgroundColor: '#012169', 
                color: '#ffffff', 
                padding: '8px 16px', 
                textDecoration: 'none', 
                borderRadius: '4px', 
                display: 'inline-block', 
                marginTop: '10px',
                fontSize: '14px'
              }}
            >
              Track Your Order
            </a>
          )}
        </div>
      )}
      
      <p>If you have any questions about your order, please contact our customer service team.</p>
      
      <p>Thank you for shopping with Coco Milk Kids!</p>
      
      <p>Warm regards,<br />The Coco Milk Kids Team</p>
    </BaseEmailTemplate>
  );
};

/**
 * Generates the HTML for the order confirmation email
 */
export const generateOrderConfirmationEmail = async (props: OrderConfirmationEmailProps): Promise<string> => {
  return await renderEmailTemplate(<OrderConfirmationEmail {...props} />);
};