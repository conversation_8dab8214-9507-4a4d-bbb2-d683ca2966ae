import React from 'react';
import { BaseEmailTemplate } from './base-template';
import { renderEmailTemplate } from '../server/render-email';

interface PasswordResetEmailProps {
  firstName: string;
  resetLink: string;
  expiryTime?: string;
  customMessage?: string;
  requestIp?: string;
  requestDate?: string;
}

/**
 * Password reset email template sent when a user requests a password reset
 */
export const PasswordResetEmail: React.FC<PasswordResetEmailProps> = ({
  firstName,
  resetLink,
  expiryTime = '60 minutes',
  customMessage,
  requestIp,
  requestDate = new Date().toLocaleString(),
}) => {
  return (
    <BaseEmailTemplate
      previewText={`Password Reset Request for your Coco Milk Kids account`}
      logoTheme="dark"
    >
      <h1 style={{ fontSize: '24px', marginBottom: '20px' }}>Password Reset Request</h1>
      
      <p>Hello {firstName},</p>
      
      <p>We received a request to reset the password for your Coco Milk Kids account. To proceed with resetting your password, please click the button below:</p>
      
      {customMessage && <p>{customMessage}</p>}
      
      <div style={{ margin: '30px 0', textAlign: 'center' }}>
        <a 
          href={resetLink} 
          className="button" 
          style={{ 
            backgroundColor: '#6C1411', 
            color: '#ffffff', 
            padding: '12px 24px', 
            textDecoration: 'none', 
            borderRadius: '4px', 
            fontWeight: 400 
          }}
        >
          Reset Password
        </a>
      </div>
      
      <p style={{ fontSize: '14px', color: '#6c757d' }}>
        If the button above doesn't work, copy and paste the following link into your browser:
      </p>
      
      <p style={{ 
        fontSize: '14px', 
        wordBreak: 'break-all', 
        backgroundColor: '#f8f9fa', 
        padding: '10px', 
        borderRadius: '4px' 
      }}>
        {resetLink}
      </p>
      
      <div style={{ marginTop: '30px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
        <h3 style={{ fontSize: '16px', marginTop: '0', marginBottom: '10px' }}>Important Security Information</h3>
        <ul style={{ paddingLeft: '20px', margin: '0' }}>
          <li>This password reset link will expire in {expiryTime}.</li>
          <li>If you didn't request a password reset, please ignore this email or contact customer support if you have concerns.</li>
          {requestIp && <li>This request was made from IP address: {requestIp}</li>}
          {requestDate && <li>Request time: {requestDate}</li>}
        </ul>
      </div>
      
      <p style={{ marginTop: '30px' }}>For security reasons, this link can only be used once. If you need to reset your password again, please submit a new request.</p>
      
      <p>If you have any questions or need assistance, our customer service team is always ready to help.</p>
      
      <p>Regards,<br />The Coco Milk Kids Security Team</p>
    </BaseEmailTemplate>
  );
};

/**
 * Generates the HTML for the password reset email
 */
export const generatePasswordResetEmail = async (props: PasswordResetEmailProps): Promise<string> => {
  return await renderEmailTemplate(<PasswordResetEmail {...props} />);
};