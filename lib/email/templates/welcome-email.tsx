import React from 'react';
import { BaseEmailTemplate } from './base-template';
import { renderEmailTemplate } from '../server/render-email';

interface WelcomeEmailProps {
  firstName: string;
  signupDate?: string;
  customMessage?: string;
  ctaUrl?: string;
  ctaText?: string;
  featuredProducts?: Array<{
    name: string;
    imageUrl: string;
    price: string;
    url: string;
  }>;
}

/**
 * Welcome email template sent to new users after registration
 */
export const WelcomeEmail: React.FC<WelcomeEmailProps> = ({
  firstName,
  signupDate = new Date().toLocaleDateString(),
  customMessage,
  ctaUrl = 'https://cocomilkkids.com/products',
  ctaText = 'Shop Now',
  featuredProducts = [],
}) => {
  return (
    <BaseEmailTemplate
      previewText={`Welcome to Coco Milk Kids, ${firstName}! Thank you for joining our family.`}
    >
      <h1 style={{ fontSize: '24px', marginBottom: '20px' }}>Welcome to Coco Milk Kids!</h1>
      
      <p>Hello {firstName},</p>
      
      <p>Thank you for joining the Coco Milk Kids family on {signupDate}. We're thrilled to have you with us!</p>
      
      {customMessage && <p>{customMessage}</p>}
      
      <p>At Coco Milk Kids, we're passionate about providing quality children's clothing and accessories that combine comfort, style, and durability.</p>
      
      <div style={{ margin: '30px 0', textAlign: 'center' }}>
        <a href={ctaUrl} className="button" style={{ backgroundColor: '#6C1411', color: '#ffffff', padding: '12px 24px', textDecoration: 'none', borderRadius: '4px', fontWeight: 400 }}>
          {ctaText}
        </a>
      </div>
      
      {featuredProducts.length > 0 && (
        <>
          <h2 style={{ fontSize: '20px', marginTop: '30px', marginBottom: '15px' }}>Featured Products You Might Love</h2>
          
          <table border={0} cellPadding={0} cellSpacing={0} width="100%">
            <tr>
              {featuredProducts.map((product, index) => (
                <td key={index} width={`${100 / featuredProducts.length}%`} style={{ padding: '10px', textAlign: 'center', verticalAlign: 'top' }}>
                  <a href={product.url} style={{ textDecoration: 'none', color: 'inherit' }}>
                    <img 
                      src={product.imageUrl} 
                      alt={product.name} 
                      style={{ width: '100%', maxWidth: '150px', height: 'auto', marginBottom: '10px' }} 
                    />
                    <p style={{ margin: '5px 0', fontSize: '14px', fontWeight: 'bold' }}>{product.name}</p>
                    <p style={{ margin: '5px 0', fontSize: '14px' }}>{product.price}</p>
                  </a>
                </td>
              ))}
            </tr>
          </table>
        </>
      )}
      
      <div style={{ marginTop: '30px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
        <h3 style={{ fontSize: '18px', marginTop: '0' }}>What's Next?</h3>
        <ul style={{ paddingLeft: '20px', margin: '10px 0' }}>
          <li>Browse our latest collections</li>
          <li>Complete your profile for personalized recommendations</li>
          <li>Follow us on social media for updates and promotions</li>
        </ul>
      </div>
      
      <p style={{ marginTop: '30px' }}>If you have any questions or need assistance, our customer service team is always ready to help.</p>
      
      <p>Warm regards,<br />The Coco Milk Kids Team</p>
    </BaseEmailTemplate>
  );
};

/**
 * Generates the HTML for the welcome email
 */
export const generateWelcomeEmail = async (props: WelcomeEmailProps): Promise<string> => {
  return await renderEmailTemplate(<WelcomeEmail {...props} />);
};