import React from 'react';

interface BaseEmailProps {
  previewText?: string;
  children: React.ReactNode;
  footerText?: string;
  logoVariant?: 'standard' | 'horizontal' | 'square' | 'icon-only';
  logoTheme?: 'light' | 'dark' | 'color';
}

/**
 * Base email template that provides consistent styling and structure for all emails
 * This template includes the Coco Milk Kids branding and responsive design
 */
export const BaseEmailTemplate: React.FC<BaseEmailProps> = ({
  previewText = "Coco Milk Kids - Children's Clothing and Accessories",
  children,
  footerText = `© ${new Date().getFullYear()} Coco Milk Kids. All rights reserved.`,
  logoVariant = 'horizontal',
  logoTheme = 'color',
}) => {
  return (
    <html lang='en'>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta httpEquiv="Content-Type" content="text/html; charset=UTF-8" />
        <title>Coco Milk Kids</title>
        <style dangerouslySetInnerHTML={{ __html: `
          /* Base styles */
          body {
            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
            -webkit-font-smoothing: antialiased;
            font-size: 16px;
            line-height: 1.5;
            margin: 0;
            padding: 0;
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
            background-color: #f8f9fa;
            color: #333333;
          }
          
          .email-wrapper {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
          }
          
          .email-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #f0f0f0;
          }
          
          .email-body {
            padding: 30px 20px;
          }
          
          .email-footer {
            padding: 20px;
            text-align: center;
            font-size: 14px;
            color: #6c757d;
            border-top: 1px solid #f0f0f0;
          }
          
          /* Typography */
          h1, h2, h3, h4, h5, h6 {
            font-weight: 400;
            letter-spacing: 0.01em;
            line-height: 1.2;
            margin-top: 0;
            margin-bottom: 16px;
            color: #0D0D0D;
          }
          
          p {
            margin-top: 0;
            margin-bottom: 16px;
          }
          
          a {
            color: #6C1411;
            text-decoration: none;
          }
          
          a:hover {
            text-decoration: underline;
          }
          
          /* Buttons */
          .button {
            display: inline-block;
            background-color: #6C1411;
            color: #ffffff !important;
            font-size: 16px;
            font-weight: 400;
            text-align: center;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 4px;
            margin: 16px 0;
          }
          
          .button-secondary {
            background-color: #012169;
          }
          
          /* Responsive */
          @media only screen and (max-width: 600px) {
            .email-wrapper {
              width: 100% !important;
            }
            
            .email-body {
              padding: 20px 15px !important;
            }
          }
          
          /* Dark mode support */
          @media (prefers-color-scheme: dark) {
            body {
              background-color: #121212;
              color: #f8f9fa;
            }
            
            .email-wrapper {
              background-color: #1e1e1e;
            }
            
            .email-header, .email-footer {
              border-color: #333333;
            }
            
            h1, h2, h3, h4, h5, h6 {
              color: #ffffff;
            }
            
            .email-footer {
              color: #adb5bd;
            }
            
            a {
              color: #e57373;
            }
          }
        ` }} />
      </head>
      <body>
        <div style={{ display: 'none', fontSize: '1px', lineHeight: '1px', maxHeight: '0px', maxWidth: '0px', opacity: 0, overflow: 'hidden' }}>
          {previewText}
        </div>
        <table border={0} cellPadding={0} cellSpacing={0} width="100%">
          <tr>
            <td align="center" style={{ padding: '20px 0' }}>
              <table className="email-wrapper" border={0} cellPadding={0} cellSpacing={0} width="100%" style={{ maxWidth: '600px' }}>
                <tr>
                  <td className="email-header">
                    {/* Logo SVG inline for email compatibility */}
                    <svg
                      width={200}
                      height={50}
                      viewBox="0 0 200 50"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      style={{ maxWidth: '100%', height: 'auto' }}
                    >
                      {/* Hanger */}
                      <path
                        d="M30 25C30 25 25 25 25 30L20 40H40L35 30C35 25 30 25 30 25Z"
                        stroke={logoTheme === 'dark' ? '#0D0D0D' : logoTheme === 'light' ? '#FFFFFF' : '#6C1411'}
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        fill="none"
                      />
                      <path
                        d="M30 25V20"
                        stroke={logoTheme === 'dark' ? '#0D0D0D' : logoTheme === 'light' ? '#FFFFFF' : '#6C1411'}
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />

                      {/* coco */}
                      <path
                        d="M55 30C55 27.2386 57.2386 25 60 25C62.7614 25 65 27.2386 65 30C65 32.7614 62.7614 35 60 35C57.2386 35 55 32.7614 55 30Z"
                        stroke={logoTheme === 'dark' ? '#0D0D0D' : logoTheme === 'light' ? '#FFFFFF' : '#6C1411'}
                        strokeWidth="1.5"
                        fill="none"
                      />
                      <path
                        d="M70 30C70 27.2386 72.2386 25 75 25C77.7614 25 80 27.2386 80 30C80 32.7614 77.7614 35 75 35C72.2386 35 70 32.7614 70 30Z"
                        stroke={logoTheme === 'dark' ? '#0D0D0D' : logoTheme === 'light' ? '#FFFFFF' : '#6C1411'}
                        strokeWidth="1.5"
                        fill="none"
                      />
                      <path
                        d="M85 30C85 27.2386 87.2386 25 90 25C92.7614 25 95 27.2386 95 30C95 32.7614 92.7614 35 90 35C87.2386 35 85 32.7614 85 30Z"
                        stroke={logoTheme === 'dark' ? '#0D0D0D' : logoTheme === 'light' ? '#FFFFFF' : '#6C1411'}
                        strokeWidth="1.5"
                        fill="none"
                      />
                      <path
                        d="M100 30C100 27.2386 102.239 25 105 25C107.761 25 110 27.2386 110 30C110 32.7614 107.761 35 105 35C102.239 35 100 32.7614 100 30Z"
                        stroke={logoTheme === 'dark' ? '#0D0D0D' : logoTheme === 'light' ? '#FFFFFF' : '#6C1411'}
                        strokeWidth="1.5"
                        fill="none"
                      />

                      {/* milk */}
                      <path
                        d="M125 25V30M125 30L120 25M125 30L130 25"
                        stroke={logoTheme === 'dark' ? '#0D0D0D' : logoTheme === 'light' ? '#FFFFFF' : '#6C1411'}
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M135 25V30"
                        stroke={logoTheme === 'dark' ? '#0D0D0D' : logoTheme === 'light' ? '#FFFFFF' : '#6C1411'}
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M145 25V30L150 25V30"
                        stroke={logoTheme === 'dark' ? '#0D0D0D' : logoTheme === 'light' ? '#FFFFFF' : '#6C1411'}
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M160 25V30M160 25L155 30M160 25L165 30"
                        stroke={logoTheme === 'dark' ? '#0D0D0D' : logoTheme === 'light' ? '#FFFFFF' : '#6C1411'}
                        strokeWidth="1.5"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />

                      {/* KIDS */}
                      <path
                        d="M175 35L170 30L175 30L170 35"
                        stroke={logoTheme === 'dark' ? '#2B1D18' : logoTheme === 'light' ? '#E5E5E5' : '#012169'}
                        strokeWidth="1"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M180 30V35"
                        stroke={logoTheme === 'dark' ? '#2B1D18' : logoTheme === 'light' ? '#E5E5E5' : '#012169'}
                        strokeWidth="1"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M185 30H190V32.5H185V35H190"
                        stroke={logoTheme === 'dark' ? '#2B1D18' : logoTheme === 'light' ? '#E5E5E5' : '#012169'}
                        strokeWidth="1"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M195 30C195 30 200 30 200 32.5C200 35 195 35 195 35"
                        stroke={logoTheme === 'dark' ? '#2B1D18' : logoTheme === 'light' ? '#E5E5E5' : '#012169'}
                        strokeWidth="1"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </td>
                </tr>
                <tr>
                  <td className="email-body">
                    {children}
                  </td>
                </tr>
                <tr>
                  <td className="email-footer">
                    <p>{footerText}</p>
                    <p>
                      <a href="https://cocomilkkids.com/privacy" target="_blank">Privacy Policy</a> | 
                      <a href="https://cocomilkkids.com/terms" target="_blank"> Terms of Service</a>
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </body>
    </html>
  );
};