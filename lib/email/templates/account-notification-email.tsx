import React from 'react';
import { BaseEmailTemplate } from './base-template';
import { renderEmailTemplate } from '../server/render-email';

interface AccountNotificationEmailProps {
  firstName: string;
  notificationType: 'info' | 'warning' | 'success';
  title: string;
  message: string;
  actionLink?: string;
  actionText?: string;
  details?: Array<{ label: string; value: string }>;
  timestamp?: string;
}

/**
 * Account notification email template for various account-related notifications
 * Can be used for: email changes, profile updates, security alerts, etc.
 */
export const AccountNotificationEmail: React.FC<AccountNotificationEmailProps> = ({
  firstName,
  notificationType = 'info',
  title,
  message,
  actionLink,
  actionText = 'View Details',
  details = [],
  timestamp = new Date().toLocaleString(),
}) => {
  // Set colors based on notification type
  const colors = {
    info: {
      bg: '#e3f2fd',
      border: '#90caf9',
      icon: '📝',
    },
    warning: {
      bg: '#fff3e0',
      border: '#ffcc80',
      icon: '⚠️',
    },
    success: {
      bg: '#e8f5e9',
      border: '#a5d6a7',
      icon: '✅',
    },
  };
  
  const currentColors = colors[notificationType];
  
  return (
    <BaseEmailTemplate
      previewText={`${title} - Coco Milk Kids Account Notification`}
    >
      <div style={{ 
        backgroundColor: currentColors.bg, 
        borderLeft: `4px solid ${currentColors.border}`,
        padding: '15px',
        marginBottom: '20px',
        borderRadius: '4px'
      }}>
        <h1 style={{ 
          fontSize: '24px', 
          marginTop: '0',
          marginBottom: '10px', 
          display: 'flex',
          alignItems: 'center'
        }}>
          <span style={{ marginRight: '10px', fontSize: '24px' }}>{currentColors.icon}</span>
          {title}
        </h1>
      </div>
      
      <p>Hello {firstName},</p>
      
      <p>{message}</p>
      
      {details.length > 0 && (
        <div style={{ marginTop: '20px', marginBottom: '20px' }}>
          <table border={0} cellPadding={0} cellSpacing={0} width="100%" style={{ backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
            <tbody>
              {details.map((detail, index) => (
                <tr key={index}>
                  <td style={{ padding: '10px 15px', borderBottom: index < details.length - 1 ? '1px solid #e9ecef' : 'none', fontWeight: 'bold', width: '40%' }}>
                    {detail.label}
                  </td>
                  <td style={{ padding: '10px 15px', borderBottom: index < details.length - 1 ? '1px solid #e9ecef' : 'none' }}>
                    {detail.value}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {actionLink && (
        <div style={{ margin: '30px 0', textAlign: 'center' }}>
          <a 
            href={actionLink} 
            className="button" 
            style={{ 
              backgroundColor: notificationType === 'warning' ? '#f57c00' : notificationType === 'success' ? '#43a047' : '#6C1411',
              color: '#ffffff', 
              padding: '12px 24px', 
              textDecoration: 'none', 
              borderRadius: '4px', 
              fontWeight: 400 
            }}
          >
            {actionText}
          </a>
        </div>
      )}
      
      <p style={{ fontSize: '14px', color: '#6c757d', marginTop: '30px' }}>
        This notification was sent on {timestamp}.
      </p>
      
      <p>If you did not initiate this action or have any questions, please contact our customer support team immediately.</p>
      
      <p>Regards,<br />The Coco Milk Kids Team</p>
    </BaseEmailTemplate>
  );
};

/**
 * Generates the HTML for the account notification email
 */
export const generateAccountNotificationEmail = async (props: AccountNotificationEmailProps): Promise<string> => {
  return await renderEmailTemplate(<AccountNotificationEmail {...props} />);
};