# Coco Milk Kids Email Templates

This directory contains the email templates and utilities for sending branded emails from the Coco Milk Kids store.

## Overview

The email system is built using React components that are rendered to static HTML strings. This approach allows for:

1. Consistent branding across all emails
2. Type-safe templates with TypeScript
3. Easy customization and maintenance
4. Responsive design that works across email clients

## Directory Structure

```
lib/email/
├── README.md                  # This documentation
├── send-email.ts              # Core email sending functionality
├── email-service.ts           # Service for sending different types of emails
├── index.ts                   # Main exports
└── templates/                 # Email templates
    ├── base-template.tsx      # Base template all emails extend
    ├── welcome-email.tsx      # Welcome email for new users
    ├── order-confirmation-email.tsx  # Order confirmation email
    ├── password-reset-email.tsx      # Password reset email
    ├── account-notification-email.tsx # General account notifications
    └── index.ts               # Template exports
```

## Usage

### Sending Emails

To send an email, use the `emailService` which provides methods for each email type:

```typescript
import { emailService } from '@/lib/email';

// Send a welcome email
await emailService.sendWelcomeEmail('<EMAIL>', {
  firstName: '<PERSON>',
  signupDate: new Date().toLocaleDateString(),
  // other options...
});

// Send an order confirmation
await emailService.sendOrderConfirmationEmail('<EMAIL>', {
  customerName: 'John Doe',
  orderNumber: '12345',
  // other required fields...
});

// Send a password reset email
await emailService.sendPasswordResetEmail('<EMAIL>', {
  firstName: 'John',
  resetLink: 'https://example.com/reset?token=abc123',
  // other options...
});

// Send an account notification
await emailService.sendAccountNotificationEmail('<EMAIL>', {
  firstName: 'John',
  notificationType: 'info', // 'info', 'warning', or 'success'
  title: 'Profile Updated',
  message: 'Your profile has been updated successfully.',
  // other options...
});
```

### Customizing Templates

Each email template accepts a set of props that can be used to customize the content. See the TypeScript interfaces in each template file for the available options.

### Admin Interface

An admin interface for previewing and testing email templates is available at `/admin/email-templates`.

## Email Template Design

All email templates follow these design principles:

1. **Responsive**: Templates work on desktop and mobile devices
2. **Branded**: Uses Coco Milk Kids colors and logo
3. **Accessible**: Good contrast and readable text
4. **Compatible**: Designed to work across major email clients

## Integration with Nodemailer

The email system is designed to work with Nodemailer. The `send-email.ts` file should be updated with your Nodemailer configuration.

Example configuration:

```typescript
// In send-email.ts
import nodemailer from 'nodemailer';

export async function sendEmail(options: EmailOptions): Promise<boolean> {
  const from = options.from || process.env.EMAIL_FROM || '<EMAIL>';
  
  try {
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_SERVER_HOST,
      port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
      secure: process.env.EMAIL_SERVER_SECURE === 'true',
      auth: {
        user: process.env.EMAIL_SERVER_USER,
        pass: process.env.EMAIL_SERVER_PASSWORD,
      },
    });
    
    await transporter.sendMail({
      from,
      to: options.to,
      subject: options.subject,
      text: options.text || '',
      html: options.html,
    });
    
    return true;
  } catch (error) {
    console.error('Error sending email:', error);
    return false;
  }
}
```

## Environment Variables

The following environment variables should be set for email functionality:

```
EMAIL_FROM=<EMAIL>
EMAIL_SERVER_HOST=smtp.example.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_SECURE=false
EMAIL_SERVER_USER=username
EMAIL_SERVER_PASSWORD=password
```