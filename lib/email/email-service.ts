import { sendEmail } from './send-email';
import {
  generateWelcomeEmail,
  generateOrderConfirmationEmail,
  generatePasswordResetEmail,
  generateAccountNotificationEmail,
} from './templates';

/**
 * Email service that provides methods to send various types of emails
 * This service handles the generation of email content and sending
 */
export const emailService = {
  /**
   * Send a welcome email to a new user
   */
  sendWelcomeEmail: async (
    to: string,
    {
      firstName,
      signupDate,
      customMessage,
      ctaUrl,
      ctaText,
      featuredProducts,
    }: Parameters<typeof generateWelcomeEmail>[0]
  ) => {
    const html = await generateWelcomeEmail({
      firstName,
      signupDate,
      customMessage,
      ctaUrl,
      ctaText,
      featuredProducts,
    });

    return sendEmail({
      to,
      subject: `Welcome to Coco Milk Kids, ${firstName}!`,
      html,
    });
  },

  /**
   * Send an order confirmation email
   */
  sendOrderConfirmationEmail: async (
    to: string,
    props: Parameters<typeof generateOrderConfirmationEmail>[0]
  ) => {
    const html = await generateOrderConfirmationEmail(props);

    return sendEmail({
      to,
      subject: `Order Confirmation #${props.orderNumber} - Coco Milk Kids`,
      html,
    });
  },

  /**
   * Send a password reset email
   */
  sendPasswordResetEmail: async (
    to: string,
    {
      firstName,
      resetLink,
      expiryTime,
      customMessage,
      requestIp,
      requestDate,
    }: Parameters<typeof generatePasswordResetEmail>[0]
  ) => {
    const html = await generatePasswordResetEmail({
      firstName,
      resetLink,
      expiryTime,
      customMessage,
      requestIp,
      requestDate,
    });

    return sendEmail({
      to,
      subject: 'Password Reset Request - Coco Milk Kids',
      html,
    });
  },

  /**
   * Send an account notification email
   */
  sendAccountNotificationEmail: async (
    to: string,
    props: Parameters<typeof generateAccountNotificationEmail>[0]
  ) => {
    const html = await generateAccountNotificationEmail(props);

    return sendEmail({
      to,
      subject: `${props.title} - Coco Milk Kids Account Notification`,
      html,
    });
  },
};

export default emailService;