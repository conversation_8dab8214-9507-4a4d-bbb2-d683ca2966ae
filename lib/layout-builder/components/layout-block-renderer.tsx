'use client'

import React from 'react'
import { LayoutBlock, ResolvedLayout } from '../types'
import { cn } from '@/lib/utils'
import { 
  Search, 
  ShoppingCart, 
  Menu, 
  User, 
  Home,
  FileText,
  Tag,
  Calendar,
  Filter,
  Grid,
  List,
  Link,
  Share2,
  Copyright,
  Navigation as NavigationIcon,
  Image as ImageIcon,
  Type,
  Box
} from 'lucide-react'

interface LayoutBlockRendererProps {
  block: LayoutBlock
  layout: ResolvedLayout
  isPreview?: boolean
  className?: string
}

export function LayoutBlockRenderer({ 
  block, 
  layout, 
  isPreview = false, 
  className 
}: LayoutBlockRendererProps) {
  // Don't render if block is not visible
  if (!block.isVisible) {
    return null
  }

  // Apply responsive visibility
  // In a real implementation, you'd check the current viewport
  const devicePreview = 'desktop' // This would come from context
  const responsiveSettings = block.responsive[devicePreview as keyof typeof block.responsive]
  
  if (!responsiveSettings?.isVisible) {
    return null
  }

  // Generate block styles
  const blockStyles = generateBlockStyles(block)
  
  // Render based on block type
  const blockContent = renderBlockContent(block, layout, isPreview)

  return (
    <div 
      className={cn(
        'layout-block',
        `block-type-${block.type}`,
        blockStyles.className,
        className
      )}
      style={blockStyles.style}
      data-block-id={block.id}
      data-block-type={block.type}
    >
      {blockContent}
    </div>
  )
}

// Generate CSS styles for the block
function generateBlockStyles(block: LayoutBlock) {
  const { styling, configuration } = block
  
  const style: React.CSSProperties = {}
  const classNames: string[] = []

  // Background
  if (styling.background.type === 'color' && styling.background.color) {
    style.backgroundColor = styling.background.color
  } else if (styling.background.type === 'image' && styling.background.image) {
    style.backgroundImage = `url(${styling.background.image.url})`
    style.backgroundSize = styling.background.image.size
    style.backgroundPosition = styling.background.image.position
    style.backgroundRepeat = styling.background.image.repeat
  }

  // Spacing/Padding
  if (styling.spacing) {
    style.paddingTop = styling.spacing.top
    style.paddingRight = styling.spacing.right
    style.paddingBottom = styling.spacing.bottom
    style.paddingLeft = styling.spacing.left
  }

  // Border
  if (styling.border && styling.border.style !== 'none') {
    style.borderWidth = styling.border.width
    style.borderStyle = styling.border.style
    style.borderColor = styling.border.color
    style.borderRadius = styling.border.radius
  }

  // Typography
  if (styling.typography) {
    style.fontFamily = styling.typography.fontFamily
    style.fontSize = styling.typography.fontSize
    style.fontWeight = styling.typography.fontWeight
    style.lineHeight = styling.typography.lineHeight
    style.letterSpacing = styling.typography.letterSpacing
    style.textAlign = styling.typography.textAlign
    style.textTransform = styling.typography.textTransform
  }

  // Colors
  if (styling.colors?.text) {
    style.color = styling.colors.text
  }

  // Shadow
  if (styling.shadow && styling.shadow.type !== 'none') {
    const { x, y, blur, spread, color } = styling.shadow
    style.boxShadow = `${x}px ${y}px ${blur}px ${spread}px ${color}`
  }

  // Alignment
  if (configuration.alignment) {
    classNames.push(`text-${configuration.alignment}`)
  }

  // Size
  if (configuration.size) {
    classNames.push(`size-${configuration.size}`)
  }

  return {
    style,
    className: classNames.join(' ')
  }
}

// Render block content based on type
function renderBlockContent(block: LayoutBlock, layout: ResolvedLayout, isPreview: boolean) {
  switch (block.type) {
    case 'logo':
      return <LogoBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'navigation':
      return <NavigationBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'search':
      return <SearchBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'cart':
      return <CartBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'content':
      return <ContentBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'widget':
      return <WidgetBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'social':
      return <SocialBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'copyright':
      return <CopyrightBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'breadcrumbs':
      return <BreadcrumbsBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'filters':
      return <FiltersBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'categories':
      return <CategoriesBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'recent-posts':
      return <RecentPostsBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'tags':
      return <TagsBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'links':
      return <LinksBlock block={block} layout={layout} isPreview={isPreview} />
    
    case 'custom':
      return <CustomBlock block={block} layout={layout} isPreview={isPreview} />
    
    default:
      return <DefaultBlock block={block} layout={layout} isPreview={isPreview} />
  }
}

// Individual Block Components

function LogoBlock({ block, layout, isPreview }: BlockComponentProps) {
  const logoUrl = block.content.image || '/logo.png'
  const logoText = block.content.text || 'Logo'
  const logoLink = block.content.links?.[0]?.url || '/'

  if (isPreview) {
    return (
      <div className="flex items-center gap-2">
        <ImageIcon className="w-8 h-8 text-gray-400" />
        <span className="font-bold text-lg">{logoText}</span>
      </div>
    )
  }

  return (
    <a href={logoLink} className="flex items-center gap-2">
      {block.content.image ? (
        <img src={logoUrl} alt={logoText} className="h-8 w-auto" />
      ) : (
        <span className="font-bold text-lg">{logoText}</span>
      )}
    </a>
  )
}

function NavigationBlock({ block, layout, isPreview }: BlockComponentProps) {
  const menuId = block.content.menu
  const menu = layout.menus.find(m => m.id === menuId)
  
  if (isPreview) {
    return (
      <nav className="flex items-center gap-4">
        <NavigationIcon className="w-5 h-5 text-gray-400" />
        <span className="text-sm text-gray-600">Navigation Menu</span>
      </nav>
    )
  }

  if (!menu) {
    return (
      <nav className="flex items-center gap-4">
        <span className="text-sm text-gray-500">No menu selected</span>
      </nav>
    )
  }

  return (
    <nav className="flex items-center gap-4">
      {menu.items.map((item) => (
        <a
          key={item.id}
          href={item.url}
          target={item.target}
          className="text-sm hover:text-primary transition-colors"
        >
          {item.icon && <span className="mr-1">{item.icon}</span>}
          {item.text}
        </a>
      ))}
    </nav>
  )
}

function SearchBlock({ block, layout, isPreview }: BlockComponentProps) {
  const placeholder = block.content.text || 'Search...'
  
  if (isPreview) {
    return (
      <div className="flex items-center gap-2 p-2 border rounded">
        <Search className="w-4 h-4 text-gray-400" />
        <span className="text-sm text-gray-500">{placeholder}</span>
      </div>
    )
  }

  return (
    <div className="relative">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
      <input
        type="search"
        placeholder={placeholder}
        className="pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
      />
    </div>
  )
}

function CartBlock({ block, layout, isPreview }: BlockComponentProps) {
  if (isPreview) {
    return (
      <div className="flex items-center gap-2">
        <ShoppingCart className="w-5 h-5 text-gray-400" />
        <span className="text-sm text-gray-600">Cart (0)</span>
      </div>
    )
  }

  return (
    <button className="flex items-center gap-2 hover:text-primary transition-colors">
      <ShoppingCart className="w-5 h-5" />
      <span className="text-sm">Cart (0)</span>
    </button>
  )
}

function ContentBlock({ block, layout, isPreview }: BlockComponentProps) {
  const content = block.content.html || block.content.text || ''
  
  if (isPreview) {
    return (
      <div className="flex items-center gap-2 p-4 border-2 border-dashed border-gray-300 rounded">
        <FileText className="w-5 h-5 text-gray-400" />
        <span className="text-sm text-gray-600">Content Block</span>
      </div>
    )
  }

  if (block.content.html) {
    return (
      <div 
        className="prose prose-sm max-w-none"
        dangerouslySetInnerHTML={{ __html: content }}
      />
    )
  }

  return (
    <div className="prose prose-sm max-w-none">
      <p>{content}</p>
    </div>
  )
}

function WidgetBlock({ block, layout, isPreview }: BlockComponentProps) {
  const widgetId = block.content.widget
  const widget = layout.widgets.find(w => w.widgets.some(widget => widget.id === widgetId))?.widgets.find(w => w.id === widgetId)
  
  if (isPreview) {
    return (
      <div className="flex items-center gap-2 p-3 border rounded">
        <Box className="w-5 h-5 text-gray-400" />
        <span className="text-sm text-gray-600">Widget: {widget?.title || 'Unknown'}</span>
      </div>
    )
  }

  if (!widget) {
    return (
      <div className="p-3 border border-dashed rounded text-center text-gray-500">
        Widget not found
      </div>
    )
  }

  return (
    <div className="widget-container">
      {widget.title && (
        <h3 className="widget-title font-medium mb-2">{widget.title}</h3>
      )}
      <div className="widget-content">
        {/* Widget content would be rendered here based on widget type */}
        <div dangerouslySetInnerHTML={{ __html: widget.content }} />
      </div>
    </div>
  )
}

function SocialBlock({ block, layout, isPreview }: BlockComponentProps) {
  const socialLinks = block.content.links || []
  
  if (isPreview) {
    return (
      <div className="flex items-center gap-2">
        <Share2 className="w-5 h-5 text-gray-400" />
        <span className="text-sm text-gray-600">Social Links</span>
      </div>
    )
  }

  return (
    <div className="flex items-center gap-3">
      {socialLinks.map((link, index) => (
        <a
          key={index}
          href={link.url}
          target={link.target}
          className="text-gray-600 hover:text-primary transition-colors"
          title={link.text}
        >
          {link.icon ? (
            <span dangerouslySetInnerHTML={{ __html: link.icon }} />
          ) : (
            <Share2 className="w-5 h-5" />
          )}
        </a>
      ))}
    </div>
  )
}

function CopyrightBlock({ block, layout, isPreview }: BlockComponentProps) {
  const copyrightText = block.content.text || `© ${new Date().getFullYear()} All rights reserved.`
  
  if (isPreview) {
    return (
      <div className="flex items-center gap-2">
        <Copyright className="w-4 h-4 text-gray-400" />
        <span className="text-sm text-gray-600">{copyrightText}</span>
      </div>
    )
  }

  return (
    <div className="text-sm text-gray-600">
      {copyrightText}
    </div>
  )
}

function BreadcrumbsBlock({ block, layout, isPreview }: BlockComponentProps) {
  if (isPreview) {
    return (
      <nav className="flex items-center gap-2 text-sm">
        <Home className="w-4 h-4" />
        <span>Home</span>
        <span>/</span>
        <span>Category</span>
        <span>/</span>
        <span className="text-gray-600">Current Page</span>
      </nav>
    )
  }

  // In a real implementation, breadcrumbs would be generated based on current route
  return (
    <nav className="flex items-center gap-2 text-sm">
      <a href="/" className="hover:text-primary">Home</a>
      <span>/</span>
      <span className="text-gray-600">Current Page</span>
    </nav>
  )
}

function FiltersBlock({ block, layout, isPreview }: BlockComponentProps) {
  if (isPreview) {
    return (
      <div className="flex items-center gap-2 p-3 border rounded">
        <Filter className="w-5 h-5 text-gray-400" />
        <span className="text-sm text-gray-600">Filters</span>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      <h3 className="font-medium">Filters</h3>
      <div className="space-y-2">
        <label className="flex items-center gap-2">
          <input type="checkbox" className="rounded" />
          <span className="text-sm">Filter Option 1</span>
        </label>
        <label className="flex items-center gap-2">
          <input type="checkbox" className="rounded" />
          <span className="text-sm">Filter Option 2</span>
        </label>
      </div>
    </div>
  )
}

function CategoriesBlock({ block, layout, isPreview }: BlockComponentProps) {
  if (isPreview) {
    return (
      <div className="flex items-center gap-2 p-3 border rounded">
        <Grid className="w-5 h-5 text-gray-400" />
        <span className="text-sm text-gray-600">Categories</span>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      <h3 className="font-medium">Categories</h3>
      <ul className="space-y-1">
        <li><a href="#" className="text-sm hover:text-primary">Category 1</a></li>
        <li><a href="#" className="text-sm hover:text-primary">Category 2</a></li>
        <li><a href="#" className="text-sm hover:text-primary">Category 3</a></li>
      </ul>
    </div>
  )
}

function RecentPostsBlock({ block, layout, isPreview }: BlockComponentProps) {
  if (isPreview) {
    return (
      <div className="flex items-center gap-2 p-3 border rounded">
        <Calendar className="w-5 h-5 text-gray-400" />
        <span className="text-sm text-gray-600">Recent Posts</span>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      <h3 className="font-medium">Recent Posts</h3>
      <ul className="space-y-2">
        <li>
          <a href="#" className="text-sm hover:text-primary block">
            Sample Post Title 1
          </a>
          <span className="text-xs text-gray-500">2 days ago</span>
        </li>
        <li>
          <a href="#" className="text-sm hover:text-primary block">
            Sample Post Title 2
          </a>
          <span className="text-xs text-gray-500">1 week ago</span>
        </li>
      </ul>
    </div>
  )
}

function TagsBlock({ block, layout, isPreview }: BlockComponentProps) {
  if (isPreview) {
    return (
      <div className="flex items-center gap-2 p-3 border rounded">
        <Tag className="w-5 h-5 text-gray-400" />
        <span className="text-sm text-gray-600">Tags</span>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      <h3 className="font-medium">Tags</h3>
      <div className="flex flex-wrap gap-2">
        <span className="px-2 py-1 bg-gray-100 text-xs rounded">Tag 1</span>
        <span className="px-2 py-1 bg-gray-100 text-xs rounded">Tag 2</span>
        <span className="px-2 py-1 bg-gray-100 text-xs rounded">Tag 3</span>
      </div>
    </div>
  )
}

function LinksBlock({ block, layout, isPreview }: BlockComponentProps) {
  const links = block.content.links || []
  
  if (isPreview) {
    return (
      <div className="flex items-center gap-2 p-3 border rounded">
        <Link className="w-5 h-5 text-gray-400" />
        <span className="text-sm text-gray-600">Links ({links.length})</span>
      </div>
    )
  }

  return (
    <div className="space-y-3">
      <h3 className="font-medium">Links</h3>
      <ul className="space-y-1">
        {links.map((link, index) => (
          <li key={index}>
            <a 
              href={link.url} 
              target={link.target}
              className="text-sm hover:text-primary"
            >
              {link.text}
            </a>
          </li>
        ))}
      </ul>
    </div>
  )
}

function CustomBlock({ block, layout, isPreview }: BlockComponentProps) {
  if (isPreview) {
    return (
      <div className="flex items-center gap-2 p-4 border-2 border-dashed border-gray-300 rounded">
        <Box className="w-5 h-5 text-gray-400" />
        <span className="text-sm text-gray-600">Custom Block: {block.name}</span>
      </div>
    )
  }

  return (
    <div className="custom-block">
      {block.content.html ? (
        <div dangerouslySetInnerHTML={{ __html: block.content.html }} />
      ) : (
        <div className="p-4 border border-dashed rounded text-center text-gray-500">
          Custom block content
        </div>
      )}
    </div>
  )
}

function DefaultBlock({ block, layout, isPreview }: BlockComponentProps) {
  return (
    <div className="flex items-center gap-2 p-3 border border-dashed rounded">
      <Type className="w-5 h-5 text-gray-400" />
      <span className="text-sm text-gray-600">
        Unknown block type: {block.type}
      </span>
    </div>
  )
}

// Helper interface for block component props
interface BlockComponentProps {
  block: LayoutBlock
  layout: ResolvedLayout
  isPreview: boolean
}