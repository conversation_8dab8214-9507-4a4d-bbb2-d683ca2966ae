import { prisma } from '@/lib/prisma'
import { Layout, LayoutAssignment, NavigationMenu, WidgetArea, ResolvedLayout } from '../types'
import { NextJSLayout } from '../types/nextjs-types'

export class LayoutService {
  // Get layout by ID
  static async getLayoutById(id: string): Promise<Layout | null> {
    try {
      const layout = await prisma.layout.findUnique({
        where: { id },
        include: {
          assignments: {
            include: {
              conditions: true
            }
          },
          sections: {
            include: {
              layoutBlocks: true
            },
            orderBy: { position: 'asc' }
          },
          versions: {
            orderBy: { versionNumber: 'desc' },
            take: 5
          }
        }
      })

      if (!layout) return null

      return this.transformPrismaLayout(layout)
    } catch (error) {
      console.error('Error fetching layout:', error)
      throw new Error('Failed to fetch layout')
    }
  }

  // Get layouts with filtering and pagination
  static async getLayouts(params: {
    page?: number
    limit?: number
    type?: string
    category?: string
    search?: string
    isTemplate?: boolean
    isActive?: boolean
  }) {
    try {
      const {
        page = 1,
        limit = 10,
        type,
        category,
        search,
        isTemplate,
        isActive
      } = params

      const skip = (page - 1) * limit

      // Build where clause
      const where: any = {}
      
      if (type) where.type = type
      if (category) where.category = category
      if (isTemplate !== undefined) where.isTemplate = isTemplate
      if (isActive !== undefined) where.isActive = isActive
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { tags: { hasSome: [search] } }
        ]
      }

      const [layouts, total] = await Promise.all([
        prisma.layout.findMany({
          where,
          skip,
          take: limit,
          orderBy: { updatedAt: 'desc' },
          include: {
            _count: {
              select: {
                assignments: true,
                sections: true
              }
            }
          }
        }),
        prisma.layout.count({ where })
      ])

      return {
        layouts: layouts.map(layout => this.transformPrismaLayout(layout)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    } catch (error) {
      console.error('Error fetching layouts:', error)
      throw new Error('Failed to fetch layouts')
    }
  }

  // Create new layout
  static async createLayout(layoutData: Omit<Layout, 'id' | 'createdAt' | 'updatedAt'>, userId?: string): Promise<Layout> {
    try {
      const layout = await prisma.layout.create({
        data: {
          ...layoutData,
          structure: layoutData.structure as any,
          styling: layoutData.styling as any,
          responsive: layoutData.responsive as any,
          conditions: layoutData.conditions as any,
          createdBy: userId,
          updatedBy: userId,
        },
        include: {
          assignments: {
            include: {
              conditions: true
            }
          },
          sections: {
            include: {
              layoutBlocks: true
            }
          }
        }
      })

      return this.transformPrismaLayout(layout)
    } catch (error) {
      console.error('Error creating layout:', error)
      throw new Error('Failed to create layout')
    }
  }

  // Update layout
  static async updateLayout(id: string, updates: Partial<Layout>, userId?: string): Promise<Layout> {
    try {
      const layout = await prisma.layout.update({
        where: { id },
        data: {
          ...updates,
          structure: updates.structure as any,
          styling: updates.styling as any,
          responsive: updates.responsive as any,
          conditions: updates.conditions as any,
          updatedBy: userId,
          updatedAt: new Date(),
        },
        include: {
          assignments: {
            include: {
              conditions: true
            }
          },
          sections: {
            include: {
              layoutBlocks: true
            }
          }
        }
      })

      return this.transformPrismaLayout(layout)
    } catch (error) {
      console.error('Error updating layout:', error)
      throw new Error('Failed to update layout')
    }
  }

  // Delete layout
  static async deleteLayout(id: string): Promise<void> {
    try {
      // Check if layout is in use
      const assignmentCount = await prisma.layoutAssignment.count({
        where: { layoutId: id }
      })

      if (assignmentCount > 0) {
        throw new Error('Cannot delete layout that is currently assigned')
      }

      await prisma.layout.delete({
        where: { id }
      })
    } catch (error) {
      console.error('Error deleting layout:', error)
      throw error
    }
  }

  // Resolve layout for a specific context
  static async resolveLayout(params: {
    targetType: string
    targetId?: string
    targetSlug?: string
  }): Promise<ResolvedLayout | null> {
    try {
      const { targetType, targetId, targetSlug } = params

      // Find the best matching layout assignment
      const assignment = await prisma.layoutAssignment.findFirst({
        where: {
          targetType,
          ...(targetId && { targetId }),
          ...(targetSlug && { targetSlug }),
          isActive: true
        },
        orderBy: { priority: 'desc' },
        include: {
          layout: {
            include: {
              sections: {
                include: {
                  layoutBlocks: true
                },
                orderBy: { position: 'asc' }
              }
            }
          },
          conditions: true
        }
      })

      if (!assignment) return null

      // Get navigation menus
      const menus = await prisma.navigationMenu.findMany({
        where: { isActive: true }
      })

      // Get widget areas
      const widgets = await prisma.widgetArea.findMany({
        where: { isActive: true }
      })

      return {
        layout: this.transformPrismaLayout(assignment.layout),
        sections: assignment.layout.sections.map(section => ({
          ...section,
          blocks: section.layoutBlocks || []
        })) as any,
        assignments: [assignment] as any,
        menus: menus.map(menu => ({
          ...menu,
          items: menu.items as any,
          settings: menu.settings as any
        })) as NavigationMenu[],
        widgets: widgets.map(widget => ({
          ...widget,
          widgets: widget.widgets as any,
          settings: widget.settings as any
        })) as WidgetArea[]
      }
    } catch (error) {
      console.error('Error resolving layout:', error)
      throw new Error('Failed to resolve layout')
    }
  }

  // Create layout assignment
  static async createLayoutAssignment(assignmentData: {
    layoutId: string
    targetType: string
    targetId?: string
    targetSlug?: string
    priority?: number
    conditions?: any[]
  }): Promise<LayoutAssignment> {
    try {
      const assignment = await prisma.layoutAssignment.create({
        data: {
          layoutId: assignmentData.layoutId,
          targetType: assignmentData.targetType,
          targetId: assignmentData.targetId,
          targetSlug: assignmentData.targetSlug,
          priority: assignmentData.priority || 0,
          conditions: assignmentData.conditions ? {
            create: assignmentData.conditions.map(condition => ({
              type: condition.type,
              operator: condition.operator,
              value: condition.value
            }))
          } : undefined
        },
        include: {
          conditions: true,
          layout: true
        }
      })

      return assignment as any
    } catch (error) {
      console.error('Error creating layout assignment:', error)
      throw new Error('Failed to create layout assignment')
    }
  }

  // Get layout templates
  static async getLayoutTemplates(params: {
    category?: string
    type?: string
    search?: string
  } = {}) {
    try {
      const { category, type, search } = params

      const where: any = { isPublic: true }
      
      if (category) where.category = category
      if (type) where.type = type
      
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { tags: { hasSome: [search] } }
        ]
      }

      const templates = await prisma.layoutTemplate.findMany({
        where,
        orderBy: [
          { isSystem: 'desc' },
          { usageCount: 'desc' },
          { createdAt: 'desc' }
        ]
      })

      return templates.map(template => ({
        ...template,
        sections: template.sections as any,
        features: template.features as any,
        metadata: template.metadata as any,
        layoutData: template.layoutData as any
      }))
    } catch (error) {
      console.error('Error fetching layout templates:', error)
      throw new Error('Failed to fetch layout templates')
    }
  }

  // Save layout as template
  static async saveAsTemplate(params: {
    layoutId: string
    name: string
    description?: string
    category: string
    isPublic?: boolean
    userId?: string
  }) {
    try {
      const { layoutId, name, description, category, isPublic = true, userId } = params

      // Get the layout
      const layout = await this.getLayoutById(layoutId)
      if (!layout) {
        throw new Error('Layout not found')
      }

      // Extract sections for template
      const sections = Object.values(layout.structure).map(section => ({
        type: section?.type,
        name: section?.name,
        enabled: section?.isVisible
      })).filter(Boolean)

      // Create template
      const template = await prisma.layoutTemplate.create({
        data: {
          name,
          description,
          category,
          type: layout.type,
          sections: sections as any,
          features: [], // Extract from layout if needed
          metadata: {}, // Extract from layout if needed
          tags: layout.tags,
          layoutData: layout as any,
          isPublic,
          createdBy: userId
        }
      })

      return template
    } catch (error) {
      console.error('Error saving layout as template:', error)
      throw new Error('Failed to save layout as template')
    }
  }

  // Increment layout usage count
  static async incrementUsageCount(id: string): Promise<void> {
    try {
      await prisma.layout.update({
        where: { id },
        data: {
          usageCount: {
            increment: 1
          }
        }
      })
    } catch (error) {
      console.error('Error incrementing usage count:', error)
      // Don't throw error for usage count updates
    }
  }

  // Transform Prisma layout to our Layout type
  private static transformPrismaLayout(prismaLayout: any): Layout {
    return {
      id: prismaLayout.id,
      name: prismaLayout.name,
      description: prismaLayout.description,
      type: prismaLayout.type,
      category: prismaLayout.category,
      structure: prismaLayout.structure || {},
      styling: prismaLayout.styling || {},
      responsive: prismaLayout.responsive || {},
      conditions: prismaLayout.conditions || {},
      isTemplate: prismaLayout.isTemplate,
      isSystem: prismaLayout.isSystem,
      isActive: prismaLayout.isActive,
      usageCount: prismaLayout.usageCount,
      thumbnail: prismaLayout.thumbnail,
      tags: prismaLayout.tags || [],
      createdBy: prismaLayout.createdBy,
      updatedBy: prismaLayout.updatedBy,
      createdAt: prismaLayout.createdAt,
      updatedAt: prismaLayout.updatedAt
    }
  }

  // Convert Layout to NextJSLayout
  static convertToNextJSLayout(layout: Layout): NextJSLayout {
    return {
      ...layout,
      nextjs: {
        type: 'root',
        route: 'app/layout.tsx',
        metadata: {
          title: layout.name,
          description: layout.description
        },
        imports: ['import React from "react"'],
        exports: [
          {
            name: 'default',
            type: 'default',
            isAsync: false,
            returnType: 'JSX.Element'
          }
        ],
        appRouterFeatures: ['metadata']
      }
    }
  }

  // Convert NextJSLayout to Layout
  static convertFromNextJSLayout(nextjsLayout: NextJSLayout): Layout {
    const { nextjs, ...layout } = nextjsLayout
    return layout
  }
}