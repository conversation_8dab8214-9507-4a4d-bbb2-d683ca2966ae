import { db } from "@/lib/db"
import { hash, compare } from "bcryptjs"

export async function getUserById(id: string) {
  try {
    const user = await db.user.findUnique({
      where: { id },
      include: {
        assignedRoles: {
          include: {
            role: true
          }
        }
      }
    })
    
    return user
  } catch (error) {
    console.error("Error getting user by ID:", error)
    return null
  }
}

export async function getUserByEmail(email: string) {
  try {
    const user = await db.user.findUnique({
      where: { email },
      include: {
        assignedRoles: {
          include: {
            role: true
          }
        }
      }
    })
    
    return user
  } catch (error) {
    console.error("Error getting user by email:", error)
    return null
  }
}

export async function createUser(data: {
  email: string
  password: string
  firstName?: string
  lastName?: string
  displayName?: string
}) {
  try {
    const hashedPassword = await hash(data.password, 10)
    
    const user = await db.user.create({
      data: {
        email: data.email,
        password: hashedPassword,
        firstName: data.firstName,
        lastName: data.lastName,
        displayName: data.displayName || data.email.split('@')[0],
        emailVerified: false,
      }
    })
    
    return user
  } catch (error) {
    console.error("Error creating user:", error)
    throw error
  }
}

export async function verifyPassword(userId: string, password: string) {
  try {
    const user = await db.user.findUnique({
      where: { id: userId },
      select: { password: true }
    })
    
    if (!user) return false
    
    return compare(password, user.password)
  } catch (error) {
    console.error("Error verifying password:", error)
    return false
  }
}

export async function updateUserPassword(userId: string, newPassword: string) {
  try {
    const hashedPassword = await hash(newPassword, 10)
    
    await db.user.update({
      where: { id: userId },
      data: { password: hashedPassword }
    })
    
    return true
  } catch (error) {
    console.error("Error updating user password:", error)
    return false
  }
}

export async function getUserRoles(userId: string) {
  try {
    const userRoles = await db.userRole.findMany({
      where: { 
        userId,
        isActive: true
      },
      include: {
        role: true
      }
    })
    
    // If no roles are found, check if we need to create a default customer role
    if (userRoles.length === 0) {
      console.log(`No roles found for user ${userId}, checking if we need to create a default customer role`)
      
      // Find the customer role
      const customerRole = await db.role.findFirst({
        where: { slug: "customer" }
      })
      
      if (customerRole) {
        // Create the user role association
        try {
          const newUserRole = await db.userRole.create({
            data: {
              userId: userId,
              roleId: customerRole.id,
              assignedBy: userId, // Self-assigned
              isActive: true,
            },
            include: {
              role: true
            }
          })
          
          console.log(`Created default customer role for user ${userId}`)
          return [newUserRole.role]
        } catch (createError) {
          console.error("Error creating default customer role:", createError)
        }
      }
    }
    
    return userRoles.map(ur => ur.role)
  } catch (error) {
    console.error("Error getting user roles:", error)
    return []
  }
}