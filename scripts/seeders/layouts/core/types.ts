/**
 * Layout Seeding Types
 * Shared TypeScript type definitions for layout seeders
 */

import { NextJSLayout, NextJSLayoutType, AppRouterFeature } from '@/lib/layout-builder/types/nextjs-types'

// Base seeding configuration
export interface BaseSeedingOptions {
  skipExisting?: boolean
  verbose?: boolean
  dryRun?: boolean
}

// Category-specific seeding options
export interface NextJSLayoutSeedingOptions extends BaseSeedingOptions {
  includeRoot?: boolean
  includeNested?: boolean
  includeTemplate?: boolean
  includeGroup?: boolean
}

export interface MarketingLayoutSeedingOptions extends BaseSeedingOptions {
  includeSaaS?: boolean
  includeProduct?: boolean
  includeCampaign?: boolean
}

export interface EcommerceLayoutSeedingOptions extends BaseSeedingOptions {
  includeShop?: boolean
  includeProduct?: boolean
  includeCheckout?: boolean
}

export interface ContentLayoutSeedingOptions extends BaseSeedingOptions {
  includeBlog?: boolean
  includeCMS?: boolean
  includeDocumentation?: boolean
}

export interface AdminLayoutSeedingOptions extends BaseSeedingOptions {
  includeDashboard?: boolean
  includeUser?: boolean
  includeSettings?: boolean
}

export interface SpecializedLayoutSeedingOptions extends BaseSeedingOptions {
  includeAuth?: boolean
  includeError?: boolean
  includeMaintenance?: boolean
}

// Complete seeding configuration
export interface CompleteLayoutSeedingOptions extends BaseSeedingOptions {
  categories?: {
    nextjs?: NextJSLayoutSeedingOptions
    marketing?: MarketingLayoutSeedingOptions
    ecommerce?: EcommerceLayoutSeedingOptions
    content?: ContentLayoutSeedingOptions
    admin?: AdminLayoutSeedingOptions
    specialized?: SpecializedLayoutSeedingOptions
  }
}

// Seeding results
export interface SeedingResult {
  created: number
  skipped: number
  errors: number
  duration: number
}

export interface CategorySeedingResult {
  [key: string]: SeedingResult
}

export interface CompleteSeedingResult {
  categories: CategorySeedingResult
  total: SeedingResult
}

// Layout template definition
export interface LayoutTemplate extends Omit<NextJSLayout, 'id' | 'createdAt' | 'updatedAt'> {
  // Additional template-specific properties
  priority?: number
  dependencies?: string[]
  variants?: LayoutVariant[]
}

// Layout variants for A/B testing or different use cases
export interface LayoutVariant {
  name: string
  description: string
  modifications: Partial<NextJSLayout>
  conditions?: Record<string, any>
}

// Layout assignment configuration
export interface LayoutAssignmentConfig {
  targetType: 'global' | 'page' | 'post-type' | 'specific' | 'conditional'
  targetId?: string
  targetSlug?: string
  priority: number
  conditions?: Record<string, any>
  isActive: boolean
}

// Seeding context for tracking and logging
export interface SeedingContext {
  category: string
  subcategory?: string
  startTime: Date
  totalTemplates: number
  processedTemplates: number
  errors: string[]
  warnings: string[]
}

// Layout validation result
export interface LayoutValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  suggestions: string[]
}

// Database operation result
export interface DatabaseOperationResult {
  success: boolean
  data?: any
  error?: string
  duration: number
}

// Seeding statistics
export interface SeedingStatistics {
  totalLayouts: number
  layoutsByCategory: Record<string, number>
  layoutsByType: Record<NextJSLayoutType, number>
  averageCreationTime: number
  totalDuration: number
  errorRate: number
}

// Layout metadata for tracking
export interface LayoutMetadata {
  templateId: string
  category: string
  subcategory: string
  version: string
  createdBy: string
  tags: string[]
  dependencies: string[]
}

// Export utility types
export type LayoutSeedingFunction<T extends BaseSeedingOptions = BaseSeedingOptions> = (
  options?: T
) => Promise<SeedingResult>

export type CategorySeedingFunction<T extends BaseSeedingOptions = BaseSeedingOptions> = (
  options?: T
) => Promise<CategorySeedingResult>

// Default configurations
export const DEFAULT_SEEDING_OPTIONS: BaseSeedingOptions = {
  skipExisting: true,
  verbose: false,
  dryRun: false
}

export const DEFAULT_NEXTJS_OPTIONS: NextJSLayoutSeedingOptions = {
  ...DEFAULT_SEEDING_OPTIONS,
  includeRoot: true,
  includeNested: true,
  includeTemplate: true,
  includeGroup: true
}

export const DEFAULT_MARKETING_OPTIONS: MarketingLayoutSeedingOptions = {
  ...DEFAULT_SEEDING_OPTIONS,
  includeSaaS: true,
  includeProduct: true,
  includeCampaign: true
}

export const DEFAULT_ECOMMERCE_OPTIONS: EcommerceLayoutSeedingOptions = {
  ...DEFAULT_SEEDING_OPTIONS,
  includeShop: true,
  includeProduct: true,
  includeCheckout: true
}

export const DEFAULT_CONTENT_OPTIONS: ContentLayoutSeedingOptions = {
  ...DEFAULT_SEEDING_OPTIONS,
  includeBlog: true,
  includeCMS: true,
  includeDocumentation: true
}

export const DEFAULT_ADMIN_OPTIONS: AdminLayoutSeedingOptions = {
  ...DEFAULT_SEEDING_OPTIONS,
  includeDashboard: true,
  includeUser: true,
  includeSettings: true
}

export const DEFAULT_SPECIALIZED_OPTIONS: SpecializedLayoutSeedingOptions = {
  ...DEFAULT_SEEDING_OPTIONS,
  includeAuth: true,
  includeError: true,
  includeMaintenance: true
}

export const DEFAULT_COMPLETE_OPTIONS: CompleteLayoutSeedingOptions = {
  ...DEFAULT_SEEDING_OPTIONS,
  categories: {
    nextjs: DEFAULT_NEXTJS_OPTIONS,
    marketing: DEFAULT_MARKETING_OPTIONS,
    ecommerce: DEFAULT_ECOMMERCE_OPTIONS,
    content: DEFAULT_CONTENT_OPTIONS,
    admin: DEFAULT_ADMIN_OPTIONS,
    specialized: DEFAULT_SPECIALIZED_OPTIONS
  }
}
