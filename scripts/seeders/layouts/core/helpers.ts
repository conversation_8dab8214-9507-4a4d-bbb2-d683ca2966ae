/**
 * Layout Seeding Helpers
 * Shared utility functions for layout seeders
 */

import { PrismaClient } from '@prisma/client'
import { 
  LayoutTemplate, 
  SeedingResult, 
  SeedingContext, 
  LayoutValidationResult,
  DatabaseOperationResult,
  BaseSeedingOptions,
  LayoutAssignmentConfig
} from './types'

const prisma = new PrismaClient()

// Helper function to create default responsive settings
export function createDefaultResponsive() {
  const defaultSpacing = { top: 0, right: 0, bottom: 0, left: 0 }
  const defaultTypography = {
    fontFamily: 'system-ui',
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 1.5,
    letterSpacing: 0,
    textAlign: 'left' as const,
    textTransform: 'none' as const
  }

  return {
    mobile: { 
      display: 'block' as const, 
      width: '100%', 
      height: 'auto', 
      spacing: defaultSpacing,
      typography: { ...defaultTypography, fontSize: 14 }
    },
    tablet: { 
      display: 'block' as const, 
      width: '100%', 
      height: 'auto', 
      spacing: defaultSpacing,
      typography: defaultTypography
    },
    desktop: { 
      display: 'block' as const, 
      width: '100%', 
      height: 'auto', 
      spacing: defaultSpacing,
      typography: defaultTypography
    },
    large: { 
      display: 'block' as const, 
      width: '100%', 
      height: 'auto', 
      spacing: defaultSpacing,
      typography: defaultTypography
    }
  }
}

// Helper function to create default styling
export function createDefaultStyling() {
  const defaultSpacing = { top: 0, right: 0, bottom: 0, left: 0 }
  
  return {
    theme: 'default',
    colorScheme: 'light' as const,
    typography: {
      fontFamily: 'system-ui',
      fontSize: 16,
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: 0,
      textAlign: 'left' as const,
      textTransform: 'none' as const
    },
    spacing: defaultSpacing,
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#8b5cf6',
      text: '#1f2937',
      background: '#ffffff',
      border: '#e5e7eb'
    }
  }
}

// Helper function to create default block configuration
export function createDefaultBlockConfiguration(overrides: any = {}) {
  return {
    size: 'medium' as const,
    alignment: 'left' as const,
    spacing: { top: 0, right: 0, bottom: 0, left: 0 },
    animation: {
      type: 'none' as const,
      duration: 0,
      delay: 0,
      easing: 'ease' as const
    },
    ...overrides
  }
}

// Helper function to create default block styling
export function createDefaultBlockStyling(overrides: any = {}) {
  return {
    background: { type: 'color', color: 'transparent' },
    border: { width: 0, style: 'none', color: 'transparent', radius: 0 },
    spacing: { top: 0, right: 0, bottom: 0, left: 0 },
    typography: {
      fontFamily: 'system-ui',
      fontSize: 16,
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: 0,
      textAlign: 'left' as const,
      textTransform: 'none' as const
    },
    colors: {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#8b5cf6',
      text: '#1f2937',
      background: '#ffffff',
      border: '#e5e7eb'
    },
    shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' },
    ...overrides
  }
}

// Generate unique layout ID
export function generateLayoutId(category: string, type: string): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 11)
  return `${category}-${type}-${timestamp}-${random}`
}

// Validate layout template
export function validateLayoutTemplate(template: LayoutTemplate): LayoutValidationResult {
  const errors: string[] = []
  const warnings: string[] = []
  const suggestions: string[] = []

  // Required fields validation
  if (!template.name) errors.push('Layout name is required')
  if (!template.description) warnings.push('Layout description is recommended')
  if (!template.type) errors.push('Layout type is required')
  if (!template.category) errors.push('Layout category is required')
  if (!template.structure) errors.push('Layout structure is required')

  // NextJS configuration validation
  if (!template.nextjs) {
    errors.push('NextJS configuration is required')
  } else {
    if (!template.nextjs.route) errors.push('NextJS route is required')
    if (!template.nextjs.type) errors.push('NextJS type is required')
    if (!template.nextjs.exports || template.nextjs.exports.length === 0) {
      warnings.push('NextJS exports are recommended')
    }
  }

  // Structure validation
  if (template.structure) {
    if (!template.structure.main) {
      errors.push('Main section is required in layout structure')
    }
  }

  // Suggestions
  if (!template.tags || template.tags.length === 0) {
    suggestions.push('Add tags for better categorization')
  }
  if (!template.responsive) {
    suggestions.push('Add responsive configuration for better mobile support')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    suggestions
  }
}

// Check if layout exists
export async function layoutExists(name: string, type: string): Promise<boolean> {
  try {
    const existing = await prisma.layout.findFirst({
      where: { name, type }
    })
    return !!existing
  } catch (error) {
    console.error('Error checking layout existence:', error)
    return false
  }
}

// Create layout in database
export async function createLayout(template: LayoutTemplate): Promise<DatabaseOperationResult> {
  const startTime = Date.now()
  
  try {
    const layoutId = generateLayoutId(template.category, template.type)
    
    const layout = await prisma.layout.create({
      data: {
        id: layoutId,
        name: template.name,
        description: template.description,
        type: template.type,
        category: template.category,
        structure: template.structure as any,
        styling: template.styling as any,
        responsive: template.responsive as any,
        conditions: template.conditions as any,
        isTemplate: template.isTemplate,
        isSystem: template.isSystem,
        isActive: template.isActive,
        usageCount: template.usageCount,
        tags: template.tags
      }
    })

    return {
      success: true,
      data: layout,
      duration: Date.now() - startTime
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      duration: Date.now() - startTime
    }
  }
}

// Create layout assignment
export async function createLayoutAssignment(
  layoutId: string, 
  config: LayoutAssignmentConfig
): Promise<DatabaseOperationResult> {
  const startTime = Date.now()
  
  try {
    const assignmentId = `assignment-${layoutId}`
    
    const assignment = await prisma.layoutAssignment.create({
      data: {
        id: assignmentId,
        layoutId: layoutId,
        targetType: config.targetType,
        targetId: config.targetId,
        targetSlug: config.targetSlug,
        priority: config.priority,
        conditions: config.conditions as any,
        isActive: config.isActive
      }
    })

    return {
      success: true,
      data: assignment,
      duration: Date.now() - startTime
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      duration: Date.now() - startTime
    }
  }
}

// Logging utilities
export function logProgress(context: SeedingContext, message: string, verbose: boolean = false) {
  if (verbose) {
    const progress = context.totalTemplates > 0 
      ? Math.round((context.processedTemplates / context.totalTemplates) * 100)
      : 0
    console.log(`[${context.category}] ${progress}% - ${message}`)
  }
}

export function logSuccess(message: string, verbose: boolean = false) {
  if (verbose) {
    console.log(`✅ ${message}`)
  }
}

export function logWarning(message: string, verbose: boolean = false) {
  if (verbose) {
    console.log(`⚠️  ${message}`)
  }
}

export function logError(message: string, verbose: boolean = false) {
  if (verbose) {
    console.log(`❌ ${message}`)
  }
}

export function logSkipped(message: string, verbose: boolean = false) {
  if (verbose) {
    console.log(`⏭️  ${message}`)
  }
}

// Create seeding summary
export function createSeedingSummary(
  category: string,
  result: SeedingResult,
  templates: LayoutTemplate[],
  verbose: boolean = false
): string {
  if (!verbose) return ''

  const duration = (result.duration / 1000).toFixed(2)
  const total = result.created + result.skipped + result.errors
  
  return `
🎨 ${category} Layout Seeding Complete!
📊 Summary:
- Created: ${result.created} layouts
- Skipped: ${result.skipped} layouts
- Errors: ${result.errors} layouts
- Total Templates: ${templates.length}
- Duration: ${duration}s

🚀 Created Layouts:
${templates.slice(0, result.created).map(t => `- ${t.name} (${t.nextjs.type})`).join('\n')}
  `
}

// Cleanup function
export async function cleanup() {
  await prisma.$disconnect()
}

// Export Prisma client for direct use
export { prisma }
