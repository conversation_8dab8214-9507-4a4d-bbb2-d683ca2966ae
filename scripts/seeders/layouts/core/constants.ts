/**
 * Layout Seeding Constants
 * Shared constants and configurations for layout seeders
 */

import { NextJSLayoutType, AppRouterFeature } from '@/lib/layout-builder/types/nextjs-types'

// Layout categories
export const LAYOUT_CATEGORIES = {
  NEXTJS: 'nextjs',
  MARKETING: 'marketing', 
  ECOMMERCE: 'ecommerce',
  CONTENT: 'content',
  ADMIN: 'admin',
  SPECIALIZED: 'specialized'
} as const

// NextJS layout types
export const NEXTJS_LAYOUT_TYPES: Record<string, NextJSLayoutType> = {
  ROOT: 'root',
  NESTED: 'nested',
  TEMPLATE: 'template',
  GROUP: 'group'
} as const

// App Router features
export const APP_ROUTER_FEATURES: Record<string, AppRouterFeature> = {
  METADATA: 'metadata',
  LOADING: 'loading',
  ERROR: 'error',
  NOT_FOUND: 'not-found',
  TEMPLATE: 'template',
  PARALLEL_ROUTES: 'parallel-routes'
} as const

// Layout assignment priorities
export const ASSIGNMENT_PRIORITIES = {
  SYSTEM: 100,
  GLOBAL: 90,
  CATEGORY: 80,
  SPECIFIC: 70,
  CONDITIONAL: 60,
  DEFAULT: 50
} as const

// Common layout routes
export const COMMON_ROUTES = {
  ROOT: 'app/layout.tsx',
  DASHBOARD: 'app/(dashboard)/layout.tsx',
  MARKETING: 'app/(marketing)/layout.tsx',
  SHOP: 'app/(shop)/layout.tsx',
  BLOG: 'app/(blog)/layout.tsx',
  AUTH: 'app/(auth)/layout.tsx',
  ADMIN: 'app/(admin)/layout.tsx'
} as const

// Color schemes
export const COLOR_SCHEMES = {
  LIGHT: {
    primary: '#3b82f6',
    secondary: '#64748b',
    accent: '#8b5cf6',
    text: '#1f2937',
    background: '#ffffff',
    border: '#e5e7eb'
  },
  DARK: {
    primary: '#60a5fa',
    secondary: '#94a3b8',
    accent: '#a78bfa',
    text: '#f9fafb',
    background: '#111827',
    border: '#374151'
  },
  BRAND: {
    primary: '#3b82f6',
    secondary: '#1e40af',
    accent: '#fbbf24',
    text: '#1f2937',
    background: '#ffffff',
    border: '#e5e7eb'
  }
} as const

// Typography presets
export const TYPOGRAPHY_PRESETS = {
  DEFAULT: {
    fontFamily: 'system-ui',
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 1.5,
    letterSpacing: 0,
    textAlign: 'left' as const,
    textTransform: 'none' as const
  },
  HEADING: {
    fontFamily: 'system-ui',
    fontSize: 32,
    fontWeight: 700,
    lineHeight: 1.2,
    letterSpacing: -0.5,
    textAlign: 'left' as const,
    textTransform: 'none' as const
  },
  SUBHEADING: {
    fontFamily: 'system-ui',
    fontSize: 24,
    fontWeight: 600,
    lineHeight: 1.3,
    letterSpacing: -0.25,
    textAlign: 'left' as const,
    textTransform: 'none' as const
  },
  BODY: {
    fontFamily: 'system-ui',
    fontSize: 16,
    fontWeight: 400,
    lineHeight: 1.6,
    letterSpacing: 0,
    textAlign: 'left' as const,
    textTransform: 'none' as const
  },
  CAPTION: {
    fontFamily: 'system-ui',
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 1.4,
    letterSpacing: 0,
    textAlign: 'left' as const,
    textTransform: 'none' as const
  }
} as const

// Spacing presets
export const SPACING_PRESETS = {
  NONE: { top: 0, right: 0, bottom: 0, left: 0 },
  XS: { top: 4, right: 4, bottom: 4, left: 4 },
  SM: { top: 8, right: 8, bottom: 8, left: 8 },
  MD: { top: 16, right: 16, bottom: 16, left: 16 },
  LG: { top: 24, right: 24, bottom: 24, left: 24 },
  XL: { top: 32, right: 32, bottom: 32, left: 32 },
  XXL: { top: 48, right: 48, bottom: 48, left: 48 }
} as const

// Container presets
export const CONTAINER_PRESETS = {
  FULL: {
    maxWidth: '100%',
    padding: SPACING_PRESETS.NONE,
    margin: SPACING_PRESETS.NONE,
    centered: false
  },
  CONTAINED: {
    maxWidth: 1200,
    padding: SPACING_PRESETS.MD,
    margin: SPACING_PRESETS.NONE,
    centered: true
  },
  NARROW: {
    maxWidth: 800,
    padding: SPACING_PRESETS.MD,
    margin: SPACING_PRESETS.NONE,
    centered: true
  },
  WIDE: {
    maxWidth: 1400,
    padding: SPACING_PRESETS.LG,
    margin: SPACING_PRESETS.NONE,
    centered: true
  }
} as const

// Shadow presets
export const SHADOW_PRESETS = {
  NONE: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' },
  SM: { type: 'box', x: 0, y: 1, blur: 2, spread: 0, color: 'rgba(0,0,0,0.05)' },
  MD: { type: 'box', x: 0, y: 4, blur: 6, spread: -1, color: 'rgba(0,0,0,0.1)' },
  LG: { type: 'box', x: 0, y: 10, blur: 15, spread: -3, color: 'rgba(0,0,0,0.1)' },
  XL: { type: 'box', x: 0, y: 20, blur: 25, spread: -5, color: 'rgba(0,0,0,0.1)' }
} as const

// Border presets
export const BORDER_PRESETS = {
  NONE: { width: 0, style: 'none', color: 'transparent', radius: 0 },
  THIN: { width: 1, style: 'solid', color: '#e5e7eb', radius: 0 },
  MEDIUM: { width: 2, style: 'solid', color: '#d1d5db', radius: 0 },
  THICK: { width: 4, style: 'solid', color: '#9ca3af', radius: 0 },
  ROUNDED: { width: 1, style: 'solid', color: '#e5e7eb', radius: 8 },
  PILL: { width: 1, style: 'solid', color: '#e5e7eb', radius: 9999 }
} as const

// Animation presets
export const ANIMATION_PRESETS = {
  NONE: { type: 'none', duration: 0, delay: 0, easing: 'ease' },
  FADE_IN: { type: 'fadeIn', duration: 300, delay: 0, easing: 'ease-out' },
  SLIDE_UP: { type: 'slideUp', duration: 400, delay: 0, easing: 'ease-out' },
  SLIDE_DOWN: { type: 'slideDown', duration: 400, delay: 0, easing: 'ease-out' },
  SCALE_IN: { type: 'scaleIn', duration: 200, delay: 0, easing: 'ease-out' },
  BOUNCE: { type: 'bounce', duration: 600, delay: 0, easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)' }
} as const

// Responsive breakpoints
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1280,
  LARGE: 1536
} as const

// Common metadata templates
export const METADATA_TEMPLATES = {
  DEFAULT: {
    robots: { index: true, follow: true },
    authors: [{ name: 'Development Team' }]
  },
  MARKETING: {
    robots: { index: true, follow: true },
    authors: [{ name: 'Marketing Team' }],
    openGraph: {
      type: 'website',
      locale: 'en_US'
    },
    twitter: {
      card: 'summary_large_image'
    }
  },
  ADMIN: {
    robots: { index: false, follow: false },
    authors: [{ name: 'Admin Team' }]
  },
  BLOG: {
    robots: { index: true, follow: true },
    authors: [{ name: 'Content Team' }],
    openGraph: {
      type: 'article',
      locale: 'en_US'
    },
    twitter: {
      card: 'summary_large_image'
    }
  }
} as const

// Common import statements
export const COMMON_IMPORTS = {
  REACT: 'import React from "react"',
  NEXT_FONT: 'import { Inter } from "next/font/google"',
  GLOBALS_CSS: 'import "./globals.css"',
  COMPONENTS: {
    HEADER: 'import { Header } from "@/components/layout/header"',
    FOOTER: 'import { Footer } from "@/components/layout/footer"',
    SIDEBAR: 'import { Sidebar } from "@/components/layout/sidebar"',
    NAVIGATION: 'import { Navigation } from "@/components/layout/navigation"'
  }
} as const

// Common export templates
export const EXPORT_TEMPLATES = {
  DEFAULT_LAYOUT: {
    name: 'default',
    type: 'default',
    isAsync: false,
    parameters: ['children: React.ReactNode'],
    returnType: 'JSX.Element'
  },
  ASYNC_LAYOUT: {
    name: 'default',
    type: 'default',
    isAsync: true,
    parameters: ['children: React.ReactNode'],
    returnType: 'Promise<JSX.Element>'
  }
} as const

// Seeding performance limits
export const PERFORMANCE_LIMITS = {
  MAX_BATCH_SIZE: 50,
  MAX_CONCURRENT_OPERATIONS: 10,
  TIMEOUT_MS: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY_MS: 1000
} as const

// Validation rules
export const VALIDATION_RULES = {
  MIN_NAME_LENGTH: 3,
  MAX_NAME_LENGTH: 100,
  MIN_DESCRIPTION_LENGTH: 10,
  MAX_DESCRIPTION_LENGTH: 500,
  MAX_TAGS: 10,
  REQUIRED_FIELDS: ['name', 'type', 'category', 'structure', 'nextjs']
} as const
