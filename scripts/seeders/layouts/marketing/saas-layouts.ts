/**
 * SaaS Marketing Layouts Seeder
 * Creates SaaS-focused marketing and landing page layouts
 */

import { 
  LayoutTemplate, 
  MarketingLayoutSeedingOptions, 
  SeedingResult,
  DEFAULT_MARKETING_OPTIONS 
} from '../core/types'
import { 
  createDefaultResponsive, 
  createDefaultStyling, 
  createDefaultBlockConfiguration, 
  createDefaultBlockStyling,
  validateLayoutTemplate,
  layoutExists,
  createLayout,
  createLayoutAssignment,

  logSuccess,
  logSkipped,
  logError,
  createSeedingSummary,
  cleanup
} from '../core/helpers'
import { 
  NEXTJS_LAYOUT_TYPES, 
  APP_ROUTER_FEATURES, 
  COMMON_ROUTES, 
  COLOR_SCHEMES, 
  SPACING_PRESETS,
  CONTAINER_PRESETS,
  SHADOW_PRESETS,
  METADATA_TEMPLATES,
  COMMON_IMPORTS,

  ASSIGNMENT_PRIORITIES
} from '../core/constants'

// SaaS layout templates
const saasLayoutTemplates: LayoutTemplate[] = [
  {
    name: 'SaaS Landing Page Layout',
    description: 'Modern SaaS landing page with hero section, features, pricing, and conversion optimization',
    type: 'page',
    category: 'landing',
    structure: {
      header: {
        id: 'header-saas',
        type: 'header',
        name: 'SaaS Header',
        position: 1,
        blocks: [
          {
            id: 'saas-logo',
            type: 'logo',
            name: 'SaaS Logo',
            position: 1,
            configuration: createDefaultBlockConfiguration({ 
              size: 'large', 
              alignment: 'left' 
            }),
            content: { text: 'SaaS App', image: '/saas-logo.svg' },
            styling: createDefaultBlockStyling({
              spacing: { top: 20, right: 24, bottom: 20, left: 24 }
            }),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          },
          {
            id: 'saas-nav',
            type: 'navigation',
            name: 'SaaS Navigation',
            position: 2,
            configuration: createDefaultBlockConfiguration({
              alignment: 'center',
              layout: 'horizontal'
            }),
            content: { menu: 'saas-menu' },
            styling: createDefaultBlockStyling(),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          },
          {
            id: 'saas-cta',
            type: 'custom',
            name: 'Header CTA',
            position: 3,
            configuration: createDefaultBlockConfiguration({
              size: 'medium',
              alignment: 'right'
            }),
            content: { 
              text: 'Get Started Free',
              url: '/signup',
              style: 'primary'
            },
            styling: createDefaultBlockStyling({
              colors: {
                primary: '#3b82f6',
                secondary: '#ffffff',
                accent: '#1d4ed8',
                text: '#ffffff',
                background: '#3b82f6',
                border: '#3b82f6'
              }
            }),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          }
        ],
        configuration: {
          layout: 'flex',
          alignment: 'center',
          spacing: SPACING_PRESETS.NONE,
          background: { type: 'color', color: '#ffffff' },
          container: CONTAINER_PRESETS.CONTAINED
        },
        styling: {
          background: { type: 'color', color: '#ffffff' },
          border: { width: 1, style: 'solid', color: COLOR_SCHEMES.LIGHT.border, radius: 0 },
          spacing: SPACING_PRESETS.NONE,
          shadow: SHADOW_PRESETS.SM
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      },
      main: {
        id: 'main-saas',
        type: 'main',
        name: 'SaaS Main Content',
        position: 2,
        blocks: [
          {
            id: 'hero-section',
            type: 'custom',
            name: 'Hero Section',
            position: 1,
            configuration: createDefaultBlockConfiguration({
              size: 'large',
              alignment: 'center'
            }),
            content: {
              headline: 'Transform Your Business with Our SaaS Solution',
              subheadline: 'Streamline operations, boost productivity, and scale your business with our powerful platform.',
              ctaText: 'Start Free Trial',
              ctaUrl: '/signup',
              image: '/hero-saas.jpg'
            },
            styling: createDefaultBlockStyling({
              spacing: { top: 80, right: 24, bottom: 80, left: 24 },
              background: { type: 'gradient', color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' },
              typography: {
                fontFamily: 'system-ui',
                fontSize: 48,
                fontWeight: 700,
                lineHeight: 1.2,
                letterSpacing: -1,
                textAlign: 'center' as const,
                textTransform: 'none' as const
              },
              colors: {
                primary: '#ffffff',
                secondary: '#f3f4f6',
                accent: '#3b82f6',
                text: '#ffffff',
                background: 'transparent',
                border: 'transparent'
              }
            }),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          },
          {
            id: 'features-section',
            type: 'custom',
            name: 'Features Section',
            position: 2,
            configuration: createDefaultBlockConfiguration({
              size: 'large',
              alignment: 'center',
              layout: 'grid'
            }),
            content: {
              title: 'Powerful Features for Modern Businesses',
              subtitle: 'Everything you need to succeed in one platform',
              features: [
                {
                  title: 'Advanced Analytics',
                  description: 'Get deep insights into your business performance',
                  icon: 'chart-bar'
                },
                {
                  title: 'Team Collaboration',
                  description: 'Work together seamlessly with your team',
                  icon: 'users'
                },
                {
                  title: 'Secure & Reliable',
                  description: 'Enterprise-grade security and 99.9% uptime',
                  icon: 'shield'
                }
              ]
            },
            styling: createDefaultBlockStyling({
              spacing: { top: 80, right: 24, bottom: 80, left: 24 },
              background: { type: 'color', color: '#f9fafb' }
            }),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          }
        ],
        configuration: {
          layout: 'block',
          alignment: 'left',
          spacing: SPACING_PRESETS.NONE,
          background: { type: 'color', color: 'transparent' },
          container: CONTAINER_PRESETS.FULL
        },
        styling: {
          background: { type: 'color', color: 'transparent' },
          border: { width: 0, style: 'none', color: 'transparent', radius: 0 },
          spacing: SPACING_PRESETS.NONE,
          shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      },
      footer: {
        id: 'footer-saas',
        type: 'footer',
        name: 'SaaS Footer',
        position: 3,
        blocks: [
          {
            id: 'footer-cta',
            type: 'custom',
            name: 'Footer CTA',
            position: 1,
            configuration: createDefaultBlockConfiguration({
              size: 'large',
              alignment: 'center'
            }),
            content: {
              title: 'Ready to Get Started?',
              subtitle: 'Join thousands of businesses already using our platform',
              ctaText: 'Start Your Free Trial',
              ctaUrl: '/signup'
            },
            styling: createDefaultBlockStyling({
              spacing: { top: 60, right: 24, bottom: 60, left: 24 },
              background: { type: 'color', color: '#1f2937' },
              colors: {
                primary: '#ffffff',
                secondary: '#d1d5db',
                accent: '#3b82f6',
                text: '#ffffff',
                background: '#1f2937',
                border: '#374151'
              }
            }),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          }
        ],
        configuration: {
          layout: 'block',
          alignment: 'center',
          spacing: SPACING_PRESETS.NONE,
          background: { type: 'color', color: '#1f2937' },
          container: CONTAINER_PRESETS.FULL
        },
        styling: {
          background: { type: 'color', color: '#1f2937' },
          border: { width: 0, style: 'none', color: 'transparent', radius: 0 },
          spacing: SPACING_PRESETS.NONE,
          shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      }
    },
    styling: createDefaultStyling(),
    responsive: createDefaultResponsive(),
    conditions: {},
    isTemplate: true,
    isSystem: false,
    isActive: true,
    usageCount: 0,
    tags: ['saas', 'landing', 'marketing', 'conversion', 'hero', 'features'],
    nextjs: {
      type: NEXTJS_LAYOUT_TYPES.NESTED,
      route: COMMON_ROUTES.MARKETING,
      metadata: {
        title: { default: 'SaaS Landing Page', template: '%s | SaaS App' },
        description: 'Transform your business with our powerful SaaS solution. Start your free trial today.',
        keywords: ['saas', 'business', 'productivity', 'software', 'automation'],
        authors: [...METADATA_TEMPLATES.MARKETING.authors],
        openGraph: {
          type: 'website',
          locale: 'en_US',
          siteName: 'SaaS App',
          title: 'Transform Your Business with Our SaaS Solution',
          description: 'Streamline operations, boost productivity, and scale your business with our powerful platform.',
          images: [
            {
              url: '/og-saas-landing.jpg',
              width: 1200,
              height: 630,
              alt: 'SaaS Landing Page'
            }
          ]
        },
        twitter: {
          card: 'summary_large_image',
          site: '@saasapp',
          creator: '@saasapp'
        },
        robots: METADATA_TEMPLATES.MARKETING.robots
      },
      imports: [
        COMMON_IMPORTS.REACT,
        'import { LandingHeader } from "@/components/marketing/landing-header"',
        'import { LandingFooter } from "@/components/marketing/landing-footer"',
        'import { HeroSection } from "@/components/marketing/hero-section"',
        'import { FeaturesSection } from "@/components/marketing/features-section"'
      ],
      exports: [{
        name: 'default',
        type: 'default',
        isAsync: false,
        parameters: ['children: React.ReactNode'],
        returnType: 'JSX.Element'
      }],
      appRouterFeatures: [
        APP_ROUTER_FEATURES.METADATA, 
        APP_ROUTER_FEATURES.LOADING
      ]
    },
    priority: ASSIGNMENT_PRIORITIES.CATEGORY
  }
]

export async function seedSaaSLayouts(options: MarketingLayoutSeedingOptions = {}) {
  const config = { ...DEFAULT_MARKETING_OPTIONS, ...options }
  
  if (config.verbose) {
    console.log('🎯 Seeding SaaS Marketing Layouts...')
  }

  const startTime = Date.now()
  let createdCount = 0
  let skippedCount = 0
  let errorCount = 0

  try {
    for (const template of saasLayoutTemplates) {
      // Validate template
      const validation = validateLayoutTemplate(template)
      if (!validation.isValid) {
        logError(`Invalid template ${template.name}: ${validation.errors.join(', ')}`, config.verbose)
        errorCount++
        continue
      }

      // Check if layout exists
      if (config.skipExisting && await layoutExists(template.name, template.type)) {
        logSkipped(`Existing layout: ${template.name}`, config.verbose)
        skippedCount++
        continue
      }

      // Create layout
      const layoutResult = await createLayout(template)
      if (!layoutResult.success) {
        logError(`Failed to create layout ${template.name}: ${layoutResult.error}`, config.verbose)
        errorCount++
        continue
      }

      // Create layout assignment
      const assignmentResult = await createLayoutAssignment(layoutResult.data.id, {
        targetType: 'conditional',
        priority: template.priority || ASSIGNMENT_PRIORITIES.CATEGORY,
        conditions: { path: '/landing/*', type: 'saas' },
        isActive: true
      })

      if (!assignmentResult.success) {
        logError(`Failed to create assignment for ${template.name}: ${assignmentResult.error}`, config.verbose)
      }

      logSuccess(`Created SaaS layout: ${template.name}`, config.verbose)
      createdCount++
    }

    const result: SeedingResult = {
      created: createdCount,
      skipped: skippedCount,
      errors: errorCount,
      duration: Date.now() - startTime
    }

    if (config.verbose) {
      console.log(createSeedingSummary('SaaS Marketing Layouts', result, saasLayoutTemplates, true))
    }

    return result

  } catch (error) {
    logError(`Error seeding SaaS layouts: ${error}`, config.verbose)
    throw error
  }
}

// Export for use in other seeders
export { saasLayoutTemplates }

// Run directly if called as main module
if (require.main === module) {
  seedSaaSLayouts({ verbose: true })
    .then(() => {
      console.log('✅ SaaS marketing layouts seeding completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ SaaS marketing layouts seeding failed:', error)
      process.exit(1)
    })
    .finally(() => {
      cleanup()
    })
}
