/**
 * NextJS Root Layouts Seeder
 * Creates root application layouts with global navigation and structure
 */

import { 
  LayoutTemplate, 
  NextJSLayoutSeedingOptions, 
  SeedingResult,
  DEFAULT_NEXTJS_OPTIONS 
} from '../core/types'
import { 
  createDefaultResponsive, 
  createDefaultStyling, 
  createDefaultBlockConfiguration, 
  createDefaultBlockStyling,
  validateLayoutTemplate,
  layoutExists,
  createLayout,
  createLayoutAssignment,
  logProgress,
  logSuccess,
  logSkipped,
  logError,
  createSeedingSummary,
  cleanup
} from '../core/helpers'
import { 
  NEXTJS_LAYOUT_TYPES, 
  APP_ROUTER_FEATURES, 
  COMMON_ROUTES, 
  COLOR_SCHEMES, 
  SPACING_PRESETS,
  CONTAINER_PRESETS,
  SHADOW_PRESETS,
  METADATA_TEMPLATES,
  COMMON_IMPORTS,
  EXPORT_TEMPLATES,
  ASSIGNMENT_PRIORITIES
} from '../core/constants'

// Root layout templates
const rootLayoutTemplates: LayoutTemplate[] = [
  {
    name: 'Root Application Layout',
    description: 'Main application layout with global navigation, header, and footer',
    type: 'site',
    category: 'corporate',
    structure: {
      header: {
        id: 'header-root',
        type: 'header',
        name: 'Global Header',
        position: 1,
        blocks: [
          {
            id: 'logo-block',
            type: 'logo',
            name: 'Application Logo',
            position: 1,
            configuration: createDefaultBlockConfiguration({ 
              size: 'large', 
              alignment: 'left' 
            }),
            content: { text: 'My App', image: '/logo.svg' },
            styling: createDefaultBlockStyling({
              spacing: SPACING_PRESETS.MD
            }),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          },
          {
            id: 'nav-block',
            type: 'navigation',
            name: 'Main Navigation',
            position: 2,
            configuration: createDefaultBlockConfiguration({
              alignment: 'center',
              layout: 'horizontal',
              background: { type: 'color', color: 'transparent' },
              container: CONTAINER_PRESETS.CONTAINED
            }),
            content: { menu: 'main-menu' },
            styling: createDefaultBlockStyling(),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          }
        ],
        configuration: {
          layout: 'flex',
          alignment: 'center',
          spacing: SPACING_PRESETS.NONE,
          background: { type: 'color', color: COLOR_SCHEMES.LIGHT.background },
          container: CONTAINER_PRESETS.CONTAINED
        },
        styling: {
          background: { type: 'color', color: COLOR_SCHEMES.LIGHT.background },
          border: { width: 1, style: 'solid', color: COLOR_SCHEMES.LIGHT.border, radius: 0 },
          spacing: SPACING_PRESETS.NONE,
          shadow: SHADOW_PRESETS.SM
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      },
      main: {
        id: 'main-root',
        type: 'main',
        name: 'Main Content Area',
        position: 2,
        blocks: [],
        configuration: {
          layout: 'block',
          alignment: 'left',
          spacing: SPACING_PRESETS.NONE,
          background: { type: 'color', color: 'transparent' },
          container: {
            maxWidth: 1200,
            padding: { top: 32, right: 16, bottom: 32, left: 16 },
            margin: SPACING_PRESETS.NONE,
            centered: true
          }
        },
        styling: {
          background: { type: 'color', color: 'transparent' },
          border: { width: 0, style: 'none', color: 'transparent', radius: 0 },
          spacing: SPACING_PRESETS.NONE,
          shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      },
      footer: {
        id: 'footer-root',
        type: 'footer',
        name: 'Global Footer',
        position: 3,
        blocks: [
          {
            id: 'footer-content',
            type: 'content',
            name: 'Footer Content',
            position: 1,
            configuration: createDefaultBlockConfiguration({ 
              size: 'medium', 
              alignment: 'center' 
            }),
            content: { 
              text: '© 2024 My App. All rights reserved.',
              html: '<p class="text-center text-gray-600">© 2024 My App. All rights reserved.</p>'
            },
            styling: createDefaultBlockStyling({
              spacing: SPACING_PRESETS.MD,
              typography: {
                fontFamily: 'system-ui',
                fontSize: 14,
                fontWeight: 400,
                lineHeight: 1.5,
                letterSpacing: 0,
                textAlign: 'center' as const,
                textTransform: 'none' as const
              },
              colors: {
                primary: '#6b7280',
                secondary: '#9ca3af',
                accent: '#3b82f6',
                text: '#6b7280',
                background: 'transparent',
                border: 'transparent'
              }
            }),
            responsive: createDefaultResponsive(),
            conditions: {},
            isVisible: true
          }
        ],
        configuration: {
          layout: 'flex',
          alignment: 'center',
          spacing: SPACING_PRESETS.NONE,
          background: { type: 'color', color: '#f9fafb' },
          container: {
            maxWidth: 1200,
            padding: { top: 32, right: 16, bottom: 32, left: 16 },
            margin: SPACING_PRESETS.NONE,
            centered: true
          }
        },
        styling: {
          background: { type: 'color', color: '#f9fafb' },
          border: { width: 1, style: 'solid', color: COLOR_SCHEMES.LIGHT.border, radius: 0 },
          spacing: SPACING_PRESETS.NONE,
          shadow: { type: 'none', x: 0, y: 0, blur: 0, spread: 0, color: 'transparent' }
        },
        responsive: createDefaultResponsive(),
        isVisible: true
      }
    },
    styling: createDefaultStyling(),
    responsive: createDefaultResponsive(),
    conditions: {},
    isTemplate: true,
    isSystem: false,
    isActive: true,
    usageCount: 0,
    tags: ['nextjs', 'root', 'application', 'global'],
    nextjs: {
      type: NEXTJS_LAYOUT_TYPES.ROOT,
      route: COMMON_ROUTES.ROOT,
      metadata: {
        title: { default: 'My App', template: '%s | My App' },
        description: 'A modern web application built with NextJS',
        keywords: ['nextjs', 'react', 'typescript', 'web app'],
        authors: [...METADATA_TEMPLATES.DEFAULT.authors],
        openGraph: {
          type: 'website',
          locale: 'en_US',
          siteName: 'My App',
          title: 'My App',
          description: 'A modern web application built with NextJS'
        },
        twitter: {
          card: 'summary_large_image',
          site: '@myapp'
        },
        robots: METADATA_TEMPLATES.DEFAULT.robots
      },
      imports: [
        COMMON_IMPORTS.REACT,
        COMMON_IMPORTS.NEXT_FONT,
        COMMON_IMPORTS.GLOBALS_CSS
      ],
      exports: [{
        name: 'default',
        type: 'default',
        isAsync: false,
        parameters: ['children: React.ReactNode'],
        returnType: 'JSX.Element'
      }],
      appRouterFeatures: [
        APP_ROUTER_FEATURES.METADATA, 
        APP_ROUTER_FEATURES.LOADING, 
        APP_ROUTER_FEATURES.ERROR, 
        APP_ROUTER_FEATURES.NOT_FOUND
      ]
    },
    priority: ASSIGNMENT_PRIORITIES.GLOBAL
  }
]

export async function seedRootLayouts(options: NextJSLayoutSeedingOptions = {}) {
  const config = { ...DEFAULT_NEXTJS_OPTIONS, ...options }
  
  if (config.verbose) {
    console.log('🚀 Seeding NextJS Root Layouts...')
  }

  const startTime = Date.now()
  let createdCount = 0
  let skippedCount = 0
  let errorCount = 0

  try {
    for (const template of rootLayoutTemplates) {
      // Validate template
      const validation = validateLayoutTemplate(template)
      if (!validation.isValid) {
        logError(`Invalid template ${template.name}: ${validation.errors.join(', ')}`, config.verbose)
        errorCount++
        continue
      }

      // Check if layout exists
      if (config.skipExisting && await layoutExists(template.name, template.type)) {
        logSkipped(`Existing layout: ${template.name}`, config.verbose)
        skippedCount++
        continue
      }

      // Create layout
      const layoutResult = await createLayout(template)
      if (!layoutResult.success) {
        logError(`Failed to create layout ${template.name}: ${layoutResult.error}`, config.verbose)
        errorCount++
        continue
      }

      // Create layout assignment
      const assignmentResult = await createLayoutAssignment(layoutResult.data.id, {
        targetType: 'global',
        priority: template.priority || ASSIGNMENT_PRIORITIES.GLOBAL,
        conditions: {},
        isActive: true
      })

      if (!assignmentResult.success) {
        logError(`Failed to create assignment for ${template.name}: ${assignmentResult.error}`, config.verbose)
      }

      logSuccess(`Created root layout: ${template.name}`, config.verbose)
      createdCount++
    }

    const result: SeedingResult = {
      created: createdCount,
      skipped: skippedCount,
      errors: errorCount,
      duration: Date.now() - startTime
    }

    if (config.verbose) {
      console.log(createSeedingSummary('NextJS Root Layouts', result, rootLayoutTemplates, true))
    }

    return result

  } catch (error) {
    logError(`Error seeding root layouts: ${error}`, config.verbose)
    throw error
  }
}

// Export for use in other seeders
export { rootLayoutTemplates }

// Run directly if called as main module
if (require.main === module) {
  seedRootLayouts({ verbose: true })
    .then(() => {
      console.log('✅ NextJS root layouts seeding completed successfully!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('❌ NextJS root layouts seeding failed:', error)
      process.exit(1)
    })
    .finally(() => {
      cleanup()
    })
}
