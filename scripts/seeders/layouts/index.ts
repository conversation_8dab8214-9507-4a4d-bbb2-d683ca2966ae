/**
 * Layout Seeders Main Index
 * Orchestrates all layout seeding operations with proper organization and separation of concerns
 */

import { 
  CompleteLayoutSeedingOptions, 
  CompleteSeedingResult,
  CategorySeedingResult,
  SeedingResult,
  DEFAULT_COMPLETE_OPTIONS 
} from './core/types'
import { logProgress, logSuccess, logError, cleanup } from './core/helpers'

// Import category seeders
import { seedRootLayouts } from './nextjs/root-layouts'

interface SeedingModule {
  name: string
  category: string
  seeder: () => Promise<SeedingResult>
  enabled: boolean
}

export async function seedCompleteLayouts(options: CompleteLayoutSeedingOptions = {}) {
  const config = { ...DEFAULT_COMPLETE_OPTIONS, ...options }
  
  if (config.verbose) {
    console.log('🎨 Starting Complete Layout Seeding...')
    console.log('📋 Configuration:', {
      skipExisting: config.skipExisting,
      dryRun: config.dryRun,
      categories: Object.keys(config.categories || {}).filter(key => 
        config.categories?.[key as keyof typeof config.categories]
      )
    })
  }

  const startTime = Date.now()
  const results: CompleteSeedingResult = {
    categories: {},
    total: {
      created: 0,
      skipped: 0,
      errors: 0,
      duration: 0
    }
  }

  try {
    // Define seeding modules
    const seedingModules: SeedingModule[] = [
      // NextJS Layouts
      {
        name: 'Root Layouts',
        category: 'nextjs',
        seeder: () => seedRootLayouts(config.categories?.nextjs),
        enabled: config.categories?.nextjs?.includeRoot !== false
      },
      
      // TODO: Add more seeding modules
      // {
      //   name: 'Nested Layouts',
      //   category: 'nextjs',
      //   seeder: () => seedNestedLayouts(config.categories?.nextjs),
      //   enabled: config.categories?.nextjs?.includeNested !== false
      // },
      // {
      //   name: 'SaaS Layouts',
      //   category: 'marketing',
      //   seeder: () => seedSaaSLayouts(config.categories?.marketing),
      //   enabled: config.categories?.marketing?.includeSaaS !== false
      // },
      // {
      //   name: 'Shop Layouts',
      //   category: 'ecommerce',
      //   seeder: () => seedShopLayouts(config.categories?.ecommerce),
      //   enabled: config.categories?.ecommerce?.includeShop !== false
      // },
      // {
      //   name: 'Blog Layouts',
      //   category: 'content',
      //   seeder: () => seedBlogLayouts(config.categories?.content),
      //   enabled: config.categories?.content?.includeBlog !== false
      // },
      // {
      //   name: 'Dashboard Layouts',
      //   category: 'admin',
      //   seeder: () => seedDashboardLayouts(config.categories?.admin),
      //   enabled: config.categories?.admin?.includeDashboard !== false
      // },
      // {
      //   name: 'Auth Layouts',
      //   category: 'specialized',
      //   seeder: () => seedAuthLayouts(config.categories?.specialized),
      //   enabled: config.categories?.specialized?.includeAuth !== false
      // }
    ]

    // Filter enabled modules
    const enabledModules = seedingModules.filter(module => module.enabled)
    
    if (config.verbose) {
      console.log(`\n📦 Enabled Seeding Modules: ${enabledModules.length}`)
      enabledModules.forEach(module => {
        console.log(`  - ${module.category}/${module.name}`)
      })
    }

    // Execute seeding modules
    for (const module of enabledModules) {
      if (config.verbose) {
        console.log(`\n🚀 Seeding ${module.category}/${module.name}...`)
      }

      try {
        const moduleStartTime = Date.now()
        const result = await module.seeder()
        
        // Initialize category results if not exists
        if (!results.categories[module.category]) {
          results.categories[module.category] = {
            created: 0,
            skipped: 0,
            errors: 0,
            duration: 0
          }
        }

        // Accumulate results
        results.categories[module.category].created += result.created
        results.categories[module.category].skipped += result.skipped
        results.categories[module.category].errors += result.errors
        results.categories[module.category].duration += Date.now() - moduleStartTime

        results.total.created += result.created
        results.total.skipped += result.skipped
        results.total.errors += result.errors

        logSuccess(`${module.category}/${module.name}: ${result.created} created, ${result.skipped} skipped, ${result.errors} errors`, config.verbose)

      } catch (error) {
        logError(`Failed to seed ${module.category}/${module.name}: ${error}`, config.verbose)
        
        // Initialize category if not exists
        if (!results.categories[module.category]) {
          results.categories[module.category] = {
            created: 0,
            skipped: 0,
            errors: 0,
            duration: 0
          }
        }
        
        results.categories[module.category].errors += 1
        results.total.errors += 1
      }
    }

    // Calculate total duration
    results.total.duration = Date.now() - startTime

    // Generate final report
    if (config.verbose) {
      generateFinalReport(results, enabledModules)
    }

    return results

  } catch (error) {
    logError(`Critical error during layout seeding: ${error}`, config.verbose)
    throw error
  } finally {
    await cleanup()
  }
}

function generateFinalReport(results: CompleteSeedingResult, modules: SeedingModule[]) {
  const duration = (results.total.duration / 1000).toFixed(2)
  const successRate = results.total.created + results.total.skipped > 0 
    ? Math.round((results.total.created / (results.total.created + results.total.skipped + results.total.errors)) * 100)
    : 0

  console.log(`
🎨 Complete Layout Seeding Summary
=====================================

📊 Results by Category:`)

  Object.entries(results.categories).forEach(([category, result]) => {
    const categoryDuration = (result.duration / 1000).toFixed(2)
    console.log(`- ${category.charAt(0).toUpperCase() + category.slice(1)}: ${result.created} created, ${result.skipped} skipped, ${result.errors} errors (${categoryDuration}s)`)
  })

  console.log(`
🎯 Total Summary:
- Layouts Created: ${results.total.created}
- Layouts Skipped: ${results.total.skipped}
- Errors: ${results.total.errors}
- Success Rate: ${successRate}%
- Total Duration: ${duration}s
- Modules Executed: ${modules.length}

${results.total.errors === 0 ? '✅ All layouts seeded successfully!' : '⚠️  Some errors occurred during seeding.'}
  `)
}

// Export individual category seeders for direct use
export { seedRootLayouts }

// Export types for external use
export type { 
  CompleteLayoutSeedingOptions, 
  CompleteSeedingResult,
  CategorySeedingResult,
  SeedingResult 
}

// Run directly if called as main module
if (require.main === module) {
  const args = process.argv.slice(2)
  const verbose = args.includes('--verbose') || args.includes('-v')
  const dryRun = args.includes('--dry-run') || args.includes('-d')

  seedCompleteLayouts({ 
    verbose, 
    dryRun,
    skipExisting: true 
  })
    .then((results) => {
      const hasErrors = results.total.errors > 0
      console.log(hasErrors 
        ? '⚠️  Layout seeding completed with errors!' 
        : '✅ Layout seeding completed successfully!'
      )
      process.exit(hasErrors ? 1 : 0)
    })
    .catch((error) => {
      console.error('❌ Layout seeding failed:', error)
      process.exit(1)
    })
}
