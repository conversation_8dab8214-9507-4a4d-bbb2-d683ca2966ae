# 🎨 Layout Seeders

Dedicated seeding system for NextJS layouts with complete routing and rendering configurations.

## 📁 Folder Structure

```
scripts/seeders/layouts/
├── README.md                    # This documentation
├── index.ts                     # Main entry point and orchestrator
├── core/                        # Core seeding utilities
│   ├── helpers.ts              # Shared helper functions
│   ├── types.ts                # TypeScript type definitions
│   └── constants.ts            # Shared constants and configurations
├── nextjs/                     # NextJS-specific layouts
│   ├── root-layouts.ts         # Root application layouts
│   ├── nested-layouts.ts       # Nested route layouts
│   ├── template-layouts.ts     # Template layouts
│   └── group-layouts.ts        # Route group layouts
├── marketing/                  # Marketing and landing pages
│   ├── saas-layouts.ts         # SaaS landing pages
│   ├── product-layouts.ts      # Product landing pages
│   └── campaign-layouts.ts     # Campaign-specific layouts
├── ecommerce/                  # E-commerce layouts
│   ├── shop-layouts.ts         # Shop and catalog layouts
│   ├── product-layouts.ts      # Product detail layouts
│   └── checkout-layouts.ts     # Checkout and cart layouts
├── content/                    # Content management layouts
│   ├── blog-layouts.ts         # Blog and article layouts
│   ├── cms-layouts.ts          # CMS layouts
│   └── documentation-layouts.ts # Documentation layouts
├── admin/                      # Administrative layouts
│   ├── dashboard-layouts.ts    # Admin dashboard layouts
│   ├── user-layouts.ts         # User management layouts
│   └── settings-layouts.ts     # Settings and configuration layouts
└── specialized/                # Specialized use cases
    ├── auth-layouts.ts         # Authentication layouts
    ├── error-layouts.ts        # Error page layouts
    └── maintenance-layouts.ts  # Maintenance page layouts
```

## 🚀 Quick Start

### Run All Layout Seeders
```bash
npm run seed:layouts
```

### Run Category-Specific Seeders
```bash
# NextJS layouts
npm run seed:layouts:nextjs

# Marketing layouts
npm run seed:layouts:marketing

# E-commerce layouts
npm run seed:layouts:ecommerce

# Content layouts
npm run seed:layouts:content

# Admin layouts
npm run seed:layouts:admin

# Specialized layouts
npm run seed:layouts:specialized
```

### Run Individual Seeders
```bash
# Root layouts only
npm run seed:layouts:nextjs:root

# SaaS landing pages only
npm run seed:layouts:marketing:saas

# Shop layouts only
npm run seed:layouts:ecommerce:shop
```

## 🎯 Layout Categories

### 1. NextJS Layouts (`nextjs/`)
Core NextJS App Router layouts with routing configuration.

- **Root Layouts**: Global application layouts
- **Nested Layouts**: Route-specific nested layouts
- **Template Layouts**: Reusable template layouts
- **Group Layouts**: Route group layouts

### 2. Marketing Layouts (`marketing/`)
Marketing and conversion-focused layouts.

- **SaaS Layouts**: SaaS landing pages and marketing sites
- **Product Layouts**: Product-specific landing pages
- **Campaign Layouts**: Marketing campaign layouts

### 3. E-commerce Layouts (`ecommerce/`)
E-commerce and shopping layouts.

- **Shop Layouts**: Product catalogs and category pages
- **Product Layouts**: Product detail and showcase layouts
- **Checkout Layouts**: Cart, checkout, and payment layouts

### 4. Content Layouts (`content/`)
Content management and publishing layouts.

- **Blog Layouts**: Blog posts, archives, and author pages
- **CMS Layouts**: Content management interfaces
- **Documentation Layouts**: Documentation and help pages

### 5. Admin Layouts (`admin/`)
Administrative and dashboard layouts.

- **Dashboard Layouts**: Admin dashboard interfaces
- **User Layouts**: User management interfaces
- **Settings Layouts**: Configuration and settings pages

### 6. Specialized Layouts (`specialized/`)
Specialized use case layouts.

- **Auth Layouts**: Login, signup, and authentication pages
- **Error Layouts**: 404, 500, and error page layouts
- **Maintenance Layouts**: Maintenance and coming soon pages

## ⚙️ Configuration

### Global Configuration
```typescript
interface LayoutSeedingConfig {
  skipExisting: boolean
  verbose: boolean
  categories: {
    nextjs: boolean
    marketing: boolean
    ecommerce: boolean
    content: boolean
    admin: boolean
    specialized: boolean
  }
}
```

### Category-Specific Configuration
Each category supports its own configuration options:

```typescript
// NextJS layouts configuration
interface NextJSLayoutConfig {
  includeRoot: boolean
  includeNested: boolean
  includeTemplate: boolean
  includeGroup: boolean
}

// Marketing layouts configuration
interface MarketingLayoutConfig {
  includeSaaS: boolean
  includeProduct: boolean
  includeCampaign: boolean
}
```

## 🏗️ Development Guidelines

### Adding New Layout Categories

1. **Create Category Folder**
```bash
mkdir scripts/seeders/layouts/[category-name]
```

2. **Create Seeder Files**
```typescript
// scripts/seeders/layouts/[category]/[type]-layouts.ts
export async function seed[Type]Layouts(options: SeedingOptions) {
  // Implementation
}
```

3. **Update Main Index**
```typescript
// scripts/seeders/layouts/index.ts
import { seed[Type]Layouts } from './[category]/[type]-layouts'

// Add to main seeding function
```

4. **Add Package.json Scripts**
```json
{
  "scripts": {
    "seed:layouts:[category]": "tsx scripts/seeders/layouts/[category]/index.ts",
    "seed:layouts:[category]:[type]": "tsx scripts/seeders/layouts/[category]/[type]-layouts.ts"
  }
}
```

### Code Standards

- **TypeScript**: Full type safety with proper interfaces
- **Error Handling**: Comprehensive error handling and logging
- **Modularity**: Each seeder should be independently runnable
- **Consistency**: Use shared helpers and constants
- **Documentation**: Document all layout templates and configurations

### Testing

- **Unit Tests**: Test individual seeder functions
- **Integration Tests**: Test complete seeding workflows
- **Validation**: Validate seeded layouts render correctly
- **Performance**: Monitor seeding performance and database impact

## 📊 Monitoring and Maintenance

### Seeding Reports
Each seeder generates detailed reports:

```
🎨 Layout Seeding Report
========================

Category: NextJS Layouts
- Root Layouts: 2 created, 0 skipped
- Nested Layouts: 3 created, 1 skipped
- Template Layouts: 1 created, 0 skipped
- Group Layouts: 2 created, 0 skipped

Total: 8 layouts created, 1 skipped
Duration: 2.3 seconds
```

### Database Impact
- Monitor database size growth
- Track layout usage and performance
- Clean up unused or duplicate layouts
- Optimize layout structure for performance

### Maintenance Tasks
- Regular cleanup of test layouts
- Update layout templates with new features
- Validate layout compatibility with NextJS updates
- Performance optimization and caching improvements

## 🔧 Troubleshooting

### Common Issues

1. **Duplicate Layouts**: Use `skipExisting: true` option
2. **Type Errors**: Ensure proper TypeScript configuration
3. **Database Errors**: Check Prisma schema compatibility
4. **Performance Issues**: Use batch operations for large datasets

### Debug Mode
```bash
# Run with verbose logging
npm run seed:layouts -- --verbose

# Run specific category with debug
npm run seed:layouts:nextjs -- --debug
```

### Validation
```bash
# Validate seeded layouts
npm run validate:layouts

# Test layout rendering
npm run test:layouts:render
```
