/**
 * South African Kids Clothing Products Seeder
 * Creates realistic SA kids clothing catalog with variants, images, categories, and tags
 */

import { PrismaClient } from '@prisma/client'
import { faker } from '@faker-js/faker'

const prisma = new PrismaClient()

// South African kids clothing specific data
const productCategories = [
  { name: 'Baby Clothing', slug: 'baby-clothing', description: 'Comfortable clothing for babies 0-24 months' },
  { name: 'Toddler Clothing', slug: 'toddler-clothing', description: 'Stylish clothes for toddlers 2-4 years' },
  { name: 'Kids Clothing', slug: 'kids-clothing', description: 'Trendy clothing for kids 5-12 years' },
  { name: 'School Uniforms', slug: 'school-uniforms', description: 'Quality school uniform pieces' },
  { name: 'Accessories', slug: 'accessories', description: 'Hats, bags, and other accessories' },
  { name: 'Shoes', slug: 'shoes', description: 'Comfortable and stylish footwear' },
  { name: 'Sleepwear', slug: 'sleepwear', description: 'Cozy pajamas and sleepwear' },
  { name: 'Outerwear', slug: 'outerwear', description: 'Jackets, coats, and warm clothing' },
  { name: 'Swimwear', slug: 'swimwear', description: 'Swimming costumes and beach wear' }
]

const productTags = [
  'kidswear',
  'minimalstyle',
  'oversizestyle',
  'coolkid',
  'madeinsouthafrica',
  'genderneutral',
  'unisexclothing',
  'organic',
  'sustainable',
  'schoolready',
  'playtime',
  'comfortable',
  'durable',
  'breathable',
  'cottonblend'
]

const productTypes = [
  'T-Shirts', 'Polo Shirts', 'Button-up Shirts', 'Dresses', 'Skirts', 'Pants', 'Chinos', 'Shorts', 
  'Hoodies', 'Sweaters', 'Cardigans', 'Jackets', 'Blazers', 'Pajamas', 'Underwear', 'Socks', 
  'Shoes', 'Sandals', 'Hats', 'Caps', 'Bags', 'Swimwear', 'Tracksuit', 'Leggings'
]

const saVendors = [
  'CocoMilk Kids',
  'Little Baobab',
  'Mzansi Minis',
  'Safari Kids',
  'Ubuntu Clothing',
  'Proudly SA Kids',
  'Rainbow Nation Wear',
  'Springbok Style',
  'African Sun Kids',
  'Cape Town Kids Co'
]

const saColors = [
  'White', 'Black', 'Navy', 'Grey', 'Khaki', 'Burgundy', 'Forest Green', 'Royal Blue',
  'Cream', 'Stone', 'Charcoal', 'Olive', 'Rust', 'Teal', 'Mustard', 'Coral'
]

const saMaterials = [
  'Cotton Twill', 'Combed Cotton', 'Paper Cotton Poplin', 'Cotton Jersey', 'Cotton Blend',
  'Organic Cotton', 'Bamboo Cotton', 'Linen Blend', 'Poly-Cotton', 'French Terry'
]

const saStyles = [
  'Oversized', 'Relaxed Fit', 'Slim Fit', 'Regular Fit', 'Tailored', 'Casual', 
  'Smart Casual', 'Formal', 'Sporty', 'Minimalist'
]

const sizes = {
  baby: ['Newborn', '0-3M', '3-6M', '6-9M', '9-12M', '12-18M', '18-24M'],
  toddler: ['2T', '3T', '4T'],
  kids: ['3-4', '4-5', '5-6', '6-7', '7-8', '8-9', '9-10', '10-11', '11-12'],
  shoes: ['4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '1', '2', '3']
}

// South African specific product templates
const saProductTemplates = [
  {
    type: 'T-Shirts',
    titles: ['Oversized Cotton Tee', 'Gender Neutral Basic Tee', 'Minimalist Cotton T-Shirt', 'Combed Cotton Tee'],
    descriptions: ['Made from premium combed cotton', 'Oversized fit for comfort', 'Gender neutral design', 'Breathable cotton blend'],
    priceRange: { min: 180, max: 320 }
  },
  {
    type: 'Chinos',
    titles: ['Cotton Twill Chino Pants', 'Relaxed Fit Chinos', 'Smart Casual Chinos', 'Tailored Chino Trousers'],
    descriptions: ['Cotton twill construction', 'Perfect for school or play', 'Adjustable waistband', 'Durable and comfortable'],
    priceRange: { min: 320, max: 480 }
  },
  {
    type: 'Button-up Shirts',
    titles: ['Double Breasted Shirt', 'Cotton Poplin Shirt', 'Smart Casual Shirt', 'School Ready Shirt'],
    descriptions: ['Paper cotton poplin fabric', 'Double breasted design', 'Perfect for formal occasions', 'Easy care instructions'],
    priceRange: { min: 280, max: 420 }
  },
  {
    type: 'Dresses',
    titles: ['Cotton Jersey Dress', 'Minimalist Shift Dress', 'Casual Play Dress', 'Smart Casual Dress'],
    descriptions: ['Comfortable cotton jersey', 'Easy to wear design', 'Perfect for any occasion', 'Machine washable'],
    priceRange: { min: 250, max: 380 }
  },
  {
    type: 'Hoodies',
    titles: ['Oversized Cotton Hoodie', 'Cozy Fleece Hoodie', 'Minimalist Hoodie', 'Casual Cotton Hoodie'],
    descriptions: ['French terry cotton', 'Oversized comfortable fit', 'Perfect for cooler weather', 'Kangaroo pocket'],
    priceRange: { min: 380, max: 550 }
  },
  {
    type: 'Shorts',
    titles: ['Cotton Twill Shorts', 'Casual Play Shorts', 'Smart Casual Shorts', 'Comfortable Cotton Shorts'],
    descriptions: ['Durable cotton twill', 'Perfect for summer', 'Adjustable waistband', 'Multiple pockets'],
    priceRange: { min: 220, max: 350 }
  }
]

function generateSAProductTitle(template: any, vendor: string) {
  const title = faker.helpers.arrayElement(template.titles)
  return title
}

function generateSAProductDescription(template: any, title: string, material: string, style: string) {
  const baseDesc = faker.helpers.arrayElement(template.descriptions)
  const features = [
    `Made from premium ${material.toLowerCase()}`,
    `${style.toLowerCase()} fit for maximum comfort`,
    'Machine washable for easy care',
    'Designed and made in South Africa',
    'Hypoallergenic and skin-friendly',
    'Fade-resistant colors',
    'Durable construction for active kids',
    'Breathable fabric for all-day comfort'
  ]
  
  const selectedFeatures = faker.helpers.arrayElements(features, { min: 3, max: 5 })
  
  return `${title} - ${baseDesc}. Features: ${selectedFeatures.join(', ')}. Perfect for South African kids who love comfort and style.`
}

function getSizesForCategory(category: string, productType: string) {
  if (category.includes('Baby')) return sizes.baby
  if (category.includes('Toddler')) return sizes.toddler
  if (productType.toLowerCase().includes('shoe') || productType.toLowerCase().includes('sandal')) return sizes.shoes
  return sizes.kids
}

function generateSAProductImages(productSlug: string, colorCount: number) {
  const images = []
  // Use more realistic placeholder images for South African kids clothing
  const imageCategories = ['fashion', 'clothing', 'kids']
  
  for (let i = 0; i < Math.min(colorCount + 1, 4); i++) {
    const category = faker.helpers.arrayElement(imageCategories)
    images.push({
      url: `https://picsum.photos/800/800?random=${faker.string.numeric(6)}&category=${category}`,
      altText: `${productSlug.replace(/-/g, ' ')} - Image ${i + 1}`,
      position: i,
      width: 800,
      height: 800
    })
  }
  return images
}

function generateSAPrice(template: any) {
  return faker.number.float({ 
    min: template.priceRange.min, 
    max: template.priceRange.max, 
    fractionDigits: 0 
  })
}

function generateUniqueSKU(vendor: string, handle: string, color: string, size: string, index: number) {
  const timestamp = Date.now().toString().slice(-6)
  const randomSuffix = faker.string.alphanumeric(3).toUpperCase()
  return `${vendor.replace(/\s+/g, '').toUpperCase().slice(0, 3)}-${handle.toUpperCase().slice(0, 6)}-${color.toUpperCase().slice(0, 3)}-${size.replace(/[^A-Z0-9]/g, '')}-${index}-${timestamp}-${randomSuffix}`
}

export async function seedProducts(count: number = 50) {
  try {
    console.log(`🇿🇦 Creating ${count} South African kids clothing products...`)
    
    // Note: Not cleaning up existing data to preserve any existing products
    
    // First create product tags
    console.log('🏷️ Creating product tags...')
    const tags = []
    for (const tagName of productTags) {
      const existingTag = await prisma.productTag.findUnique({
        where: { slug: tagName }
      })
      
      if (!existingTag) {
        const tag = await prisma.productTag.create({
          data: {
            name: tagName.charAt(0).toUpperCase() + tagName.slice(1).replace(/([A-Z])/g, ' $1'),
            slug: tagName,
            description: `${tagName} related products`
          }
        })
        tags.push(tag)
      } else {
        tags.push(existingTag)
      }
    }
    
    // Create categories
    console.log('📂 Creating product categories...')
    const categories = []
    for (const categoryData of productCategories) {
      const existingCategory = await prisma.productCategory.findUnique({
        where: { slug: categoryData.slug }
      })
      
      if (!existingCategory) {
        const category = await prisma.productCategory.create({
          data: {
            ...categoryData,
            isVisible: true,
            position: categories.length
          }
        })
        categories.push(category)
      } else {
        categories.push(existingCategory)
      }
    }
    
    const products = []
    
    for (let i = 0; i < count; i++) {
      const template = faker.helpers.arrayElement(saProductTemplates)
      const category = faker.helpers.arrayElement(categories)
      const vendor = faker.helpers.arrayElement(saVendors)
      const material = faker.helpers.arrayElement(saMaterials)
      const style = faker.helpers.arrayElement(saStyles)
      
      const title = generateSAProductTitle(template, vendor)
      const slug = `${title.toLowerCase().replace(/[^a-z0-9]+/g, '-')}-${i}`
      const handle = slug
      
      // Check if product already exists
      const existingProduct = await prisma.product.findUnique({
        where: { slug }
      })
      
      if (!existingProduct) {
        const basePrice = generateSAPrice(template)
        const compareAtPrice = Math.random() < 0.3 ? Math.round(basePrice * 1.25) : null
        const costPerItem = Math.round(basePrice * 0.65)
        
        const product = await prisma.product.create({
          data: {
            title,
            slug,
            handle,
            description: generateSAProductDescription(template, title, material, style),
            descriptionHtml: `<p>${generateSAProductDescription(template, title, material, style)}</p>`,
            vendor,
            productType: template.type.toLowerCase(),
            status: faker.helpers.arrayElement(['active', 'active', 'active', 'draft']), // 75% active
            publishedAt: Math.random() < 0.8 ? faker.date.past({ years: 1 }) : null,
            price: basePrice,
            compareAtPrice,
            costPerItem,
            currency: 'ZAR',
            trackQuantity: true,
            continueSellingWhenOutOfStock: Math.random() < 0.2,
            inventoryQuantity: faker.number.int({ min: 5, max: 50 }),
            requiresShipping: true,
            isTaxable: true,
            weight: faker.number.float({ min: 0.1, max: 1.5, fractionDigits: 2 }),
            weightUnit: 'kg',
            dimensionLength: faker.number.float({ min: 30, max: 60, fractionDigits: 1 }),
            dimensionWidth: faker.number.float({ min: 20, max: 40, fractionDigits: 1 }),
            dimensionHeight: faker.number.float({ min: 1, max: 5, fractionDigits: 1 }),
            dimensionUnit: 'cm',
            hasVariants: true,
            seoTitle: `${title} | ${vendor} | South African Kids Clothing`,
            seoDescription: `Shop ${title.toLowerCase()} at ${vendor}. Premium South African kids clothing made from ${material.toLowerCase()}.`,
            seoKeywords: [
              title.toLowerCase(),
              template.type.toLowerCase(),
              'south african kids clothing',
              'made in south africa',
              material.toLowerCase(),
              style.toLowerCase()
            ],
            metafields: {
              material: material.toLowerCase(),
              style: style.toLowerCase(),
              origin: 'South Africa',
              care_instructions: 'Machine wash cold, tumble dry low'
            },
            averageRating: faker.number.float({ min: 3.5, max: 5.0, fractionDigits: 1 }),
            reviewCount: faker.number.int({ min: 0, max: 25 })
          }
        })
        
        // Create product category relation
        await prisma.productCategoryRelation.create({
          data: {
            productId: product.id,
            categoryId: category.id
          }
        })
        
        // Create product tag relations (2-4 tags per product)
        const selectedTags = faker.helpers.arrayElements(tags, { min: 2, max: 4 })
        for (const tag of selectedTags) {
          await prisma.productTagRelation.create({
            data: {
              productId: product.id,
              tagId: tag.id
            }
          })
        }
        
        // Create product variants
        const availableColors = faker.helpers.arrayElements(saColors, { min: 2, max: 4 })
        const availableSizes = getSizesForCategory(category.name, template.type)
        const selectedSizes = faker.helpers.arrayElements(availableSizes, { min: 3, max: 6 })
        
        // Create color option
        await prisma.productOption.create({
          data: {
            productId: product.id,
            name: 'Color',
            position: 1,
            values: availableColors
          }
        })
        
        // Create size option
        await prisma.productOption.create({
          data: {
            productId: product.id,
            name: 'Size',
            position: 2,
            values: selectedSizes
          }
        })
        
        // Create variants for each color/size combination
        let variantPosition = 1
        for (const color of availableColors) {
          for (const size of selectedSizes) {
            const variantPriceAdjustment = faker.number.float({ min: -20, max: 30, fractionDigits: 0 })
            const variantPrice = Math.max(basePrice + variantPriceAdjustment, 150) // Minimum R150
            const sku = generateUniqueSKU(vendor, product.handle, color, size, variantPosition)
            
            const variant = await prisma.productVariant.create({
              data: {
                productId: product.id,
                title: `${color} / ${size}`,
                price: variantPrice,
                compareAtPrice: compareAtPrice ? compareAtPrice + variantPriceAdjustment : null,
                costPerItem: Math.round(variantPrice * 0.65),
                sku,
                barcode: faker.string.numeric(13),
                position: variantPosition++,
                weight: faker.number.float({ min: 0.1, max: 1.5, fractionDigits: 2 }),
                weightUnit: 'kg',
                requiresShipping: true,
                taxable: true,
                inventoryQuantity: faker.number.int({ min: 0, max: 20 }),
                inventoryPolicy: 'deny',
                fulfillmentService: 'manual',
                inventoryManagement: true,
                available: faker.number.int({ min: 0, max: 20 }) > 0,
                metafields: {
                  color: color.toLowerCase(),
                  size: size.toLowerCase(),
                  material: material.toLowerCase()
                }
              }
            })

            // Create variant options
            await prisma.productVariantOption.create({
              data: {
                variantId: variant.id,
                name: 'Color',
                value: color
              }
            })

            await prisma.productVariantOption.create({
              data: {
                variantId: variant.id,
                name: 'Size',
                value: size
              }
            })
          }
        }
        
        // Create product images
        const imageData = generateSAProductImages(product.slug, availableColors.length)
        for (const image of imageData) {
          await prisma.productImage.create({
            data: {
              productId: product.id,
              ...image
            }
          })
        }
        
        products.push(product)
        
        if ((i + 1) % 10 === 0) {
          console.log(`✅ Created ${i + 1}/${count} South African products`)
        }
      }
    }
    
    console.log(`🎉 Successfully created ${products.length} South African kids clothing products`)
    console.log(`📊 Product statistics:`)
    console.log(`   - Active products: ${products.filter(p => p.status === 'active').length}`)
    console.log(`   - Categories: ${categories.length}`)
    console.log(`   - Tags: ${tags.length}`)
    console.log(`   - Average price: R${Math.round(products.reduce((sum, p) => sum + Number(p.price), 0) / products.length)}`)
    console.log(`   - Price range: R${Math.min(...products.map(p => Number(p.price)))} - R${Math.max(...products.map(p => Number(p.price)))}`)
    
    return { products, categories, tags }
    
  } catch (error) {
    console.error('❌ Error seeding South African products:', error)
    throw error
  }
}

// Run the seeder if called directly
if (require.main === module) {
  const count = process.argv[2] ? parseInt(process.argv[2]) : 50
  
  seedProducts(count)
    .then(() => {
      console.log('🎉 South African kids clothing seeding completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 South African product seeding failed:', error)
      process.exit(1)
    })
    .finally(() => {
      prisma.$disconnect()
    })
}