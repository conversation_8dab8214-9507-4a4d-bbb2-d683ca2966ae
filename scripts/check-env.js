// Simple script to check if environment variables are loaded correctly
require('dotenv').config();

console.log('Checking Appwrite environment variables:');
console.log('NEXT_PUBLIC_APPWRITE_ENDPOINT:', process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT ? '✅ Set' : '❌ Missing');
console.log('NEXT_PUBLIC_APPWRITE_PROJECT_ID:', process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID ? '✅ Set' : '❌ Missing');
console.log('APPWRITE_API_KEY:', process.env.APPWRITE_API_KEY ? '✅ Set' : '❌ Missing');

// Show the first few characters of the API key if it exists (for security)
if (process.env.APPWRITE_API_KEY) {
  const apiKey = process.env.APPWRITE_API_KEY;
  const maskedKey = apiKey.substring(0, 10) + '...' + apiKey.substring(apiKey.length - 5);
  console.log('APPWRITE_API_KEY (masked):', maskedKey);
}