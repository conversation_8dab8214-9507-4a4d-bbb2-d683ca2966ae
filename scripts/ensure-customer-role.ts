import { db } from "@/lib/db"

async function ensureCustomerRole() {
  console.log("Checking if customer role exists...")
  
  // Check if customer role exists
  const customerRole = await db.role.findFirst({
    where: { slug: "customer" }
  })
  
  if (customerRole) {
    console.log("Customer role already exists:", customerRole)
    return customerRole
  }
  
  // Create customer role if it doesn't exist
  console.log("Creating customer role...")
  const newCustomerRole = await db.role.create({
    data: {
      name: "Customer",
      slug: "customer",
      description: "Regular customer of the store",
      isSystem: true,
      isActive: true,
      level: 1,
      capabilities: {
        view_products: true,
        place_orders: true,
        view_own_orders: true,
        manage_own_profile: true,
      },
      contentTypePermissions: {},
      restrictions: {},
    }
  })
  
  console.log("Created customer role:", newCustomerRole)
  return newCustomerRole
}

// Run the function
ensureCustomerRole()
  .then(() => {
    console.log("Done!")
    process.exit(0)
  })
  .catch((error) => {
    console.error("Error:", error)
    process.exit(1)
  })