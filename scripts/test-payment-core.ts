/**
 * Test Payment Core Library
 * 
 * This script tests the payment core library by creating a test payment.
 * 
 * Usage:
 * ```
 * npm run test:payment-core
 * ```
 */

import { 
  paymentService, 
  PaymentGateway, 
  PaymentMethod, 
  PaymentRequest,
  logger
} from '../lib/payment-core'

// Test payment request
const testPaymentRequest: PaymentRequest = {
  amount: {
    amount: 100.00,
    currency: 'ZAR'
  },
  customer: {
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User'
  },
  items: [
    {
      id: 'test-product',
      name: 'Test Product',
      quantity: 1,
      unitPrice: 100.00,
      totalPrice: 100.00
    }
  ],
  metadata: {
    orderId: 'test-order-123',
    customerId: 'test-customer-456',
    source: 'test-script'
  },
  returnUrl: 'http://localhost:3090/checkout/success?orderId=test-order-123',
  cancelUrl: 'http://localhost:3090/checkout/cancel?orderId=test-order-123',
  notifyUrl: 'http://localhost:3090/api/webhooks/payments',
  reference: 'TEST-REF-' + Date.now(),
  description: 'Test Payment'
}

// Test gateways to try
const gatewaysToTest = [
  PaymentGateway.PAYFAST,
  PaymentGateway.OZOW,
  PaymentGateway.SNAPSCAN,
  PaymentGateway.YOCO,
  PaymentGateway.MANUAL
]

// Test function
async function testPaymentCore() {
  try {
    console.log('Initializing payment service...')
    await paymentService.init()
    
    console.log('Getting available payment methods...')
    const methods = await paymentService.getAvailablePaymentMethods()
    console.log(`Found ${methods.length} payment methods:`)
    methods.forEach(method => {
      console.log(`- ${method.displayName} (${method.method}): ${method.gateways.join(', ')}`)
    })
    
    // Test each gateway
    for (const gateway of gatewaysToTest) {
      console.log(`\nTesting ${gateway} gateway...`)
      
      try {
        const response = await paymentService.createPayment(testPaymentRequest, gateway)
        
        if (response.success) {
          console.log(`✅ ${gateway} payment created successfully!`)
          console.log(`Transaction ID: ${response.transactionId}`)
          console.log(`Payment URL: ${response.paymentUrl}`)
          console.log(`Status: ${response.status}`)
        } else {
          console.log(`❌ ${gateway} payment failed:`)
          console.log(`Error: ${response.error?.message}`)
          console.log(`Code: ${response.error?.code}`)
        }
      } catch (error) {
        console.log(`❌ ${gateway} test failed with error:`)
        console.error(error)
      }
    }
    
    console.log('\nPayment core test completed!')
  } catch (error) {
    console.error('Test failed:', error)
  }
}

// Run the test
testPaymentCore().catch(error => {
  console.error('Unhandled error:', error)
  process.exit(1)
})