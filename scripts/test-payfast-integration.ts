/**
 * Test PayFast Integration
 * 
 * This script tests the PayFast integration by creating a test payment
 * and verifying the response.
 */

import { 
  PaymentRequest, 
  PaymentMethod, 
  PaymentGateway 
} from '../lib/payment-core/types'
import { PayfastService } from '../lib/payment-core/services/payfast-service'
import { getPayfastConfig } from '../lib/payment-core/config/payfast-config'

async function main() {
  console.log('Testing PayFast Integration')
  console.log('---------------------------')

  // Check environment variables
  const config = getPayfastConfig()
  if (!config.merchantId || !config.merchantKey) {
    console.error('❌ PayFast configuration is missing. Please set PAYFAST_MERCHANT_ID and PAYFAST_MERCHANT_KEY environment variables.')
    process.exit(1)
  }

  console.log(`✅ PayFast configuration found`)
  console.log(`   Merchant ID: ${config.merchantId.substring(0, 4)}...`)
  console.log(`   Sandbox Mode: ${config.sandbox ? 'Yes' : 'No'}`)
  console.log(`   Passphrase Set: ${config.passphrase ? 'Yes' : 'No'}`)
  console.log(`   Enabled: ${config.enabled ? 'Yes' : 'No'}`)
  console.log()

  // Create test payment request
  const paymentRequest: PaymentRequest = {
    amount: {
      amount: 10.00,
      currency: 'ZAR'
    },
    customer: {
      email: '<EMAIL>',
      firstName: 'Test',
      lastName: 'User',
      phone: '0123456789'
    },
    items: [
      {
        id: '1',
        name: 'Test Product',
        description: 'This is a test product',
        quantity: 1,
        unitPrice: 10.00,
        totalPrice: 10.00
      }
    ],
    metadata: {
      orderId: 'test-order-123',
      source: 'test-script'
    },
    returnUrl: 'http://localhost:3000/checkout/success?orderId=test-order-123',
    cancelUrl: 'http://localhost:3000/checkout/cancel?orderId=test-order-123',
    notifyUrl: 'http://localhost:3000/api/webhooks/payfast/notify',
    reference: 'TEST-123',
    description: 'Test Payment'
  }

  console.log('Creating test payment...')
  
  try {
    // Initialize PayFast service
    const payfastService = new PayfastService()
    
    // Create payment
    const response = await payfastService.createPayment(paymentRequest)
    
    if (response.success) {
      console.log('✅ Payment created successfully')
      console.log(`   Transaction ID: ${response.transactionId}`)
      console.log(`   Status: ${response.status}`)
      console.log(`   Payment URL: ${response.paymentUrl}`)
    } else {
      console.error('❌ Payment creation failed')
      console.error(`   Error: ${response.error?.message}`)
      console.error(`   Code: ${response.error?.code}`)
    }
  } catch (error) {
    console.error('❌ Error testing PayFast integration')
    console.error(error)
  }
}

// Run the test
main().catch(console.error)