/**
 * Setup Media Settings Script
 * 
 * This script initializes the Appwrite media settings collection and creates a default media settings document.
 * It also sets up the media bucket for file storage.
 */

import { getAppwriteServer } from '@/lib/appwrite/server';
import { getMediaSettingsService, defaultMediaSettings } from '@/lib/appwrite/media-settings';

async function setupMediaSettings() {
  try {
    console.log('🚀 Setting up Appwrite media settings...');
    
    // Initialize Appwrite server
    const appwriteServer = getAppwriteServer();
    
    // Setup media bucket
    console.log('📦 Setting up media storage bucket...');
    await appwriteServer.createMediaBucket();
    
    // Initialize media settings service
    const mediaSettingsService = getMediaSettingsService();
    
    // Create default media settings
    console.log('⚙️ Creating default media settings...');
    const settings = await mediaSettingsService.getMediaSettings();
    
    console.log('✅ Media settings setup completed successfully!');
    console.log('Current settings:', JSON.stringify(settings, null, 2));
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error setting up media settings:', error);
    process.exit(1);
  }
}

// Run the setup
setupMediaSettings();