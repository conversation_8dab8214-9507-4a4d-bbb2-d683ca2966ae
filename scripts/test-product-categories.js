#!/usr/bin/env node

/**
 * Test script to verify product category foreign key constraint fixes
 * This script tests creating products with valid and invalid category IDs
 */

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'

async function testProductCategories() {
  console.log('🧪 Testing Product Categories Foreign Key Constraints...\n')

  try {
    // Test 1: Get available categories
    console.log('1️⃣ Fetching available categories...')
    const categoriesResponse = await fetch(`${API_BASE}/api/e-commerce/categories`)
    const categoriesData = await categoriesResponse.json()
    
    if (!categoriesData.success) {
      throw new Error(`Failed to fetch categories: ${categoriesData.error}`)
    }
    
    const categories = categoriesData.data
    console.log(`✅ Found ${categories.length} categories`)
    
    if (categories.length > 0) {
      console.log(`   First category: ${categories[0].name} (ID: ${categories[0].id})`)
    }

    // Test 2: Create product with valid category ID
    if (categories.length > 0) {
      console.log('\n2️⃣ Testing product creation with valid category ID...')
      const validCategoryId = categories[0].id
      
      const productData = {
        title: 'Test Product - Valid Category',
        description: 'Test product with valid category ID',
        price: 99.99,
        currency: 'ZAR',
        categoryIds: [validCategoryId],
        status: 'draft'
      }
      
      const createResponse = await fetch(`${API_BASE}/api/e-commerce/products`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData)
      })
      
      const createData = await createResponse.json()
      
      if (createData.success) {
        console.log(`✅ Product created successfully with valid category`)
        console.log(`   Product ID: ${createData.data.id}`)
        console.log(`   Categories: ${createData.data.categories?.map(c => c.name).join(', ') || 'None'}`)
        
        // Clean up - delete the test product
        await fetch(`${API_BASE}/api/e-commerce/products/id/${createData.data.id}`, {
          method: 'DELETE'
        })
        console.log(`   🗑️ Test product cleaned up`)
      } else {
        console.log(`❌ Failed to create product with valid category: ${createData.error}`)
      }
    }

    // Test 3: Create product with invalid category ID
    console.log('\n3️⃣ Testing product creation with invalid category ID...')
    const invalidCategoryId = 'invalid-category-id-12345'
    
    const invalidProductData = {
      title: 'Test Product - Invalid Category',
      description: 'Test product with invalid category ID',
      price: 99.99,
      currency: 'ZAR',
      categoryIds: [invalidCategoryId],
      status: 'draft'
    }
    
    const invalidResponse = await fetch(`${API_BASE}/api/e-commerce/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invalidProductData)
    })
    
    const invalidData = await invalidResponse.json()
    
    if (!invalidData.success) {
      console.log(`✅ Correctly rejected invalid category ID`)
      console.log(`   Error: ${invalidData.error}`)
    } else {
      console.log(`❌ Should have rejected invalid category ID but didn't`)
    }

    // Test 4: Create product with mixed valid/invalid category IDs
    if (categories.length > 0) {
      console.log('\n4️⃣ Testing product creation with mixed valid/invalid category IDs...')
      const validCategoryId = categories[0].id
      const invalidCategoryId = 'invalid-category-id-67890'
      
      const mixedProductData = {
        title: 'Test Product - Mixed Categories',
        description: 'Test product with mixed valid/invalid category IDs',
        price: 99.99,
        currency: 'ZAR',
        categoryIds: [validCategoryId, invalidCategoryId],
        status: 'draft'
      }
      
      const mixedResponse = await fetch(`${API_BASE}/api/e-commerce/products`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mixedProductData)
      })
      
      const mixedData = await mixedResponse.json()
      
      if (!mixedData.success) {
        console.log(`✅ Correctly rejected mixed valid/invalid category IDs`)
        console.log(`   Error: ${mixedData.error}`)
      } else {
        console.log(`❌ Should have rejected mixed category IDs but didn't`)
      }
    }

    // Test 5: Create product with empty category array
    console.log('\n5️⃣ Testing product creation with empty category array...')
    const emptyProductData = {
      title: 'Test Product - No Categories',
      description: 'Test product with no categories',
      price: 99.99,
      currency: 'ZAR',
      categoryIds: [],
      status: 'draft'
    }
    
    const emptyResponse = await fetch(`${API_BASE}/api/e-commerce/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emptyProductData)
    })
    
    const emptyData = await emptyResponse.json()
    
    if (emptyData.success) {
      console.log(`✅ Product created successfully with no categories`)
      console.log(`   Product ID: ${emptyData.data.id}`)
      
      // Clean up - delete the test product
      await fetch(`${API_BASE}/api/e-commerce/products/id/${emptyData.data.id}`, {
        method: 'DELETE'
      })
      console.log(`   🗑️ Test product cleaned up`)
    } else {
      console.log(`❌ Failed to create product with no categories: ${emptyData.error}`)
    }

    console.log('\n🎉 All tests completed!')

  } catch (error) {
    console.error('❌ Test failed:', error.message)
    process.exit(1)
  }
}

// Run the tests
testProductCategories()
