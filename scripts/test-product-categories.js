#!/usr/bin/env node

/**
 * Comprehensive test script to verify foreign key constraint fixes across the ecommerce system
 * Tests: Categories, Collections, Tags, Products, Variants, Orders
 */

const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000'

async function testCategoryHierarchy() {
  console.log('🏗️ Testing Category Hierarchy Foreign Key Constraints...\n')

  try {
    // Test 1: Create parent category
    console.log('1️⃣ Creating parent category...')
    const parentResponse = await fetch(`${API_BASE}/api/e-commerce/categories`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Test Parent Category',
        description: 'Parent category for testing'
      })
    })

    const parentData = await parentResponse.json()
    if (!parentData.success) {
      throw new Error(`Failed to create parent category: ${parentData.error}`)
    }

    const parentId = parentData.data.id
    console.log(`✅ Parent category created: ${parentId}`)

    // Test 2: Create child category with valid parent
    console.log('\n2️⃣ Creating child category with valid parent...')
    const childResponse = await fetch(`${API_BASE}/api/e-commerce/categories`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Test Child Category',
        description: 'Child category for testing',
        parentId: parentId
      })
    })

    const childData = await childResponse.json()
    if (childData.success) {
      console.log(`✅ Child category created successfully`)

      // Clean up child
      await fetch(`${API_BASE}/api/e-commerce/categories/${childData.data.id}`, {
        method: 'DELETE'
      })
    } else {
      console.log(`❌ Failed to create child category: ${childData.error}`)
    }

    // Test 3: Try to create category with invalid parent
    console.log('\n3️⃣ Testing category creation with invalid parent ID...')
    const invalidParentResponse = await fetch(`${API_BASE}/api/e-commerce/categories`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Test Invalid Parent Category',
        description: 'Category with invalid parent',
        parentId: 'invalid-parent-id-12345'
      })
    })

    const invalidParentData = await invalidParentResponse.json()
    if (!invalidParentData.success) {
      console.log(`✅ Correctly rejected invalid parent ID: ${invalidParentData.error}`)
    } else {
      console.log(`❌ Should have rejected invalid parent ID`)
    }

    // Test 4: Try to create circular reference
    console.log('\n4️⃣ Testing circular reference prevention...')
    const circularResponse = await fetch(`${API_BASE}/api/e-commerce/categories/${parentId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: 'Test Parent Category',
        parentId: parentId // Try to make it its own parent
      })
    })

    const circularData = await circularResponse.json()
    if (!circularData.success) {
      console.log(`✅ Correctly prevented circular reference: ${circularData.error}`)
    } else {
      console.log(`❌ Should have prevented circular reference`)
    }

    // Clean up parent
    await fetch(`${API_BASE}/api/e-commerce/categories/${parentId}`, {
      method: 'DELETE'
    })
    console.log(`🗑️ Parent category cleaned up`)

  } catch (error) {
    console.error('❌ Category hierarchy test failed:', error.message)
  }
}

async function testProductCategories() {
  console.log('🧪 Testing Product Categories Foreign Key Constraints...\n')

  try {
    // Test 1: Get available categories
    console.log('1️⃣ Fetching available categories...')
    const categoriesResponse = await fetch(`${API_BASE}/api/e-commerce/categories`)
    const categoriesData = await categoriesResponse.json()
    
    if (!categoriesData.success) {
      throw new Error(`Failed to fetch categories: ${categoriesData.error}`)
    }
    
    const categories = categoriesData.data
    console.log(`✅ Found ${categories.length} categories`)
    
    if (categories.length > 0) {
      console.log(`   First category: ${categories[0].name} (ID: ${categories[0].id})`)
    }

    // Test 2: Create product with valid category ID
    if (categories.length > 0) {
      console.log('\n2️⃣ Testing product creation with valid category ID...')
      const validCategoryId = categories[0].id
      
      const productData = {
        title: 'Test Product - Valid Category',
        description: 'Test product with valid category ID',
        price: 99.99,
        currency: 'ZAR',
        categoryIds: [validCategoryId],
        status: 'draft'
      }
      
      const createResponse = await fetch(`${API_BASE}/api/e-commerce/products`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData)
      })
      
      const createData = await createResponse.json()
      
      if (createData.success) {
        console.log(`✅ Product created successfully with valid category`)
        console.log(`   Product ID: ${createData.data.id}`)
        console.log(`   Categories: ${createData.data.categories?.map(c => c.name).join(', ') || 'None'}`)
        
        // Clean up - delete the test product
        await fetch(`${API_BASE}/api/e-commerce/products/id/${createData.data.id}`, {
          method: 'DELETE'
        })
        console.log(`   🗑️ Test product cleaned up`)
      } else {
        console.log(`❌ Failed to create product with valid category: ${createData.error}`)
      }
    }

    // Test 3: Create product with invalid category ID
    console.log('\n3️⃣ Testing product creation with invalid category ID...')
    const invalidCategoryId = 'invalid-category-id-12345'
    
    const invalidProductData = {
      title: 'Test Product - Invalid Category',
      description: 'Test product with invalid category ID',
      price: 99.99,
      currency: 'ZAR',
      categoryIds: [invalidCategoryId],
      status: 'draft'
    }
    
    const invalidResponse = await fetch(`${API_BASE}/api/e-commerce/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invalidProductData)
    })
    
    const invalidData = await invalidResponse.json()
    
    if (!invalidData.success) {
      console.log(`✅ Correctly rejected invalid category ID`)
      console.log(`   Error: ${invalidData.error}`)
    } else {
      console.log(`❌ Should have rejected invalid category ID but didn't`)
    }

    // Test 4: Create product with mixed valid/invalid category IDs
    if (categories.length > 0) {
      console.log('\n4️⃣ Testing product creation with mixed valid/invalid category IDs...')
      const validCategoryId = categories[0].id
      const invalidCategoryId = 'invalid-category-id-67890'
      
      const mixedProductData = {
        title: 'Test Product - Mixed Categories',
        description: 'Test product with mixed valid/invalid category IDs',
        price: 99.99,
        currency: 'ZAR',
        categoryIds: [validCategoryId, invalidCategoryId],
        status: 'draft'
      }
      
      const mixedResponse = await fetch(`${API_BASE}/api/e-commerce/products`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mixedProductData)
      })
      
      const mixedData = await mixedResponse.json()
      
      if (!mixedData.success) {
        console.log(`✅ Correctly rejected mixed valid/invalid category IDs`)
        console.log(`   Error: ${mixedData.error}`)
      } else {
        console.log(`❌ Should have rejected mixed category IDs but didn't`)
      }
    }

    // Test 5: Create product with empty category array
    console.log('\n5️⃣ Testing product creation with empty category array...')
    const emptyProductData = {
      title: 'Test Product - No Categories',
      description: 'Test product with no categories',
      price: 99.99,
      currency: 'ZAR',
      categoryIds: [],
      status: 'draft'
    }
    
    const emptyResponse = await fetch(`${API_BASE}/api/e-commerce/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emptyProductData)
    })
    
    const emptyData = await emptyResponse.json()
    
    if (emptyData.success) {
      console.log(`✅ Product created successfully with no categories`)
      console.log(`   Product ID: ${emptyData.data.id}`)
      
      // Clean up - delete the test product
      await fetch(`${API_BASE}/api/e-commerce/products/id/${emptyData.data.id}`, {
        method: 'DELETE'
      })
      console.log(`   🗑️ Test product cleaned up`)
    } else {
      console.log(`❌ Failed to create product with no categories: ${emptyData.error}`)
    }

    console.log('\n🎉 Product categories tests completed!')

  } catch (error) {
    console.error('❌ Product categories test failed:', error.message)
  }
}

async function testCollectionsConstraints() {
  console.log('\n📚 Testing Collections Foreign Key Constraints...\n')

  try {
    // Test 1: Create collection with invalid product IDs
    console.log('1️⃣ Testing collection creation with invalid product IDs...')
    const invalidResponse = await fetch(`${API_BASE}/api/e-commerce/collections`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: 'Test Collection - Invalid Products',
        description: 'Collection with invalid product IDs',
        productIds: ['invalid-product-1', 'invalid-product-2']
      })
    })

    const invalidData = await invalidResponse.json()
    if (!invalidData.success) {
      console.log(`✅ Correctly rejected invalid product IDs: ${invalidData.error}`)
    } else {
      console.log(`❌ Should have rejected invalid product IDs`)
    }

    // Test 2: Create collection without product IDs (should work)
    console.log('\n2️⃣ Creating collection without product IDs...')
    const validResponse = await fetch(`${API_BASE}/api/e-commerce/collections`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        title: 'Test Collection - No Products',
        description: 'Collection without products'
      })
    })

    const validData = await validResponse.json()
    if (validData.success) {
      console.log(`✅ Collection created successfully without products`)

      // Clean up
      await fetch(`${API_BASE}/api/e-commerce/collections/${validData.data.id}`, {
        method: 'DELETE'
      })
      console.log(`🗑️ Collection cleaned up`)
    } else {
      console.log(`❌ Failed to create collection: ${validData.error}`)
    }

    console.log('\n🎉 Collections tests completed!')

  } catch (error) {
    console.error('❌ Collections test failed:', error.message)
  }
}

async function testOrderConstraints() {
  console.log('\n🛒 Testing Order Foreign Key Constraints...\n')

  try {
    // Test 1: Create order with invalid product IDs
    console.log('1️⃣ Testing order creation with invalid product IDs...')
    const invalidOrderResponse = await fetch(`${API_BASE}/api/e-commerce/orders`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        customerEmail: '<EMAIL>',
        shippingAddress: {
          firstName: 'Test',
          lastName: 'User',
          address1: '123 Main St',
          city: 'Cape Town',
          province: 'Western Cape',
          postalCode: '8000',
          country: 'ZA',
          phone: '0123456789'
        },
        items: [
          {
            productId: 'invalid-product-id',
            quantity: 1,
            price: 100,
            name: 'Invalid Product'
          }
        ]
      })
    })

    const invalidOrderData = await invalidOrderResponse.json()
    if (!invalidOrderData.success) {
      console.log(`✅ Correctly rejected order with invalid product ID: ${invalidOrderData.error}`)
    } else {
      console.log(`❌ Should have rejected order with invalid product ID`)
    }

    console.log('\n🎉 Order tests completed!')

  } catch (error) {
    console.error('❌ Order test failed:', error.message)
  }
}

async function runAllTests() {
  console.log('🚀 Starting Comprehensive Foreign Key Constraint Tests...\n')

  await testCategoryHierarchy()
  await testProductCategories()
  await testCollectionsConstraints()
  await testOrderConstraints()

  console.log('\n🎉 All foreign key constraint tests completed!')
}

// Run all tests
runAllTests()
