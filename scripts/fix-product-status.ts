#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function fixProductStatus() {
  try {
    console.log('🔍 Checking product status...')
    
    // Check the specific product that's failing
    const product = await prisma.product.findUnique({
      where: { id: 'cmc1r0wzy00048954ppp73euf' },
      select: {
        id: true,
        title: true,
        status: true,
        isAvailable: true,
        availableForSale: true,
        inventoryQuantity: true,
        trackQuantity: true
      }
    })

    if (!product) {
      console.log('❌ Product not found')
      return
    }

    console.log('📦 Current product status:', product)

    // Update the product to be available
    const updatedProduct = await prisma.product.update({
      where: { id: 'cmc1r0wzy00048954ppp73euf' },
      data: {
        status: 'active',
        isAvailable: true,
        availableForSale: true,
        publishedAt: new Date()
      }
    })

    console.log('✅ Product updated successfully:', {
      id: updatedProduct.id,
      title: updatedProduct.title,
      status: updatedProduct.status,
      isAvailable: updatedProduct.isAvailable,
      availableForSale: updatedProduct.availableForSale
    })

    // Also check and fix all products that might have similar issues
    const allProducts = await prisma.product.findMany({
      where: {
        OR: [
          { status: 'draft' },
          { isAvailable: false },
          { availableForSale: false }
        ]
      },
      select: {
        id: true,
        title: true,
        status: true,
        isAvailable: true,
        availableForSale: true
      }
    })

    if (allProducts.length > 0) {
      console.log(`\n🔧 Found ${allProducts.length} products with availability issues:`)
      allProducts.forEach(p => {
        console.log(`  - ${p.title}: status=${p.status}, available=${p.isAvailable}, forSale=${p.availableForSale}`)
      })

      // Fix all products
      const updateResult = await prisma.product.updateMany({
        where: {
          OR: [
            { status: 'draft' },
            { isAvailable: false },
            { availableForSale: false }
          ]
        },
        data: {
          status: 'active',
          isAvailable: true,
          availableForSale: true,
          publishedAt: new Date()
        }
      })

      console.log(`✅ Updated ${updateResult.count} products to be available`)
    }

  } catch (error) {
    console.error('❌ Error fixing product status:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixProductStatus()