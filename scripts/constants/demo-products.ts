// prisma/seed.ts
import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

async function main() {
  // Seed Tags
  const tags = [
    'kidswear',
    'minimalstyle',
    'oversizestyle',
    'coolkid',
    'madeinsouthafrica',
    'genderneutral',
    'unisexclothing',
  ];
  await Promise.all(
    tags.map((name) =>
      prisma.productTag.upsert({
        where: { slug: name },
        update: {},
        create: { name, slug: name },
      })
    )
  );

  // Shared metadata
  const sharedSizes = ['3-4', '4-5', '5-6', '6-7', '7-8'];
  const now = new Date();

  const productsData = [
    {
      title: 'Black Cotton Twill Chino Pants',
      slug: 'black-cotton-twill-chino-pants',
      description: 'Black cotton twill chino pants in an oversized fit.',
      descriptionHtml: '<p>Black cotton twill chino pants in an oversized fit.</p>',
      vendor: 'CocoMilk Kids',
      productType: 'pants',
      handle: 'black-cotton-chino-pants',
      status: 'published',
      publishedAt: now,
      price: 350.0,
      compareAtPrice: 500.0,
      costPerItem: 200.0,
      currency: 'ZAR',
      trackQuantity: true,
      continueSellingWhenOutOfStock: false,
      inventoryQuantity: 10,
      weight: 0.25,
      weightUnit: 'kg',
      dimensionLength: 50.0,
      dimensionWidth: 30.0,
      dimensionHeight: 2.0,
      dimensionUnit: 'cm',
      hasVariants: false,
      seoTitle: 'Black Twill Chino Pants | CocoMilk Kids',
      seoDescription: 'Oversized black cotton twill chino pants for kids.',
      seoKeywords: ['chino', 'pants', 'oversized', 'kids'],
      metafields: { material: 'cotton twill', fit: 'oversized' },
      isGiftCard: false,
      requiresShipping: true,
      isTaxable: true,
      isVisible: true,
      isAvailable: true,
      availableForSale: true,
      averageRating: 4.5,
      reviewCount: 12,
      // Connect tags
      tags: {
        create: tags.map((slug) => ({ tag: { connect: { slug } } })),
      },
      // inventoryItems, cartItems, etc. can be added similarly
      createdAt: now,
      updatedAt: now,
      imageUrls: ['https://example.com/images/chino1.jpg'],
    },
    {
      title: 'Double Breasted Shirt',
      slug: 'double-breasted-shirt',
      description: 'Double breasted shirt made from paper cotton poplin.',
      descriptionHtml: '<p>Double breasted shirt made from paper cotton poplin.</p>',
      vendor: 'CocoMilk Kids',
      productType: 'shirt',
      handle: 'double-breasted-shirt',
      status: 'published',
      publishedAt: now,
      price: 320.0,
      compareAtPrice: 450.0,
      costPerItem: 180.0,
      currency: 'ZAR',
      trackQuantity: true,
      continueSellingWhenOutOfStock: false,
      inventoryQuantity: 8,
      weight: 0.2,
      weightUnit: 'kg',
      dimensionLength: 40.0,
      dimensionWidth: 25.0,
      dimensionHeight: 2.0,
      dimensionUnit: 'cm',
      hasVariants: false,
      seoTitle: 'Double Breasted Poplin Shirt | CocoMilk Kids',
      seoDescription: 'Stylish double breasted shirt in paper cotton poplin.',
      seoKeywords: ['shirt', 'poplin', 'double breasted', 'kids'],
      metafields: { material: 'paper cotton poplin', style: 'double breasted' },
      isGiftCard: false,
      requiresShipping: true,
      isTaxable: true,
      isVisible: true,
      isAvailable: true,
      availableForSale: true,
      averageRating: 4.7,
      reviewCount: 8,
      tags: {
        create: tags.map((slug) => ({ tag: { connect: { slug } } })),
      },
      createdAt: now,
      updatedAt: now,
      imageUrls: ['https://example.com/images/shirt1.jpg'],
    },
    {
      title: 'Gender Neutral Oversized White Tee',
      slug: 'gender-neutral-oversized-white-tee',
      description: 'Gender neutral oversized white tee made from combed cotton.',
      descriptionHtml: '<p>Gender neutral oversized white tee made from combed cotton.</p>',
      vendor: 'CocoMilk Kids',
      productType: 't-shirt',
      handle: 'oversized-white-tee',
      status: 'published',
      publishedAt: now,
      price: 210.0,
      compareAtPrice: null,
      costPerItem: 120.0,
      currency: 'ZAR',
      trackQuantity: true,
      continueSellingWhenOutOfStock: true,
      inventoryQuantity: 15,
      weight: 0.15,
      weightUnit: 'kg',
      dimensionLength: 45.0,
      dimensionWidth: 28.0,
      dimensionHeight: 1.5,
      dimensionUnit: 'cm',
      hasVariants: false,
      seoTitle: 'Gender Neutral White Tee | CocoMilk Kids',
      seoDescription: 'Combed cotton gender neutral oversized white tee.',
      seoKeywords: ['tee', 'white', 'oversized', 'gender neutral'],
      metafields: { material: 'combed cotton', fit: 'oversized' },
      isGiftCard: false,
      requiresShipping: true,
      isTaxable: true,
      isVisible: true,
      isAvailable: true,
      availableForSale: true,
      averageRating: 4.3,
      reviewCount: 20,
      tags: {
        create: tags.map((slug) => ({ tag: { connect: { slug } } })),
      },
      createdAt: now,
      updatedAt: now,
      imageUrls: ['https://example.com/images/tee1.jpg'],
    },
  ];

  for (const data of productsData) {
    const { imageUrls, tags: tagSlugs, ...productFields } = data;
    const product = await prisma.product.create({
      data: {
        ...productFields,
        compareAtPrice: productFields.compareAtPrice ?? undefined,
        costPerItem: productFields.costPerItem ?? undefined,
        dimensionLength: productFields.dimensionLength ?? undefined,
        dimensionWidth: productFields.dimensionWidth ?? undefined,
        dimensionHeight: productFields.dimensionHeight ?? undefined,
        // Connect tags
        tags: {
          create: tagSlugs.map((slug) => ({ tag: { connect: { slug } } })),
        },
      },
    });

    // Seed images
    await Promise.all(
      imageUrls.map((url, i) =>
        prisma.productImage.create({
          data: {
            productId: product.id,
            url,
            position: i + 1,
          },
        })
      )
    );
  }
}

main()
  .then(() => console.log('✅ Seed completed.'))
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
