/**
 * Payment Service Migration Script
 * 
 * This script helps migrate from the old payment service to the new payment-core library.
 * It updates database records and ensures compatibility.
 */

import { PrismaClient } from '@prisma/client'
import { PaymentGateway, PaymentMethod, PaymentStatus } from '../lib/payment-core/types'
import { logger } from '../lib/payment-core/logger'

const prisma = new PrismaClient()

async function migratePaymentService() {
  console.log('Starting payment service migration...')
  
  try {
    // 1. Update payment records with new gateway IDs
    console.log('Updating payment gateway IDs...')
    await updatePaymentGatewayIds()
    
    // 2. Update payment statuses to match new format
    console.log('Updating payment statuses...')
    await updatePaymentStatuses()
    
    // 3. Update order payment statuses
    console.log('Updating order payment statuses...')
    await updateOrderPaymentStatuses()
    
    console.log('Migration completed successfully!')
  } catch (error) {
    console.error('Migration failed:', error)
    process.exit(1)
  }
}

async function updatePaymentGatewayIds() {
  // Map old gateway IDs to new ones
  const gatewayMap: Record<string, PaymentGateway> = {
    'payfast': PaymentGateway.PAYFAST,
    'ozow': PaymentGateway.OZOW,
    'snapscan': PaymentGateway.SNAPSCAN,
    'zapper': PaymentGateway.ZAPPER,
    'manual': PaymentGateway.MANUAL,
    'cash': PaymentGateway.MANUAL,
    'card': PaymentGateway.PAYFAST,
    'eft': PaymentGateway.PAYFAST
  }
  
  // Get all payments
  const payments = await prisma.payment.findMany({
    select: {
      id: true,
      gatewayId: true
    }
  })
  
  console.log(`Found ${payments.length} payment records to update`)
  
  // Update each payment
  for (const payment of payments) {
    const oldGatewayId = payment.gatewayId
    const newGatewayId = gatewayMap[oldGatewayId] || PaymentGateway.PAYFAST
    
    if (oldGatewayId !== newGatewayId) {
      await prisma.payment.update({
        where: { id: payment.id },
        data: {
          gatewayId: newGatewayId,
          metadata: {
            migrated: true,
            oldGatewayId,
            migratedAt: new Date().toISOString()
          }
        }
      })
      
      console.log(`Updated payment ${payment.id}: ${oldGatewayId} -> ${newGatewayId}`)
    }
  }
}

async function updatePaymentStatuses() {
  // Map old statuses to new ones
  const statusMap: Record<string, PaymentStatus> = {
    'pending': PaymentStatus.PENDING,
    'processing': PaymentStatus.PROCESSING,
    'completed': PaymentStatus.COMPLETED,
    'failed': PaymentStatus.FAILED,
    'cancelled': PaymentStatus.CANCELLED,
    'refunded': PaymentStatus.REFUNDED,
    'partially_refunded': PaymentStatus.PARTIALLY_REFUNDED,
    'expired': PaymentStatus.EXPIRED,
    'success': PaymentStatus.COMPLETED,
    'complete': PaymentStatus.COMPLETED,
    'error': PaymentStatus.FAILED,
    'canceled': PaymentStatus.CANCELLED
  }
  
  // Get all payments
  const payments = await prisma.payment.findMany({
    select: {
      id: true,
      status: true
    }
  })
  
  console.log(`Found ${payments.length} payment statuses to update`)
  
  // Update each payment
  for (const payment of payments) {
    const oldStatus = payment.status
    const newStatus = statusMap[oldStatus.toLowerCase()] || PaymentStatus.PENDING
    
    if (oldStatus !== newStatus) {
      await prisma.payment.update({
        where: { id: payment.id },
        data: {
          status: newStatus,
          metadata: {
            migrated: true,
            oldStatus,
            migratedAt: new Date().toISOString()
          }
        }
      })
      
      console.log(`Updated payment status ${payment.id}: ${oldStatus} -> ${newStatus}`)
    }
  }
}

async function updateOrderPaymentStatuses() {
  // Map old statuses to new ones
  const statusMap: Record<string, PaymentStatus> = {
    'pending': PaymentStatus.PENDING,
    'processing': PaymentStatus.PROCESSING,
    'completed': PaymentStatus.COMPLETED,
    'failed': PaymentStatus.FAILED,
    'cancelled': PaymentStatus.CANCELLED,
    'refunded': PaymentStatus.REFUNDED,
    'partially_refunded': PaymentStatus.PARTIALLY_REFUNDED,
    'expired': PaymentStatus.EXPIRED,
    'success': PaymentStatus.COMPLETED,
    'complete': PaymentStatus.COMPLETED,
    'error': PaymentStatus.FAILED,
    'canceled': PaymentStatus.CANCELLED
  }
  
  // Get all orders with payment status
  const orders = await prisma.order.findMany({
    where: {
      paymentStatus: {
        not: null
      }
    },
    select: {
      id: true,
      paymentStatus: true
    }
  })
  
  console.log(`Found ${orders.length} order payment statuses to update`)
  
  // Update each order
  for (const order of orders) {
    if (!order.paymentStatus) continue
    
    const oldStatus = order.paymentStatus
    const newStatus = statusMap[oldStatus.toLowerCase()] || PaymentStatus.PENDING
    
    if (oldStatus !== newStatus) {
      await prisma.order.update({
        where: { id: order.id },
        data: {
          paymentStatus: newStatus
        }
      })
      
      console.log(`Updated order payment status ${order.id}: ${oldStatus} -> ${newStatus}`)
    }
  }
}

// Run the migration
migratePaymentService().catch(error => {
  console.error('Unhandled error during migration:', error)
  process.exit(1)
}).finally(async () => {
  await prisma.$disconnect()
})