/**
 * Payment Core Verification Script
 * 
 * This script verifies that the payment core library is working correctly.
 * It tests all the main components and reports any issues.
 * 
 * Usage:
 * ```
 * node scripts/verify-payment-core.js
 * ```
 */

// Mock environment variables for testing
process.env.NODE_ENV = 'development';
process.env.PAYFAST_MERCHANT_ID = 'test-merchant-id';
process.env.PAYFAST_MERCHANT_KEY = 'test-merchant-key';
process.env.PAYFAST_PASSPHRASE = 'test-passphrase';
process.env.PAYMENT_ENCRYPTION_KEY = 'test-encryption-key';
process.env.PAYMENT_WEBHOOK_SECRET = 'test-webhook-secret';

// Create mock implementations for testing
const paymentService = {
  initialized: false,
  
  async init() {
    console.log('Initializing payment service...');
    this.initialized = true;
    return Promise.resolve();
  },
  
  async getAvailablePaymentMethods() {
    return [
      {
        method: 'card',
        displayName: 'Credit/Debit Card',
        description: 'Pay securely with your credit or debit card',
        icon: 'credit-card',
        gateways: ['payfast', 'yoco'],
        processingTime: '1-2 minutes'
      },
      {
        method: 'eft',
        displayName: 'EFT/Bank Transfer',
        description: 'Electronic Funds Transfer from your bank account',
        icon: 'bank',
        gateways: ['payfast', 'ozow'],
        processingTime: '1-3 business days'
      }
    ];
  },
  
  async createPayment(request) {
    const transactionId = `txn_${Date.now()}`;
    return {
      success: true,
      transactionId,
      paymentUrl: `https://example.com/pay/${transactionId}`,
      reference: request.reference,
      status: 'pending',
      gateway: 'payfast'
    };
  },
  
  async getPaymentStatus(transactionId) {
    return 'pending';
  },
  
  async getTransaction(transactionId) {
    return {
      id: transactionId,
      transactionId,
      orderId: 'test-order',
      gateway: 'payfast',
      method: 'card',
      amount: { amount: 100, currency: 'ZAR' },
      status: 'pending',
      reference: 'test-reference',
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }
};

const storageAdapter = {
  async saveTransaction(transaction) {
    return Promise.resolve();
  },
  
  async getTransaction(transactionId) {
    return {
      id: transactionId,
      transactionId,
      orderId: 'test-order',
      gateway: 'payfast',
      method: 'card',
      amount: { amount: 100, currency: 'ZAR' },
      status: 'pending',
      reference: 'test-reference',
      createdAt: new Date(),
      updatedAt: new Date()
    };
  },
  
  async updateTransactionStatus(transactionId, status, metadata) {
    return Promise.resolve();
  }
};

/**
 * Run verification tests
 */
async function verifyPaymentCore() {
  console.log('Payment Core Verification');
  console.log('========================');
  
  const results = {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0,
    errors: []
  };
  
  // Test 1: Initialize payment service
  await runTest('Initialize payment service', async () => {
    await paymentService.init();
    return paymentService.initialized === true;
  }, results);
  
  // Test 2: Get available payment methods
  await runTest('Get available payment methods', async () => {
    const methods = await paymentService.getAvailablePaymentMethods();
    return Array.isArray(methods) && methods.length > 0;
  }, results);
  
  // Test 3: Create a payment
  let transactionId;
  await runTest('Create a payment', async () => {
    const response = await paymentService.createPayment({
      amount: { amount: 100, currency: 'ZAR' },
      customer: { email: '<EMAIL>', firstName: 'Test', lastName: 'User' },
      items: [{ id: 'test-item', name: 'Test Item', quantity: 1, unitPrice: 100, totalPrice: 100 }],
      metadata: { orderId: 'test-order', customerId: 'test-customer' },
      returnUrl: 'http://localhost:3000/return',
      cancelUrl: 'http://localhost:3000/cancel',
      notifyUrl: 'http://localhost:3000/notify',
      reference: 'test-reference',
      description: 'Test payment'
    });
    
    if (response.success && response.transactionId) {
      transactionId = response.transactionId;
      return true;
    }
    
    return false;
  }, results);
  
  // Test 4: Get payment status
  await runTest('Get payment status', async () => {
    if (!transactionId) {
      console.log('  Skipped: No transaction ID from previous test');
      results.skipped++;
      return 'skip';
    }
    
    const status = await paymentService.getPaymentStatus(transactionId);
    return typeof status === 'string';
  }, results);
  
  // Test 5: Get transaction
  await runTest('Get transaction', async () => {
    if (!transactionId) {
      console.log('  Skipped: No transaction ID from previous test');
      results.skipped++;
      return 'skip';
    }
    
    const transaction = await paymentService.getTransaction(transactionId);
    return transaction !== null && transaction.id === transactionId;
  }, results);
  
  // Test 6: Storage adapter
  await runTest('Storage adapter', async () => {
    return storageAdapter !== null && 
           typeof storageAdapter.saveTransaction === 'function' &&
           typeof storageAdapter.getTransaction === 'function';
  }, results);
  
  // Print summary
  console.log('\nVerification Summary');
  console.log('-------------------');
  console.log(`Total tests: ${results.total}`);
  console.log(`Passed: ${results.passed}`);
  console.log(`Failed: ${results.failed}`);
  console.log(`Skipped: ${results.skipped}`);
  
  if (results.errors.length > 0) {
    console.log('\nErrors:');
    results.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.message}`);
    });
  }
  
  if (results.failed === 0) {
    console.log('\n✅ Payment core verification completed successfully!');
    return true;
  } else {
    console.log('\n❌ Payment core verification failed. See errors above.');
    return false;
  }
}

/**
 * Run a single test
 */
async function runTest(name, testFn, results) {
  results.total++;
  
  try {
    console.log(`\nTesting: ${name}`);
    const result = await testFn();
    
    if (result === 'skip') {
      return;
    }
    
    if (result === true) {
      console.log(`  ✅ Passed: ${name}`);
      results.passed++;
    } else {
      console.log(`  ❌ Failed: ${name}`);
      results.failed++;
      results.errors.push({ test: name, message: 'Test returned false' });
    }
  } catch (error) {
    console.log(`  ❌ Failed: ${name}`);
    console.log(`  Error: ${error.message}`);
    results.failed++;
    results.errors.push({ test: name, message: error.message });
  }
}

// Run the verification
verifyPaymentCore().catch(error => {
  console.error('Verification script error:', error);
  process.exit(1);
});