/**
 * Test Payment Service
 * 
 * This script tests the payment service implementation.
 * 
 * Usage:
 * ```
 * npx ts-node scripts/test-payment-service.ts
 * ```
 */

// Import directly from the specific files to avoid ESM issues
import { paymentService } from '../lib/payment-core/service'
import { PaymentGateway, PaymentMethod, PaymentStatus } from '../lib/payment-core/types'
import { generateTransactionId, generatePaymentReference } from '../lib/payment-core/utils'

async function testPaymentService() {
  try {
    console.log('Initializing payment service...')
    await paymentService.init()
    
    // Test 1: Get available payment methods
    console.log('\n--- Test 1: Get Available Payment Methods ---')
    const methods = await paymentService.getAvailablePaymentMethods()
    console.log(`Found ${methods.length} payment methods:`)
    methods.forEach(method => {
      console.log(`- ${method.displayName} (${method.method}): ${method.gateways.join(', ')}`)
    })
    
    // Test 2: Create a payment
    console.log('\n--- Test 2: Create Payment ---')
    const orderId = `order-${Date.now()}`
    const reference = generatePaymentReference(orderId)
    const transactionId = generateTransactionId()
    
    const paymentRequest = {
      amount: {
        amount: 100.00,
        currency: 'ZAR',
        formatted: 'R 100.00'
      },
      customer: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        phone: '+27123456789'
      },
      items: [
        {
          id: 'product-1',
          name: 'Test Product',
          quantity: 1,
          unitPrice: 100.00,
          totalPrice: 100.00
        }
      ],
      metadata: {
        orderId,
        customerId: 'customer-123',
        source: 'test-script'
      },
      returnUrl: 'http://localhost:3000/checkout/success',
      cancelUrl: 'http://localhost:3000/checkout/cancel',
      notifyUrl: 'http://localhost:3000/api/webhooks/payments',
      reference,
      description: 'Test payment',
      paymentMethod: PaymentMethod.CARD
    }
    
    const paymentResponse = await paymentService.createPayment(paymentRequest, PaymentGateway.PAYFAST)
    
    if (paymentResponse.success) {
      console.log('Payment created successfully!')
      console.log(`Transaction ID: ${paymentResponse.transactionId}`)
      console.log(`Payment URL: ${paymentResponse.paymentUrl}`)
      console.log(`Status: ${paymentResponse.status}`)
    } else {
      console.log('Payment creation failed:')
      console.log(`Error: ${paymentResponse.error?.message}`)
      console.log(`Code: ${paymentResponse.error?.code}`)
    }
    
    // Test 3: Get payment status
    console.log('\n--- Test 3: Get Payment Status ---')
    const statusTransactionId = paymentResponse.transactionId || transactionId
    const status = await paymentService.getPaymentStatus(statusTransactionId, PaymentGateway.PAYFAST)
    console.log(`Payment status for ${statusTransactionId}: ${status}`)
    
    // Test 4: Simulate webhook
    console.log('\n--- Test 4: Simulate Webhook ---')
    const webhookResponse = await paymentService.handleWebhook({
      gateway: PaymentGateway.PAYFAST,
      payload: {
        m_payment_id: statusTransactionId,
        pf_payment_id: 'PF12345',
        payment_status: 'COMPLETE',
        amount_gross: '100.00',
        amount_fee: '-2.90',
        amount_net: '97.10',
        custom_str1: reference,
        custom_str2: orderId,
        custom_str3: 'customer-123',
        item_name: 'Test payment',
        item_description: 'Test payment description',
        email_address: '<EMAIL>'
      }
    })
    
    if (webhookResponse.success) {
      console.log('Webhook processed successfully!')
      console.log(`Transaction ID: ${webhookResponse.transactionId}`)
      console.log(`Event: ${webhookResponse.event}`)
    } else {
      console.log('Webhook processing failed:')
      console.log(`Error: ${webhookResponse.error}`)
    }
    
    // Test 5: Get updated payment status
    console.log('\n--- Test 5: Get Updated Payment Status ---')
    const updatedStatus = await paymentService.getPaymentStatus(statusTransactionId, PaymentGateway.PAYFAST)
    console.log(`Updated payment status for ${statusTransactionId}: ${updatedStatus}`)
    
    // Test 6: Get transaction
    console.log('\n--- Test 6: Get Transaction ---')
    try {
      const transaction = await paymentService.getTransaction(statusTransactionId)
      if (transaction) {
        console.log('Transaction found:')
        console.log(`ID: ${transaction.id}`)
        console.log(`Order ID: ${transaction.orderId}`)
        console.log(`Status: ${transaction.status}`)
        console.log(`Amount: ${transaction.amount.amount} ${transaction.amount.currency}`)
        console.log(`Created: ${transaction.createdAt}`)
        console.log(`Updated: ${transaction.updatedAt}`)
      } else {
        console.log('Transaction not found')
      }
    } catch (error) {
      console.error('Error getting transaction:', error)
    }
    
    // Test 7: Process refund
    console.log('\n--- Test 7: Process Refund ---')
    const refundResponse = await paymentService.processRefund(
      statusTransactionId,
      50.00,
      'Customer requested partial refund',
      PaymentGateway.PAYFAST
    )
    
    if (refundResponse.success) {
      console.log('Refund processed successfully!')
      console.log(`Status: ${refundResponse.status}`)
      console.log(`Message: ${refundResponse.message}`)
    } else {
      console.log('Refund processing failed:')
      console.log(`Error: ${refundResponse.error?.message}`)
      console.log(`Code: ${refundResponse.error?.code}`)
    }
    
    console.log('\nAll tests completed!')
  } catch (error) {
    console.error('Test failed with error:', error)
  }
}

// Run the test
testPaymentService().catch(error => {
  console.error('Unhandled error:', error)
  process.exit(1)
})