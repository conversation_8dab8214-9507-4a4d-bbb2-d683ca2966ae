#!/usr/bin/env tsx

import { AppwriteServerService } from '../lib/appwrite/server'
import { config } from 'dotenv'

// Load environment variables
config()

async function testAppwriteConnection() {
  console.log('🚀 Testing Appwrite connection...')
  
  try {
    // Test environment variables
    console.log('📋 Checking environment variables...')
    const endpoint = process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT
    const projectId = process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID
    const apiKey = process.env.APPWRITE_API_KEY
    
    console.log(`Endpoint: ${endpoint ? '✅ Set' : '❌ Missing'}`)
    console.log(`Project ID: ${projectId ? '✅ Set' : '❌ Missing'}`)
    console.log(`API Key: ${apiKey ? '✅ Set' : '❌ Missing'}`)
    
    if (!endpoint || !projectId || !apiKey) {
      console.error('❌ Missing required environment variables')
      process.exit(1)
    }
    
    // Test AppwriteServerService initialization
    console.log('\n🔧 Testing Appwrite server service initialization...')
    const serverService = AppwriteServerService.getInstance()
    
    // Test basic connectivity by listing buckets
    console.log('\n🗂️ Testing storage connectivity...')
    const buckets = await serverService.listBuckets()
    console.log(`✅ Successfully connected! Found ${buckets.total} bucket(s)`)
    
    // Test media bucket setup
    console.log('\n📁 Testing media bucket setup...')
    const mediaSetup = await serverService.setupMediaLibrary()
    console.log('✅ Media library infrastructure verified/created successfully!')
    
    console.log('\n🎉 All Appwrite tests passed!')
    
  } catch (error) {
    console.error('❌ Appwrite connection test failed:', error)
    
    if (error instanceof Error) {
      console.error('Error details:', error.message)
      if (error.stack) {
        console.error('Stack trace:', error.stack)
      }
    }
    
    process.exit(1)
  }
}

if (require.main === module) {
  testAppwriteConnection()
}

export { testAppwriteConnection }