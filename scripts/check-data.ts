import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  // Count users
  const userCount = await prisma.user.count();
  console.log(`Total users: ${userCount}`);

  // Count products
  const productCount = await prisma.product.count();
  console.log(`Total products: ${productCount}`);

  // Count product variants
  const variantCount = await prisma.productVariant.count();
  console.log(`Total product variants: ${variantCount}`);

  // Count orders
  const orderCount = await prisma.order.count();
  console.log(`Total orders: ${orderCount}`);

  // Count order items
  const orderItemCount = await prisma.orderItem.count();
  console.log(`Total order items: ${orderItemCount}`);

  // Count payments
  const paymentCount = await prisma.payment.count();
  console.log(`Total payments: ${paymentCount}`);

  // Count fulfillments
  const fulfillmentCount = await prisma.orderFulfillment.count();
  console.log(`Total fulfillments: ${fulfillmentCount}`);

  // Count fulfillment items
  const fulfillmentItemCount = await prisma.orderFulfillmentItem.count();
  console.log(`Total fulfillment items: ${fulfillmentItemCount}`);
}

main()
  .catch((e) => {
    console.error('Error checking data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });