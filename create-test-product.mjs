import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function createTestProduct() {
  try {
    // Check if test product already exists
    const existingProduct = await prisma.product.findUnique({
      where: { slug: 'test-product' }
    })

    if (existingProduct) {
      console.log('Test product already exists:', existingProduct.id)
      return existingProduct
    }

    const product = await prisma.product.create({
      data: {
        title: 'Test Product',
        slug: 'test-product',
        description: 'A test product for cart functionality',
        handle: 'test-product',
        status: 'active',
        price: 99.99,
        currency: 'ZAR',
        trackQuantity: true,
        inventoryQuantity: 100,
        isAvailable: true,
        availableForSale: true,
        isVisible: true,
        requiresShipping: true,
        isTaxable: true
      }
    })
    
    console.log('Test product created:', product.id)
    return product
  } catch (error) {
    console.error('Error creating test product:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestProduct()