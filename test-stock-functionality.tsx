import React from 'react'
import { ProductCard } from './components/storefront/products/product-card'
import { Product } from './lib/ecommerce/types/product'

// Test products with different stock levels
const testProducts: Product[] = [
  {
    id: "test-1",
    title: "In Stock Product",
    slug: "in-stock-product",
    description: "A product with plenty of stock",
    handle: "in-stock-product",
    status: "active",
    price: { amount: 299, currency: "ZAR" },
    compareAtPrice: { amount: 399, currency: "ZAR" },
    trackQuantity: true,
    continueSellingWhenOutOfStock: false,
    inventoryQuantity: 25,
    images: [
      {
        id: "img-1",
        url: "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg",
        altText: "In Stock Product",
        position: 1
      }
    ],
    hasVariants: false,
    variants: [],
    options: [],
    categories: [],
    tags: [],
    collections: [],
    seo: {},
    isGiftCard: false,
    requiresShipping: true,
    isTaxable: true,
    isVisible: true,
    isAvailable: true,
    availableForSale: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "test-2",
    title: "Low Stock Product",
    slug: "low-stock-product",
    description: "A product with low stock",
    handle: "low-stock-product",
    status: "active",
    price: { amount: 199, currency: "ZAR" },
    trackQuantity: true,
    continueSellingWhenOutOfStock: false,
    inventoryQuantity: 3, // Low stock (≤ 5)
    images: [
      {
        id: "img-2",
        url: "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg",
        altText: "Low Stock Product",
        position: 1
      }
    ],
    hasVariants: false,
    variants: [],
    options: [],
    categories: [],
    tags: [],
    collections: [],
    seo: {},
    isGiftCard: false,
    requiresShipping: true,
    isTaxable: true,
    isVisible: true,
    isAvailable: true,
    availableForSale: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "test-3",
    title: "Out of Stock Product",
    slug: "out-of-stock-product",
    description: "A product that's out of stock",
    handle: "out-of-stock-product",
    status: "active",
    price: { amount: 450, currency: "ZAR" },
    trackQuantity: true,
    continueSellingWhenOutOfStock: false,
    inventoryQuantity: 0, // Out of stock
    images: [
      {
        id: "img-3",
        url: "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg",
        altText: "Out of Stock Product",
        position: 1
      }
    ],
    hasVariants: false,
    variants: [],
    options: [],
    categories: [],
    tags: [],
    collections: [],
    seo: {},
    isGiftCard: false,
    requiresShipping: true,
    isTaxable: true,
    isVisible: true,
    isAvailable: true,
    availableForSale: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "test-4",
    title: "Backorder Product",
    slug: "backorder-product",
    description: "A product available on backorder",
    handle: "backorder-product",
    status: "active",
    price: { amount: 350, currency: "ZAR" },
    trackQuantity: true,
    continueSellingWhenOutOfStock: true, // Allow backorders
    inventoryQuantity: 0,
    images: [
      {
        id: "img-4",
        url: "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg",
        altText: "Backorder Product",
        position: 1
      }
    ],
    hasVariants: false,
    variants: [],
    options: [],
    categories: [],
    tags: [],
    collections: [],
    seo: {},
    isGiftCard: false,
    requiresShipping: true,
    isTaxable: true,
    isVisible: true,
    isAvailable: true,
    availableForSale: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "test-5",
    title: "No Tracking Product",
    slug: "no-tracking-product",
    description: "A product without inventory tracking",
    handle: "no-tracking-product",
    status: "active",
    price: { amount: 250, currency: "ZAR" },
    trackQuantity: false, // No inventory tracking
    continueSellingWhenOutOfStock: false,
    inventoryQuantity: 0,
    images: [
      {
        id: "img-5",
        url: "/assets/images/cocomilk_kids-20221116_113539-1613711150.jpg",
        altText: "No Tracking Product",
        position: 1
      }
    ],
    hasVariants: false,
    variants: [],
    options: [],
    categories: [],
    tags: [],
    collections: [],
    seo: {},
    isGiftCard: false,
    requiresShipping: true,
    isTaxable: true,
    isVisible: true,
    isAvailable: true,
    availableForSale: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]

export function StockFunctionalityTest() {
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Stock Functionality Test</h1>
      <p className="text-gray-600 mb-8">
        This page demonstrates the enhanced stock functionality in product cards.
        Each product shows different stock levels and behaviors:
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
        {testProducts.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            showQuickActions={true}
            showRating={true}
            showCompare={true}
            variant="default"
          />
        ))}
      </div>
      
      <div className="mt-12 bg-gray-50 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Stock Status Legend</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-sm"><strong>In Stock:</strong> Product has sufficient inventory (&gt;5 units)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span className="text-sm"><strong>Low Stock:</strong> Product has limited inventory (≤5 units)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <span className="text-sm"><strong>Out of Stock:</strong> Product is unavailable (0 units)</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span className="text-sm"><strong>Backorder:</strong> Product can be ordered despite being out of stock</span>
          </div>
        </div>
        
        <div className="mt-6">
          <h3 className="text-lg font-medium mb-2">Features Implemented:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
            <li>Real-time stock level display based on inventory data</li>
            <li>Color-coded stock indicators (green, orange, red, blue)</li>
            <li>Dynamic "Add to Cart" button states</li>
            <li>Stock quantity display for tracked products</li>
            <li>Backorder support for out-of-stock items</li>
            <li>Disabled cart functionality for unavailable items</li>
            <li>Urgency indicators for low stock items</li>
            <li>Hover effects that show/hide stock information</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

export default StockFunctionalityTest