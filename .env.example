# WordPress/WooCommerce Configuration
WORDPRESS_URL=https://your-wordpress-site.com
WOOCOMMERCE_CONSUMER_KEY=ck_your_consumer_key_here
WOOCOMMERCE_CONSUMER_SECRET=cs_your_consumer_secret_here

# WordPress Authentication
WORDPRESS_JWT_SECRET=your_jwt_secret_here
WORDPRESS_APPLICATION_PASSWORD_USERNAME=your_wp_username
WORDPRESS_APPLICATION_PASSWORD=your_application_password
WORDPRESS_USERNAME=your_wp_username
WORDPRESS_PASSWORD=your_application_password

# OpenAI Configuration (existing)
OPENAI_API_KEY=your_openai_api_key_here

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret_here

# Admin Authentication
JWT_SECRET=your_jwt_secret_for_admin_auth_here_32_chars_min

# Master Admin Credentials (Emergency Access)
ENABLE_MASTER_ACCESS=true
MASTER_ADMIN_EMAIL=<EMAIL>
MASTER_ADMIN_PASSWORD=CocoMaster2024!

# Currency Configuration
DEFAULT_CURRENCY=ZAR
CURRENCY_SYMBOL=R

# Payment Gateway Configuration
# PayFast (South Africa's leading payment gateway)
PAYFAST_MERCHANT_ID=30410597
PAYFAST_MERCHANT_KEY=frwh0zepghtgk
PAYFAST_PASSPHRASE=your_payfast_passphrase
PAYFAST_SANDBOX=true

# Ozow (Instant EFT payments)
OZOW_API_KEY=your_ozow_api_key
OZOW_PRIVATE_KEY=your_ozow_private_key
OZOW_SITE_CODE=your_ozow_site_code
OZOW_SANDBOX=true

# SnapScan (QR code payments)
SNAPSCAN_API_KEY=your_snapscan_api_key
SNAPSCAN_MERCHANT_ID=your_snapscan_merchant_id
SNAPSCAN_SANDBOX=true

# Yoco (Card payments and POS)
YOCO_SECRET_KEY=your_yoco_secret_key
YOCO_PUBLIC_KEY=your_yoco_public_key
YOCO_SANDBOX=true

# PayU (International and local card processing)
PAYU_API_KEY=your_payu_api_key
PAYU_SAFE_KEY=your_payu_safe_key
PAYU_MERCHANT_ID=your_payu_merchant_id
PAYU_SANDBOX=true

# Zapper (QR code payments)
ZAPPER_MERCHANT_ID=your_zapper_merchant_id
ZAPPER_API_KEY=your_zapper_api_key
ZAPPER_SITE_ID=your_zapper_site_id
ZAPPER_SANDBOX=true

# Payment Core Configuration
PAYMENT_WEBHOOK_SECRET=your_webhook_secret_key_32_chars_min
PAYMENT_DEFAULT_GATEWAY=payfast
PAYMENT_DEFAULT_CURRENCY=ZAR
PAYMENT_AUTO_RETRY=true
PAYMENT_MAX_RETRIES=3
PAYMENT_RETRY_DELAY=1000
PAYMENT_LOG_LEVEL=info
PAYMENT_LOG_TO_CONSOLE=true
PAYMENT_LOG_TO_FILE=false
PAYMENT_LOG_FILE_PATH=./logs/payment.log
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Payment Security
PAYMENT_ENCRYPTION_KEY=your_payment_encryption_key_32_chars_min
PAYMENT_RATE_LIMIT_WINDOW=900000
PAYMENT_RATE_LIMIT_MAX=100

# South African Business Details
SA_VAT_NUMBER=your_vat_number
SA_BUSINESS_REGISTRATION=your_business_registration_number
SA_VAT_REGISTRATION=your_vat_registration_number

# Shipping Provider Configuration
# The Courier Guy
COURIER_GUY_USERNAME=your_courier_guy_username
COURIER_GUY_PASSWORD=your_courier_guy_password
COURIER_GUY_ACCOUNT=your_courier_guy_account_number

# PostNet
POSTNET_API_KEY=your_postnet_api_key
POSTNET_ACCOUNT=your_postnet_account_number

# Aramex
ARAMEX_USERNAME=your_aramex_username
ARAMEX_PASSWORD=your_aramex_password
ARAMEX_ACCOUNT=your_aramex_account_number
ARAMEX_PIN=your_aramex_pin
ARAMEX_ENTITY=your_aramex_entity

# Email Configuration
# SMTP
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# SendGrid
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=Coco Milk Kids

# SMS Configuration
# Clickatell
CLICKATELL_API_KEY=your_clickatell_api_key
CLICKATELL_FROM_NUMBER=your_from_number

# Twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=your_twilio_phone_number

# Push Notifications
# Firebase Cloud Messaging
FCM_SERVER_KEY=your_fcm_server_key
FCM_SENDER_ID=your_fcm_sender_id

# Analytics Configuration
GOOGLE_ANALYTICS_ID=your_google_analytics_id
FACEBOOK_PIXEL_ID=your_facebook_pixel_id

# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_appwrite_project_id
APPWRITE_API_KEY=your_appwrite_api_key
NEXT_PUBLIC_APPWRITE_DATABASE_ID=your_database_id
NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=your_storage_bucket_id

# Appwrite Media Library Configuration
NEXT_PUBLIC_APPWRITE_MEDIA_BUCKET_ID=media
NEXT_PUBLIC_APPWRITE_MEDIA_COLLECTION_ID=media-metadata
