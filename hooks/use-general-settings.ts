import useSWR from 'swr';
import axios from 'axios';

const fetcher = (url: string) => axios.get(url).then((res) => res.data);

export const useGeneralSettings = () => {
  const { data, error, mutate } = useSWR('/api/settings/general', fetcher);

  const createOrUpdateSettings = async (settings: {
    siteName: string;
    siteDescription: string;
  }) => {
    try {
      const response = await axios.post('/api/settings/general', settings);
      mutate(response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to save settings:', error);
      throw error;
    }
  };

  return {
    settings: data,
    isLoading: !error && !data,
    isError: error,
    createOrUpdateSettings,
  };
};
