import { useState, useCallback, useEffect } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'

interface Product {
  id: string
  status: 'active' | 'draft' | 'archived'
  price: number
  inventoryQuantity: number
  updatedAt: string
  // Add other product properties as needed
}

interface ApiResponse<T> {
  success: boolean
  data: T
  error?: string
}

interface ProductFilters {
  status?: ('active' | 'draft' | 'archived')[]
  categoryIds?: string[]
  priceRange?: { min: number; max: number } | null
  inStock?: boolean
  query?: string
  sort?: {
    field: string
    direction: 'asc' | 'desc'
  }
  page?: number
  limit?: number
}

export function useProducts(initialParams: ProductFilters = {}) {
  const queryClient = useQueryClient()
  const [params, setParams] = useState<ProductFilters>(initialParams)

  const buildQueryParams = useCallback((searchParams: ProductFilters) => {
    const params = new URLSearchParams()
    
    if (searchParams.query) params.append('q', searchParams.query)
    if (searchParams.status?.length) params.append('status', searchParams.status.join(','))
    if (searchParams.categoryIds?.length) params.append('categories', searchParams.categoryIds.join(','))
    if (searchParams.inStock !== undefined) params.append('inStock', String(searchParams.inStock))
    if (searchParams.sort?.field) params.append('sortBy', searchParams.sort.field)
    if (searchParams.sort?.direction) params.append('sortDir', searchParams.sort.direction)
    if (searchParams.page) params.append('page', searchParams.page.toString())
    if (searchParams.limit) params.append('limit', searchParams.limit.toString())
    
    return params.toString()
  }, [])

  const fetchProducts = useCallback(async (): Promise<Product[]> => {
    try {
      const queryString = buildQueryParams(params)
      const response = await fetch(`/api/admin/products?${queryString}`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data: ApiResponse<Product[]> = await response.json()
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch products')
      }

      return data.data
    } catch (error) {
      console.error('Error in fetchProducts:', error)
      throw error
    }
  }, [params, buildQueryParams])

  const searchProducts = useCallback((searchParams: ProductFilters) => {
    setParams(prev => ({
      ...prev,
      ...searchParams,
      page: searchParams.page !== undefined ? searchParams.page : 1
    }))
  }, [])

  const { 
    data: products = [],
    isLoading,
    error,
    refetch
  } = useQuery<Product[], Error>({
    queryKey: ['products', params],
    queryFn: fetchProducts,
    refetchOnWindowFocus: false,
    retry: 2
  })

  return {
    products,
    isLoading,
    error: error || undefined,
    refetch,
  }
}

interface ProductStats {
  total: number
  active: number
  draft: number
  lowStock: number
  outOfStock: number
  totalValue: number
  averagePrice: number
  recentlyUpdated: number
}

export function useProductStats() {
  const [stats, setStats] = useState<ProductStats>({
    total: 0,
    active: 0,
    draft: 0,
    lowStock: 0,
    outOfStock: 0,
    totalValue: 0,
    averagePrice: 0,
    recentlyUpdated: 0
  })

  const { products: allProducts, isLoading: allLoading, error: allError } = useProducts({})
  const { products: lowStockProducts, isLoading: lowStockLoading, error: lowStockError } = useProducts({ inventoryStatus: 'low' })
  const { products: outOfStockProducts, isLoading: outOfStockLoading, error: outOfStockError } = useProducts({ inventoryStatus: 'out' })

  useEffect(() => {
    if (!allLoading && !lowStockLoading && !outOfStockLoading) {
      const totalProducts = allProducts.length
      const activeProducts = allProducts.filter(p => p.status === 'active').length
      const draftProducts = allProducts.filter(p => p.status === 'draft').length
      const totalValue = allProducts.reduce((sum, product) => sum + (product.price || 0) * (product.inventoryQuantity || 0), 0)
      const averagePrice = totalProducts > 0 
        ? Math.round((allProducts.reduce((sum, product) => sum + (product.price || 0), 0) / totalProducts) * 100) / 100 
        : 0
      
      // Get products updated in the last 7 days
      const recentlyUpdated = allProducts.filter(product => {
        const updatedAt = new Date(product.updatedAt)
        const weekAgo = new Date()
        weekAgo.setDate(weekAgo.getDate() - 7)
        return updatedAt > weekAgo
      }).length

      setStats({
        total: totalProducts,
        active: activeProducts,
        draft: draftProducts,
        lowStock: lowStockProducts.length,
        outOfStock: outOfStockProducts.length,
        totalValue,
        averagePrice,
        recentlyUpdated
      })
    }
  }, [allProducts, lowStockProducts, outOfStockProducts, allLoading, lowStockLoading, outOfStockLoading])

  const error = allError || lowStockError || outOfStockError
  const isLoading = allLoading || lowStockLoading || outOfStockLoading

  return {
    stats,
    isLoading,
    error
  }
}
