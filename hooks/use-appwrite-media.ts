import { useState, useEffect } from 'react';
import { useMediaSettings } from './use-media-settings';
import { MediaFile, MediaLibrary, MediaUploadOptions } from '@/lib/appwrite/media';
import { toast } from 'sonner';

export function useAppwriteMedia() {
  const { settings, isLoading: isLoadingSettings } = useMediaSettings();
  const [mediaLibrary, setMediaLibrary] = useState<MediaLibrary | null>(null);
  const [files, setFiles] = useState<MediaFile[]>([]);
  const [totalFiles, setTotalFiles] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  
  // Initialize media library when settings are loaded
  useEffect(() => {
    if (settings && settings.storageProvider === 'appwrite' && settings.appwriteSettings) {
      const bucketId = settings.appwriteSettings.bucketId;
      setMediaLibrary(new MediaLibrary(bucketId));
    }
  }, [settings]);
  
  // Load files
  const loadFiles = async (
    limit: number = 25, 
    offset: number = 0, 
    filters: { 
      type?: 'image' | 'video' | 'audio' | 'document' | 'all';
      folder?: string;
      search?: string;
    } = {}
  ) => {
    if (!mediaLibrary) return;
    
    setIsLoading(true);
    
    try {
      const result = await mediaLibrary.getFiles(filters, limit, offset);
      setFiles(result.files);
      setTotalFiles(result.total);
    } catch (error) {
      console.error('Error loading files:', error);
      toast.error('Failed to load media files');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Upload file
  const uploadFile = async (options: MediaUploadOptions) => {
    if (!mediaLibrary) {
      toast.error('Media library not initialized');
      return null;
    }
    
    setIsUploading(true);
    
    try {
      const file = await mediaLibrary.uploadFile(options);
      
      // Refresh the file list
      loadFiles();
      
      toast.success('File uploaded successfully');
      return file;
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error('Failed to upload file');
      return null;
    } finally {
      setIsUploading(false);
    }
  };
  
  // Delete file
  const deleteFile = async (fileId: string) => {
    if (!mediaLibrary) {
      toast.error('Media library not initialized');
      return false;
    }
    
    try {
      await mediaLibrary.deleteFile(fileId);
      
      // Remove file from state
      setFiles(files.filter(file => file.$id !== fileId));
      setTotalFiles(prev => prev - 1);
      
      toast.success('File deleted successfully');
      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      toast.error('Failed to delete file');
      return false;
    }
  };
  
  // Get file URL
  const getFileUrl = (fileId: string) => {
    if (!mediaLibrary) return '';
    return mediaLibrary.getFileUrl(fileId);
  };
  
  // Get file preview URL
  const getFilePreview = (
    fileId: string, 
    width: number = 400, 
    height: number = 300,
    options?: {
      gravity?: 'center' | 'top-left' | 'top' | 'top-right' | 'left' | 'right' | 'bottom-left' | 'bottom' | 'bottom-right';
      quality?: number;
      borderWidth?: number;
      borderColor?: string;
      borderRadius?: number;
      opacity?: number;
      rotation?: number;
      background?: string;
      output?: 'jpg' | 'jpeg' | 'png' | 'gif' | 'webp';
    }
  ) => {
    if (!mediaLibrary) return '';
    
    return mediaLibrary.getFilePreview(
      fileId, 
      width, 
      height,
      options?.gravity || 'center',
      options?.quality || 100,
      options?.borderWidth || 0,
      options?.borderColor || '',
      options?.borderRadius || 0,
      options?.opacity || 1,
      options?.rotation || 0,
      options?.background || '',
      options?.output || 'webp'
    );
  };
  
  // Format file size
  const formatFileSize = (bytes: number) => {
    return MediaLibrary.formatFileSize(bytes);
  };
  
  // Get file type icon
  const getFileTypeIcon = (mimeType: string) => {
    return MediaLibrary.getFileTypeIcon(mimeType);
  };
  
  return {
    files,
    totalFiles,
    isLoading: isLoading || isLoadingSettings,
    isUploading,
    loadFiles,
    uploadFile,
    deleteFile,
    getFileUrl,
    getFilePreview,
    formatFileSize,
    getFileTypeIcon,
    isInitialized: !!mediaLibrary,
  };
}