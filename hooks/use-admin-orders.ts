'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { AdminOrdersResponse, OrderFilters } from '@/types/admin'

export function useAdminOrders() {
  const router = useRouter()
  const searchParams = useSearchParams()

  const [data, setData] = useState<AdminOrdersResponse['data'] | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [filters, setFilters] = useState<OrderFilters>(() => {
    const params = new URLSearchParams(searchParams.toString())
    return {
      page: parseInt(params.get('page') || '1'),
      limit: parseInt(params.get('limit') || '10'),
      status: params.get('status') || undefined,
      paymentStatus: params.get('paymentStatus') || undefined,
      customerEmail: params.get('customerEmail') || undefined,
      orderNumber: params.get('orderNumber') || undefined,
      startDate: params.get('startDate') || undefined,
      endDate: params.get('endDate') || undefined,
    }
  })

  const fetchOrders = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.set(key, String(value))
        }
      })

      const response = await fetch(`/api/admin/orders?${params.toString()}`)
      const result: AdminOrdersResponse = await response.json()

      if (result.success) {
        setData(result.data)
      } else {
        throw new Error('Failed to fetch orders')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
    } finally {
      setIsLoading(false)
    }
  }, [filters])

  useEffect(() => {
    fetchOrders()
  }, [fetchOrders])

  useEffect(() => {
    const params = new URLSearchParams()
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        params.set(key, String(value))
      }
    })
    router.push(`?${params.toString()}`, { scroll: false })
  }, [filters, router])

  const handleFilterChange = (newFilters: Partial<OrderFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }))
  }

  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }))
  }

  return {
    data,
    isLoading,
    error,
    filters,
    handleFilterChange,
    handlePageChange,
  }
}
