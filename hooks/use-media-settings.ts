import { useState } from 'react';
import useSWR from 'swr';
import { toast } from 'sonner';

// Define the MediaSettings type directly here to avoid importing server-side code
export interface MediaSettings {
  storageProvider: 'local' | 's3' | 'cloudinary' | 'google' | 'appwrite';
  localSettings?: {
    uploadDir: string;
    maxFileSize: number;
    allowedFileTypes: string;
  };
  s3Settings?: {
    accessKeyId: string;
    secretAccessKey: string;
    region: string;
    bucket: string;
    endpoint?: string;
  };
  cloudinarySettings?: {
    cloudName: string;
    apiKey: string;
    apiSecret: string;
    uploadPreset?: string;
  };
  googleSettings?: {
    projectId: string;
    bucketName: string;
    keyFilePath: string;
  };
  appwriteSettings?: {
    bucketId: string;
    maximumFileSize: number;
    allowedFileExtensions: string[];
    compression: boolean;
    encryption: boolean;
    antivirus: boolean;
  };
  imageOptimization: {
    enabled: boolean;
    quality?: number;
    maxWidth?: number;
    maxHeight?: number;
    formats?: string[];
  };
  cacheControl: {
    enabled: boolean;
    maxAge?: number;
    staleWhileRevalidate?: number;
  };
}

const fetcher = async (url: string) => {
  const res = await fetch(url);
  
  if (!res.ok) {
    const error = new Error('An error occurred while fetching the data.');
    throw error;
  }
  
  return res.json();
};

export function useMediaSettings() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { 
    data: settings, 
    error, 
    isLoading, 
    mutate 
  } = useSWR<MediaSettings>('/api/settings/media', fetcher);
  
  const updateSettings = async (newSettings: MediaSettings) => {
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/settings/media', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(newSettings),
      });
      
      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }
      
      const updatedSettings = await response.json();
      
      // Update the local data without revalidating
      mutate(updatedSettings, false);
      
      toast.success('Media settings updated successfully');
      
      return updatedSettings;
    } catch (error) {
      console.error('Error updating media settings:', error);
      toast.error('Failed to update media settings');
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return {
    settings,
    isLoading,
    error,
    isSubmitting,
    updateSettings,
  };
}