"use client"

import { useContext } from "react"
import { CartContext } from "@/components/storefront/cart/cart-provider"

/**
 * @deprecated This hook is deprecated and will be removed in a future version.
 * Please import useCart from '@/lib/ecommerce/hooks/use-cart' instead.
 */
export function useCart() {
  console.warn(
    "Warning: The useCart hook from @/hooks/use-cart is deprecated. " +
    "Please import useCart from '@/lib/ecommerce/hooks/use-cart' instead."
  )
  
  const context = useContext(CartContext)
  if (context === undefined) {
    throw new Error("useCart must be used within a CartProvider")
  }
  return context
}
