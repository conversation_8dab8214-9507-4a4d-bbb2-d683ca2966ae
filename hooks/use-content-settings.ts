import useSWR from 'swr';
import axios from 'axios';

const fetcher = (url: string) => axios.get(url).then((res) => res.data);

export const useContentSettings = () => {
  const { data, error, mutate } = useSWR('/api/settings/content', fetcher);

  const createOrUpdateSettings = async (settings: {
    enableComments: boolean;
    postsPerPage: number;
  }) => {
    try {
      const response = await axios.post('/api/settings/content', settings);
      mutate(response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to save content settings:', error);
      throw error;
    }
  };

  return {
    settings: data,
    isLoading: !error && !data,
    isError: error,
    createOrUpdateSettings,
  };
};
