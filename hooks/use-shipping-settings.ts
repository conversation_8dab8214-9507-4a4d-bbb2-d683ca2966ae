import useSWR from 'swr';
import axios from 'axios';

const fetcher = (url: string) => axios.get(url).then((res) => res.data);

export const useShippingSettings = () => {
  const { data, error, mutate } = useSWR('/api/settings/shipping', fetcher);

  const createOrUpdateSettings = async (settings: {
    enableFreeShipping: boolean;
    freeShippingThreshold: number;
    flatRateShipping: boolean;
    flatRateAmount: number;
    enableLocalPickup: boolean;
  }) => {
    try {
      const response = await axios.post('/api/settings/shipping', settings);
      mutate(response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to save shipping settings:', error);
      throw error;
    }
  };

  return {
    settings: data,
    isLoading: !error && !data,
    isError: error,
    createOrUpdateSettings,
  };
};