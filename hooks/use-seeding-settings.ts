import useSWR from 'swr';
import axios from 'axios';

const fetcher = (url: string) => axios.get(url).then((res) => res.data);

export interface SeedingSettings {
  enableDemoData: boolean;
  seedCategories: boolean;
  seedProducts: number;
  seedCustomers: number;
  seedOrders: number;
  seedReviews: number;
  preserveExistingData: boolean;
  seedingEnvironment: 'development' | 'staging' | 'production';
}

export interface SeederRunOptions {
  type: 'products' | 'customers' | 'orders' | 'reviews' | 'categories' | 'all';
  count?: number;
  options?: {
    withImages?: boolean;
    withVariations?: boolean;
    randomPrices?: boolean;
    statusDistribution?: Record<string, number>;
  };
}

export const useSeedingSettings = () => {
  const { data, error, mutate } = useSWR<SeedingSettings>('/api/settings/seeding', fetcher);

  const createOrUpdateSettings = async (settings: SeedingSettings) => {
    try {
      const response = await axios.post('/api/settings/seeding', settings);
      mutate(response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to save seeding settings:', error);
      throw error;
    }
  };

  const runSeeder = async (options: SeederRunOptions) => {
    try {
      const response = await axios.post('/api/settings/seeding/run', options);
      return response.data;
    } catch (error) {
      console.error('Failed to run seeder:', error);
      throw error;
    }
  };

  const clearData = async (dataType?: 'products' | 'customers' | 'orders' | 'reviews' | 'categories' | 'all') => {
    try {
      const response = await axios.delete('/api/settings/seeding/clear', {
        data: { type: dataType || 'all' }
      });
      return response.data;
    } catch (error) {
      console.error('Failed to clear data:', error);
      throw error;
    }
  };

  const getSeedingStatus = async () => {
    try {
      const response = await axios.get('/api/settings/seeding/status');
      return response.data;
    } catch (error) {
      console.error('Failed to get seeding status:', error);
      throw error;
    }
  };

  return {
    settings: data,
    isLoading: !error && !data,
    isError: error,
    createOrUpdateSettings,
    runSeeder,
    clearData,
    getSeedingStatus
  };
};