import useSWR from 'swr';
import axios from 'axios';

const fetcher = (url: string) => axios.get(url).then((res) => res.data);

export const useSecuritySettings = () => {
  const { data, error, mutate } = useSWR('/api/settings/security', fetcher);

  const createOrUpdateSettings = async (settings: {
    enableTwoFactorAuth: boolean;
    passwordPolicy: {
      minLength: number;
      requireSpecialChar: boolean;
      requireNumber: boolean;
      requireUppercase: boolean;
    };
    sessionTimeout: number;
  }) => {
    try {
      const response = await axios.post('/api/settings/security', settings);
      mutate(response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to save security settings:', error);
      throw error;
    }
  };

  return {
    settings: data,
    isLoading: !error && !data,
    isError: error,
    createOrUpdateSettings,
  };
};