'use client';

import { useState, useCallback } from 'react';
import { useAdminUI } from '@/stores/use-admin-ui';

/**
 * Hook for sending admin notifications
 * 
 * This hook provides a simple interface for sending notifications to admin users
 * based on the configured notification settings.
 * Uses API routes instead of directly calling the notification library.
 */
export const useAdminNotifications = () => {
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const addNotification = useAdminUI((state) => state.addNotification);

  /**
   * Send a notification to admin users
   */
  const sendNotification = useCallback(async ({
    title,
    message,
    type = 'info',
    channels = ['IN_APP'],
    data = {},
  }: {
    title: string;
    message: string;
    type?: 'info' | 'success' | 'warning' | 'error';
    channels?: Array<'EMAIL' | 'IN_APP' | 'PUSH'>;
    data?: Record<string, any>;
  }) => {
    setSending(true);
    setError(null);

    try {
      // Always add to admin UI for immediate feedback
      addNotification({
        type,
        title,
        message,
      });

      // Send through the admin notifications API
      const response = await fetch('/api/notifications/admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title,
          message,
          type,
          channels,
          data,
        }),
      });

      const result = await response.json();
      
      if (!result.success) {
        console.error('Failed to send notifications:', result.error);
        setError(result.error || 'Failed to send notifications');
        return false;
      }

      return true;
    } catch (err) {
      console.error('Failed to send notification:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
      return false;
    } finally {
      setSending(false);
    }
  }, [addNotification]);

  /**
   * Send a notification about a new order
   */
  const notifyNewOrder = useCallback(async (orderId: string, customerName: string, total: string) => {
    return sendNotification({
      title: 'New Order Received',
      message: `Order #${orderId} from ${customerName} for ${total}`,
      type: 'info',
      data: { orderId, customerName, total },
    });
  }, [sendNotification]);

  /**
   * Send a notification about an order status change
   */
  const notifyOrderStatusChange = useCallback(async (orderId: string, status: string) => {
    return sendNotification({
      title: 'Order Status Updated',
      message: `Order #${orderId} status changed to ${status}`,
      type: 'info',
      data: { orderId, status },
    });
  }, [sendNotification]);

  /**
   * Send a notification about low stock
   */
  const notifyLowStock = useCallback(async (productId: string, productName: string, quantity: number) => {
    return sendNotification({
      title: 'Low Stock Alert',
      message: `${productName} is running low (${quantity} remaining)`,
      type: 'warning',
      data: { productId, productName, quantity },
    });
  }, [sendNotification]);

  /**
   * Send a notification about a payment
   */
  const notifyPayment = useCallback(async (orderId: string, amount: string, status: 'success' | 'failed') => {
    return sendNotification({
      title: status === 'success' ? 'Payment Received' : 'Payment Failed',
      message: status === 'success' 
        ? `Payment of ${amount} received for order #${orderId}` 
        : `Payment of ${amount} failed for order #${orderId}`,
      type: status === 'success' ? 'success' : 'error',
      data: { orderId, amount, status },
    });
  }, [sendNotification]);

  return {
    sendNotification,
    notifyNewOrder,
    notifyOrderStatusChange,
    notifyLowStock,
    notifyPayment,
    sending,
    error,
    clearError: () => setError(null),
  };
};