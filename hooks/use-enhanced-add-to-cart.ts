// Enhanced Add to Cart hook with comprehensive error handling
'use client'

import { useState, useCallback } from 'react'
import { useCart } from '@/lib/ecommerce/hooks/use-cart'
import { <PERSON>t<PERSON><PERSON>r<PERSON><PERSON><PERSON>, CartSuccess<PERSON>and<PERSON> } from '@/lib/ecommerce/utils/cart-error-handler'

export interface AddToCartInput {
  productId: string
  variantId?: string
  quantity?: number
  customAttributes?: Record<string, any>
  productName?: string
  variantName?: string
}

export interface UseEnhancedAddToCartOptions {
  userId?: string
  sessionId?: string
  onSuccess?: (productName: string) => void
  onError?: (error: any) => void
  enableRetry?: boolean
  maxRetries?: number
}

export interface UseEnhancedAddToCartReturn {
  addToCart: (input: AddToCartInput) => Promise<void>
  isLoading: boolean
  error: string | null
  retryCount: number
  canRetry: boolean
  retry: () => Promise<void>
  clearError: () => void
}

export function useEnhancedAddToCart(options: UseEnhancedAddToCartOptions = {}): UseEnhancedAddToCartReturn {
  const {
    userId,
    sessionId,
    onSuccess,
    onError,
    enableRetry = true,
    maxRetries = 3
  } = options

  const { addToCart: baseAddToCart, loading: cartLoading, error: cartError } = useCart({
    userId,
    sessionId,
    autoFetch: false
  })

  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [retryCount, setRetryCount] = useState(0)
  const [lastInput, setLastInput] = useState<AddToCartInput | null>(null)

  const clearError = useCallback(() => {
    setError(null)
    setRetryCount(0)
  }, [])

  const canRetry = enableRetry && retryCount < maxRetries && error !== null

  const performAddToCart = useCallback(async (input: AddToCartInput, isRetry = false) => {
    try {
      setIsLoading(true)
      setError(null)

      if (!isRetry) {
        setLastInput(input)
        setRetryCount(0)
      }

      // Validate input
      if (!input.productId) {
        throw new Error('Product ID is required')
      }

      if (input.quantity && input.quantity <= 0) {
        throw new Error('Quantity must be greater than 0')
      }

      // Call the base addToCart function
      await baseAddToCart({
        productId: input.productId,
        variantId: input.variantId,
        quantity: input.quantity || 1,
        customAttributes: input.customAttributes
      })

      // Success handling
      const productName = input.productName || 'Item'
      const variant = input.variantName
      
      CartSuccessHandler.handle('added to cart', productName, {
        quantity: input.quantity || 1,
        variant
      })

      if (onSuccess) {
        onSuccess(productName)
      }

      // Reset retry count on success
      setRetryCount(0)
      setLastInput(null)

    } catch (err) {
      console.error('Add to cart error:', err)
      
      const errorMessage = err instanceof Error ? err.message : 'Failed to add item to cart'
      setError(errorMessage)

      // Handle error with enhanced error handler
      const cartError = CartErrorHandler.createError(
        getErrorCodeFromMessage(errorMessage),
        errorMessage,
        isRetryableError(errorMessage)
      )

      CartErrorHandler.handle(cartError, {
        onRetry: canRetry ? () => retry() : undefined,
        onRefresh: () => window.location.reload()
      })

      if (onError) {
        onError(err)
      }

      throw err
    } finally {
      setIsLoading(false)
    }
  }, [baseAddToCart, onSuccess, onError, retryCount, maxRetries, canRetry])

  const addToCart = useCallback(async (input: AddToCartInput) => {
    await performAddToCart(input, false)
  }, [performAddToCart])

  const retry = useCallback(async () => {
    if (!lastInput || !canRetry) {
      return
    }

    setRetryCount(prev => prev + 1)
    await performAddToCart(lastInput, true)
  }, [lastInput, canRetry, performAddToCart])

  return {
    addToCart,
    isLoading: isLoading || cartLoading,
    error: error || (cartError?.message || null),
    retryCount,
    canRetry,
    retry,
    clearError
  }
}

// Helper functions
function getErrorCodeFromMessage(message: string): string {
  const lowerMessage = message.toLowerCase()
  
  if (lowerMessage.includes('stock') || lowerMessage.includes('inventory')) {
    return 'INSUFFICIENT_STOCK'
  }
  if (lowerMessage.includes('not found') || lowerMessage.includes('404')) {
    return 'NOT_FOUND'
  }
  if (lowerMessage.includes('network') || lowerMessage.includes('fetch')) {
    return 'NETWORK_ERROR'
  }
  if (lowerMessage.includes('timeout')) {
    return 'TIMEOUT_ERROR'
  }
  if (lowerMessage.includes('server') || lowerMessage.includes('500')) {
    return 'SERVER_ERROR'
  }
  if (lowerMessage.includes('validation') || lowerMessage.includes('invalid')) {
    return 'VALIDATION_ERROR'
  }
  if (lowerMessage.includes('session') || lowerMessage.includes('expired')) {
    return 'SESSION_EXPIRED'
  }
  
  return 'ADD_TO_CART_ERROR'
}

function isRetryableError(message: string): boolean {
  const lowerMessage = message.toLowerCase()
  return (
    lowerMessage.includes('network') ||
    lowerMessage.includes('timeout') ||
    lowerMessage.includes('server') ||
    lowerMessage.includes('500') ||
    lowerMessage.includes('fetch')
  )
}