import useSWR from 'swr';
import axios from 'axios';

const fetcher = (url: string) => axios.get(url).then((res) => res.data);

export const useStoreSettings = () => {
  const { data, error, mutate } = useSWR('/api/settings/store', fetcher);

  const createOrUpdateSettings = async (settings: {
    storeName: string;
    storeEmail: string;
    country: string;
    currency: string;
  }) => {
    try {
      const response = await axios.post('/api/settings/store', settings);
      mutate(response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to save store settings:', error);
      throw error;
    }
  };

  return {
    settings: data,
    isLoading: !error && !data,
    isError: error,
    createOrUpdateSettings,
  };
};
