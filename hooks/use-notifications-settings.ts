import useSWR from 'swr';
import axios from 'axios';

const fetcher = (url: string) => axios.get(url).then((res) => res.data);

export const useNotificationsSettings = () => {
  const { data, error, mutate } = useSWR('/api/settings/notifications', fetcher);

  const createOrUpdateSettings = async (settings: {
    emailNotifications: {
      newOrders: boolean;
      orderStatusChanges: boolean;
      lowStock: boolean;
      customerSignups: boolean;
    };
    adminNotifications: {
      enableBrowserNotifications: boolean;
      enableEmailDigest: boolean;
      digestFrequency: string;
    };
  }) => {
    try {
      const response = await axios.post('/api/settings/notifications', settings);
      mutate(response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to save notifications settings:', error);
      throw error;
    }
  };

  return {
    settings: data,
    isLoading: !error && !data,
    isError: error,
    createOrUpdateSettings,
  };
};