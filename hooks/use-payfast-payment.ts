/**
 * Payfast Payment Hook
 * 
 * Custom hook for handling Payfast payment operations
 */

import { useState } from 'react'
import { 
  PaymentRequest, 
  PaymentResponse, 
  PaymentMethod, 
  PaymentGateway,
  PaymentStatus
} from '@/lib/payment-core/types'

interface UsePayfastPaymentProps {
  onSuccess?: (response: PaymentResponse) => void
  onError?: (error: string) => void
  onStatusChange?: (status: PaymentStatus) => void
}

export function usePayfastPayment({
  onSuccess,
  onError,
  onStatusChange
}: UsePayfastPaymentProps = {}) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [paymentResponse, setPaymentResponse] = useState<PaymentResponse | null>(null)
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null)

  /**
   * Create a payment with Payfast
   */
  const createPayment = async (paymentRequest: PaymentRequest, method: PaymentMethod = PaymentMethod.CARD) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/payments/payfast/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentRequest,
          method,
        }),
      })

      const result = await response.json()

      if (result.success && result.paymentUrl) {
        setPaymentResponse(result)
        setPaymentStatus(result.status)
        onSuccess?.(result)
        onStatusChange?.(result.status)
        return result
      } else {
        const errorMessage = result.error?.message || 'Failed to create payment'
        setError(errorMessage)
        onError?.(errorMessage)
        return result
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred'
      setError(errorMessage)
      onError?.(errorMessage)
      
      return {
        success: false,
        status: PaymentStatus.FAILED,
        error: {
          code: 'UNEXPECTED_ERROR',
          message: errorMessage
        }
      }
    } finally {
      setLoading(false)
    }
  }

  /**
   * Check payment status
   */
  const checkPaymentStatus = async (transactionId: string) => {
    setLoading(true)
    
    try {
      const response = await fetch(`/api/payments/status/${transactionId}?gateway=${PaymentGateway.PAYFAST}`, {
        method: 'GET',
      })

      const result = await response.json()
      
      if (result.success) {
        setPaymentStatus(result.status)
        onStatusChange?.(result.status)
        return result.status
      } else {
        setError(result.error || 'Failed to check payment status')
        return PaymentStatus.FAILED
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to check payment status'
      setError(errorMessage)
      return PaymentStatus.FAILED
    } finally {
      setLoading(false)
    }
  }

  /**
   * Redirect to Payfast payment page
   */
  const redirectToPayment = (paymentUrl: string) => {
    if (paymentUrl) {
      window.location.href = paymentUrl
    } else if (paymentResponse?.paymentUrl) {
      window.location.href = paymentResponse.paymentUrl
    } else {
      setError('No payment URL available')
      onError?.('No payment URL available')
    }
  }

  return {
    createPayment,
    checkPaymentStatus,
    redirectToPayment,
    loading,
    error,
    paymentResponse,
    paymentStatus,
  }
}