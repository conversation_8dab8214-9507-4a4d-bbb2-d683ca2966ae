import useSWR from 'swr';
import axios from 'axios';
import { PaymentSettings } from '@/lib/settings/settings-service';

const fetcher = (url: string) => axios.get(url).then((res) => res.data);

export const usePaymentsSettings = () => {
  const { data, error, mutate } = useSWR<PaymentSettings>('/api/settings/payments', fetcher);

  const createOrUpdateSettings = async (settings: Partial<PaymentSettings>) => {
    try {
      const response = await axios.post('/api/settings/payments', settings);
      mutate(response.data);
      return response.data;
    } catch (error) {
      console.error('Failed to save payments settings:', error);
      throw error;
    }
  };

  const updatePaymentGateway = async (
    gateway: keyof PaymentSettings,
    config: Partial<PaymentSettings[keyof PaymentSettings]>
  ) => {
    try {
      if (!data) throw new Error('Payment settings not loaded');
      
      const updatedSettings = { ...data };
      updatedSettings[gateway] = {
        ...updatedSettings[gateway],
        ...config
      } as any;
      
      return await createOrUpdateSettings(updatedSettings);
    } catch (error) {
      console.error(`Failed to update ${gateway} settings:`, error);
      throw error;
    }
  };

  const testGatewayConnection = async (gateway: keyof PaymentSettings) => {
    try {
      const response = await axios.post(`/api/payments/test-connection`, { gateway });
      return response.data;
    } catch (error) {
      console.error(`Failed to test ${gateway} connection:`, error);
      throw error;
    }
  };

  return {
    settings: data,
    isLoading: !error && !data,
    isError: error,
    createOrUpdateSettings,
    updatePaymentGateway,
    testGatewayConnection
  };
};
