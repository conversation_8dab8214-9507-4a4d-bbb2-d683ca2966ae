'use client'

import { useState, useEffect, useCallback } from 'react'
import { toast } from 'sonner'

export interface MediaFile {
  id: string
  name: string
  mimeType: string
  size: number
  createdAt: string
  updatedAt: string
  url: string
  previewUrl?: string
  downloadUrl: string
  type: 'image' | 'video' | 'audio' | 'document' | 'other'
  metadata: {
    folder: string
    alt: string
    title: string
    description: string
    tags: string[]
    width?: number
    height?: number
  }
}

export interface MediaFilters {
  search?: string
  type?: 'all' | 'image' | 'video' | 'audio' | 'document'
  folder?: string
  sortBy?: 'name' | 'createdAt' | 'size'
  sortOrder?: 'asc' | 'desc'
}

export interface MediaPagination {
  page: number
  limit: number
  total: number
  totalPages: number
}

export interface UseMediaOptions {
  initialFilters?: MediaFilters
  initialPage?: number
  initialLimit?: number
  autoFetch?: boolean
}

export interface UseMediaResult {
  files: MediaFile[]
  pagination: MediaPagination
  loading: boolean
  error: string | null
  filters: MediaFilters
  setFilters: (filters: MediaFilters) => void
  setPage: (page: number) => void
  refetch: () => Promise<void>
  uploadFiles: (files: File[], metadata?: Partial<MediaFile['metadata']>) => Promise<MediaFile[]>
  deleteFile: (fileId: string) => Promise<void>
  updateFile: (fileId: string, updates: Partial<MediaFile>) => Promise<MediaFile>
}

export function useMedia(options: UseMediaOptions = {}): UseMediaResult {
  const {
    initialFilters = {},
    initialPage = 1,
    initialLimit = 20,
    autoFetch = true
  } = options

  const [files, setFiles] = useState<MediaFile[]>([])
  const [pagination, setPagination] = useState<MediaPagination>({
    page: initialPage,
    limit: initialLimit,
    total: 0,
    totalPages: 0
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<MediaFilters>(initialFilters)

  const fetchFiles = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...filters
      })

      const response = await fetch(`/api/admin/media?${params}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch files')
      }

      setFiles(result.data.files)
      setPagination(result.data.pagination)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch files'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [pagination.page, pagination.limit, filters])

  const setPage = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page }))
  }, [])

  const uploadFiles = useCallback(async (
    filesToUpload: File[], 
    metadata: Partial<MediaFile['metadata']> = {}
  ): Promise<MediaFile[]> => {
    try {
      setLoading(true)
      setError(null)

      console.log('Starting upload process:', {
        fileCount: filesToUpload.length,
        metadata
      })

      // Validate files before upload
      const invalidFiles = filesToUpload.filter(file => 
        !file.name || file.name === 'undefined' || !file.type
      )
      
      if (invalidFiles.length > 0) {
        console.error('Invalid files detected:', invalidFiles.map(f => ({ name: f.name, type: f.type, size: f.size })))
        throw new Error(`Invalid file objects detected. This usually means File objects were incorrectly created. Check that you're passing proper File instances.`)
      }

      const formData = new FormData()
      filesToUpload.forEach((file, index) => {
        console.log(`Adding file ${index + 1}: ${file.name} (${file.type}, ${file.size} bytes)`)
        formData.append('files', file)
      })
      
      if (metadata.folder) formData.append('folder', metadata.folder)
      if (metadata.alt) formData.append('alt', metadata.alt)
      if (metadata.title) formData.append('title', metadata.title)
      if (metadata.description) formData.append('description', metadata.description)
      if (metadata.tags) formData.append('tags', metadata.tags.join(','))

      console.log('Sending upload request to /api/admin/media')

      const response = await fetch('/api/admin/media', {
        method: 'POST',
        body: formData
      })

      console.log('Upload response received:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      })

      const result = await response.json()
      console.log('Upload result:', result)

      if (!response.ok) {
        throw new Error(result.error || 'Failed to upload files')
      }

      // Handle the response structure
      const uploadResults = result.data?.files || []
      const uploadedFiles = uploadResults.filter((file: any) => !file.error)
      const failedFiles = uploadResults.filter((file: any) => file.error)

      console.log('Upload summary:', {
        total: uploadResults.length,
        successful: uploadedFiles.length,
        failed: failedFiles.length
      })

      if (uploadedFiles.length > 0) {
        toast.success(`Successfully uploaded ${uploadedFiles.length} file(s)`)
        await fetchFiles() // Refresh the list
      }

      if (failedFiles.length > 0) {
        const errorMessages = failedFiles.map((f: any) => `${f.name}: ${f.error}`).join('\n')
        toast.error(`Failed to upload ${failedFiles.length} file(s):\n${errorMessages}`)
      }

      // Transform uploaded files to the expected format
      const transformedFiles: MediaFile[] = uploadedFiles.map((file: any) => ({
        id: file.id || file.fileId || '',
        name: file.name || '',
        mimeType: file.mimeType || '',
        size: file.size || 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        url: file.url || '',
        previewUrl: file.previewUrl || null,
        downloadUrl: file.downloadUrl || '',
        type: file.type || 'other',
        metadata: file.metadata || {
          folder: 'root',
          alt: '',
          title: '',
          description: '',
          tags: []
        }
      }))

      return transformedFiles

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload files'
      console.error('Upload error:', err)
      setError(errorMessage)
      toast.error(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [fetchFiles])

  const deleteFile = useCallback(async (fileId: string): Promise<void> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/admin/media/${fileId}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete file')
      }

      toast.success('File deleted successfully')
      await fetchFiles() // Refresh the list

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete file'
      setError(errorMessage)
      toast.error(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [fetchFiles])

  const updateFile = useCallback(async (
    fileId: string, 
    updates: Partial<MediaFile>
  ): Promise<MediaFile> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/admin/media/${fileId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update file')
      }

      toast.success('File updated successfully')
      await fetchFiles() // Refresh the list

      return result.data

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update file'
      setError(errorMessage)
      toast.error(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [fetchFiles])

  // Auto-fetch on mount and when dependencies change
  useEffect(() => {
    if (autoFetch) {
      fetchFiles()
    }
  }, [fetchFiles, autoFetch])

  return {
    files,
    pagination,
    loading,
    error,
    filters,
    setFilters,
    setPage,
    refetch: fetchFiles,
    uploadFiles,
    deleteFile,
    updateFile
  }
}
