# Payment Integration Documentation

This document outlines the payment integration implementation for the Coco Milk Store e-commerce platform.

## Overview

The payment system is designed to support multiple South African payment gateways, providing a unified interface for processing payments. The system is built with flexibility and extensibility in mind, allowing for easy addition of new payment gateways.

## Supported Payment Gateways

The following payment gateways are supported:

- **PayFast** - South Africa's leading payment gateway
- **Ozow** - Instant EFT payments
- **SnapScan** - QR code payments
- **Yoco** - Card payments and POS
- **PayU** - International and local card processing
- **Zapper** - QR code payments
- **Manual** - Manual bank transfers

## Supported Payment Methods

The following payment methods are supported:

- Credit/Debit Card
- Instant EFT
- QR Code Payments
- Manual EFT
- Digital Wallet
- Vouchers
- Cryptocurrency

## Implementation Details

### Core Components

1. **Payment Service** - The main entry point for payment operations
2. **Payment Gateways** - Implementations for each supported payment gateway
3. **Webhook Handler** - Processes payment notifications from gateways
4. **Payment Status Page** - Displays payment status to customers
5. **Payment Form** - UI component for selecting payment methods and initiating payments

### Configuration

Payment gateway configuration is stored in environment variables. See `.env.example` for the required variables.

### API Endpoints

The following API endpoints are available:

- `POST /api/payments/create` - Create a new payment
- `GET /api/payments/status` - Check payment status
- `GET /api/payments/methods` - Get available payment methods
- `POST /api/webhooks/payments` - Webhook endpoint for payment notifications

### Testing

A test script is available to verify the payment integration:

```bash
npm run test:payment-core
```

This script tests the payment service by creating test payments with each supported gateway.

### Payment Flow

1. Customer selects items and proceeds to checkout
2. Customer selects a payment method
3. System creates a payment request with the selected gateway
4. Customer is redirected to the payment gateway or shown payment instructions
5. Customer completes payment on the gateway
6. Gateway sends a webhook notification to our system
7. System updates the payment status and fulfills the order
8. Customer is redirected to the payment status page

## Security Considerations

- All payment data is encrypted in transit using HTTPS
- Sensitive payment information is never stored on our servers
- Webhook signatures are verified to prevent tampering
- Payment encryption keys are stored securely in environment variables

## Testing in Sandbox Mode

All payment gateways are configured to use sandbox/test mode in development environments. This allows for testing the payment flow without processing real payments.

## Going Live

To switch to production mode:

1. Update the environment variables to use production credentials
2. Set the `SANDBOX` variables to `false`
3. Update the webhook URLs to use the production domain
4. Test thoroughly with small real transactions before full deployment

## Troubleshooting

Common issues and their solutions:

- **Webhook not received**: Check firewall settings and ensure the webhook URL is publicly accessible
- **Payment creation failed**: Verify gateway credentials and ensure required fields are provided
- **Payment status unknown**: Check the transaction ID and gateway logs

## Future Improvements

- Add support for recurring payments
- Implement payment analytics dashboard
- Add support for additional payment gateways
- Enhance fraud detection capabilities