# PayFast Payment Integration

This document provides a comprehensive guide to integrating PayFast payments into your application.

## Overview

The PayFast integration allows you to process payments using the PayFast payment gateway, which is popular in South Africa. It supports various payment methods including credit/debit cards, instant EFT, and mobile payments.

## Features

- Multiple payment methods (Card, EFT, Mobile Money, QR Code, Bank Transfer)
- Secure payment processing
- Webhook support for payment notifications
- Comprehensive error handling
- Transaction logging
- Refund support (via PayFast merchant portal)

## Prerequisites

Before you can use the PayFast integration, you need to:

1. Create a PayFast merchant account at [PayFast](https://www.payfast.co.za)
2. Obtain your Merchant ID and Merchant Key from your PayFast account
3. Set up a passphrase in your PayFast account (optional but recommended)
4. Configure your server to handle PayFast ITN (Instant Transaction Notification) callbacks

## Environment Variables

Add the following environment variables to your `.env` file:

```
PAYFAST_MERCHANT_ID=your_merchant_id
PAYFAST_MERCHANT_KEY=your_merchant_key
PAYFAST_PASSPHRASE=your_passphrase
PAYFAST_SANDBOX=true  # Set to false for production
PAYFAST_ENABLED=true
```

## Basic Usage

### 1. Process a payment

```tsx
import { usePayfastPayment } from '@/hooks/use-payfast-payment'
import { PaymentMethod } from '@/lib/payment-core/types'

// Inside your component
const { createPayment, loading, error } = usePayfastPayment({
  onSuccess: (response) => {
    console.log('Payment created:', response)
    // Redirect to payment URL
    if (response.paymentUrl) {
      window.location.href = response.paymentUrl
    }
  },
  onError: (error) => {
    console.error('Payment error:', error)
  }
})

// Create payment request
const paymentRequest = {
  amount: {
    amount: 100.00,
    currency: 'ZAR'
  },
  customer: {
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    phone: '0123456789'
  },
  items: [
    {
      id: '1',
      name: 'Product Name',
      quantity: 1,
      unitPrice: 100.00,
      totalPrice: 100.00
    }
  ],
  metadata: {
    orderId: 'order-123',
    source: 'checkout'
  },
  returnUrl: `${window.location.origin}/checkout/success?orderId=order-123`,
  cancelUrl: `${window.location.origin}/checkout/cancel?orderId=order-123`,
  notifyUrl: `${window.location.origin}/api/webhooks/payfast/notify`,
  reference: 'order-123',
  description: 'Payment for order #123'
}

// Process payment
await createPayment(paymentRequest, PaymentMethod.CARD)
```

### 2. Use the PayFast Payment Processor component

```tsx
import PayfastPaymentProcessor from '@/components/checkout/PayfastPaymentProcessor'

// Inside your component
return (
  <PayfastPaymentProcessor
    orderId="order-123"
    method={PaymentMethod.CARD}
    amount={100.00}
    currency="ZAR"
    customerEmail="<EMAIL>"
    customerFirstName="John"
    customerLastName="Doe"
    customerPhone="0123456789"
    onSuccess={(transactionId) => {
      console.log('Payment successful:', transactionId)
    }}
    onError={(error) => {
      console.error('Payment error:', error)
    }}
  />
)
```

### 3. Use the enhanced PayFast component

```tsx
import { EnhancedPayfastPayment } from '@/components/payments/enhanced-payfast-payment'
import { PaymentMethod } from '@/lib/payment-core/types'

// Inside your component
const paymentRequest = {
  // ... payment request object as shown above
}

return (
  <EnhancedPayfastPayment
    paymentRequest={paymentRequest}
    method={PaymentMethod.CARD}
    onSuccess={(response) => {
      console.log('Payment created:', response)
    }}
    onError={(error) => {
      console.error('Payment error:', error)
    }}
    showLogo={true}
    showSecurityBadge={true}
  />
)
```

## Webhook Handling

PayFast sends ITN (Instant Transaction Notification) callbacks to your server when a payment status changes. The webhook handler is located at:

```
/api/webhooks/payfast/notify
```

Make sure this URL is accessible from the internet and is configured in your PayFast account.

The webhook handler:

1. Validates the request is from PayFast
2. Verifies the signature if a passphrase is set
3. Updates the order status based on the payment status
4. Returns a 200 OK response to acknowledge receipt

## Security Considerations

1. **Signature Validation**: Always validate the signature of PayFast requests using your passphrase
2. **IP Validation**: In production, validate that webhook requests come from PayFast's IP ranges
3. **Amount Validation**: Verify that the payment amount matches the order amount
4. **SSL**: Ensure all communication with PayFast is over HTTPS

## Testing

You can test the PayFast integration using the sandbox environment:

1. Set `PAYFAST_SANDBOX=true` in your environment variables
2. Use the test credentials provided by PayFast
3. Visit the demo page at `/payments-demo/payfast` to test the integration

## Troubleshooting

### Common Issues

1. **Signature Validation Fails**: Ensure your passphrase is correctly set in both your code and PayFast account
2. **Payment Not Processing**: Check that your merchant ID and key are correct
3. **Webhook Not Receiving Updates**: Ensure your notify URL is accessible from the internet and correctly configured

### Debugging

Enable debug logging by setting the log level in your payment service configuration:

```typescript
const paymentService = new PaymentService({
  logLevel: 'debug'
})
```

## Additional Resources

- [PayFast Developer Documentation](https://developers.payfast.co.za/)
- [PayFast API Reference](https://developers.payfast.co.za/api)
- [PayFast Integration Guide](https://developers.payfast.co.za/integration-guide)