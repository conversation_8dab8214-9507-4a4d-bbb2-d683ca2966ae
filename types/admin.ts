import { Order } from '@prisma/client'

type OrderStatus = 'PENDING' | 'PROCESSING' | 'SHIPPED' | 'DELIVERED' | 'CANCELLED' | 'REFUNDED'
type PaymentStatus = 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED' | 'PARTIALLY_REFUNDED'

export interface AdminOrder extends Omit<Order, 'items'> {
  customer?: {
    id: string
    email: string
    firstName: string | null
    lastName: string | null
    phone: string | null
  } | null
  customerEmail: string
  customerPhone?: string | null
  shippingAddress: Record<string, any> | null
  billingAddress?: Record<string, any> | null
  shippingCost: number
  taxAmount: number
  discountAmount: number
  items: Array<{
    id: string
    productId: string
    variantId: string | null
    name: string
    price: number
    quantity: number
    subtotal: number
    image?: string | null
    color?: string | null
    size?: string | null
  }>
  fulfillments?: Array<{
    id: string
    status: string
    trackingNumber: string | null
    trackingUrl: string | null
    items: Array<{
      id: string
      orderItemId: string
      quantity: number
    }>
    createdAt: Date
    updatedAt: Date
  }>
  payments?: Array<{
    id: string
    amount: number
    currency: string
    status: string
    method: string
    transactionId: string | null
    createdAt: Date
  }>
}

export interface AdminOrdersResponse {
  success: boolean
  data: {
    items: AdminOrder[]
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
      hasNext: boolean
      hasPrev: boolean
    }
  }
}

export interface AdminOrderResponse {
  success: boolean
  data: AdminOrder
}

export interface OrderFilters {
  page?: number
  limit?: number
  status?: OrderStatus
  paymentStatus?: PaymentStatus
  customerEmail?: string
  orderNumber?: string
  startDate?: string
  endDate?: string
}

export interface OrderUpdateData {
  status?: OrderStatus
  paymentStatus?: PaymentStatus
  trackingNumber?: string
  notes?: string
}
