import { User } from "next-auth"
import { JWT } from "next-auth/jwt"

declare module "next-auth" {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: {
      id: string
      role?: string
      firstName?: string | null
      lastName?: string | null
      displayName?: string | null
      avatar?: string | null
      emailVerified?: boolean
    } & DefaultSession["user"]
  }

  /**
   * The shape of the user object returned in the OAuth providers' `profile` callback,
   * or the second parameter of the `session` callback, when using a database.
   */
  interface User {
    id: string
    role?: string
    firstName?: string | null
    lastName?: string | null
    displayName?: string | null
    avatar?: string | null
    emailVerified?: boolean
  }
}

declare module "next-auth/jwt" {
  /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */
  interface JWT {
    role?: string
    firstName?: string | null
    lastName?: string | null
    displayName?: string | null
    avatar?: string | null
    emailVerified?: boolean
  }
}