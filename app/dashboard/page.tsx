'use client'

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dashboard } from "@/components/customer/dashboard"

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  useEffect(() => {
    if (status === "unauthenticated") {
      router.replace("/login")
    }
  }, [status, router])

  if (status === "loading") {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <p>Loading...</p>
      </div>
    )
  }

  if (!session) {
    return null
  }

  return (
    <div className="flex flex-col min-h-screen">
      <header className="border-b px-6 py-3">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold">Customer Dashboard</h1>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-muted-foreground">
              {session.user?.email || "Unknown Email"}
            </span>
            <Button variant="outline" size="sm" onClick={() => router.push("/api/auth/signout")}>
              Logout
            </Button>
          </div>
        </div>
      </header>
      <main className="flex-1 overflow-auto p-6">
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Welcome, {session.user?.name || "Customer"}</CardTitle>
            <CardDescription>Manage your account and orders</CardDescription>
          </CardHeader>
          <CardContent>
            <Dashboard />
          </CardContent>
        </Card>
      </main>
    </div>
  )
}
