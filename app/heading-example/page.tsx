import { Heading } from "@/components/ui/heading"

export default function HeadingExamplePage() {
  return (
    <div className="container mx-auto py-10 space-y-8">
      <div>
        <Heading as="h1" size="h1">
          Heading 1 (Default)
        </Heading>
        <Heading as="h1" size="h1" variant="primary">
          Heading 1 (Primary)
        </Heading>
        <Heading as="h1" size="h1" variant="secondary">
          Heading 1 (Secondary)
        </Heading>
        <Heading as="h1" size="h1" variant="muted">
          Heading 1 (Muted)
        </Heading>
      </div>

      <div>
        <Heading as="h2" size="h2">
          Heading 2
        </Heading>
        <Heading as="h3" size="h3">
          Heading 3
        </Heading>
        <Heading as="h4" size="h4">
          Heading 4
        </Heading>
        <Heading as="h5" size="h5">
          Heading 5
        </Heading>
        <Heading as="h6" size="h6">
          Heading 6
        </Heading>
      </div>

      <div>
        <Heading size="display">
          Display Heading (Larger than h1)
        </Heading>
        <Heading size="hero">
          Hero Heading (Largest)
        </Heading>
      </div>

      <div>
        <Heading align="left">Left Aligned Heading</Heading>
        <Heading align="center">Center Aligned Heading</Heading>
        <Heading align="right">Right Aligned Heading</Heading>
      </div>

      <div>
        <Heading weight="normal">Normal Weight</Heading>
        <Heading weight="medium">Medium Weight</Heading>
        <Heading weight="semibold">Semibold Weight</Heading>
        <Heading weight="bold">Bold Weight</Heading>
        <Heading weight="extrabold">Extrabold Weight</Heading>
      </div>

      <div>
        <Heading transform="uppercase">Uppercase Heading</Heading>
        <Heading transform="lowercase">Lowercase Heading</Heading>
        <Heading transform="capitalize">capitalize heading</Heading>
      </div>

      <div className="max-w-md">
        <Heading truncate>
          This is a very long heading that will be truncated when it exceeds the container width
        </Heading>
      </div>

      <div>
        <Heading 
          size="h2" 
          variant="primary" 
          align="center" 
          weight="bold" 
          transform="uppercase"
          className="border-b-2 border-primary pb-2"
        >
          Combined Styles with Custom Class
        </Heading>
      </div>
    </div>
  )
}