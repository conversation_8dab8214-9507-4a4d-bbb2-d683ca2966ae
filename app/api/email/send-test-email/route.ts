import { NextRequest, NextResponse } from 'next/server';
import { emailService } from '@/lib/email/email-service';

export async function POST(req: NextRequest) {
  try {
    const { emailType, to, data } = await req.json();
    
    if (!emailType || !to) {
      return NextResponse.json(
        { error: 'Email type and recipient are required' },
        { status: 400 }
      );
    }
    
    let result = false;
    
    switch (emailType) {
      case 'welcome':
        result = await emailService.sendWelcomeEmail(to, {
          firstName: data.firstName || 'Customer',
          ...data,
        });
        break;
        
      case 'order-confirmation':
        result = await emailService.sendOrderConfirmationEmail(to, {
          customerName: data.customerName || 'Customer',
          orderNumber: data.orderNumber || '12345',
          orderDate: data.orderDate || new Date().toLocaleDateString(),
          items: data.items || [],
          subtotal: data.subtotal || 'R0.00',
          shipping: data.shipping || 'R0.00',
          total: data.total || 'R0.00',
          shippingAddress: data.shippingAddress || {
            name: 'Customer',
            street: '123 Main St',
            city: 'Cape Town',
            postalCode: '8001',
            country: 'South Africa',
          },
          paymentMethod: data.paymentMethod || 'Credit Card',
          ...data,
        });
        break;
        
      case 'password-reset':
        result = await emailService.sendPasswordResetEmail(to, {
          firstName: data.firstName || 'Customer',
          resetLink: data.resetLink || 'https://cocomilkkids.com/reset-password?token=example',
          ...data,
        });
        break;
        
      case 'account-notification':
        result = await emailService.sendAccountNotificationEmail(to, {
          firstName: data.firstName || 'Customer',
          notificationType: data.notificationType || 'info',
          title: data.title || 'Account Notification',
          message: data.message || 'This is a notification about your account.',
          ...data,
        });
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid email type' },
          { status: 400 }
        );
    }
    
    if (result) {
      return NextResponse.json({ success: true });
    } else {
      return NextResponse.json(
        { error: 'Failed to send email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error sending test email:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}