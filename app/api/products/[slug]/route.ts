import { NextResponse } from "next/server"
import { useProducts } from "@/lib/ecommerce/hooks/use-products"

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    const { products } = await useProducts()
    const product = products.find(p => p.slug === params.slug)

    if (!product) {
      return new NextResponse("Product not found", { status: 404 })
    }

    return NextResponse.json(product)
  } catch (error) {
    console.error("Error fetching product:", error)
    return new NextResponse("Internal Server Error", { status: 500 })
  }
}