import NextAuth from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials"

import { compare } from "bcryptjs"
// Temporary comment out for prismadb until the library is properly set up
// import prismadb from "@/lib/prismadb"

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        // Temporary mock user check until prismadb is set up
        // const user = await prismadb.user.findUnique({
        //   where: { email: credentials.email }
        // })
        // Mock user for development
        const user = {
          id: "1",
          email: credentials.email,
          name: "Test User",
          hashedPassword: "$2a$10$X7VYFDe.9HRl1eL6Larr5eO2oB/wc7RMhG5bXJm2kVv3oA4wXw8uW" // This is a dummy hash
        }

        if (!user || !user.hashedPassword) {
          return null
        }

        const isPasswordValid = await compare(credentials.password, user.hashedPassword)

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name
        }
      }
    })
  ],
  session: {
    strategy: "jwt"
  },
  callbacks: {
    async session({ session, token }) {
      if (token && session.user) {
        session.user = { ...session.user, id: token.sub || "" }
      }
      return session
    }
  },
  secret: process.env.NEXTAUTH_SECRET,
  pages: {
    signIn: "/login"
  }
})

export { handler as GET, handler as POST }
