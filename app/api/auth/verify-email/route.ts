import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { z } from "zod"

// Schema for validating email verification
const emailVerificationSchema = z.object({
  token: z.string().min(1, "Token is required"),
})

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json()
    const validationResult = emailVerificationSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      )
    }

    const { token } = validationResult.data

    // Find verification token
    const verificationToken = await db.verificationToken.findFirst({
      where: {
        token,
        expires: { gt: new Date() }
      }
    })

    if (!verificationToken) {
      return NextResponse.json(
        { error: "Invalid or expired verification token" },
        { status: 400 }
      )
    }

    // Update user email verification status
    await db.user.update({
      where: { id: verificationToken.userId },
      data: { emailVerified: new Date() }
    })

    // Delete verification token
    await db.verificationToken.delete({
      where: { id: verificationToken.id }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("[EMAIL_VERIFICATION]", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}