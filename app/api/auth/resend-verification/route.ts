import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { z } from "zod"
import crypto from "crypto"
import { sendEmail } from "@/lib/email/send-email"
import { auth } from "@/auth"

// Schema for validating resend verification request
const resendVerificationSchema = z.object({
  email: z.string().email("Invalid email address").optional(),
})

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json()
    const validationResult = resendVerificationSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      )
    }

    const { email } = validationResult.data
    let userId: string | undefined

    // If email is provided, find user by email
    if (email) {
      const user = await db.user.findUnique({
        where: { email },
        select: { id: true, emailVerified: true }
      })

      // If user doesn't exist or is already verified, return success (for security)
      if (!user) {
        return NextResponse.json({ success: true })
      }

      if (user.emailVerified) {
        return NextResponse.json(
          { error: "Email is already verified" },
          { status: 400 }
        )
      }

      userId = user.id
    } else {
      // If no email provided, get user from session
      const session = await auth()

      if (!session || !session.user) {
        return NextResponse.json(
          { error: "Unauthorized" },
          { status: 401 }
        )
      }

      const user = await db.user.findUnique({
        where: { id: session.user.id },
        select: { id: true, email: true, emailVerified: true }
      })

      if (!user) {
        return NextResponse.json(
          { error: "User not found" },
          { status: 404 }
        )
      }

      if (user.emailVerified) {
        return NextResponse.json(
          { error: "Email is already verified" },
          { status: 400 }
        )
      }

      userId = user.id
      email = user.email
    }

    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString("hex")
    const tokenExpiry = new Date(Date.now() + 3600000) // 1 hour from now

    // Store verification token in database
    await db.verificationToken.upsert({
      where: { userId },
      update: {
        token: verificationToken,
        expires: tokenExpiry
      },
      create: {
        userId,
        token: verificationToken,
        expires: tokenExpiry
      }
    })

    // Send verification email
    const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL}/verify-email?token=${verificationToken}`
    
    await sendEmail({
      to: email!,
      subject: "Verify your email address",
      html: `
        <p>Please verify your email address by clicking the link below:</p>
        <p><a href="${verificationUrl}">Verify Email</a></p>
        <p>If you didn't request this, please ignore this email.</p>
        <p>This link will expire in 1 hour.</p>
      `
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("[RESEND_VERIFICATION]", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}