import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { z } from "zod"
import { hash } from "bcryptjs"
import crypto from "crypto"
import { sendEmail } from "@/lib/email/send-email"

// Schema for validating password reset request
const passwordResetRequestSchema = z.object({
  email: z.string().email("Invalid email address"),
})

// Schema for validating password reset
const passwordResetSchema = z.object({
  token: z.string().min(1, "Token is required"),
  password: z.string().min(8, "Password must be at least 8 characters"),
})

// POST /api/auth/reset-password - Request password reset
export async function POST(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json()
    const validationResult = passwordResetRequestSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      )
    }

    const { email } = validationResult.data

    // Check if user exists
    const user = await db.user.findUnique({
      where: { email }
    })

    // Always return success even if user doesn't exist (for security)
    if (!user) {
      return NextResponse.json({ success: true })
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString("hex")
    const resetTokenExpiry = new Date(Date.now() + 3600000) // 1 hour from now

    // Store reset token in database
    await db.passwordResetToken.upsert({
      where: { userId: user.id },
      update: {
        token: resetToken,
        expires: resetTokenExpiry
      },
      create: {
        userId: user.id,
        token: resetToken,
        expires: resetTokenExpiry
      }
    })

    // Send password reset email
    const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${resetToken}`
    
    await sendEmail({
      to: email,
      subject: "Reset your password",
      html: `
        <p>You requested a password reset.</p>
        <p>Click <a href="${resetUrl}">here</a> to reset your password.</p>
        <p>If you didn't request this, please ignore this email.</p>
        <p>This link will expire in 1 hour.</p>
      `
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("[PASSWORD_RESET_REQUEST]", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PUT /api/auth/reset-password - Reset password with token
export async function PUT(req: NextRequest) {
  try {
    // Parse and validate request body
    const body = await req.json()
    const validationResult = passwordResetSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      )
    }

    const { token, password } = validationResult.data

    // Find reset token
    const resetToken = await db.passwordResetToken.findFirst({
      where: {
        token,
        expires: { gt: new Date() }
      },
      include: { user: true }
    })

    if (!resetToken) {
      return NextResponse.json(
        { error: "Invalid or expired reset token" },
        { status: 400 }
      )
    }

    // Hash new password
    const hashedPassword = await hash(password, 10)

    // Update user password
    await db.user.update({
      where: { id: resetToken.userId },
      data: { password: hashedPassword }
    })

    // Delete reset token
    await db.passwordResetToken.delete({
      where: { id: resetToken.id }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("[PASSWORD_RESET]", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}