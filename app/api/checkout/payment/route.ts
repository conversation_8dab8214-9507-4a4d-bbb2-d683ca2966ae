/**
 * Checkout Payment API Route
 * 
 * Processes payment for an order during checkout.
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { processOrderPayment } from '@/lib/payment-core/ecommerce-integration'
import { PaymentGateway, PaymentMethod } from '@/lib/payment-core/types'
import { logger } from '@/lib/payment-core/logger'

// Validation schema for payment request
const paymentRequestSchema = z.object({
  orderId: z.string().uuid(),
  gateway: z.nativeEnum(PaymentGateway).optional(),
  method: z.nativeEnum(PaymentMethod).optional(),
  returnUrl: z.string().url().optional(),
  cancelUrl: z.string().url().optional(),
  notifyUrl: z.string().url().optional()
})

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json()
    const validationResult = paymentRequestSchema.safeParse(body)
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request data', 
          details: validationResult.error.format() 
        },
        { status: 400 }
      )
    }
    
    const { orderId, gateway, method, returnUrl, cancelUrl, notifyUrl } = validationResult.data
    
    // Process payment
    const result = await processOrderPayment(
      orderId,
      gateway,
      method,
      returnUrl,
      cancelUrl,
      notifyUrl
    )
    
    // Return response
    if (result.success) {
      return NextResponse.json({
        success: true,
        paymentUrl: result.paymentUrl,
        transactionId: result.transactionId,
        status: result.status,
        message: result.message
      })
    } else {
      return NextResponse.json({
        success: false,
        error: result.error?.message || 'Payment processing failed',
        errorCode: result.error?.code,
        status: result.status
      }, { status: 400 })
    }
    
  } catch (error) {
    logger.error('Checkout payment processing error', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Get available payment methods
export async function GET() {
  try {
    const { getCheckoutPaymentMethods } = await import('@/lib/payment-core/ecommerce-integration')
    const methods = await getCheckoutPaymentMethods()
    
    return NextResponse.json({ success: true, methods })
  } catch (error) {
    logger.error('Failed to get payment methods', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { success: false, error: 'Failed to get payment methods' },
      { status: 500 }
    )
  }
}