/**
 * Payment Status API Route
 * 
 * Checks the status of a payment.
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { PrismaClient } from '@prisma/client'
import { paymentService } from '@/lib/payment-core/service'
import { PaymentGateway, PaymentStatus } from '@/lib/payment-core/types'
import { logger } from '@/lib/payment-core/logger'

// Initialize Prisma client
const prisma = new PrismaClient()

// Validation schema for status request
const statusRequestSchema = z.object({
  transactionId: z.string(),
  gateway: z.nativeEnum(PaymentGateway).optional(),
  orderId: z.string().uuid().optional()
})

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const url = new URL(request.url)
    const transactionId = url.searchParams.get('transactionId')
    const gateway = url.searchParams.get('gateway') as PaymentGateway | null
    const orderId = url.searchParams.get('orderId')
    
    // Validate parameters
    if (!transactionId && !orderId) {
      return NextResponse.json(
        { success: false, error: 'Either transactionId or orderId is required' },
        { status: 400 }
      )
    }
    
    // If orderId is provided, get payment details from database
    if (orderId) {
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        select: {
          id: true,
          orderNumber: true,
          status: true,
          paymentStatus: true,
          payments: {
            select: {
              id: true,
              gatewayId: true,
              gatewayPaymentId: true,
              status: true,
              paymentMethodId: true,
              failureMessage: true,
              receiptUrl: true
            },
            orderBy: {
              createdAt: 'desc'
            },
            take: 1
          }
        }
      })
      
      if (!order) {
        return NextResponse.json(
          { success: false, error: 'Order not found' },
          { status: 404 }
        )
      }

      const latestPayment = order.payments[0] || null
      
      return NextResponse.json({
        success: true,
        orderId: order.id,
        orderNumber: order.orderNumber,
        orderStatus: order.status,
        paymentStatus: order.paymentStatus,
        transactionId: latestPayment?.gatewayPaymentId || latestPayment?.id,
        gateway: latestPayment?.gatewayId,
        method: latestPayment?.paymentMethodId,
        paymentUrl: latestPayment?.receiptUrl,
        error: latestPayment?.failureMessage
      })
    }
    
    // If transactionId is provided, check payment status from gateway
    if (transactionId && gateway) {
      const status = await paymentService.getPaymentStatus(transactionId, gateway)
      
      // Get payment from database
      const payment = await prisma.payment.findFirst({
        where: {
          OR: [
            { id: transactionId },
            { gatewayPaymentId: transactionId },
            { paymentNumber: transactionId }
          ]
        },
        select: {
          id: true,
          paymentNumber: true,
          orderId: true,
          status: true,
          gatewayId: true,
          gatewayPaymentId: true,
          amount: true,
          currency: true,
          customerId: true,
          metadata: true
        }
      })
      
      return NextResponse.json({
        success: true,
        status,
        transactionId,
        gateway,
        payment: payment || null
      })
    }
    
    // If only transactionId is provided, get payment from database
    if (transactionId) {
      const payment = await prisma.payment.findFirst({
        where: {
          OR: [
            { id: transactionId },
            { gatewayPaymentId: transactionId },
            { paymentNumber: transactionId }
          ]
        },
        select: {
          id: true,
          paymentNumber: true,
          orderId: true,
          status: true,
          gatewayId: true,
          gatewayPaymentId: true,
          amount: true,
          currency: true,
          customerId: true,
          metadata: true
        }
      })
      
      if (!payment) {
        return NextResponse.json(
          { success: false, error: 'Payment not found' },
          { status: 404 }
        )
      }
      
      return NextResponse.json({
        success: true,
        status: payment.status,
        transactionId: payment.gatewayPaymentId || payment.id,
        gateway: payment.gatewayId,
        payment
      })
    }
    
    return NextResponse.json(
      { success: false, error: 'Invalid request' },
      { status: 400 }
    )
    
  } catch (error) {
    logger.error('Payment status check error', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}