import { NextResponse } from 'next/server'
import { getEnabledGateways, PAYMENT_METHOD_CONFIG } from '@/lib/payments/config'
import { PaymentGateway, PaymentMethod } from '@/lib/payments/types'

export async function GET() {
  try {
    const enabledGateways = getEnabledGateways()
    
    // Create gateway info with supported methods
    const gatewayInfo = enabledGateways.map(gateway => {
      let supportedMethods: PaymentMethod[] = []
      
      switch (gateway) {
        case PaymentGateway.PAYFAST:
          supportedMethods = [PaymentMethod.CARD, PaymentMethod.EFT]
          break
        case PaymentGateway.OZOW:
          supportedMethods = [PaymentMethod.INSTANT_EFT, PaymentMethod.EFT]
          break
        case PaymentGateway.SNAPSCAN:
          supportedMethods = [PaymentMethod.QR_CODE]
          break
        case PaymentGateway.YOCO:
          supportedMethods = [PaymentMethod.CARD]
          break
        case PaymentGateway.PAYU:
          supportedMethods = [PaymentMethod.CARD]
          break
      }

      return {
        gateway,
        supportedMethods,
        name: gateway === PaymentGateway.PAYFAST ? 'PayFast' :
              gateway === PaymentGateway.OZOW ? 'Ozow' :
              gateway === PaymentGateway.SNAPSCAN ? 'SnapScan' :
              gateway === PaymentGateway.YOCO ? 'Yoco' :
              gateway === PaymentGateway.PAYU ? 'PayU' : gateway
      }
    })

    return NextResponse.json({
      success: true,
      gateways: gatewayInfo,
      paymentMethods: PAYMENT_METHOD_CONFIG
    })

  } catch (error) {
    console.error('Failed to get enabled gateways:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to load payment gateways',
        gateways: [],
        paymentMethods: PAYMENT_METHOD_CONFIG
      },
      { status: 500 }
    )
  }
}