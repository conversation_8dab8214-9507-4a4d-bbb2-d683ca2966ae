/**
 * Payment Status API
 * 
 * This API endpoint checks the status of a payment.
 */

import { NextRequest, NextResponse } from 'next/server'
import { paymentService, PaymentGateway, PaymentStatus } from '@/lib/payment-core'
import { logger } from '@/lib/payment-core/logger'

export async function GET(req: NextRequest) {
  try {
    // Initialize payment service
    await paymentService.init()
    
    // Get query parameters
    const url = new URL(req.url)
    const transactionId = url.searchParams.get('transactionId')
    const reference = url.searchParams.get('reference')
    const gateway = url.searchParams.get('gateway') as PaymentGateway
    
    // Validate parameters
    if (!transactionId && !reference) {
      return NextResponse.json(
        { error: 'Transaction ID or reference is required' },
        { status: 400 }
      )
    }
    
    logger.info('Checking payment status', {
      transactionId,
      reference,
      gateway
    })
    
    let status: PaymentStatus
    
    if (transactionId) {
      // Check status by transaction ID
      status = await paymentService.getPaymentStatus(transactionId, gateway)
    } else if (reference) {
      // Check status by reference
      // This would typically involve a database lookup
      // For now, we'll return a mock status
      status = PaymentStatus.PENDING
    } else {
      status = PaymentStatus.UNKNOWN
    }
    
    logger.info('Payment status result', {
      transactionId,
      reference,
      status
    })
    
    return NextResponse.json({
      success: true,
      status,
      transactionId,
      reference,
      gateway
    })
    
  } catch (error) {
    logger.error('Error checking payment status', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to check payment status',
        details: error instanceof Error ? error.message : undefined
      },
      { status: 500 }
    )
  }
}