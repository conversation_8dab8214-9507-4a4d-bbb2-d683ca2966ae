/**
 * Create Payment API
 * 
 * This API endpoint creates a new payment.
 */

import { NextRequest, NextResponse } from 'next/server'
import { paymentService, PaymentGateway, PaymentMethod, PaymentRequest } from '@/lib/payment-core'
import { logger } from '@/lib/payment-core/logger'

export async function POST(req: NextRequest) {
  try {
    // Initialize payment service
    await paymentService.init()
    
    // Get request body
    const body = await req.json()
    
    // Validate required fields
    if (!body.amount || !body.gateway) {
      return NextResponse.json(
        { error: 'Amount and gateway are required' },
        { status: 400 }
      )
    }
    
    // Generate a reference if not provided
    const reference = body.reference || `ORDER-${Date.now()}-${Math.floor(Math.random() * 1000)}`
    
    // Create payment request
    const paymentRequest: PaymentRequest = {
      amount: {
        amount: parseFloat(body.amount),
        currency: body.currency || 'ZAR'
      },
      customer: {
        email: body.email || '<EMAIL>',
        firstName: body.firstName || 'Guest',
        lastName: body.lastName || 'Customer',
        phone: body.phone,
        address: body.address
      },
      items: body.items || [
        {
          id: 'default-item',
          name: 'Order Payment',
          quantity: 1,
          unitPrice: parseFloat(body.amount),
          totalPrice: parseFloat(body.amount)
        }
      ],
      metadata: {
        orderId: body.orderId || reference,
        customerId: body.customerId || 'guest',
        source: body.source || 'api'
      },
      returnUrl: body.returnUrl || `${req.nextUrl.origin}/checkout/payment-status?reference=${reference}`,
      cancelUrl: body.cancelUrl || `${req.nextUrl.origin}/checkout/payment-status?reference=${reference}&cancelled=true`,
      notifyUrl: body.notifyUrl || `${req.nextUrl.origin}/api/webhooks/payments`,
      reference,
      description: body.description || `Payment for order ${reference}`,
      paymentMethod: body.paymentMethod as PaymentMethod
    }
    
    logger.info('Creating payment', {
      gateway: body.gateway,
      amount: paymentRequest.amount.amount,
      reference: paymentRequest.reference
    })
    
    // Create payment
    const response = await paymentService.createPayment(
      paymentRequest, 
      body.gateway as PaymentGateway
    )
    
    if (response.success) {
      logger.info('Payment created successfully', {
        gateway: body.gateway,
        transactionId: response.transactionId,
        reference: response.reference
      })
    } else {
      logger.error('Payment creation failed', {
        gateway: body.gateway,
        error: response.error
      })
    }
    
    return NextResponse.json(response)
    
  } catch (error) {
    logger.error('Error creating payment', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create payment',
        details: error instanceof Error ? error.message : undefined
      },
      { status: 500 }
    )
  }
}