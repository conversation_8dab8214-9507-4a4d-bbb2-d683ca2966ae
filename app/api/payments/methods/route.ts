/**
 * Payment Methods API
 * 
 * This API endpoint returns the available payment methods.
 */

import { NextRequest, NextResponse } from 'next/server'
import { paymentService, PaymentMethod, PaymentGateway } from '@/lib/payment-core'
import { logger } from '@/lib/payment-core/logger'

// Payment method icons
const PAYMENT_ICONS = {
  [PaymentMethod.CARD]: '/images/payment/card.svg',
  [PaymentMethod.INSTANT_EFT]: '/images/payment/eft.svg',
  [PaymentMethod.EFT]: '/images/payment/bank.svg',
  [PaymentMethod.QR_CODE]: '/images/payment/qr.svg',
  [PaymentMethod.WALLET]: '/images/payment/wallet.svg',
  [PaymentMethod.VOUCHER]: '/images/payment/voucher.svg',
  [PaymentMethod.CRYPTO]: '/images/payment/crypto.svg',
  [PaymentMethod.MANUAL]: '/images/payment/manual.svg',
  default: '/images/payment/default.svg'
}

// Payment method descriptions
const PAYMENT_DESCRIPTIONS = {
  [PaymentMethod.CARD]: 'Pay securely with your credit or debit card',
  [PaymentMethod.INSTANT_EFT]: 'Pay directly from your bank account',
  [PaymentMethod.EFT]: 'Make a manual bank transfer',
  [PaymentMethod.QR_CODE]: 'Scan a QR code with your banking app',
  [PaymentMethod.WALLET]: 'Pay with your digital wallet',
  [PaymentMethod.VOUCHER]: 'Redeem a voucher or gift card',
  [PaymentMethod.CRYPTO]: 'Pay with cryptocurrency',
  [PaymentMethod.MANUAL]: 'Pay via manual bank transfer',
  default: 'Select this payment method'
}

// Payment method display names
const PAYMENT_NAMES = {
  [PaymentMethod.CARD]: 'Credit/Debit Card',
  [PaymentMethod.INSTANT_EFT]: 'Instant EFT',
  [PaymentMethod.EFT]: 'Manual EFT',
  [PaymentMethod.QR_CODE]: 'Scan to Pay',
  [PaymentMethod.WALLET]: 'Digital Wallet',
  [PaymentMethod.VOUCHER]: 'Voucher',
  [PaymentMethod.CRYPTO]: 'Cryptocurrency',
  [PaymentMethod.MANUAL]: 'Manual Payment',
  default: 'Other Payment Method'
}

export async function GET(req: NextRequest) {
  try {
    // Initialize payment service
    await paymentService.init()
    
    // Get available payment methods
    const availableMethods = await paymentService.getAvailablePaymentMethods()
    
    // Format methods for the frontend
    const methods = availableMethods.map(method => ({
      id: method.method,
      name: PAYMENT_NAMES[method.method] || method.displayName || PAYMENT_NAMES.default,
      description: PAYMENT_DESCRIPTIONS[method.method] || PAYMENT_DESCRIPTIONS.default,
      icon: PAYMENT_ICONS[method.method] || PAYMENT_ICONS.default,
      method: method.method,
      gateways: method.gateways
    }))
    
    logger.info('Payment methods fetched', {
      count: methods.length,
      methods: methods.map(m => m.method)
    })
    
    return NextResponse.json({
      success: true,
      methods
    })
    
  } catch (error) {
    logger.error('Error fetching payment methods', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    // Return fallback methods
    const fallbackMethods = [
      {
        id: PaymentMethod.CARD,
        name: PAYMENT_NAMES[PaymentMethod.CARD],
        description: PAYMENT_DESCRIPTIONS[PaymentMethod.CARD],
        icon: PAYMENT_ICONS[PaymentMethod.CARD],
        method: PaymentMethod.CARD,
        gateways: [PaymentGateway.PAYFAST, PaymentGateway.YOCO]
      },
      {
        id: PaymentMethod.INSTANT_EFT,
        name: PAYMENT_NAMES[PaymentMethod.INSTANT_EFT],
        description: PAYMENT_DESCRIPTIONS[PaymentMethod.INSTANT_EFT],
        icon: PAYMENT_ICONS[PaymentMethod.INSTANT_EFT],
        method: PaymentMethod.INSTANT_EFT,
        gateways: [PaymentGateway.OZOW]
      },
      {
        id: PaymentMethod.QR_CODE,
        name: PAYMENT_NAMES[PaymentMethod.QR_CODE],
        description: PAYMENT_DESCRIPTIONS[PaymentMethod.QR_CODE],
        icon: PAYMENT_ICONS[PaymentMethod.QR_CODE],
        method: PaymentMethod.QR_CODE,
        gateways: [PaymentGateway.SNAPSCAN]
      },
      {
        id: PaymentMethod.EFT,
        name: PAYMENT_NAMES[PaymentMethod.EFT],
        description: PAYMENT_DESCRIPTIONS[PaymentMethod.EFT],
        icon: PAYMENT_ICONS[PaymentMethod.EFT],
        method: PaymentMethod.EFT,
        gateways: [PaymentGateway.MANUAL]
      }
    ]
    
    return NextResponse.json({
      success: true,
      methods: fallbackMethods,
      error: 'Using fallback methods due to error'
    })
  }
}