/**
 * PayFast Payment Status API
 * 
 * Checks the status of a payment using the PayFast gateway
 */

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { PaymentStatus } from '@/lib/payment-core/types'
import { logger } from '@/lib/payment-core/logger'

interface RouteParams {
  params: {
    transactionId: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { transactionId } = params

    if (!transactionId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Missing transaction ID' 
        },
        { status: 400 }
      )
    }

    // Find payment transaction in database
    const transaction = await prisma.paymentTransaction.findUnique({
      where: { transactionId }
    })

    if (!transaction) {
      // Check if it's in the order table
      const order = await prisma.order.findFirst({
        where: { transactionId }
      })

      if (order) {
        return NextResponse.json({
          success: true,
          status: mapOrderStatusToPaymentStatus(order.status),
          orderId: order.id,
          reference: order.reference
        })
      }

      return NextResponse.json(
        { 
          success: false, 
          error: 'Transaction not found' 
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      status: transaction.status,
      transactionId: transaction.transactionId,
      reference: transaction.reference,
      orderId: transaction.orderId
    })

  } catch (error) {
    logger.error('PayFast payment status API error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    })

    return NextResponse.json(
      {
        success: false,
        error: 'An internal error occurred while checking payment status',
      },
      { status: 500 }
    )
  }
}

/**
 * Map order status to payment status
 */
function mapOrderStatusToPaymentStatus(orderStatus: string): PaymentStatus {
  switch (orderStatus.toLowerCase()) {
    case 'paid':
      return PaymentStatus.COMPLETED
    case 'payment_failed':
      return PaymentStatus.FAILED
    case 'cancelled':
      return PaymentStatus.CANCELLED
    case 'refunded':
      return PaymentStatus.REFUNDED
    case 'pending':
      return PaymentStatus.PENDING
    case 'processing':
      return PaymentStatus.PROCESSING
    default:
      return PaymentStatus.PENDING
  }
}