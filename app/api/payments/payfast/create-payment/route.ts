/**
 * PayFast Payment Creation API
 * 
 * Creates a payment using the PayFast gateway
 */

import { NextRequest, NextResponse } from 'next/server'
import { PayfastService } from '@/lib/payment-core/services/payfast-service'
import { PaymentRequest, PaymentMethod } from '@/lib/payment-core/types'
import { logger } from '@/lib/payment-core/logger'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      paymentRequest, 
      method = PaymentMethod.CARD 
    }: { 
      paymentRequest: PaymentRequest
      method?: PaymentMethod 
    } = body

    // Validate request body
    if (!paymentRequest) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            code: 'INVALID_REQUEST', 
            message: 'Missing required field: paymentRequest' 
          } 
        },
        { status: 400 }
      )
    }

    // Validate required fields
    if (!paymentRequest.amount || !paymentRequest.customer || !paymentRequest.reference) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            code: 'INVALID_REQUEST', 
            message: 'Missing required fields in payment request' 
          } 
        },
        { status: 400 }
      )
    }

    // Initialize PayFast service
    const payfastService = new PayfastService()

    // Create payment
    const response = await payfastService.createPayment(paymentRequest)

    logger.info('PayFast payment creation response', {
      success: response.success,
      reference: paymentRequest.reference,
      hasPaymentUrl: !!response.paymentUrl,
    })

    return NextResponse.json(response)

  } catch (error) {
    logger.error('PayFast payment creation API error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    })

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An internal error occurred while processing your payment',
        },
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      error: 'Method not allowed. Use POST to create payments.' 
    },
    { status: 405 }
  )
}