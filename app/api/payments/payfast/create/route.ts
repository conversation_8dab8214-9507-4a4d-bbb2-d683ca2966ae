import { NextRequest, NextResponse } from 'next/server'
import { PayFastGateway } from '@/lib/payments/gateways/payfast'
import { PaymentRequest, PaymentMethod } from '@/lib/payments/types'
import { logger } from '@/lib/payments/logger'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { paymentRequest, method }: { 
      paymentRequest: PaymentRequest
      method: PaymentMethod 
    } = body

    // Validate request body
    if (!paymentRequest || !method) {
      return NextResponse.json(
        { 
          success: false, 
          error: { 
            code: 'INVALID_REQUEST', 
            message: 'Missing required fields: paymentRequest and method' 
          } 
        },
        { status: 400 }
      )
    }

    // Initialize PayFast gateway
    const gateway = new PayFastGateway()

    // Create payment
    const response = await gateway.createPayment(paymentRequest)

    logger.info('PayFast payment creation response', {
      success: response.success,
      reference: paymentRequest.reference,
      hasPaymentUrl: !!response.paymentUrl,
    })

    return NextResponse.json(response)

  } catch (error) {
    logger.error('PayFast payment creation API error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    })

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An internal error occurred while processing your payment',
        },
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      error: 'Method not allowed. Use POST to create payments.' 
    },
    { status: 405 }
  )
}