import { NextResponse } from 'next/server';
import { settingsService } from '@/lib/settings/settings-service';
import { PayFastService, OzowService } from '@/lib/ecommerce/services/payment-service';

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { gateway } = body;

    if (!gateway) {
      return new NextResponse('Gateway parameter is required', { status: 400 });
    }

    const paymentSettings = await settingsService.getPaymentSettings();
    
    let result: {
      success: boolean;
      message: string;
      details: null | Record<string, any>;
    } = {
      success: false,
      message: 'Connection failed',
      details: null
    };

    switch (gateway) {
      case 'payfast':
        if (!paymentSettings.payfast.merchantId || !paymentSettings.payfast.merchantKey) {
          result.message = 'PayFast credentials are missing';
        } else {
          try {
            // Create service instance
            const payfastService = new PayFastService();
            
            // Manually load configuration to test connection
            await payfastService.testConnection(
              paymentSettings.payfast.merchantId,
              paymentSettings.payfast.merchantKey,
              paymentSettings.payfast.passphrase,
              paymentSettings.payfast.testMode
            );
            
            result.success = true;
            result.message = 'PayFast connection successful';
            result.details = {
              merchantId: paymentSettings.payfast.merchantId,
              testMode: paymentSettings.payfast.testMode,
              environment: paymentSettings.payfast.testMode ? 'sandbox' : 'production'
            };
          } catch (error: any) {
            console.error('PayFast test connection error:', error);
            result.message = `PayFast connection failed: ${error.message || 'Unknown error'}`;
          }
        }
        break;
        
      case 'ozow':
        if (!paymentSettings.ozow.siteCode || !paymentSettings.ozow.privateKey) {
          result.message = 'Ozow credentials are missing';
        } else {
          try {
            // Create service instance
            const ozowService = new OzowService();
            
            // Manually load configuration to test connection
            await ozowService.testConnection(
              paymentSettings.ozow.siteCode,
              paymentSettings.ozow.privateKey,
              paymentSettings.ozow.testMode
            );
            
            result.success = true;
            result.message = 'Ozow connection successful';
            result.details = {
              siteCode: paymentSettings.ozow.siteCode,
              testMode: paymentSettings.ozow.testMode,
              environment: paymentSettings.ozow.testMode ? 'staging' : 'production'
            };
          } catch (error: any) {
            console.error('Ozow test connection error:', error);
            result.message = `Ozow connection failed: ${error.message || 'Unknown error'}`;
          }
        }
        break;
        
      case 'stripe':
        if (!paymentSettings.stripe.secretKey || !paymentSettings.stripe.publishableKey) {
          result.message = 'Stripe credentials are missing';
        } else {
          // In a real app, you would use the Stripe SDK to validate the API key
          result.success = true;
          result.message = 'Stripe connection successful';
          result.details = {
            publishableKey: `${paymentSettings.stripe.publishableKey.substring(0, 8)}...`,
            testMode: paymentSettings.stripe.testMode,
            environment: paymentSettings.stripe.testMode ? 'test' : 'live'
          };
        }
        break;
        
      case 'paypal':
        if (!paymentSettings.paypal.clientId || !paymentSettings.paypal.clientSecret) {
          result.message = 'PayPal credentials are missing';
        } else {
          // In a real app, you would use the PayPal SDK to validate the credentials
          result.success = true;
          result.message = 'PayPal connection successful';
          result.details = {
            clientId: `${paymentSettings.paypal.clientId.substring(0, 8)}...`,
            mode: paymentSettings.paypal.mode,
            environment: paymentSettings.paypal.testMode ? 'sandbox' : 'live'
          };
        }
        break;
        
      default:
        result.message = `Unknown gateway: ${gateway}`;
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('[PAYMENTS_TEST_CONNECTION]', error);
    return new NextResponse('Internal error', { status: 500 });
  }
}