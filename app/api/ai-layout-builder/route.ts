import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { z } from 'zod'
import { Layout, LayoutSection, LayoutBlock } from '@/lib/layout-builder/types'

// Validation schema
const aiLayoutSchema = z.object({
  messages: z.array(z.object({
    role: z.enum(['user', 'assistant', 'system']),
    content: z.string()
  })),
  layoutType: z.enum(['site', 'page', 'post-type', 'template']).optional(),
  category: z.enum(['ecommerce', 'blog', 'portfolio', 'landing', 'corporate', 'custom']).optional(),
  style: z.enum(['modern', 'minimal', 'bold', 'elegant', 'playful']).optional(),
  responsive: z.boolean().default(true),
  accessibility: z.boolean().default(true),
  seo: z.boolean().default(true)
})

// POST /api/ai-layout-builder - Generate layout using AI
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = aiLayoutSchema.parse(body)
    
    const { messages, layoutType, category, style, responsive, accessibility, seo } = validatedData

    // Extract the user's prompt
    const userMessage = messages.find(m => m.role === 'user')?.content
    if (!userMessage) {
      return NextResponse.json(
        { error: 'No user message found' },
        { status: 400 }
      )
    }

    // Generate layout using AI
    const generatedLayout = await generateAILayout({
      prompt: userMessage,
      layoutType: layoutType || 'page',
      category: category || 'custom',
      style: style || 'modern',
      responsive,
      accessibility,
      seo
    })

    return NextResponse.json({
      layout: generatedLayout,
      message: 'Layout generated successfully using AI'
    })

  } catch (error) {
    console.error('Error generating AI layout:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to generate AI layout' },
      { status: 500 }
    )
  }
}

// AI Layout Generation Function
async function generateAILayout(params: {
  prompt: string
  layoutType: string
  category: string
  style: string
  responsive: boolean
  accessibility: boolean
  seo: boolean
}): Promise<Layout> {
  const { prompt, layoutType, category, style, responsive, accessibility, seo } = params

  // Analyze the prompt to determine layout requirements
  const analysis = analyzePrompt(prompt)
  
  // Generate layout based on analysis
  const layout = createLayoutFromAnalysis(analysis, {
    layoutType,
    category,
    style,
    responsive,
    accessibility,
    seo
  })

  return layout
}

// Analyze user prompt to extract layout requirements
function analyzePrompt(prompt: string) {
  const promptLower = prompt.toLowerCase()
  
  const analysis = {
    sections: [] as string[],
    features: [] as string[],
    style: 'modern',
    purpose: 'general',
    hasHeader: false,
    hasFooter: false,
    hasSidebar: false,
    hasNavigation: false,
    hasSearch: false,
    hasCart: false,
    hasLogin: false,
    hasContact: false,
    hasBlog: false,
    hasGallery: false,
    hasTestimonials: false,
    hasFeatures: false,
    hasPricing: false,
    hasTeam: false
  }

  // Detect sections
  if (promptLower.includes('header') || promptLower.includes('navigation') || promptLower.includes('nav')) {
    analysis.sections.push('header')
    analysis.hasHeader = true
    analysis.hasNavigation = true
  }

  if (promptLower.includes('footer')) {
    analysis.sections.push('footer')
    analysis.hasFooter = true
  }

  if (promptLower.includes('sidebar') || promptLower.includes('side bar')) {
    analysis.sections.push('sidebar')
    analysis.hasSidebar = true
  }

  // Always include main content
  analysis.sections.push('main')

  // Detect features
  if (promptLower.includes('search')) {
    analysis.hasSearch = true
    analysis.features.push('search')
  }

  if (promptLower.includes('cart') || promptLower.includes('shopping') || promptLower.includes('ecommerce')) {
    analysis.hasCart = true
    analysis.features.push('cart')
    analysis.purpose = 'ecommerce'
  }

  if (promptLower.includes('login') || promptLower.includes('sign in') || promptLower.includes('account')) {
    analysis.hasLogin = true
    analysis.features.push('login')
  }

  if (promptLower.includes('contact') || promptLower.includes('contact form')) {
    analysis.hasContact = true
    analysis.features.push('contact')
  }

  if (promptLower.includes('blog') || promptLower.includes('articles') || promptLower.includes('posts')) {
    analysis.hasBlog = true
    analysis.features.push('blog')
    analysis.purpose = 'blog'
  }

  if (promptLower.includes('gallery') || promptLower.includes('portfolio') || promptLower.includes('showcase')) {
    analysis.hasGallery = true
    analysis.features.push('gallery')
    analysis.purpose = 'portfolio'
  }

  if (promptLower.includes('testimonial') || promptLower.includes('review')) {
    analysis.hasTestimonials = true
    analysis.features.push('testimonials')
  }

  if (promptLower.includes('feature') || promptLower.includes('service')) {
    analysis.hasFeatures = true
    analysis.features.push('features')
  }

  if (promptLower.includes('pricing') || promptLower.includes('plan')) {
    analysis.hasPricing = true
    analysis.features.push('pricing')
  }

  if (promptLower.includes('team') || promptLower.includes('about us') || promptLower.includes('staff')) {
    analysis.hasTeam = true
    analysis.features.push('team')
  }

  // Detect style preferences
  if (promptLower.includes('minimal') || promptLower.includes('clean') || promptLower.includes('simple')) {
    analysis.style = 'minimal'
  } else if (promptLower.includes('bold') || promptLower.includes('vibrant') || promptLower.includes('colorful')) {
    analysis.style = 'bold'
  } else if (promptLower.includes('elegant') || promptLower.includes('sophisticated') || promptLower.includes('luxury')) {
    analysis.style = 'elegant'
  } else if (promptLower.includes('playful') || promptLower.includes('fun') || promptLower.includes('creative')) {
    analysis.style = 'playful'
  }

  // Detect purpose if not already set
  if (analysis.purpose === 'general') {
    if (promptLower.includes('landing') || promptLower.includes('marketing')) {
      analysis.purpose = 'landing'
    } else if (promptLower.includes('corporate') || promptLower.includes('business')) {
      analysis.purpose = 'corporate'
    }
  }

  return analysis
}

// Create layout from analysis
function createLayoutFromAnalysis(analysis: any, options: any): Layout {
  const layoutId = `ai-layout-${Date.now()}`
  
  const layout: Layout = {
    id: layoutId,
    name: `AI Generated Layout`,
    description: `Layout generated based on user requirements`,
    type: options.layoutType,
    category: options.category,
    structure: {},
    styling: generateStyling(analysis.style, options),
    responsive: generateResponsiveSettings(options.responsive),
    conditions: {},
    isTemplate: false,
    isSystem: false,
    isActive: true,
    usageCount: 0,
    tags: ['ai-generated', analysis.style, analysis.purpose],
    createdAt: new Date(),
    updatedAt: new Date()
  }

  // Generate sections
  let position = 1

  // Header section
  if (analysis.hasHeader) {
    layout.structure.header = createHeaderSection(analysis, position++)
  }

  // Main section (always present)
  layout.structure.main = createMainSection(analysis, position++)

  // Sidebar section
  if (analysis.hasSidebar) {
    layout.structure.sidebar = createSidebarSection(analysis, position++)
  }

  // Footer section
  if (analysis.hasFooter) {
    layout.structure.footer = createFooterSection(analysis, position++)
  }

  return layout
}

// Create header section
function createHeaderSection(analysis: any, position: number): LayoutSection {
  const blocks: LayoutBlock[] = []
  let blockPosition = 1

  // Logo block
  blocks.push(createBlock('logo', 'Logo', blockPosition++, {
    text: 'Your Logo',
    links: [{ id: '1', text: 'Home', url: '/', target: '_self' }]
  }))

  // Navigation block
  if (analysis.hasNavigation) {
    blocks.push(createBlock('navigation', 'Main Navigation', blockPosition++, {
      menu: 'main-menu'
    }))
  }

  // Search block
  if (analysis.hasSearch) {
    blocks.push(createBlock('search', 'Search', blockPosition++, {
      text: 'Search...'
    }))
  }

  // Cart block
  if (analysis.hasCart) {
    blocks.push(createBlock('cart', 'Shopping Cart', blockPosition++, {}))
  }

  return {
    id: `header-${Date.now()}`,
    type: 'header',
    name: 'Header',
    position,
    blocks,
    configuration: {
      layout: 'flex',
      alignment: 'center',
      spacing: { top: '1rem', right: '2rem', bottom: '1rem', left: '2rem' },
      background: { type: 'color', color: '#ffffff' },
      container: { maxWidth: '1200px', padding: { top: '0', right: '1rem', bottom: '0', left: '1rem' } }
    },
    styling: {
      background: { type: 'color', color: '#ffffff' },
      border: { width: '0', style: 'none', color: 'transparent' },
      spacing: { top: '0', right: '0', bottom: '1px', left: '0' },
      shadow: { type: 'box', x: 0, y: 1, blur: 3, spread: 0, color: 'rgba(0,0,0,0.1)' }
    },
    responsive: {
      mobile: { isVisible: true },
      tablet: { isVisible: true },
      desktop: { isVisible: true },
      large: { isVisible: true }
    },
    isVisible: true
  }
}

// Create main section
function createMainSection(analysis: any, position: number): LayoutSection {
  const blocks: LayoutBlock[] = []
  let blockPosition = 1

  // Hero/Content block
  blocks.push(createBlock('content', 'Main Content', blockPosition++, {
    html: '<h1>Welcome to Your Website</h1><p>This is your main content area. Customize it to fit your needs.</p>'
  }))

  // Add feature-specific blocks
  if (analysis.hasFeatures) {
    blocks.push(createBlock('content', 'Features Section', blockPosition++, {
      html: '<h2>Our Features</h2><p>Showcase your key features here.</p>'
    }))
  }

  if (analysis.hasGallery) {
    blocks.push(createBlock('content', 'Gallery', blockPosition++, {
      html: '<h2>Gallery</h2><p>Display your portfolio or image gallery here.</p>'
    }))
  }

  if (analysis.hasTestimonials) {
    blocks.push(createBlock('content', 'Testimonials', blockPosition++, {
      html: '<h2>What Our Customers Say</h2><p>Customer testimonials go here.</p>'
    }))
  }

  if (analysis.hasPricing) {
    blocks.push(createBlock('content', 'Pricing', blockPosition++, {
      html: '<h2>Pricing Plans</h2><p>Display your pricing options here.</p>'
    }))
  }

  if (analysis.hasTeam) {
    blocks.push(createBlock('content', 'Our Team', blockPosition++, {
      html: '<h2>Meet Our Team</h2><p>Introduce your team members here.</p>'
    }))
  }

  if (analysis.hasContact) {
    blocks.push(createBlock('content', 'Contact Us', blockPosition++, {
      html: '<h2>Get In Touch</h2><p>Contact form and information goes here.</p>'
    }))
  }

  return {
    id: `main-${Date.now()}`,
    type: 'main',
    name: 'Main Content',
    position,
    blocks,
    configuration: {
      layout: 'block',
      alignment: 'left',
      spacing: { top: '2rem', right: '2rem', bottom: '2rem', left: '2rem' },
      background: { type: 'color', color: '#ffffff' },
      container: { maxWidth: '1200px', padding: { top: '0', right: '1rem', bottom: '0', left: '1rem' } }
    },
    styling: {
      background: { type: 'color', color: '#ffffff' },
      border: { width: '0', style: 'none', color: 'transparent' },
      spacing: { top: '0', right: '0', bottom: '0', left: '0' },
      shadow: { type: 'none' }
    },
    responsive: {
      mobile: { isVisible: true },
      tablet: { isVisible: true },
      desktop: { isVisible: true },
      large: { isVisible: true }
    },
    isVisible: true
  }
}

// Create sidebar section
function createSidebarSection(analysis: any, position: number): LayoutSection {
  const blocks: LayoutBlock[] = []
  let blockPosition = 1

  if (analysis.hasBlog) {
    blocks.push(createBlock('recent-posts', 'Recent Posts', blockPosition++, {}))
    blocks.push(createBlock('categories', 'Categories', blockPosition++, {}))
    blocks.push(createBlock('tags', 'Tags', blockPosition++, {}))
  } else {
    blocks.push(createBlock('widget', 'Sidebar Widget', blockPosition++, {
      widget: 'sidebar-widget'
    }))
  }

  return {
    id: `sidebar-${Date.now()}`,
    type: 'sidebar',
    name: 'Sidebar',
    position,
    blocks,
    configuration: {
      layout: 'block',
      alignment: 'left',
      spacing: { top: '2rem', right: '1rem', bottom: '2rem', left: '1rem' },
      background: { type: 'color', color: '#f8f9fa' },
      container: { maxWidth: '300px', padding: { top: '0', right: '1rem', bottom: '0', left: '1rem' } }
    },
    styling: {
      background: { type: 'color', color: '#f8f9fa' },
      border: { width: '1px', style: 'solid', color: '#e9ecef' },
      spacing: { top: '0', right: '0', bottom: '0', left: '0' },
      shadow: { type: 'none' }
    },
    responsive: {
      mobile: { isVisible: false },
      tablet: { isVisible: true },
      desktop: { isVisible: true },
      large: { isVisible: true }
    },
    isVisible: true
  }
}

// Create footer section
function createFooterSection(analysis: any, position: number): LayoutSection {
  const blocks: LayoutBlock[] = []
  let blockPosition = 1

  // Links block
  blocks.push(createBlock('links', 'Footer Links', blockPosition++, {
    links: [
      { id: '1', text: 'About', url: '/about', target: '_self' },
      { id: '2', text: 'Contact', url: '/contact', target: '_self' },
      { id: '3', text: 'Privacy', url: '/privacy', target: '_self' }
    ]
  }))

  // Social links
  blocks.push(createBlock('social', 'Social Media', blockPosition++, {
    links: [
      { id: '1', text: 'Facebook', url: '#', target: '_blank' },
      { id: '2', text: 'Twitter', url: '#', target: '_blank' },
      { id: '3', text: 'Instagram', url: '#', target: '_blank' }
    ]
  }))

  // Copyright
  blocks.push(createBlock('copyright', 'Copyright', blockPosition++, {
    text: `© ${new Date().getFullYear()} Your Company. All rights reserved.`
  }))

  return {
    id: `footer-${Date.now()}`,
    type: 'footer',
    name: 'Footer',
    position,
    blocks,
    configuration: {
      layout: 'flex',
      alignment: 'center',
      spacing: { top: '2rem', right: '2rem', bottom: '2rem', left: '2rem' },
      background: { type: 'color', color: '#343a40' },
      container: { maxWidth: '1200px', padding: { top: '0', right: '1rem', bottom: '0', left: '1rem' } }
    },
    styling: {
      background: { type: 'color', color: '#343a40' },
      border: { width: '1px', style: 'solid', color: '#495057' },
      spacing: { top: '1px', right: '0', bottom: '0', left: '0' },
      shadow: { type: 'none' }
    },
    responsive: {
      mobile: { isVisible: true },
      tablet: { isVisible: true },
      desktop: { isVisible: true },
      large: { isVisible: true }
    },
    isVisible: true
  }
}

// Helper function to create blocks
function createBlock(type: string, name: string, position: number, content: any): LayoutBlock {
  return {
    id: `${type}-${Date.now()}-${position}`,
    type: type as any,
    name,
    position,
    configuration: {
      size: 'medium',
      alignment: 'left',
      spacing: { top: '0.5rem', right: '0.5rem', bottom: '0.5rem', left: '0.5rem' },
      animation: { type: 'none' }
    },
    content,
    styling: {
      background: { type: 'none' },
      border: { width: '0', style: 'none', color: 'transparent' },
      spacing: { top: '0', right: '0', bottom: '0', left: '0' },
      typography: {
        fontFamily: 'inherit',
        fontSize: '16px',
        lineHeight: '1.5',
        fontWeight: 'normal'
      },
      colors: {
        primary: '#007bff',
        secondary: '#6c757d',
        background: 'transparent',
        text: '#212529'
      },
      shadow: { type: 'none' }
    },
    responsive: {
      mobile: { isVisible: true },
      tablet: { isVisible: true },
      desktop: { isVisible: true },
      large: { isVisible: true }
    },
    conditions: {},
    isVisible: true
  }
}

// Generate styling based on style preference
function generateStyling(style: string, options: any) {
  const baseColors = {
    modern: {
      primary: '#007bff',
      secondary: '#6c757d',
      accent: '#28a745',
      text: '#212529',
      background: '#ffffff',
      border: '#dee2e6'
    },
    minimal: {
      primary: '#000000',
      secondary: '#666666',
      accent: '#333333',
      text: '#000000',
      background: '#ffffff',
      border: '#e0e0e0'
    },
    bold: {
      primary: '#ff6b35',
      secondary: '#004e89',
      accent: '#ffd23f',
      text: '#1a1a1a',
      background: '#ffffff',
      border: '#cccccc'
    },
    elegant: {
      primary: '#2c3e50',
      secondary: '#95a5a6',
      accent: '#e74c3c',
      text: '#2c3e50',
      background: '#ffffff',
      border: '#bdc3c7'
    },
    playful: {
      primary: '#e91e63',
      secondary: '#9c27b0',
      accent: '#ff9800',
      text: '#212121',
      background: '#ffffff',
      border: '#e0e0e0'
    }
  }

  return {
    theme: style,
    colorScheme: 'light',
    typography: {
      fontFamily: 'Inter, sans-serif',
      fontSize: '16px',
      lineHeight: '1.5',
      fontWeight: 'normal'
    },
    spacing: { top: '0', right: '0', bottom: '0', left: '0' },
    colors: baseColors[style as keyof typeof baseColors] || baseColors.modern
  }
}

// Generate responsive settings
function generateResponsiveSettings(responsive: boolean) {
  if (!responsive) {
    return {
      mobile: { breakpoint: '768px', isVisible: true },
      tablet: { breakpoint: '1024px', isVisible: true },
      desktop: { breakpoint: '1280px', isVisible: true },
      large: { breakpoint: '1536px', isVisible: true }
    }
  }

  return {
    mobile: { 
      breakpoint: '768px', 
      isVisible: true,
      display: 'block',
      width: '100%',
      height: 'auto',
      spacing: { top: '1rem', right: '1rem', bottom: '1rem', left: '1rem' },
      typography: {
        fontFamily: 'Inter, sans-serif',
        fontSize: '14px',
        fontWeight: 400,
        lineHeight: 1.5,
        letterSpacing: 0,
        textAlign: 'left',
        textTransform: 'none'
      }
    },
    tablet: { 
      breakpoint: '1024px', 
      isVisible: true,
      display: 'block',
      width: '100%',
      height: 'auto',
      spacing: { top: '1.5rem', right: '1.5rem', bottom: '1.5rem', left: '1.5rem' },
      typography: {
        fontFamily: 'Inter, sans-serif',
        fontSize: '15px',
        fontWeight: 400,
        lineHeight: 1.5,
        letterSpacing: 0,
        textAlign: 'left',
        textTransform: 'none'
      }
    },
    desktop: { 
      breakpoint: '1280px', 
      isVisible: true,
      display: 'block',
      width: '100%',
      height: 'auto',
      spacing: { top: '2rem', right: '2rem', bottom: '2rem', left: '2rem' },
      typography: {
        fontFamily: 'Inter, sans-serif',
        fontSize: '16px',
        fontWeight: 400,
        lineHeight: 1.5,
        letterSpacing: 0,
        textAlign: 'left',
        textTransform: 'none'
      }
    },
    large: { 
      breakpoint: '1536px', 
      isVisible: true,
      display: 'block',
      width: '100%',
      height: 'auto',
      spacing: { top: '2rem', right: '2rem', bottom: '2rem', left: '2rem' },
      typography: {
        fontFamily: 'Inter, sans-serif',
        fontSize: '16px',
        fontWeight: 400,
        lineHeight: 1.5,
        letterSpacing: 0,
        textAlign: 'left',
        textTransform: 'none'
      }
    }
  }
}