import { NextRequest, NextResponse } from 'next/server'
import { appwriteNotificationService } from '@/lib/notifications/services/appwrite'
import { auth } from '@/lib/auth'
import { z } from 'zod'
import { logger } from '@/lib/notifications/utils'

// Request validation schemas
const getNotificationsSchema = z.object({
  userId: z.string(),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 50),
  offset: z.string().optional().transform(val => val ? parseInt(val) : 0),
  type: z.string().optional(),
  channel: z.string().optional(),
  unreadOnly: z.string().optional().transform(val => val === 'true'),
  search: z.string().optional()
})

/**
 * GET /api/notifications
 * Fetch notifications for a user with filtering and pagination using Appwrite
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse and validate query parameters
    const url = new URL(request.url)
    const params = Object.fromEntries(url.searchParams.entries())
    
    const validation = getNotificationsSchema.safeParse(params)
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid parameters',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const { userId, limit, offset, type, channel, unreadOnly, search } = validation.data

    // Check authorization - users can only access their own notifications
    if (userId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    // Prepare options for Appwrite service
    const options: any = { 
      limit,
      offset,
      unreadOnly
    }
    
    if (type) {
      options.type = type
    }
    
    if (search) {
      options.search = search
    }

    // Fetch notifications from Appwrite
    const notifications = await appwriteNotificationService.getNotifications(userId, options)
    
    // Filter by channel if needed (client-side filtering)
    let filteredNotifications = notifications
    if (channel) {
      filteredNotifications = notifications.filter(n => n.channel === channel)
    }
    
    // Get unread count
    const unreadCount = await appwriteNotificationService.getUnreadCount(userId)

    return NextResponse.json({
      success: true,
      notifications: filteredNotifications,
      pagination: {
        total: notifications.length,
        limit,
        offset,
        hasMore: offset + limit < notifications.length
      },
      unreadCount
    })

  } catch (error: any) {
    logger.error('GET /api/notifications error:', { error })
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/notifications
 * Create a new notification (admin only) using Appwrite
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate admin user
    const session = await auth()
    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const body = await request.json()

    // Validate required fields
    const requiredFields = ['type', 'channel', 'title', 'content']
    const missingFields = requiredFields.filter(field => !body[field])
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: `Missing required fields: ${missingFields.join(', ')}` 
        },
        { status: 400 }
      )
    }

    // Create notification using Appwrite service
    const result = await appwriteNotificationService.send(body)

    if (!result.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: result.error || 'Failed to create notification'
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      notificationId: result.notificationId,
      messageId: result.messageId
    })

  } catch (error: any) {
    logger.error('POST /api/notifications error:', { error })
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/notifications
 * Bulk delete notifications (admin only)
 * Note: This is not fully implemented in Appwrite service yet
 */
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate admin user
    const session = await auth()
    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { notificationIds, userId, olderThan } = body

    if (!notificationIds && !userId && !olderThan) {
      return NextResponse.json(
        { success: false, error: 'No deletion criteria provided' },
        { status: 400 }
      )
    }

    // For now, we'll return a not implemented response
    // This would require a custom function in Appwrite
    return NextResponse.json(
      { success: false, error: 'Bulk delete not implemented yet with Appwrite' },
      { status: 501 }
    )

  } catch (error: any) {
    logger.error('DELETE /api/notifications error:', { error })
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}
