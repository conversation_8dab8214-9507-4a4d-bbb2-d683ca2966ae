import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { appwriteNotificationService } from '@/lib/notifications/services/appwrite'
import { z } from 'zod'
import { ratelimit } from '@/lib/ratelimit'
import { logger } from '@/lib/notifications/utils'
import { NotificationRequest, NotificationResult, NotificationType, NotificationChannel, RecipientType, NotificationPriority } from '@/lib/notifications'

// Request validation schema for admin notifications
const adminNotificationSchema = z.object({
  title: z.string().min(1).max(255),
  message: z.string().min(1).max(10000),
  type: z.enum(['info', 'success', 'warning', 'error']).default('info'),
  channels: z.array(z.enum(['EMAIL', 'IN_APP', 'PUSH'])).default(['IN_APP']),
  data: z.record(z.any()).optional(),
});

/**
 * POST /api/notifications/admin
 * Send an admin notification
 * This is a simplified API for admin notifications that doesn't require recipientId
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if user is admin
    if (!session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Rate limiting
    const identifier = `admin:${session.user.id}`
    const { success: rateLimitSuccess } = await ratelimit.limit(identifier)
    
    if (!rateLimitSuccess) {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validation = adminNotificationSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const { title, message, type, channels, data } = validation.data

    // Send notifications for each channel
    const results: NotificationResult[] = []
    
    for (const channel of channels) {
      // Create notification request
      const notificationRequest: NotificationRequest = {
        type: NotificationType.CUSTOM,
        channel: channel as NotificationChannel,
        title,
        content: message,
        recipientType: RecipientType.ADMIN,
        data: data || {},
        // For IN_APP notifications, use the current user's ID
        recipientId: session.user.id,
      }

      // Add email for EMAIL notifications if available
      if (channel === NotificationChannel.EMAIL && session.user.email) {
        notificationRequest.recipientEmail = session.user.email
      }

      // Add metadata
      notificationRequest.metadata = {
        sentBy: session.user.id,
        sentAt: new Date().toISOString(),
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
      }

      // Send notification
      const result = await appwriteNotificationService.send(notificationRequest)
      results.push(result)
    }

    // Check for failures
    const failures = results.filter(result => !result.success)
    
    if (failures.length > 0) {
      logger.error('Some admin notifications failed to send:', { failures })
      
      return NextResponse.json({
        success: false,
        error: 'Some notifications failed to send',
        results
      })
    }

    return NextResponse.json({
      success: true,
      results
    })

  } catch (error: any) {
    logger.error('POST /api/notifications/admin error:', { error })
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}