import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { appwriteNotificationService } from '@/lib/notifications/services/appwrite'
import { z } from 'zod'
import { ratelimit } from '@/lib/ratelimit'
import { logger } from '@/lib/notifications/utils'
import { NotificationRequest, NotificationResult, NotificationType, NotificationChannel, RecipientType, NotificationPriority } from '@/lib/notifications'

// Request validation schema
const sendNotificationSchema = z.object({
  type: z.nativeEnum(NotificationType),
  channel: z.nativeEnum(NotificationChannel),
  title: z.string().min(1).max(255),
  content: z.string().min(1).max(10000),
  recipientId: z.string().optional(),
  recipientType: z.nativeEnum(RecipientType).optional(),
  recipientEmail: z.string().email().optional(),
  recipientPhone: z.string().optional(),
  data: z.record(z.any()).optional(),
  templateId: z.string().optional(),
  priority: z.nativeEnum(NotificationPriority).optional(),
  scheduledAt: z.union([z.string().datetime(), z.date()]).optional(),
  expiresAt: z.union([z.string().datetime(), z.date()]).optional(),
  metadata: z.record(z.any()).optional()
})

/**
 * POST /api/notifications/send
 * Send a notification through Appwrite
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Rate limiting
    const identifier = session.user.isAdmin ? `admin:${session.user.id}` : `user:${session.user.id}`
    const { success: rateLimitSuccess } = await ratelimit.limit(identifier)
    
    if (!rateLimitSuccess) {
      return NextResponse.json(
        { success: false, error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Parse and validate request body
    const body = await request.json()
    const validation = sendNotificationSchema.safeParse(body)
    
    if (!validation.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request data',
          details: validation.error.errors
        },
        { status: 400 }
      )
    }

    const notificationRequest = validation.data

    // Authorization checks
    if (!session.user.isAdmin) {
      // Non-admin users can only send to themselves
      if (notificationRequest.recipientId && notificationRequest.recipientId !== session.user.id) {
        return NextResponse.json(
          { success: false, error: 'Cannot send notifications to other users' },
          { status: 403 }
        )
      }

      // Non-admin users can only send certain types
      const allowedTypes = [NotificationType.CUSTOM]
      if (!allowedTypes.includes(notificationRequest.type)) {
        return NextResponse.json(
          { success: false, error: 'Notification type not allowed' },
          { status: 403 }
        )
      }

      // Limit channels for non-admin users
      const allowedChannels = [NotificationChannel.IN_APP]
      if (!allowedChannels.includes(notificationRequest.channel)) {
        return NextResponse.json(
          { success: false, error: 'Notification channel not allowed' },
          { status: 403 }
        )
      }
    }

    // Channel-specific validation
    if (notificationRequest.channel === NotificationChannel.EMAIL && !notificationRequest.recipientEmail) {
      return NextResponse.json(
        { success: false, error: 'recipientEmail is required for email notifications' },
        { status: 400 }
      )
    }

    if (notificationRequest.channel === NotificationChannel.SMS && !notificationRequest.recipientPhone) {
      return NextResponse.json(
        { success: false, error: 'recipientPhone is required for SMS notifications' },
        { status: 400 }
      )
    }

    if ([NotificationChannel.PUSH, NotificationChannel.IN_APP].includes(notificationRequest.channel) && !notificationRequest.recipientId) {
      return NextResponse.json(
        { success: false, error: 'recipientId is required for push and in-app notifications' },
        { status: 400 }
      )
    }

    // Add metadata
    const enrichedRequest = {
      ...notificationRequest,
      metadata: {
        ...(notificationRequest.metadata || {}),
        sentBy: session.user.id,
        sentAt: new Date().toISOString(),
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
      }
    }

    // Convert scheduledAt and expiresAt to Date objects if present
    if (enrichedRequest.scheduledAt) {
      enrichedRequest.scheduledAt = typeof enrichedRequest.scheduledAt === 'string' 
        ? new Date(enrichedRequest.scheduledAt) 
        : enrichedRequest.scheduledAt
    }
    
    if (enrichedRequest.expiresAt) {
      enrichedRequest.expiresAt = typeof enrichedRequest.expiresAt === 'string' 
        ? new Date(enrichedRequest.expiresAt) 
        : enrichedRequest.expiresAt
    }
    
    // Ensure the request conforms to NotificationRequest type
    const typedRequest: NotificationRequest = {
      ...enrichedRequest,
      scheduledAt: enrichedRequest.scheduledAt as Date | undefined,
      expiresAt: enrichedRequest.expiresAt as Date | undefined
    }

    // Send notification using Appwrite service
    const result = await appwriteNotificationService.send(typedRequest)

    // Log the attempt
    logger.info('Notification send attempt:', {
      success: result.success,
      type: notificationRequest.type,
      channel: notificationRequest.channel,
      recipient: notificationRequest.recipientEmail || notificationRequest.recipientPhone || notificationRequest.recipientId,
      sentBy: session.user.id,
      error: result.error
    })

    return NextResponse.json(result)

  } catch (error: any) {
    logger.error('POST /api/notifications/send error:', { error })
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/notifications/send
 * Send multiple notifications (admin only)
 * Note: This is not fully implemented with Appwrite yet
 */
export async function PUT(request: NextRequest) {
  try {
    // Authenticate admin user
    const session = await auth()
    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { notifications } = body

    if (!Array.isArray(notifications) || notifications.length === 0) {
      return NextResponse.json(
        { success: false, error: 'notifications array is required' },
        { status: 400 }
      )
    }

    if (notifications.length > 1000) {
      return NextResponse.json(
        { success: false, error: 'Maximum 1000 notifications per batch' },
        { status: 400 }
      )
    }

    // Validate each notification
    const validationErrors: string[] = []
    // First map to create array that might contain nulls
    const mappedNotifications: (NotificationRequest | null)[] = notifications.map((notification, index) => {
      const validation = sendNotificationSchema.safeParse(notification)
      if (!validation.success) {
        validationErrors.push(`Notification ${index}: ${validation.error.errors.map(e => e.message).join(', ')}`)
        return null
      }
      return {
        ...validation.data,
        metadata: {
          ...(validation.data.metadata || {}),
          sentBy: session.user.id,
          sentAt: new Date().toISOString(),
          batchId: `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        }
      } as NotificationRequest
    })
    
    // Then filter out nulls and explicitly type the result
    const validatedNotifications: NotificationRequest[] = mappedNotifications.filter((n): n is NotificationRequest => n !== null)

    if (validationErrors.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation errors',
          details: validationErrors
        },
        { status: 400 }
      )
    }

    // Send notifications one by one using Appwrite service
    const results: NotificationResult[] = []
    for (const notification of validatedNotifications) {
      // Convert scheduledAt and expiresAt to Date objects if present
      if (notification.scheduledAt) {
        notification.scheduledAt = typeof notification.scheduledAt === 'string' 
          ? new Date(notification.scheduledAt) 
          : notification.scheduledAt
      }
      
      if (notification.expiresAt) {
        notification.expiresAt = typeof notification.expiresAt === 'string' 
          ? new Date(notification.expiresAt) 
          : notification.expiresAt
      }
      
      // Ensure the notification conforms to NotificationRequest type
      const typedNotification: NotificationRequest = {
        ...notification,
        scheduledAt: notification.scheduledAt as Date | undefined,
        expiresAt: notification.expiresAt as Date | undefined
      }
      
      const result = await appwriteNotificationService.send(typedNotification)
      results.push(result)
    }

    const successCount = results.filter(r => r.success).length
    const failureCount = results.length - successCount

    logger.info('Bulk notification send completed:', {
      total: results.length,
      success: successCount,
      failed: failureCount,
      sentBy: session.user.id
    })

    return NextResponse.json({
      success: true,
      results,
      summary: {
        total: results.length,
        success: successCount,
        failed: failureCount
      }
    })

  } catch (error: any) {
    logger.error('PUT /api/notifications/send error:', { error })
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}
