import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { appwriteNotificationService } from '@/lib/notifications/services/appwrite'
import { logger } from '@/lib/notifications/utils'

/**
 * PATCH /api/notifications/read-all
 * Mark all notifications as read for a user using Appwrite
 */
export async function PATCH(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { userId } = body

    // Validate userId matches session or user is admin
    if (userId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    // Get unread count before marking as read
    const unreadCount = await appwriteNotificationService.getUnreadCount(userId)

    if (unreadCount === 0) {
      return NextResponse.json({
        success: true,
        message: 'No unread notifications to mark as read',
        markedCount: 0
      })
    }

    // Mark all as read using Appwrite service
    const result = await appwriteNotificationService.markAllAsRead(userId)

    if (!result.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: result.error || 'Failed to mark all notifications as read'
        },
        { status: 500 }
      )
    }

    // Log the bulk action
    const metadata = {
      userId,
      userAgent: request.headers.get('user-agent'),
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
      timestamp: new Date().toISOString()
    }

    logger.info('Bulk mark as read:', {
      userId,
      markedCount: result.markedCount || unreadCount,
      metadata
    })

    return NextResponse.json({
      success: true,
      markedCount: result.markedCount || unreadCount,
      message: `${result.markedCount || unreadCount} notifications marked as read`
    })

  } catch (error: any) {
    logger.error('PATCH /api/notifications/read-all error:', { error })
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/notifications/read-all
 * Mark all notifications as unread for a user (admin only) using Appwrite
 */
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate admin user
    const session = await auth()
    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Admin access required' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { userId } = body

    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'userId is required' },
        { status: 400 }
      )
    }

    // Get all notifications for the user
    const notifications = await appwriteNotificationService.getNotifications(userId, {})
    
    // Filter to get only read notifications
    const readNotifications = notifications.filter(n => n.readAt)
    
    if (readNotifications.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No read notifications to mark as unread',
        markedCount: 0
      })
    }

    // Mark all as unread using Appwrite service
    const result = await appwriteNotificationService.markAllAsUnread(userId)

    if (!result.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: result.error || 'Failed to mark all notifications as unread'
        },
        { status: 500 }
      )
    }

    // Log the bulk action
    logger.info('Bulk mark as unread (admin):', {
      userId,
      markedCount: result.markedCount || readNotifications.length,
      adminId: session.user.id,
      timestamp: new Date().toISOString()
    })

    return NextResponse.json({
      success: true,
      markedCount: result.markedCount || readNotifications.length,
      message: `${result.markedCount || readNotifications.length} notifications marked as unread`
    })

  } catch (error: any) {
    logger.error('DELETE /api/notifications/read-all error:', { error })
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}
