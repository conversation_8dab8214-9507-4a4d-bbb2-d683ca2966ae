import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { appwriteNotificationService } from '@/lib/notifications/services/appwrite'
import { logger } from '@/lib/notifications/utils'

interface RouteParams {
  params: {
    id: string
  }
}

/**
 * PATCH /api/notifications/[id]/read
 * Mark a specific notification as read using Appwrite
 */
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params
    const body = await request.json()
    const { userId } = body

    // Validate userId matches session or user is admin
    if (userId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    // Mark notification as read using Appwrite service
    const result = await appwriteNotificationService.markAsRead(userId, id)

    if (!result) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to mark notification as read'
        },
        { status: result.notFound ? 404 : 500 }
      )
    }

    // Log the interaction
    const metadata = {
      userId,
      userAgent: request.headers.get('user-agent'),
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
      readAt: new Date().toISOString()
    }

    logger.info('Notification marked as read', {
      notificationId: id,
      userId,
      metadata
    })

    return NextResponse.json({
      success: true,
      message: result.alreadyRead ? 'Notification already marked as read' : 'Notification marked as read',
      notificationId: id
    })

  } catch (error: any) {
    logger.error('PATCH /api/notifications/[id]/read error:', { error, notificationId: params.id })
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/notifications/[id]/read
 * Mark a specific notification as unread using Appwrite
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params
    const body = await request.json()
    const { userId } = body

    // Validate userId matches session or user is admin
    if (userId !== session.user.id && !session.user.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Forbidden' },
        { status: 403 }
      )
    }

    // Mark notification as unread using Appwrite service
    const result = await appwriteNotificationService.markAsUnread(userId, id)

    if (!result.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: result.error || 'Failed to mark notification as unread'
        },
        { status: result.notFound ? 404 : 500 }
      )
    }

    // Log the interaction
    const metadata = {
      userId,
      userAgent: request.headers.get('user-agent'),
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')
    }

    logger.info('Notification marked as unread', {
      notificationId: id,
      userId,
      metadata
    })

    return NextResponse.json({
      success: true,
      message: result.alreadyUnread ? 'Notification already marked as unread' : 'Notification marked as unread',
      notificationId: id
    })

  } catch (error: any) {
    logger.error('DELETE /api/notifications/[id]/read error:', { error, notificationId: params.id })
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    )
  }
}
