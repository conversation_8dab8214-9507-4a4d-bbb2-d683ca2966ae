import { NextRequest } from 'next/server'
import { auth } from '@/auth'
import { appwriteNotificationService } from '@/lib/notifications/services/appwrite'
import { logger } from '@/lib/notifications/utils'

// Store active connections
const connections = new Map<string, ReadableStreamDefaultController>()

/**
 * GET /api/notifications/stream
 * Server-Sent Events endpoint for real-time notifications
 * 
 * Note: This is a fallback for browsers that don't support WebSockets
 * For modern browsers, we use Appwrite's realtime service directly
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const session = await auth()
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 })
    }

    const url = new URL(request.url)
    const userId = url.searchParams.get('userId')

    // Validate user access
    if (userId !== session.user.id && !session.user.isAdmin) {
      return new Response('Forbidden', { status: 403 })
    }

    if (!userId) {
      return new Response('userId parameter is required', { status: 400 })
    }

    // Create SSE stream
    const stream = new ReadableStream({
      start(controller) {
        // Store connection
        const connectionId = `${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        connections.set(connectionId, controller)

        // Send initial connection message
        const initialMessage = {
          type: 'connected',
          timestamp: new Date().toISOString(),
          connectionId,
          message: 'Connected to SSE stream (fallback mode). Consider using Appwrite realtime directly for better performance.'
        }
        
        controller.enqueue(`data: ${JSON.stringify(initialMessage)}\n\n`)

        // Send initial sync of recent notifications
        syncNotifications(userId, controller)

        // Set up heartbeat to keep connection alive
        const heartbeatInterval = setInterval(() => {
          try {
            const heartbeat = {
              type: 'heartbeat',
              timestamp: new Date().toISOString()
            }
            controller.enqueue(`data: ${JSON.stringify(heartbeat)}\n\n`)
          } catch (error) {
            logger.error('Heartbeat error:', { error, connectionId })
            clearInterval(heartbeatInterval)
            connections.delete(connectionId)
          }
        }, 30000) // 30 seconds

        // Clean up on close
        request.signal.addEventListener('abort', () => {
          clearInterval(heartbeatInterval)
          connections.delete(connectionId)
          try {
            controller.close()
          } catch (error) {
            // Connection already closed
          }
        })

        logger.info(`SSE connection established for user ${userId}`, { connectionId })
      },

      cancel() {
        // Clean up when stream is cancelled
        const connectionToRemove = Array.from(connections.entries())
          .find(([_, ctrl]) => ctrl === this)
        
        if (connectionToRemove) {
          connections.delete(connectionToRemove[0])
          logger.info(`SSE connection closed for user ${userId}`)
        }
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    })

  } catch (error) {
    logger.error('GET /api/notifications/stream error:', { error })
    return new Response('Internal server error', { status: 500 })
  }
}

/**
 * Send initial sync of notifications to a user
 */
async function syncNotifications(userId: string, controller: ReadableStreamDefaultController) {
  try {
    // Get recent notifications using Appwrite service
    const notifications = await appwriteNotificationService.getNotifications(userId, {
      limit: 50,
      offset: 0
    })

    // Get unread count
    const unreadCount = await appwriteNotificationService.getUnreadCount(userId)

    const syncMessage = {
      type: 'sync',
      notifications,
      unreadCount,
      timestamp: new Date().toISOString()
    }

    controller.enqueue(`data: ${JSON.stringify(syncMessage)}\n\n`)

  } catch (error) {
    logger.error('Sync notifications error:', { error, userId })
  }
}

/**
 * Broadcast notification to user connections
 * This function should be called when a new notification is created
 */
export function broadcastNotification(userId: string, notification: any) {
  const userConnections = Array.from(connections.entries())
    .filter(([connectionId]) => connectionId.startsWith(userId))

  if (userConnections.length === 0) {
    return // No active connections for this user
  }

  const message = {
    type: 'notification',
    notification,
    timestamp: new Date().toISOString()
  }

  const messageData = `data: ${JSON.stringify(message)}\n\n`

  userConnections.forEach(([connectionId, controller]) => {
    try {
      controller.enqueue(messageData)
    } catch (error) {
      logger.error(`Failed to send notification to connection:`, { error, connectionId })
      connections.delete(connectionId)
    }
  })

  logger.info(`Broadcasted notification to connections`, { 
    userId, 
    connectionCount: userConnections.length,
    notificationId: notification.id
  })
}

/**
 * Broadcast read status update to user connections
 */
export function broadcastReadStatus(userId: string, notificationId: string) {
  const userConnections = Array.from(connections.entries())
    .filter(([connectionId]) => connectionId.startsWith(userId))

  if (userConnections.length === 0) {
    return
  }

  const message = {
    type: 'read',
    notificationId,
    timestamp: new Date().toISOString()
  }

  const messageData = `data: ${JSON.stringify(message)}\n\n`

  userConnections.forEach(([connectionId, controller]) => {
    try {
      controller.enqueue(messageData)
    } catch (error) {
      logger.error(`Failed to send read status to connection:`, { error, connectionId })
      connections.delete(connectionId)
    }
  })
}

/**
 * Broadcast bulk read status update to user connections
 */
export function broadcastBulkRead(userId: string) {
  const userConnections = Array.from(connections.entries())
    .filter(([connectionId]) => connectionId.startsWith(userId))

  if (userConnections.length === 0) {
    return
  }

  const message = {
    type: 'bulk_read',
    timestamp: new Date().toISOString()
  }

  const messageData = `data: ${JSON.stringify(message)}\n\n`

  userConnections.forEach(([connectionId, controller]) => {
    try {
      controller.enqueue(messageData)
    } catch (error) {
      logger.error(`Failed to send bulk read to connection:`, { error, connectionId })
      connections.delete(connectionId)
    }
  })
}

/**
 * Broadcast notification deletion to user connections
 */
export function broadcastNotificationDelete(userId: string, notificationId: string) {
  const userConnections = Array.from(connections.entries())
    .filter(([connectionId]) => connectionId.startsWith(userId))

  if (userConnections.length === 0) {
    return
  }

  const message = {
    type: 'delete',
    notificationId,
    timestamp: new Date().toISOString()
  }

  const messageData = `data: ${JSON.stringify(message)}\n\n`

  userConnections.forEach(([connectionId, controller]) => {
    try {
      controller.enqueue(messageData)
    } catch (error) {
      logger.error(`Failed to send delete notification to connection:`, { error, connectionId })
      connections.delete(connectionId)
    }
  })
}

/**
 * Get active connection count for monitoring
 */
export function getActiveConnectionCount(): number {
  return connections.size
}

/**
 * Get active connections by user for monitoring
 */
export function getActiveConnectionsByUser(): Record<string, number> {
  const userConnections: Record<string, number> = {}
  
  connections.forEach((_, connectionId) => {
    const userId = connectionId.split('_')[0]
    userConnections[userId] = (userConnections[userId] || 0) + 1
  })

  return userConnections
}
