import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/lib/auth'
import { z } from 'zod'

// Validation schema for layout updates
const updateLayoutSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  description: z.string().optional(),
  type: z.enum(['site', 'page', 'post-type', 'template']).optional(),
  category: z.enum(['ecommerce', 'blog', 'portfolio', 'landing', 'corporate', 'custom']).optional(),
  structure: z.record(z.any()).optional(),
  styling: z.record(z.any()).optional(),
  responsive: z.record(z.any()).optional(),
  conditions: z.record(z.any()).optional(),
  isTemplate: z.boolean().optional(),
  isActive: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  thumbnail: z.string().optional(),
})

interface RouteParams {
  params: {
    id: string
  }
}

// GET /api/layouts/[id] - Get specific layout
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = params

    const layout = await prisma.layout.findUnique({
      where: { id },
      include: {
        assignments: {
          include: {
            conditions: true
          }
        },
        _count: {
          select: {
            assignments: true
          }
        }
      }
    })

    if (!layout) {
      return NextResponse.json(
        { error: 'Layout not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(layout)

  } catch (error) {
    console.error('Error fetching layout:', error)
    return NextResponse.json(
      { error: 'Failed to fetch layout' },
      { status: 500 }
    )
  }
}

// PUT /api/layouts/[id] - Update layout
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = params
    const body = await request.json()

    // Validate request body
    const validatedData = updateLayoutSchema.parse(body)

    // Check if layout exists and user has permission
    const existingLayout = await prisma.layout.findUnique({
      where: { id },
      select: { 
        id: true, 
        isSystem: true, 
        createdBy: true 
      }
    })

    if (!existingLayout) {
      return NextResponse.json(
        { error: 'Layout not found' },
        { status: 404 }
      )
    }

    // Prevent modification of system layouts
    if (existingLayout.isSystem) {
      return NextResponse.json(
        { error: 'Cannot modify system layouts' },
        { status: 403 }
      )
    }

    // Update layout
    const updatedLayout = await prisma.layout.update({
      where: { id },
      data: {
        ...validatedData,
        structure: validatedData.structure as any,
        styling: validatedData.styling as any,
        responsive: validatedData.responsive as any,
        conditions: validatedData.conditions as any,
        updatedBy: session.user.id,
        updatedAt: new Date(),
      },
      include: {
        assignments: {
          include: {
            conditions: true
          }
        }
      }
    })

    return NextResponse.json(updatedLayout)

  } catch (error) {
    console.error('Error updating layout:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to update layout' },
      { status: 500 }
    )
  }
}

// DELETE /api/layouts/[id] - Delete layout
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = params

    // Check if layout exists and user has permission
    const existingLayout = await prisma.layout.findUnique({
      where: { id },
      select: { 
        id: true, 
        name: true,
        isSystem: true, 
        createdBy: true,
        _count: {
          select: {
            assignments: true
          }
        }
      }
    })

    if (!existingLayout) {
      return NextResponse.json(
        { error: 'Layout not found' },
        { status: 404 }
      )
    }

    // Prevent deletion of system layouts
    if (existingLayout.isSystem) {
      return NextResponse.json(
        { error: 'Cannot delete system layouts' },
        { status: 403 }
      )
    }

    // Check if layout is in use
    if (existingLayout._count.assignments > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete layout that is currently assigned',
          assignmentCount: existingLayout._count.assignments
        },
        { status: 400 }
      )
    }

    // Delete layout
    await prisma.layout.delete({
      where: { id }
    })

    return NextResponse.json({
      message: `Layout "${existingLayout.name}" deleted successfully`
    })

  } catch (error) {
    console.error('Error deleting layout:', error)
    return NextResponse.json(
      { error: 'Failed to delete layout' },
      { status: 500 }
    )
  }
}

// PATCH /api/layouts/[id] - Partial update (for specific operations)
export async function PATCH(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = params
    const body = await request.json()
    const { action, ...data } = body

    // Check if layout exists
    const existingLayout = await prisma.layout.findUnique({
      where: { id },
      select: { 
        id: true, 
        isSystem: true, 
        usageCount: true 
      }
    })

    if (!existingLayout) {
      return NextResponse.json(
        { error: 'Layout not found' },
        { status: 404 }
      )
    }

    let updateData: any = {
      updatedBy: session.user.id,
      updatedAt: new Date(),
    }

    switch (action) {
      case 'toggle-active':
        updateData.isActive = data.isActive
        break

      case 'increment-usage':
        updateData.usageCount = existingLayout.usageCount + 1
        break

      case 'update-thumbnail':
        updateData.thumbnail = data.thumbnail
        break

      case 'update-tags':
        updateData.tags = data.tags
        break

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    const updatedLayout = await prisma.layout.update({
      where: { id },
      data: updateData,
      select: {
        id: true,
        name: true,
        isActive: true,
        usageCount: true,
        tags: true,
        thumbnail: true,
        updatedAt: true
      }
    })

    return NextResponse.json(updatedLayout)

  } catch (error) {
    console.error('Error patching layout:', error)
    return NextResponse.json(
      { error: 'Failed to update layout' },
      { status: 500 }
    )
  }
}