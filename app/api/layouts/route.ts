import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { auth } from '@/lib/auth'
import { z } from 'zod'

// Validation schema for layout creation/update
const layoutSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().optional(),
  type: z.enum(['site', 'page', 'post-type', 'template']),
  category: z.enum(['ecommerce', 'blog', 'portfolio', 'landing', 'corporate', 'custom']),
  structure: z.record(z.any()),
  styling: z.record(z.any()),
  responsive: z.record(z.any()),
  conditions: z.record(z.any()).optional(),
  isTemplate: z.boolean().default(false),
  isSystem: z.boolean().default(false),
  isActive: z.boolean().default(true),
  tags: z.array(z.string()).default([]),
  thumbnail: z.string().optional(),
})

// GET /api/layouts - List all layouts
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const type = searchParams.get('type')
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const isTemplate = searchParams.get('isTemplate')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}
    
    if (type) where.type = type
    if (category) where.category = category
    if (isTemplate !== null) where.isTemplate = isTemplate === 'true'
    
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { tags: { hasSome: [search] } }
      ]
    }

    // Get layouts with pagination
    const [layouts, total] = await Promise.all([
      prisma.layout.findMany({
        where,
        skip,
        take: limit,
        orderBy: { updatedAt: 'desc' },
        select: {
          id: true,
          name: true,
          description: true,
          type: true,
          category: true,
          isTemplate: true,
          isSystem: true,
          isActive: true,
          usageCount: true,
          tags: true,
          thumbnail: true,
          createdAt: true,
          updatedAt: true,
          createdBy: true,
        }
      }),
      prisma.layout.count({ where })
    ])

    return NextResponse.json({
      layouts,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching layouts:', error)
    return NextResponse.json(
      { error: 'Failed to fetch layouts' },
      { status: 500 }
    )
  }
}

// POST /api/layouts - Create new layout
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Validate request body
    const validatedData = layoutSchema.parse(body)

    // Create layout
    const layout = await prisma.layout.create({
      data: {
        ...validatedData,
        structure: validatedData.structure as any,
        styling: validatedData.styling as any,
        responsive: validatedData.responsive as any,
        conditions: validatedData.conditions as any,
        createdBy: session.user.id,
        updatedBy: session.user.id,
      }
    })

    return NextResponse.json(layout, { status: 201 })

  } catch (error) {
    console.error('Error creating layout:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create layout' },
      { status: 500 }
    )
  }
}

// DELETE /api/layouts - Bulk delete layouts
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const ids = searchParams.get('ids')?.split(',') || []

    if (ids.length === 0) {
      return NextResponse.json(
        { error: 'No layout IDs provided' },
        { status: 400 }
      )
    }

    // Check if any layouts are system layouts
    const systemLayouts = await prisma.layout.findMany({
      where: {
        id: { in: ids },
        isSystem: true
      },
      select: { id: true, name: true }
    })

    if (systemLayouts.length > 0) {
      return NextResponse.json(
        { 
          error: 'Cannot delete system layouts',
          systemLayouts: systemLayouts.map(l => l.name)
        },
        { status: 400 }
      )
    }

    // Delete layouts
    const result = await prisma.layout.deleteMany({
      where: {
        id: { in: ids },
        isSystem: false // Extra safety check
      }
    })

    return NextResponse.json({
      message: `Deleted ${result.count} layouts`,
      deletedCount: result.count
    })

  } catch (error) {
    console.error('Error deleting layouts:', error)
    return NextResponse.json(
      { error: 'Failed to delete layouts' },
      { status: 500 }
    )
  }
}