import { NextRequest, NextResponse } from 'next/server'
import { OrderService } from '@/lib/ecommerce/services/order-service'
import { auth } from '@/auth'
import { authOptions } from '@/lib/auth/auth-options'

const orderService = new OrderService()

export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    const session = await auth()
    if (!session?.user?.isAdmin) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const paymentStatus = searchParams.get('paymentStatus')
    const customerEmail = searchParams.get('customerEmail')
    const orderNumber = searchParams.get('orderNumber')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    const filters = {
      page,
      limit,
      ...(status && { status }),
      ...(paymentStatus && { paymentStatus }),
      ...(customerEmail && { customerEmail }),
      ...(orderNumber && { orderNumber }),
      ...(startDate && { startDate: new Date(startDate) }),
      ...(endDate && { endDate: new Date(endDate) })
    }

    const { orders, total } = await orderService.getAdminOrders(filters)

    return NextResponse.json({
      success: true,
      data: {
        items: orders,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1
        }
      }
    })
  } catch (error) {
    console.error('Admin Orders API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch orders' },
      { status: 500 }
    )
  }
}
