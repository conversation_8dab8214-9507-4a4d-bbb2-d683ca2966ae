import { NextResponse } from 'next/server'
import { db } from '@/lib/ecommerce/services/product-service'
import { Product } from '@/lib/ecommerce/types/product'

// Disable edge runtime as it's not compatible with Prisma
export const runtime = 'nodejs';

export async function GET(request: Request) {
  try {
    console.log('Fetching products...')
    const { searchParams } = new URL(request.url)
    const inventoryStatus = searchParams.get('inventoryStatus') as 'low' | 'out' | null
    
    console.log('Inventory status:', inventoryStatus)
    
    console.log('Fetching products from database...')
    
    const where: any = { status: 'active' }
    
    if (inventoryStatus === 'low') {
      where.inventoryQuantity = {
        lt: 10,
        gt: 0
      }
    } else if (inventoryStatus === 'out') {
      where.OR = [
        { inventoryQuantity: 0 },
        { inventoryQuantity: null }
      ]
      where.trackQuantity = true
    }
    
    const products = await db.product.findMany({
      where,
      include: {
        images: true,
        categories: {
          include: {
            category: true
          }
        }
      },
      orderBy: {
        title: 'asc'
      }
    })
    
    const result = {
      success: true,
      data: products.map(product => ({
        ...product,
        price: Number(product.price) || 0,
        inventoryQuantity: Number(product.inventoryQuantity) || 0,
        // Add other transformations as needed
      }))
    }
    
    console.log('Products fetched successfully, count:', result.data.length)
    
    return NextResponse.json(result)
  } catch (error: unknown) {
    console.error('Error in products API route:');
    const err = error as Error & { cause?: unknown; status?: number };
    console.error('Error name:', err.name);
    console.error('Error message:', err.message);
    console.error('Error stack:', err.stack);
    console.error('Error cause:', err.cause);
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch products',
        details: error instanceof Error ? error.message : 'Unknown error',
        // Include more details in development
        ...(process.env.NODE_ENV === 'development' && {
          errorDetails: {
            name: err.name,
            message: err.message,
            stack: err.stack,
            cause: err.cause
          }
        })
      },
      { status: 500 }
    )
  }
}
