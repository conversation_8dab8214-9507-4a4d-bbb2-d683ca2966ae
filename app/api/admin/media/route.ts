import { NextRequest, NextResponse } from 'next/server'
import { isAppwriteConfigured } from '@/lib/appwrite'
import { APPWRITE_ERRORS } from '@/lib/appwrite/config'
import {
  getMediaFiles,
  uploadMediaFiles,
  deleteMediaFiles,
  parseAndValidateGetParams,
  type MediaFile,
  type MediaUploadResult,
  type PaginationParams
} from '@/lib/appwrite/storage'

export async function GET(request: NextRequest) {
  try {
    if (!isAppwriteConfigured()) {
      return NextResponse.json(
        { error: APPWRITE_ERRORS.INVALID_CONFIG },
        { status: 503 }
      )
    }

    // Parse and validate query parameters
    const params = parseAndValidateGetParams(request.url)
    if (!params.success) {
      return NextResponse.json(
        { error: params.error },
        { status: 400 }
      )
    }

    const { page, limit, search, type, folder, sortBy, sortOrder } = params.data

    // Use the enhanced getMediaFiles function from storage service
    const result = await getMediaFiles({
      page,
      limit,
      search,
      type,
      folder,
      sortBy,
      sortOrder
    })

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('Error fetching media files:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch media files',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    if (!isAppwriteConfigured()) {
      return NextResponse.json(
        { error: APPWRITE_ERRORS.INVALID_CONFIG },
        { status: 503 }
      )
    }

    const formData = await request.formData()
    const files = formData.getAll('files') as File[]
    
    // Validate that files were provided
    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      )
    }

    const folder = (formData.get('folder') as string) || 'root'
    const alt = (formData.get('alt') as string) || ''
    const title = (formData.get('title') as string) || ''
    const description = (formData.get('description') as string) || ''
    const tags = (formData.get('tags') as string) || ''

    console.log('Upload request received:', {
      fileCount: files.length,
      folder,
      alt,
      title,
      description,
      tags
    })

    // Use the enhanced uploadMediaFiles function from storage service
    const uploadResults = await uploadMediaFiles(files, {
      folder,
      alt,
      title,
      description,
      tags
    })

    const successful = uploadResults.filter(result => !result.error)
    const failed = uploadResults.filter(result => result.error)

    console.log('Upload results:', {
      total: files.length,
      successful: successful.length,
      failed: failed.length,
      errors: failed.map(f => ({ name: f.name, error: f.error }))
    })

    // Always return 200 for successful API call, let the client handle partial failures
    return NextResponse.json({
      success: true,
      data: {
        files: uploadResults,
        uploaded: successful.length,
        failed: failed.length,
        summary: {
          total: files.length,
          successful: successful.length,
          failed: failed.length,
          errors: failed.map(f => ({ name: f.name, error: f.error }))
        }
      }
    }, { 
      status: 200
    })

  } catch (error) {
    console.error('Error uploading files:', error)
    return NextResponse.json(
      { 
        error: APPWRITE_ERRORS.UPLOAD_FAILED,
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    if (!isAppwriteConfigured()) {
      return NextResponse.json(
        { error: APPWRITE_ERRORS.INVALID_CONFIG },
        { status: 503 }
      )
    }

    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('fileId')
    const fileIds = searchParams.get('fileIds')?.split(',') || []

    if (!fileId && fileIds.length === 0) {
      return NextResponse.json(
        { error: 'File ID(s) required' },
        { status: 400 }
      )
    }

    const idsToDelete = fileId ? [fileId] : fileIds

    // Use the enhanced deleteMediaFiles function from storage service
    const results = await deleteMediaFiles(idsToDelete)

    const successful = results.filter(r => r.success)
    const failed = results.filter(r => !r.success)

    return NextResponse.json({
      success: successful.length > 0,
      data: {
        deleted: successful.length,
        failed: failed.length,
        results
      }
    }, {
      status: failed.length === results.length ? 400 : 200
    })

  } catch (error) {
    console.error('Error deleting files:', error)
    return NextResponse.json(
      { 
        error: 'Failed to delete files',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
