import { NextRequest, NextResponse } from 'next/server'
import { isAppwriteConfigured } from '@/lib/appwrite'
import { appwriteConfig, STORAGE_BUCKETS } from '@/lib/appwrite/config'
import { getServices } from '@/lib/appwrite/client'
import { Query } from 'appwrite'

export async function GET(request: NextRequest) {
  try {
    // Check configuration
    const configured = isAppwriteConfigured()
    console.log('Appwrite configured:', configured)
    
    if (!configured) {
      return NextResponse.json({
        error: 'Appwrite not configured',
        details: {
          endpoint: !!appwriteConfig.endpoint,
          projectId: !!appwriteConfig.projectId,
          databaseId: !!appwriteConfig.databaseId,
          storageBucketId: !!appwriteConfig.storageBucketId,
        }
      }, { status: 503 })
    }

    // Test connection
    const services = getServices()
    const bucketId = STORAGE_BUCKETS.MEDIA_LIBRARY.id
    
    console.log('Testing connection to bucket:', bucketId)
    
    try {
      const files = await services.storage.listFiles(bucketId, ['Query.limit(1)'])
      console.log('Connection test successful, found files:', files.total)
      
      return NextResponse.json({
        success: true,
        message: 'Appwrite connection successful',
        details: {
          configured: true,
          connected: true,
          bucketId,
          totalFiles: files.total,
          bucketConfig: STORAGE_BUCKETS.MEDIA_LIBRARY
        }
      })
    } catch (connectionError) {
      console.error('Connection test failed:', connectionError)
      
      return NextResponse.json({
        success: false,
        message: 'Appwrite connection failed',
        error: connectionError instanceof Error ? connectionError.message : 'Unknown error',
        details: {
          configured: true,
          connected: false,
          bucketId,
          bucketConfig: STORAGE_BUCKETS.MEDIA_LIBRARY
        }
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Test endpoint error:', error)
    return NextResponse.json({
      error: 'Test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}