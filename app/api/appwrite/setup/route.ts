import { NextRequest, NextResponse } from 'next/server'
import { getAppwriteServer } from '@/lib/appwrite/server'

export async function POST(request: NextRequest) {
  try {
    // Verify API key is present
    if (!process.env.APPWRITE_API_KEY) {
      return NextResponse.json(
        { 
          error: 'APPWRITE_API_KEY environment variable is required for server operations',
          setup: false
        },
        { status: 500 }
      )
    }

    // Verify required environment variables
    const requiredEnvVars = [
      'NEXT_PUBLIC_APPWRITE_ENDPOINT',
      'NEXT_PUBLIC_APPWRITE_PROJECT_ID',
      'APPWRITE_API_KEY'
    ]

    const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
    
    if (missingVars.length > 0) {
      return NextResponse.json(
        {
          error: `Missing required environment variables: ${missingVars.join(', ')}`,
          setup: false,
          missingVars
        },
        { status: 400 }
      )
    }

    console.log('🚀 Starting Appwrite media library setup...')

    // Setup complete media library infrastructure
    const appwriteServer = getAppwriteServer()
    const result = await appwriteServer.setupMediaLibrary()

    return NextResponse.json({
      success: true,
      message: 'Media library infrastructure setup completed successfully',
      setup: true,
      data: {
        bucket: {
          $id: result.bucket.$id,
          name: result.bucket.name,
          enabled: result.bucket.enabled,
          maximumFileSize: result.bucket.maximumFileSize,
          allowedFileExtensions: result.bucket.allowedFileExtensions?.length || 0
        },
        database: {
          $id: result.database.$id,
          name: result.database.name,
          enabled: result.database.enabled
        },
        collection: {
          $id: result.collection.$id,
          name: result.collection.name,
          enabled: result.collection.enabled
        }
      }
    })

  } catch (error: any) {
    console.error('❌ Setup failed:', error)

    // Handle specific Appwrite errors
    if (error.code === 401) {
      return NextResponse.json(
        {
          error: 'Invalid API key or insufficient permissions',
          setup: false,
          details: 'Please check your APPWRITE_API_KEY and ensure it has the required scopes'
        },
        { status: 401 }
      )
    }

    if (error.code === 404) {
      return NextResponse.json(
        {
          error: 'Project not found',
          setup: false,
          details: 'Please check your NEXT_PUBLIC_APPWRITE_PROJECT_ID'
        },
        { status: 404 }
      )
    }

    return NextResponse.json(
      {
        error: 'Failed to setup media library infrastructure',
        setup: false,
        details: error.message || 'Unknown error occurred'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check if infrastructure exists
    const appwriteServer = getAppwriteServer()
    const bucketInfo = await appwriteServer.getBucketInfo()
    
    return NextResponse.json({
      setup: true,
      bucket: {
        $id: bucketInfo.$id,
        name: bucketInfo.name,
        enabled: bucketInfo.enabled,
        maximumFileSize: bucketInfo.maximumFileSize,
        allowedFileExtensions: bucketInfo.allowedFileExtensions?.length || 0,
        fileSecurity: bucketInfo.fileSecurity,
        compression: bucketInfo.compression,
        encryption: bucketInfo.encryption,
        antivirus: bucketInfo.antivirus
      }
    })

  } catch (error: any) {
    if (error.code === 404) {
      return NextResponse.json({
        setup: false,
        message: 'Media library infrastructure not found. Run setup to create it.'
      })
    }

    return NextResponse.json(
      {
        error: 'Failed to check infrastructure status',
        setup: false,
        details: error.message
      },
      { status: 500 }
    )
  }
}
