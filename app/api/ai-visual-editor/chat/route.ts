import { google } from '@ai-sdk/google';


import { streamText, tool } from 'ai';
import { z } from 'zod';
import { generateObject } from 'ai';
import {
  analyzeComponentStructure,
  generateComponentId,
  validateComponentCode,
  sanitizeComponentCode,
} from '@/lib/ai-visual-editor/utils/component-analyzer';
import {
  generatePropertiesSchema,
  generateDefaultValues,
} from '@/lib/ai-visual-editor/utils/properties-generator';
import { analyzeComponentAdvanced } from '@/lib/ai-visual-editor/utils/advanced-component-analyzer';
import { NextRequest } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export const maxDuration = 30;

// Rate limiting and security
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_MAX = 10; // requests per window
const RATE_LIMIT_WINDOW = 60000; // 1 minute

function checkRateLimit(ip: string): boolean {
  const now = Date.now();
  const userLimit = rateLimitMap.get(ip);

  if (!userLimit || now > userLimit.resetTime) {
    rateLimitMap.set(ip, { count: 1, resetTime: now + RATE_LIMIT_WINDOW });
    return true;
  }

  if (userLimit.count >= RATE_LIMIT_MAX) {
    return false;
  }

  userLimit.count++;
  return true;
}

const componentSchema = z.object({
  name: z.string(),
  description: z.string(),
  category: z.enum(['layout', 'content', 'media', 'form', 'navigation', 'data', 'landing', 'ecommerce']),
  jsx: z.string(),
  imports: z.array(z.string()).optional(),
  dependencies: z.array(z.string()).optional(),
});

export async function POST(req: NextRequest) {
  try {
    // Rate limiting
    const ip = req.headers.get('x-forwarded-for') || 'unknown';
    if (!checkRateLimit(ip)) {
      return new Response(
        JSON.stringify({
          error: 'Rate limit exceeded',
          message: 'Too many requests',
        }),
        { status: 429, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Input validation
    const body = await req.json();
    if (!body.messages || !Array.isArray(body.messages)) {
      return new Response(
        JSON.stringify({
          error: 'Invalid input',
          message: 'Messages array is required',
        }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    const { messages } = body;

    // Enhanced error handling and monitoring
    const startTime = Date.now();

    const result = streamText({
      model: google('gemini-1.5-pro-latest'),
      messages,
      maxSteps: 5,
      onFinish: (result) => {
        const duration = Date.now() - startTime;
        console.log(
          `[AI Editor] Request completed in ${duration}ms, tokens: ${
            result.usage?.totalTokens || 'unknown'
          }`
        );
      },
      tools: {
        generatePageBuilderBlock: tool({
          description:
            'Generate a new page builder block based on a description and existing block examples.',
          parameters: z.object({
            description: z
              .string()
              .describe(
                'A detailed description of the page builder block to generate.'
              ),
            blockName: z
              .string()
              .describe(
                'A descriptive, PascalCase name for the new block, e.g., "PrimaryHeroSection".'
              ),
            category: z
              .enum(['landing', 'ecommerce', 'common', 'custom'])
              .describe('The category where the block belongs.'),
            styling: z
              .enum(['modern', 'minimal', 'bold', 'elegant', 'playful'])
              .default('modern'),
            complexity: z
              .enum(['simple', 'moderate', 'complex'])
              .default('moderate'),
          }),
          execute: async ({
            description,
            blockName,
            category,
            styling,
            complexity,
          }) => {
            try {
              const startTime = Date.now();

              const blockDirectory = path.join(
                process.cwd(),
                'lib',
                'page-builder',
                'blocks',
                category
              );
              let exampleBlocks = [];
              try {
                const files = await fs.readdir(blockDirectory);
                const tsxFiles = files
                  .filter((file) => file.endsWith('.tsx'))
                  .slice(0, 3); // Limit to 3 examples
                for (const file of tsxFiles) {
                  const content = await fs.readFile(
                    path.join(blockDirectory, file),
                    'utf-8'
                  );
                  exampleBlocks.push(`// Example from ${file}\n${content}`);
                }
              } catch (error) {
                console.warn(`Could not read example blocks from ${category}:`, error);
              }

              const { object } = await generateObject({
                model: google('gemini-1.5-pro-latest'),
                schema: componentSchema,
                prompt: `
                  You are an expert React developer specializing in creating components for a page builder.
                  Your task is to generate a new block based on the user's request and existing examples.

                  **New Block Request:**
                  - Name: ${blockName}
                  - Description: ${description}
                  - Category: ${category}
                  - Styling: ${styling}
                  - Complexity: ${complexity}

                  **Requirements:**
                  1.  Create a functional React component using TypeScript.
                  2.  Use Tailwind CSS for styling, adhering to the '${styling}' design approach.
                  3.  Use shadcn/ui components where appropriate (e.g., Button, Card).
                  4.  Ensure the component is fully responsive (mobile-first).
                  5.  Implement accessibility best practices (ARIA, semantic HTML).
                  6.  Define clear TypeScript interfaces for props.
                  7.  The component must be self-contained and ready to be dropped into the page builder.
                  8.  Match the overall style, structure, and quality of the provided examples.

                  **Existing Block Examples:**
                  ${exampleBlocks.join('\n\n---\n\n')}

                  ---

                  Now, generate the JSX code for the new "${blockName}" component.
                  The component should be exported as a named function.
                  Do not include the \`import React from 'react'\` statement.
                `,
              });

              const validation = validateComponentCode(object.jsx);
              if (!validation.isValid) {
                throw new Error(
                  `Code validation failed: ${validation.errors.join(', ')}`
                );
              }

              const sanitizedCode = sanitizeComponentCode(object.jsx);
              const advancedAnalysis = analyzeComponentAdvanced(sanitizedCode);
              const propertiesConfig = generatePropertiesSchema(advancedAnalysis);
              const defaultValues = generateDefaultValues(propertiesConfig);
              const generationTime = Date.now() - startTime;

              const component = {
                id: generateComponentId(),
                name: object.name,
                description: object.description,
                category: object.category,
                jsx: sanitizedCode,
                props: {},
                propertiesConfig,
                defaultValues,
                createdAt: new Date(),
                updatedAt: new Date(),
                metadata: {
                  ...advancedAnalysis,
                  generationTime,
                },
              };

              return {
                success: true,
                component,
                message: `Generated ${object.name} block in category ${object.category}.`,
              };
            } catch (error) {
              console.error('Block generation error:', error);
              return {
                success: false,
                error:
                  error instanceof Error
                    ? error.message
                    : 'Failed to generate block',
                message:
                  'Block generation failed. Please try a different description or check the server logs.',
              };
            }
          },
        }),
        analyzeComponent: tool({
          description: 'Analyze an existing component and generate properties configuration',
          parameters: z.object({
            componentCode: z.string().describe('React component code to analyze'),
            componentName: z.string().describe('Name of the component'),
            focusArea: z.enum(['appearance', 'content', 'behavior', 'data', 'all']).default('all')
          }),
          execute: async ({ componentCode, componentName, focusArea }) => {
            try {
              const analysis = analyzeComponentStructure(componentCode)
              const propertiesConfig = generatePropertiesSchema(analysis)
              const defaultValues = generateDefaultValues(propertiesConfig)

              return {
                success: true,
                componentName,
                propertiesConfig,
                defaultValues,
                analysis: analysis.summary,
                message: `Analyzed ${componentName} and generated ${Object.keys(propertiesConfig).length} property sections`
              }
            } catch (error) {
              console.error('Component analysis error:', error)
              return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to analyze component',
                message: 'Component analysis failed. Please check the component code.'
              }
            }
          }
        }),

        optimizeComponent: tool({
          description: 'Optimize an existing component for performance, accessibility, or styling',
          parameters: z.object({
            componentCode: z.string().describe('Current component code'),
            optimizationType: z.enum(['performance', 'accessibility', 'responsive', 'styling']),
            specificRequirements: z.string().optional().describe('Specific optimization requirements')
          }),
          execute: async ({ componentCode, optimizationType, specificRequirements }) => {
            try {
              const { object } = await generateObject({
                model: google('gemini-1.5-pro-latest'),
                schema: z.object({
                  optimizedCode: z.string(),
                  changes: z.array(z.string()),
                  improvements: z.string()
                }),
                prompt: `
                  Optimize the following React component for ${optimizationType}:
                  
                  ${componentCode}
                  
                  ${specificRequirements ? `Specific requirements: ${specificRequirements}` : ''}
                  
                  Focus on:
                  ${optimizationType === 'performance' ? '- Memoization, lazy loading, efficient rendering' : ''}
                  ${optimizationType === 'accessibility' ? '- ARIA labels, keyboard navigation, screen reader support' : ''}
                  ${optimizationType === 'responsive' ? '- Mobile-first design, flexible layouts, breakpoints' : ''}
                  ${optimizationType === 'styling' ? '- Better visual hierarchy, spacing, colors, typography' : ''}
                  
                  Return the optimized code and list the changes made.
                `
              })

              const validation = validateComponentCode(object.optimizedCode)
              if (!validation.isValid) {
                throw new Error(`Optimized code validation failed: ${validation.errors.join(', ')}`)
              }

              return {
                success: true,
                optimizedCode: sanitizeComponentCode(object.optimizedCode),
                changes: object.changes,
                improvements: object.improvements,
                message: `Optimized component for ${optimizationType} with ${object.changes.length} improvements`
              }
            } catch (error) {
              console.error('Component optimization error:', error)
              return {
                success: false,
                error: error instanceof Error ? error.message : 'Failed to optimize component',
                message: 'Component optimization failed. Please try again.'
              }
            }
          }
        })
      }
    });

    return result.toDataStreamResponse();
  } catch (error) {
        console.error('API route error:', error);
    if (error instanceof Error) {
      console.error(`[AI Editor API Error] Name: ${error.name}, Message: ${error.message}`, { stack: error.stack });
    }
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: 'Failed to process request',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
}