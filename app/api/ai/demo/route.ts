import { google } from '@ai-sdk/google';
import { streamText } from 'ai';

export const maxDuration = 30;

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();

    const result = await streamText({
      model: google('gemini-1.5-pro-latest'),
      messages,
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error('[AI Demo API Error]', error);
    if (error instanceof Error) {
      console.error(`[AI Demo API Error] Name: ${error.name}, Message: ${error.message}`, { stack: error.stack });
    }
    return new Response('An error occurred.', { status: 500 });
  }
}
