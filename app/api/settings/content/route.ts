import { NextResponse } from 'next/server';

// Mock data store
let contentSettings = {
  enableComments: true,
  postsPerPage: 10,
};

export async function GET() {
  try {
    return NextResponse.json(contentSettings);
  } catch (error) {
    console.error('[SETTINGS_CONTENT_GET]', error);
    return new NextResponse('Internal error', { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { enableComments, postsPerPage } = body;

    contentSettings = { enableComments, postsPerPage };

    return NextResponse.json(contentSettings);
  } catch (error) {
    console.error('[SETTINGS_CONTENT_POST]', error);
    return new NextResponse('Internal error', { status: 500 });
  }
}
