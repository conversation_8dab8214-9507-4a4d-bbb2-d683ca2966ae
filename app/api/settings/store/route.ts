import { NextResponse } from 'next/server';

// Mock data store
let storeSettings = {
  storeName: 'Coco Milk Store',
  storeEmail: '<EMAIL>',
  country: 'United States',
  currency: 'USD',
};

export async function GET() {
  try {
    return NextResponse.json(storeSettings);
  } catch (error) {
    console.error('[SETTINGS_STORE_GET]', error);
    return new NextResponse('Internal error', { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { storeName, storeEmail, country, currency } = body;

    if (!storeName) {
      return new NextResponse('Store name is required', { status: 400 });
    }
    if (!storeEmail) {
      return new NextResponse('Store email is required', { status: 400 });
    }
    if (!country) {
      return new NextResponse('Country is required', { status: 400 });
    }
    if (!currency) {
      return new NextResponse('Currency is required', { status: 400 });
    }

    storeSettings = { storeName, storeEmail, country, currency };

    return NextResponse.json(storeSettings);
  } catch (error) {
    console.error('[SETTINGS_STORE_POST]', error);
    return new NextResponse('Internal error', { status: 500 });
  }
}
