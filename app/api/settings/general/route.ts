import { NextResponse } from 'next/server';

// Mock data store
let generalSettings = {
  siteName: 'Coco Milk Store',
  siteDescription: 'Your favorite place for coconut milk.',
};

export async function GET() {
  try {
    return NextResponse.json(generalSettings);
  } catch (error) {
    console.error('[SETTINGS_GENERAL_GET]', error);
    return new NextResponse('Internal error', { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { siteName, siteDescription } = body;

    if (!siteName) {
      return new NextResponse('Site name is required', { status: 400 });
    }

    if (!siteDescription) {
      return new NextResponse('Site description is required', { status: 400 });
    }

    generalSettings = { siteName, siteDescription };

    return NextResponse.json(generalSettings);
  } catch (error) {
    console.error('[SETTINGS_GENERAL_POST]', error);
    return new NextResponse('Internal error', { status: 500 });
  }
}
