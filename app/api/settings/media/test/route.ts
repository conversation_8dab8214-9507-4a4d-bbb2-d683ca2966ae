import { NextResponse } from 'next/server'
import { getAppwriteServer } from '@/lib/appwrite/server'
import { getMediaSettingsService } from '@/lib/appwrite/media-settings'

export async function GET() {
  try {
    // Get the Appwrite server instance
    const appwriteServer = getAppwriteServer()
    
    // Get the media settings service
    const mediaSettingsService = getMediaSettingsService()
    
    // Get the current media settings
    const settings = await mediaSettingsService.getMediaSettings()
    
    // Get bucket info if using Appwrite
    let bucketInfo = null
    if (settings.storageProvider === 'appwrite' && settings.appwriteSettings) {
      try {
        bucketInfo = await appwriteServer.getBucketInfo(settings.appwriteSettings.bucketId)
      } catch (error) {
        console.error('Error getting bucket info:', error)
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'Appwrite media settings test successful',
      settings,
      bucketInfo
    })
  } catch (error) {
    console.error('Error testing Appwrite media settings:', error)
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to test Appwrite media settings',
        message: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}