import { NextRequest, NextResponse } from 'next/server'
import { getMediaSettingsService, defaultMediaSettings } from '@/lib/appwrite/media-settings'

export async function GET() {
  try {
    // Get the media settings service
    const mediaSettingsService = getMediaSettingsService()
    
    // Fetch settings from Appwrite
    const settings = await mediaSettingsService.getMediaSettings()
    
    return NextResponse.json(settings)
  } catch (error) {
    console.error('Error fetching media settings:', error)
    
    // Fallback to default settings in case of error
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json(defaultMediaSettings)
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch media settings' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validate the data
    if (!data.storageProvider) {
      return NextResponse.json(
        { error: 'Invalid media settings data' },
        { status: 400 }
      )
    }

    // Get the media settings service
    const mediaSettingsService = getMediaSettingsService()
    
    // Update settings in Appwrite
    const updatedSettings = await mediaSettingsService.updateMediaSettings(data)
    
    return NextResponse.json(updatedSettings)
  } catch (error) {
    console.error('Error updating media settings:', error)
    return NextResponse.json(
      { error: 'Failed to update media settings' },
      { status: 500 }
    )
  }
}