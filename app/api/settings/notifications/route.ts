import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/ecommerce/config/database'

// Mock data for development - in production this would be stored in the database
let notificationSettings = {
  emailNotifications: {
    newOrders: true,
    orderStatusChanges: true,
    lowStock: true,
    customerSignups: false,
  },
  adminNotifications: {
    enableBrowserNotifications: true,
    enableEmailDigest: false,
    digestFrequency: 'daily',
  },
}

export async function GET() {
  try {
    // In a real implementation, you would fetch from the database
    // const settings = await prisma.notificationSettings.findFirst({
    //   where: { active: true }
    // })

    return NextResponse.json(notificationSettings)
  } catch (error) {
    console.error('Error fetching notification settings:', error)
    return NextResponse.json(
      { error: 'Failed to fetch notification settings' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validate the data
    if (!data.emailNotifications || !data.adminNotifications) {
      return NextResponse.json(
        { error: 'Invalid notification settings data' },
        { status: 400 }
      )
    }

    // In a real implementation, you would update the database
    // const settings = await prisma.notificationSettings.upsert({
    //   where: { active: true },
    //   update: data,
    //   create: { ...data, active: true }
    // })

    // For now, just update our mock data
    notificationSettings = {
      emailNotifications: {
        newOrders: !!data.emailNotifications.newOrders,
        orderStatusChanges: !!data.emailNotifications.orderStatusChanges,
        lowStock: !!data.emailNotifications.lowStock,
        customerSignups: !!data.emailNotifications.customerSignups,
      },
      adminNotifications: {
        enableBrowserNotifications: !!data.adminNotifications.enableBrowserNotifications,
        enableEmailDigest: !!data.adminNotifications.enableEmailDigest,
        digestFrequency: data.adminNotifications.digestFrequency || 'daily',
      },
    }

    return NextResponse.json(notificationSettings)
  } catch (error) {
    console.error('Error updating notification settings:', error)
    return NextResponse.json(
      { error: 'Failed to update notification settings' },
      { status: 500 }
    )
  }
}