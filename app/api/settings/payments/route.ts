import { NextResponse } from 'next/server';
import { settingsService } from '@/lib/settings/settings-service';

export async function GET() {
  try {
    const paymentSettings = await settingsService.getPaymentSettings();
    return NextResponse.json(paymentSettings);
  } catch (error) {
    console.error('[SETTINGS_PAYMENTS_GET]', error);
    return new NextResponse('Internal error', { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const success = await settingsService.setPaymentSettings(body);

    if (!success) {
      return new NextResponse('Failed to save payment settings', { status: 400 });
    }

    const updatedSettings = await settingsService.getPaymentSettings();
    return NextResponse.json(updatedSettings);
  } catch (error) {
    console.error('[SETTINGS_PAYMENTS_POST]', error);
    return new NextResponse('Internal error', { status: 500 });
  }
}
