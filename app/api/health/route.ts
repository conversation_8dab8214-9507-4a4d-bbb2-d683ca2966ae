import { NextResponse } from 'next/server'
import { db } from '@/lib/ecommerce/services/product-service'

export async function GET() {
  try {
    // Test database connection by querying the database version
    const result = await db.$queryRaw`SELECT version()`
    
    return NextResponse.json({
      status: 'ok',
      database: 'connected',
      version: result
    })
  } catch (error) {
    console.error('Health check failed:', error)
    return NextResponse.json(
      {
        status: 'error',
        database: 'disconnected',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
