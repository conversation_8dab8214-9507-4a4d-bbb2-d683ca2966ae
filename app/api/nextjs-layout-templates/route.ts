import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { z } from 'zod'
import { NextJSLayoutTemplate } from '@/lib/layout-builder/types/nextjs-types'

// Validation schema for template creation
const templateSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  description: z.string().min(1, 'Description is required'),
  category: z.enum(['website', 'admin', 'landing', 'blog', 'ecommerce', 'dashboard']),
  layout: z.object({
    id: z.string(),
    name: z.string(),
    nextjs: z.any(),
    structure: z.record(z.any()),
    styling: z.record(z.any())
  })
})

// Built-in templates
const builtInTemplates: NextJSLayoutTemplate[] = [
  {
    id: 'root-basic',
    name: 'Basic Root Layout',
    description: 'Simple root layout with header, main content, and footer',
    category: 'website',
    type: 'root',
    preview: '/templates/root-basic.png',
    sections: [
      { type: 'header', name: 'Header', enabled: true },
      { type: 'main', name: 'Main Content', enabled: true },
      { type: 'footer', name: 'Footer', enabled: true }
    ],
    features: ['metadata', 'loading'],
    metadata: {
      title: { default: 'My App', template: '%s | My App' },
      description: 'A modern web application'
    },
    tags: ['basic', 'website', 'root']
  },
  {
    id: 'dashboard-admin',
    name: 'Admin Dashboard',
    description: 'Dashboard layout with sidebar navigation and top bar',
    category: 'admin',
    type: 'nested',
    preview: '/templates/dashboard-admin.png',
    sections: [
      { type: 'header', name: 'Top Navigation', enabled: true },
      { type: 'sidebar', name: 'Sidebar', enabled: true },
      { type: 'main', name: 'Content Area', enabled: true }
    ],
    features: ['metadata', 'loading', 'error'],
    metadata: {
      title: 'Admin Dashboard',
      description: 'Administrative interface'
    },
    tags: ['dashboard', 'admin', 'sidebar']
  },
  {
    id: 'landing-modern',
    name: 'Modern Landing Page',
    description: 'Contemporary landing page layout with hero section',
    category: 'landing',
    type: 'root',
    preview: '/templates/landing-modern.png',
    sections: [
      { type: 'header', name: 'Navigation', enabled: true },
      { type: 'main', name: 'Hero & Content', enabled: true },
      { type: 'footer', name: 'Footer', enabled: true }
    ],
    features: ['metadata', 'loading'],
    metadata: {
      title: 'Landing Page',
      description: 'Convert visitors into customers'
    },
    tags: ['landing', 'marketing', 'modern']
  },
  {
    id: 'blog-layout',
    name: 'Blog Layout',
    description: 'Blog layout with sidebar for categories and recent posts',
    category: 'blog',
    type: 'root',
    preview: '/templates/blog-layout.png',
    sections: [
      { type: 'header', name: 'Blog Header', enabled: true },
      { type: 'main', name: 'Blog Content', enabled: true },
      { type: 'sidebar', name: 'Blog Sidebar', enabled: true },
      { type: 'footer', name: 'Blog Footer', enabled: true }
    ],
    features: ['metadata', 'loading'],
    metadata: {
      title: 'Blog',
      description: 'Latest articles and insights'
    },
    tags: ['blog', 'content', 'sidebar']
  },
  {
    id: 'ecommerce-store',
    name: 'E-commerce Store',
    description: 'E-commerce layout with product navigation and cart',
    category: 'ecommerce',
    type: 'root',
    preview: '/templates/ecommerce-store.png',
    sections: [
      { type: 'header', name: 'Store Header', enabled: true },
      { type: 'main', name: 'Product Area', enabled: true },
      { type: 'footer', name: 'Store Footer', enabled: true }
    ],
    features: ['metadata', 'loading'],
    metadata: {
      title: 'Online Store',
      description: 'Shop our amazing products'
    },
    tags: ['ecommerce', 'store', 'products']
  },
  {
    id: 'portfolio-creative',
    name: 'Creative Portfolio',
    description: 'Portfolio layout for showcasing creative work',
    category: 'website',
    type: 'root',
    preview: '/templates/portfolio-creative.png',
    sections: [
      { type: 'header', name: 'Portfolio Header', enabled: true },
      { type: 'main', name: 'Portfolio Gallery', enabled: true },
      { type: 'footer', name: 'Contact Footer', enabled: true }
    ],
    features: ['metadata', 'loading'],
    metadata: {
      title: 'Portfolio',
      description: 'Creative work and projects'
    },
    tags: ['portfolio', 'creative', 'gallery']
  }
]

// GET /api/nextjs-layout-templates - Get all templates
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')
    const search = searchParams.get('search')
    const type = searchParams.get('type')

    let templates = [...builtInTemplates]

    // TODO: Add custom templates from database
    // const customTemplates = await prisma.layoutTemplate.findMany({
    //   where: { isActive: true }
    // })
    // templates.push(...customTemplates)

    // Filter by category
    if (category && category !== 'all') {
      templates = templates.filter(template => template.category === category)
    }

    // Filter by type
    if (type) {
      templates = templates.filter(template => template.type === type)
    }

    // Filter by search
    if (search) {
      const searchLower = search.toLowerCase()
      templates = templates.filter(template =>
        template.name.toLowerCase().includes(searchLower) ||
        template.description.toLowerCase().includes(searchLower) ||
        template.tags.some(tag => tag.toLowerCase().includes(searchLower))
      )
    }

    return NextResponse.json({
      templates,
      categories: ['website', 'admin', 'landing', 'blog', 'ecommerce', 'dashboard'],
      total: templates.length
    })

  } catch (error) {
    console.error('Error fetching templates:', error)
    return NextResponse.json(
      { error: 'Failed to fetch templates' },
      { status: 500 }
    )
  }
}

// POST /api/nextjs-layout-templates - Save layout as template
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = templateSchema.parse(body)

    const { name, description, category, layout } = validatedData

    // Create template from layout
    const template: NextJSLayoutTemplate = {
      id: `custom-${Date.now()}`,
      name,
      description,
      category,
      type: layout.nextjs.type,
      preview: '', // Would be generated or uploaded
      sections: extractSectionsFromLayout(layout),
      features: layout.nextjs.appRouterFeatures || [],
      metadata: layout.nextjs.metadata || {},
      tags: ['custom', category]
    }

    // TODO: Save to database
    // const savedTemplate = await prisma.layoutTemplate.create({
    //   data: {
    //     ...template,
    //     layoutData: layout,
    //     createdBy: session.user.id
    //   }
    // })

    return NextResponse.json(template, { status: 201 })

  } catch (error) {
    console.error('Error creating template:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to create template' },
      { status: 500 }
    )
  }
}

// Helper function to extract sections from layout
function extractSectionsFromLayout(layout: any) {
  const sections = []
  
  if (layout.structure) {
    Object.entries(layout.structure).forEach(([key, section]: [string, any]) => {
      if (section) {
        sections.push({
          type: section.type,
          name: section.name,
          enabled: section.isVisible
        })
      }
    })
  }

  return sections
}