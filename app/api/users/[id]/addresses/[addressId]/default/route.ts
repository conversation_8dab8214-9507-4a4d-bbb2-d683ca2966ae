import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { auth } from "@/auth"
import { z } from "zod"

// Schema for validating default address request
const defaultAddressSchema = z.object({
  type: z.enum(["shipping", "billing", "both"])
})

// POST /api/users/[id]/addresses/[addressId]/default
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string; addressId: string } }
) {
  try {
    const session = await auth()

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user is updating their own address or is an admin
    if (session.user.id !== params.id && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      )
    }

    // Check if address exists and belongs to the user
    const existingAddress = await db.userAddress.findUnique({
      where: {
        id: params.addressId,
        userId: params.id
      }
    })

    if (!existingAddress) {
      return NextResponse.json(
        { error: "Address not found" },
        { status: 404 }
      )
    }

    // Parse and validate request body
    const body = await req.json()
    const validationResult = defaultAddressSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      )
    }

    const { type } = validationResult.data

    // Update address type if different
    if (existingAddress.type !== type) {
      await db.userAddress.update({
        where: { id: params.addressId },
        data: { type }
      })
    }

    // Clear default flag from other addresses
    if (type === "both") {
      // If both, clear default flag from all addresses
      await db.userAddress.updateMany({
        where: {
          userId: params.id,
          id: { not: params.addressId }
        },
        data: { isDefault: false }
      })
    } else {
      // If shipping or billing, clear default flag from addresses of the same type
      await db.userAddress.updateMany({
        where: {
          userId: params.id,
          id: { not: params.addressId },
          OR: [
            { type },
            { type: "both" }
          ]
        },
        data: { isDefault: false }
      })
    }

    // Set this address as default
    await db.userAddress.update({
      where: { id: params.addressId },
      data: { isDefault: true }
    })

    // Update user's default address IDs
    const updateData: any = {}
    
    if (type === "shipping" || type === "both") {
      updateData.defaultShippingAddressId = params.addressId
    }
    
    if (type === "billing" || type === "both") {
      updateData.defaultBillingAddressId = params.addressId
    }
    
    await db.user.update({
      where: { id: params.id },
      data: updateData
    })

    // Get all updated addresses
    const addresses = await db.userAddress.findMany({
      where: { userId: params.id },
      orderBy: [
        { isDefault: "desc" },
        { createdAt: "desc" }
      ]
    })

    return NextResponse.json({ success: true, addresses })
  } catch (error) {
    console.error("[ADDRESS_DEFAULT]", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}