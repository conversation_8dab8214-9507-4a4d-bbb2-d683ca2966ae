import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { auth } from "@/auth"
import { z } from "zod"

// Schema for validating address update data
const addressUpdateSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  company: z.string().optional(),
  address1: z.string().min(1, "Address line 1 is required"),
  address2: z.string().optional(),
  city: z.string().min(1, "City is required"),
  province: z.string().min(1, "Province/State is required"),
  country: z.string().min(1, "Country is required"),
  postalCode: z.string().min(1, "Postal code is required"),
  phone: z.string().optional(),
  isDefault: z.boolean().optional(),
  type: z.enum(["shipping", "billing", "both"]),
  label: z.string().optional(),
})

// GET /api/users/[id]/addresses/[addressId]
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string; addressId: string } }
) {
  try {
    const session = await auth()

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user is requesting their own address or is an admin
    if (session.user.id !== params.id && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      )
    }

    // Get the address
    const address = await db.userAddress.findUnique({
      where: {
        id: params.addressId,
        userId: params.id
      }
    })

    if (!address) {
      return NextResponse.json(
        { error: "Address not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ address })
  } catch (error) {
    console.error("[ADDRESS_GET]", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PATCH /api/users/[id]/addresses/[addressId]
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string; addressId: string } }
) {
  try {
    const session = await auth()

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user is updating their own address or is an admin
    if (session.user.id !== params.id && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      )
    }

    // Check if address exists and belongs to the user
    const existingAddress = await db.userAddress.findUnique({
      where: {
        id: params.addressId,
        userId: params.id
      }
    })

    if (!existingAddress) {
      return NextResponse.json(
        { error: "Address not found" },
        { status: 404 }
      )
    }

    // Parse and validate request body
    const body = await req.json()
    const validationResult = addressUpdateSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      )
    }

    const validatedData = validationResult.data

    // Handle default address changes
    if (validatedData.isDefault && !existingAddress.isDefault) {
      // If setting as default, update other addresses
      if (validatedData.type === "both") {
        // If both, clear default flag from all addresses
        await db.userAddress.updateMany({
          where: {
            userId: params.id,
            id: { not: params.addressId }
          },
          data: { isDefault: false }
        })
      } else {
        // If shipping or billing, clear default flag from addresses of the same type
        await db.userAddress.updateMany({
          where: {
            userId: params.id,
            id: { not: params.addressId },
            OR: [
              { type: validatedData.type },
              { type: "both" }
            ]
          },
          data: { isDefault: false }
        })
      }

      // Update user's default address IDs
      const updateData: any = {}
      
      if (validatedData.type === "shipping" || validatedData.type === "both") {
        updateData.defaultShippingAddressId = params.addressId
      }
      
      if (validatedData.type === "billing" || validatedData.type === "both") {
        updateData.defaultBillingAddressId = params.addressId
      }
      
      await db.user.update({
        where: { id: params.id },
        data: updateData
      })
    } else if (existingAddress.isDefault && !validatedData.isDefault) {
      // If removing default status, clear user's default address IDs
      const updateData: any = {}
      
      if (existingAddress.type === "shipping" || existingAddress.type === "both") {
        updateData.defaultShippingAddressId = null
      }
      
      if (existingAddress.type === "billing" || existingAddress.type === "both") {
        updateData.defaultBillingAddressId = null
      }
      
      await db.user.update({
        where: { id: params.id },
        data: updateData
      })
    } else if (existingAddress.isDefault && 
               validatedData.isDefault && 
               existingAddress.type !== validatedData.type) {
      // If changing the type of a default address, update user's default address IDs
      const updateData: any = {}
      
      // Clear old type default
      if (existingAddress.type === "shipping" || existingAddress.type === "both") {
        updateData.defaultShippingAddressId = null
      }
      
      if (existingAddress.type === "billing" || existingAddress.type === "both") {
        updateData.defaultBillingAddressId = null
      }
      
      // Set new type default
      if (validatedData.type === "shipping" || validatedData.type === "both") {
        updateData.defaultShippingAddressId = params.addressId
      }
      
      if (validatedData.type === "billing" || validatedData.type === "both") {
        updateData.defaultBillingAddressId = params.addressId
      }
      
      await db.user.update({
        where: { id: params.id },
        data: updateData
      })
    }

    // Update the address
    const updatedAddress = await db.userAddress.update({
      where: {
        id: params.addressId,
        userId: params.id
      },
      data: validatedData
    })

    return NextResponse.json({ address: updatedAddress })
  } catch (error) {
    console.error("[ADDRESS_PATCH]", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// DELETE /api/users/[id]/addresses/[addressId]
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string; addressId: string } }
) {
  try {
    const session = await auth()

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user is deleting their own address or is an admin
    if (session.user.id !== params.id && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      )
    }

    // Check if address exists and belongs to the user
    const existingAddress = await db.userAddress.findUnique({
      where: {
        id: params.addressId,
        userId: params.id
      }
    })

    if (!existingAddress) {
      return NextResponse.json(
        { error: "Address not found" },
        { status: 404 }
      )
    }

    // If this is a default address, update the user's default address IDs
    if (existingAddress.isDefault) {
      const updateData: any = {}
      
      if (existingAddress.type === "shipping" || existingAddress.type === "both") {
        updateData.defaultShippingAddressId = null
      }
      
      if (existingAddress.type === "billing" || existingAddress.type === "both") {
        updateData.defaultBillingAddressId = null
      }
      
      await db.user.update({
        where: { id: params.id },
        data: updateData
      })
    }

    // Delete the address
    await db.userAddress.delete({
      where: {
        id: params.addressId,
        userId: params.id
      }
    })

    // Find a new default address if needed
    if (existingAddress.isDefault) {
      // For shipping
      if (existingAddress.type === "shipping" || existingAddress.type === "both") {
        const newDefaultShipping = await db.userAddress.findFirst({
          where: {
            userId: params.id,
            OR: [
              { type: "shipping" },
              { type: "both" }
            ]
          },
          orderBy: { createdAt: "desc" }
        })

        if (newDefaultShipping) {
          await db.userAddress.update({
            where: { id: newDefaultShipping.id },
            data: { isDefault: true }
          })

          await db.user.update({
            where: { id: params.id },
            data: { defaultShippingAddressId: newDefaultShipping.id }
          })
        }
      }

      // For billing
      if (existingAddress.type === "billing" || existingAddress.type === "both") {
        const newDefaultBilling = await db.userAddress.findFirst({
          where: {
            userId: params.id,
            OR: [
              { type: "billing" },
              { type: "both" }
            ]
          },
          orderBy: { createdAt: "desc" }
        })

        if (newDefaultBilling) {
          await db.userAddress.update({
            where: { id: newDefaultBilling.id },
            data: { isDefault: true }
          })

          await db.user.update({
            where: { id: params.id },
            data: { defaultBillingAddressId: newDefaultBilling.id }
          })
        }
      }
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("[ADDRESS_DELETE]", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}