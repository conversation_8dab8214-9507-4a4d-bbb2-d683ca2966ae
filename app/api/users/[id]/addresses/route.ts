import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { auth } from "@/auth"
import { z } from "zod"

// Schema for validating address data
const addressSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  company: z.string().optional(),
  address1: z.string().min(1, "Address line 1 is required"),
  address2: z.string().optional(),
  city: z.string().min(1, "City is required"),
  province: z.string().min(1, "Province/State is required"),
  country: z.string().min(1, "Country is required"),
  postalCode: z.string().min(1, "Postal code is required"),
  phone: z.string().optional(),
  isDefault: z.boolean().default(false),
  type: z.enum(["shipping", "billing", "both"]),
  label: z.string().optional(),
})

// GET /api/users/[id]/addresses
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user is requesting their own addresses or is an admin
    if (session.user.id !== params.id && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      )
    }

    // Get user addresses
    const addresses = await db.userAddress.findMany({
      where: { userId: params.id },
      orderBy: [
        { isDefault: "desc" },
        { createdAt: "desc" }
      ]
    })

    return NextResponse.json({ addresses })
  } catch (error) {
    console.error("[ADDRESSES_GET]", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/users/[id]/addresses
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user is adding their own address or is an admin
    if (session.user.id !== params.id && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await req.json()
    const validationResult = addressSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      )
    }

    const validatedData = validationResult.data

    // If this is set as default, update other addresses of the same type
    if (validatedData.isDefault) {
      if (validatedData.type === "both") {
        // If both, clear default flag from all addresses
        await db.userAddress.updateMany({
          where: { userId: params.id },
          data: { isDefault: false }
        })
      } else {
        // If shipping or billing, clear default flag from addresses of the same type
        await db.userAddress.updateMany({
          where: {
            userId: params.id,
            OR: [
              { type: validatedData.type },
              { type: "both" }
            ]
          },
          data: { isDefault: false }
        })
      }

      // Update user's default address IDs
      if (validatedData.type === "shipping" || validatedData.type === "both") {
        await db.user.update({
          where: { id: params.id },
          data: { defaultShippingAddressId: null }
        })
      }
      
      if (validatedData.type === "billing" || validatedData.type === "both") {
        await db.user.update({
          where: { id: params.id },
          data: { defaultBillingAddressId: null }
        })
      }
    }

    // Create the new address
    const newAddress = await db.userAddress.create({
      data: {
        ...validatedData,
        userId: params.id
      }
    })

    // If this is the default address, update the user's default address IDs
    if (validatedData.isDefault) {
      const updateData: any = {}
      
      if (validatedData.type === "shipping" || validatedData.type === "both") {
        updateData.defaultShippingAddressId = newAddress.id
      }
      
      if (validatedData.type === "billing" || validatedData.type === "both") {
        updateData.defaultBillingAddressId = newAddress.id
      }
      
      await db.user.update({
        where: { id: params.id },
        data: updateData
      })
    }

    return NextResponse.json({ address: newAddress })
  } catch (error) {
    console.error("[ADDRESSES_POST]", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}