import { NextRequest, NextResponse } from "next/server"
import { db } from "@/lib/db"
import { auth } from "@/auth"
import { z } from "zod"

// Schema for validating user update data
const userUpdateSchema = z.object({
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  displayName: z.string().optional(),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional().transform(val => val ? new Date(val) : undefined),
  gender: z.string().optional(),
  acceptsMarketing: z.boolean().optional(),
  preferredLanguage: z.string().optional(),
  preferredCurrency: z.string().optional(),
  timezone: z.string().optional(),
  avatar: z.string().optional(),
  bio: z.string().optional(),
})

// GET /api/users/[id]
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user is requesting their own data or is an admin
    if (session.user.id !== params.id && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      )
    }

    // Get user data
    const user = await db.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        displayName: true,
        phone: true,
        dateOfBirth: true,
        gender: true,
        emailVerified: true,
        phoneVerified: true,
        lastLoginAt: true,
        acceptsMarketing: true,
        preferredLanguage: true,
        preferredCurrency: true,
        timezone: true,
        avatar: true,
        bio: true,
        isActive: true,
        defaultBillingAddressId: true,
        defaultShippingAddressId: true,
        customerSince: true,
        totalSpent: true,
        orderCount: true,
        averageOrderValue: true,
        lastOrderAt: true,
        loyaltyPoints: true,
        loyaltyTier: true,
        tags: true,
        notes: true,
        createdAt: true,
        updatedAt: true,
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({ user })
  } catch (error) {
    console.error("[USER_GET]", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// PATCH /api/users/[id]
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth()

    // Check if user is authenticated
    if (!session || !session.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    // Check if user is updating their own data or is an admin
    if (session.user.id !== params.id && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden" },
        { status: 403 }
      )
    }

    // Parse and validate request body
    const body = await req.json()
    const validationResult = userUpdateSchema.safeParse(body)

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.format() },
        { status: 400 }
      )
    }

    const validatedData = validationResult.data

    // Update user data
    const updatedUser = await db.user.update({
      where: { id: params.id },
      data: validatedData,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        displayName: true,
        phone: true,
        dateOfBirth: true,
        gender: true,
        emailVerified: true,
        phoneVerified: true,
        lastLoginAt: true,
        acceptsMarketing: true,
        preferredLanguage: true,
        preferredCurrency: true,
        timezone: true,
        avatar: true,
        bio: true,
        isActive: true,
        defaultBillingAddressId: true,
        defaultShippingAddressId: true,
        customerSince: true,
        totalSpent: true,
        orderCount: true,
        averageOrderValue: true,
        lastOrderAt: true,
        loyaltyPoints: true,
        loyaltyTier: true,
        tags: true,
        notes: true,
        createdAt: true,
        updatedAt: true,
      }
    })

    return NextResponse.json({ user: updatedUser })
  } catch (error) {
    console.error("[USER_PATCH]", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}