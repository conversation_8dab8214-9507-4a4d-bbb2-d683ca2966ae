import { NextRequest, NextResponse } from 'next/server'
import { OzowGateway } from '@/lib/payments/gateways/ozow'
import { WebhookPayload, WebhookEvent, PaymentGateway } from '@/lib/payments/types'
import { logger } from '@/lib/payments/logger'

export async function POST(request: NextRequest) {
  try {
    // Get JSON body for Ozow webhooks
    const data = await request.json()

    logger.info('Ozow webhook received', {
      gateway: PaymentGateway.OZOW,
      status: data.Status,
      transactionId: data.TransactionId,
      reference: data.TransactionReference,
    })

    // Initialize Ozow gateway
    const gateway = new OzowGateway()

    // Verify webhook signature
    const signature = data.HashCheck || ''
    if (!gateway.verifyWebhook(data, signature)) {
      logger.error('Ozow webhook signature verification failed', {
        gateway: PaymentGateway.OZOW,
        transactionId: data.TransactionId,
      })
      
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      )
    }

    // Map Ozow status to webhook event
    let event: WebhookEvent
    switch (data.Status) {
      case 'Complete':
        event = WebhookEvent.PAYMENT_COMPLETED
        break
      case 'Error':
      case 'Abandoned':
        event = WebhookEvent.PAYMENT_FAILED
        break
      case 'Cancelled':
        event = WebhookEvent.PAYMENT_CANCELLED
        break
      case 'PendingInvestigation':
        event = WebhookEvent.PAYMENT_PENDING
        break
      default:
        event = WebhookEvent.PAYMENT_PENDING
    }

    // Create webhook payload
    const webhookPayload: WebhookPayload = {
      gateway: PaymentGateway.OZOW,
      event,
      data,
      signature,
      timestamp: new Date().toISOString(),
    }

    // Process webhook
    await gateway.processWebhook(webhookPayload)

    logger.info('Ozow webhook processed successfully', {
      gateway: PaymentGateway.OZOW,
      event,
      transactionId: data.TransactionId,
    })

    // Ozow expects a 200 OK response
    return NextResponse.json({ status: 'ok' })

  } catch (error) {
    logger.error('Ozow webhook processing failed', {
      gateway: PaymentGateway.OZOW,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    })

    // Return 200 to prevent Ozow from retrying
    // Log the error for investigation
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 200 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'Ozow webhook endpoint',
      gateway: PaymentGateway.OZOW,
      methods: ['POST'],
    }
  )
}