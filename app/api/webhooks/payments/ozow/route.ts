/**
 * Ozow Webhook Handler
 */

import { NextRequest, NextResponse } from 'next/server'
import { PaymentGateway } from '@/lib/payment-core/types'
import { processWebhook } from '@/lib/payment-core/webhook-handler'
import { logger, webhookLogger } from '@/lib/payment-core/logger'

export async function POST(request: NextRequest) {
  try {
    // Log webhook received
    webhookLogger.received({
      gateway: PaymentGateway.OZOW,
      url: request.url,
      method: request.method,
      headers: Object.fromEntries(request.headers)
    })
    
    // Parse request body
    const body = await request.json().catch(() => ({}))
    
    // Get signature from headers
    const signature = request.headers.get('x-ozow-signature') || ''
    
    // Process webhook
    const result = await processWebhook(PaymentGateway.OZOW, body, signature || undefined)
    
    if (result.success) {
      return NextResponse.json({ success: true, message: result.message }, { status: 200 })
    } else {
      return NextResponse.json({ success: false, error: result.message }, { status: 400 })
    }
    
  } catch (error) {
    logger.error('Ozow webhook processing error', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}