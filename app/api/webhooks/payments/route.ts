/**
 * Payment Webhooks API Route
 * 
 * Handles incoming webhooks from payment gateways.
 * Each gateway sends notifications about payment status changes.
 */

import { NextRequest, NextResponse } from 'next/server'
import { paymentService, PaymentGateway, WebhookEvent } from '@/lib/payment-core'
import { logger } from '@/lib/payment-core/logger'

/**
 * Main webhook handler for all payment gateways
 */
export async function POST(request: NextRequest) {
  try {
    // Initialize payment service
    await paymentService.init()
    
    // Get gateway from URL or query parameter
    const url = new URL(request.url)
    const pathParts = url.pathname.split('/')
    const gatewayParam = pathParts[pathParts.length - 1]
    
    let gateway: PaymentGateway
    
    // Determine gateway from URL path or query parameter
    if (gatewayParam && Object.values(PaymentGateway).includes(gatewayParam as PaymentGateway)) {
      gateway = gatewayParam as PaymentGateway
    } else {
      const queryGateway = url.searchParams.get('gateway')
      if (queryGateway && Object.values(PaymentGateway).includes(queryGateway as PaymentGateway)) {
        gateway = queryGateway as PaymentGateway
      } else {
        // Default to PayFast if not specified
        gateway = PaymentGateway.PAYFAST
      }
    }
    
    // Log webhook received
    logger.info('Payment webhook received', {
      gateway,
      url: request.url,
      method: request.method,
      headers: Object.fromEntries(request.headers)
    })
    
    // Parse request body
    const body = await request.json().catch(() => ({}))
    
    // Get signature from headers
    const signature = request.headers.get('x-signature') || 
                     request.headers.get('signature') ||
                     request.headers.get('x-payfast-signature') ||
                     request.headers.get('x-ozow-signature') ||
                     request.headers.get('x-snapscan-signature') ||
                     request.headers.get('x-yoco-signature') ||
                     request.headers.get('x-webhook-signature') || ''
    
    // Process webhook using the payment service
    const result = await paymentService.handleWebhook({
      gateway,
      payload: body,
      signature: signature || undefined
    })
    
    if (result.success) {
      logger.info('Payment webhook processed successfully', {
        gateway,
        event: result.event,
        transactionId: result.transactionId
      })
      
      // Return appropriate response based on the gateway
      // Some gateways expect specific response formats
      switch (gateway) {
        case PaymentGateway.PAYFAST:
          return new NextResponse('OK', { status: 200 })
        
        case PaymentGateway.OZOW:
          return NextResponse.json({ status: 'OK' })
        
        case PaymentGateway.SNAPSCAN:
          return NextResponse.json({ status: 'received' })
        
        case PaymentGateway.YOCO:
          return NextResponse.json({ received: true })
        
        default:
          return NextResponse.json({ success: true, message: result.message })
      }
    } else {
      logger.error('Payment webhook processing failed', {
        gateway,
        error: result.error
      })
      
      return NextResponse.json(
        { success: false, error: result.error || 'Failed to process webhook' },
        { status: 400 }
      )
    }
    
  } catch (error) {
    logger.error('Webhook processing error', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * GET handler for webhook verification
 * Some payment gateways use GET for initial verification
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const gateway = url.searchParams.get('gateway') as PaymentGateway || PaymentGateway.PAYFAST
    
    logger.info('GET request to payment webhook', {
      gateway,
      query: Object.fromEntries(url.searchParams.entries())
    })
    
    // For most gateways, we just acknowledge the request
    return NextResponse.json({ status: 'ok' })
  } catch (error) {
    logger.error('Error handling GET request to payment webhook', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}