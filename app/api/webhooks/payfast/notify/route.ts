/**
 * PayFast Notification Webhook Handler
 * 
 * Handles ITN (Instant Transaction Notification) from PayFast
 */

import { NextRequest, NextResponse } from 'next/server'
import { PaymentGateway, WebhookEvent, PaymentStatus } from '@/lib/payment-core/types'
import { PayfastService } from '@/lib/payment-core/services/payfast-service'
import { logger } from '@/lib/payment-core/logger'
import { prisma } from '@/lib/prisma'

// PayFast server IP addresses for validation
const PAYFAST_IP_RANGES = [
  '**************/28',
  '*************/27'
]

export async function POST(request: NextRequest) {
  try {
    // Log webhook received
    logger.info('PayFast webhook received', {
      url: request.url,
      method: request.method,
      headers: Object.fromEntries(request.headers)
    })
    
    // Validate IP address (in production, you should check if the request is coming from PayFast)
    // const clientIp = request.headers.get('x-forwarded-for') || request.ip
    // const isValidIp = validateIpAddress(clientIp)
    // if (!isValidIp) {
    //   logger.warn('Invalid IP address for PayFast webhook', { ip: clientIp })
    //   return NextResponse.json({ error: 'Invalid request source' }, { status: 403 })
    // }
    
    // Parse request body
    const body = await request.formData()
    const payload = Object.fromEntries(body.entries())
    
    // Initialize PayFast service
    const payfastService = new PayfastService()
    
    // Process webhook
    const webhookPayload = await payfastService.processWebhook(payload)
    
    // Extract payment data
    const { event, data } = webhookPayload
    const transactionId = data.m_payment_id || data.pf_payment_id
    const orderId = data.custom_str2 || ''
    const paymentStatus = mapEventToStatus(event)
    
    logger.info('PayFast webhook processed', {
      event,
      transactionId,
      orderId,
      status: paymentStatus
    })
    
    // Update order status in database
    if (orderId) {
      try {
        await updateOrderStatus(orderId, paymentStatus)
        logger.info('Order status updated', { orderId, status: paymentStatus })
      } catch (error) {
        logger.error('Failed to update order status', {
          orderId,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    return NextResponse.json({ success: true })
    
  } catch (error) {
    logger.error('PayFast webhook processing error', {
      error: error instanceof Error ? error.message : 'Unknown error'
    })
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

/**
 * Map webhook event to payment status
 */
function mapEventToStatus(event: WebhookEvent): PaymentStatus {
  switch (event) {
    case WebhookEvent.PAYMENT_COMPLETED:
      return PaymentStatus.COMPLETED
    case WebhookEvent.PAYMENT_FAILED:
      return PaymentStatus.FAILED
    case WebhookEvent.PAYMENT_CANCELLED:
      return PaymentStatus.CANCELLED
    case WebhookEvent.PAYMENT_REFUNDED:
      return PaymentStatus.REFUNDED
    case WebhookEvent.PAYMENT_PENDING:
      return PaymentStatus.PENDING
    case WebhookEvent.PAYMENT_PROCESSING:
      return PaymentStatus.PROCESSING
    default:
      return PaymentStatus.PENDING
  }
}

/**
 * Update order status in database
 */
async function updateOrderStatus(orderId: string, status: PaymentStatus): Promise<void> {
  // Map payment status to order status
  let orderStatus: string
  
  switch (status) {
    case PaymentStatus.COMPLETED:
      orderStatus = 'paid'
      break
    case PaymentStatus.FAILED:
      orderStatus = 'payment_failed'
      break
    case PaymentStatus.CANCELLED:
      orderStatus = 'cancelled'
      break
    case PaymentStatus.REFUNDED:
      orderStatus = 'refunded'
      break
    default:
      orderStatus = 'pending'
  }
  
  // Update order in database
  await prisma.order.update({
    where: { id: orderId },
    data: { 
      status: orderStatus,
      paymentStatus: status
    }
  })
}

/**
 * Validate if IP address is from PayFast
 */
function validateIpAddress(ip: string | null): boolean {
  if (!ip) return false
  
  // In a real implementation, you would check if the IP is in the PayFast ranges
  // This is a simplified version
  return true
}