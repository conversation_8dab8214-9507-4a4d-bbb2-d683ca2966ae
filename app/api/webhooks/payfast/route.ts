import { NextRequest, NextResponse } from 'next/server'
import { PayFastGateway } from '@/lib/payments/gateways/payfast'
import { WebhookPayload, WebhookEvent, PaymentGateway } from '@/lib/payments/types'
import { logger } from '@/lib/payments/logger'

export async function POST(request: NextRequest) {
  try {
    // Get the raw body for signature verification
    const body = await request.text()
    const formData = new URLSearchParams(body)
    const data = Object.fromEntries(formData.entries())

    logger.info('PayFast webhook received', {
      gateway: PaymentGateway.PAYFAST,
      paymentStatus: data.payment_status,
      reference: data.m_payment_id,
      transactionId: data.pf_payment_id,
    })

    // Initialize PayFast gateway
    const gateway = new PayFastGateway()

    // Verify webhook signature
    const signature = data.signature || ''
    if (!gateway.verifyWebhook(data, signature)) {
      logger.error('PayFast webhook signature verification failed', {
        gateway: PaymentGateway.PAYFAST,
        reference: data.m_payment_id,
      })
      
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 401 }
      )
    }

    // Map PayFast status to webhook event
    let event: WebhookEvent
    switch (data.payment_status) {
      case 'COMPLETE':
        event = WebhookEvent.PAYMENT_COMPLETED
        break
      case 'FAILED':
        event = WebhookEvent.PAYMENT_FAILED
        break
      case 'CANCELLED':
        event = WebhookEvent.PAYMENT_CANCELLED
        break
      default:
        event = WebhookEvent.PAYMENT_PENDING
    }

    // Create webhook payload
    const webhookPayload: WebhookPayload = {
      gateway: PaymentGateway.PAYFAST,
      event,
      data,
      signature,
      timestamp: new Date().toISOString(),
    }

    // Process webhook
    await gateway.processWebhook(webhookPayload)

    logger.info('PayFast webhook processed successfully', {
      gateway: PaymentGateway.PAYFAST,
      event,
      reference: data.m_payment_id,
    })

    // PayFast expects a 200 OK response
    return NextResponse.json({ status: 'ok' })

  } catch (error) {
    logger.error('PayFast webhook processing failed', {
      gateway: PaymentGateway.PAYFAST,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    })

    // Return 200 to prevent PayFast from retrying
    // Log the error for investigation
    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 200 }
    )
  }
}

export async function GET() {
  return NextResponse.json(
    { 
      message: 'PayFast webhook endpoint',
      gateway: PaymentGateway.PAYFAST,
      methods: ['POST'],
    }
  )
}