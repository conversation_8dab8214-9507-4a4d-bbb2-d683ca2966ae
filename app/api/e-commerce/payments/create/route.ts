// Payment Creation API Routes
// POST /api/e-commerce/payments/create - Create payment using gateway system

import { NextRequest, NextResponse } from 'next/server'
import { PaymentManager } from '@/lib/payments/gateway-factory'
import { PaymentGateway, PaymentMethod } from '@/lib/payments/types'
import { createSuccessResponse, handleApiError } from '@/lib/ecommerce'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      orderId,
      amount,
      currency = 'ZAR',
      customerEmail,
      customerName,
      description,
      paymentMethod,
      preferredGateway,
      returnUrl,
      cancelUrl,
      notifyUrl,
      metadata = {}
    } = body

    // Validate required fields
    if (!orderId || !amount || !customerEmail || !customerName) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: orderId, amount, customerEmail, customerName'
      }, { status: 400 })
    }

    if (amount <= 0) {
      return NextResponse.json({
        success: false,
        error: 'Amount must be greater than 0'
      }, { status: 400 })
    }

    // Create payment request
    const paymentRequest = {
      amount: {
        amount: parseFloat(amount.toString()),
        currency: currency as 'ZAR'
      },
      customer: {
        email: customerEmail,
        firstName: customerName.split(' ')[0] || customerName,
        lastName: customerName.split(' ').slice(1).join(' ') || '',
        phone: metadata.customerPhone
      },
      items: metadata.items || [],
      metadata: {
        orderId,
        customerId: metadata.customerId,
        source: 'checkout',
        ...metadata
      },
      returnUrl: returnUrl || `${process.env.NEXT_PUBLIC_BASE_URL}/checkout/success?orderId=${orderId}`,
      cancelUrl: cancelUrl || `${process.env.NEXT_PUBLIC_BASE_URL}/checkout/cancelled?orderId=${orderId}`,
      notifyUrl: notifyUrl || `${process.env.NEXT_PUBLIC_BASE_URL}/api/e-commerce/payments/webhook`,
      reference: `ORDER-${orderId}-${Date.now()}`,
      description: description || `Payment for Order ${orderId}`
    }

    // Process payment
    const paymentManager = new PaymentManager()
    const result = await paymentManager.processPayment(
      paymentRequest,
      preferredGateway as PaymentGateway,
      paymentMethod as PaymentMethod
    )

    if (result.success) {
      return NextResponse.json(createSuccessResponse({
        success: true,
        transactionId: result.transactionId,
        paymentUrl: result.paymentUrl,
        qrCode: result.qrCode,
        reference: result.reference,
        status: result.status,
        message: result.message || 'Payment created successfully'
      }))
    } else {
      return NextResponse.json({
        success: false,
        error: result.error?.message || 'Payment creation failed',
        errorCode: result.error?.code
      }, { status: 400 })
    }

  } catch (error) {
    console.error('Create payment API error:', error)
    const { response, statusCode } = handleApiError(error)
    return NextResponse.json(response, { status: statusCode })
  }
}