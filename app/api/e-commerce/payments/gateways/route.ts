// Payment Gateways API Routes
// GET /api/e-commerce/payments/gateways - Get available payment gateways and methods

import { NextRequest, NextResponse } from 'next/server'
import { PaymentGatewayFactory } from '@/lib/payments/gateway-factory'
import { PAYMENT_METHOD_CONFIG } from '@/lib/payments/config'
import { PaymentMethod, PaymentGateway } from '@/lib/payments/types'
import { createSuccessResponse, handleApiError } from '@/lib/ecommerce'

export async function GET(request: NextRequest) {
  try {
    const factory = PaymentGatewayFactory.getInstance()
    const availableGateways = factory.getAvailableGateways()

    // Build payment methods with gateway support
    const paymentMethods = Object.entries(PAYMENT_METHOD_CONFIG).map(([method, config]) => {
      const supportedGateways = factory.getGatewaysForMethod(method as PaymentMethod)
      
      return {
        id: method,
        name: config.displayName,
        description: config.description,
        icon: config.icon,
        processingTime: config.processingTime,
        fees: config.fees,
        supportedGateways,
        enabled: supportedGateways.length > 0
      }
    })

    // Build gateway information
    const gateways = availableGateways.map(gatewayName => {
      const gateway = factory.getGateway(gatewayName)
      if (!gateway) return null

      return {
        id: gatewayName,
        name: gateway.displayName,
        supportedMethods: gateway.supportedMethods,
        supportedCurrencies: gateway.supportedCurrencies,
        enabled: true
      }
    }).filter(Boolean)

    return NextResponse.json(createSuccessResponse({
      paymentMethods,
      gateways,
      defaultGateway: availableGateways[0] || null,
      defaultMethod: PaymentMethod.CARD
    }))

  } catch (error) {
    console.error('Get payment gateways API error:', error)
    const { response, statusCode } = handleApiError(error)
    return NextResponse.json(response, { status: statusCode })
  }
}