// Payment Webhook API Routes
// POST /api/e-commerce/payments/webhook - Handle payment gateway webhooks

import { NextRequest, NextResponse } from 'next/server'
import { PaymentGatewayFactory } from '@/lib/payments/gateway-factory'
import { PaymentGateway, WebhookEvent } from '@/lib/payments/types'
import { orderService } from '@/lib/ecommerce'

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const gateway = searchParams.get('gateway') as PaymentGateway
    
    if (!gateway) {
      return NextResponse.json({
        success: false,
        error: 'Gateway parameter is required'
      }, { status: 400 })
    }

    const factory = PaymentGatewayFactory.getInstance()
    const gatewayInstance = factory.getGateway(gateway)

    if (!gatewayInstance) {
      return NextResponse.json({
        success: false,
        error: `Gateway ${gateway} not available`
      }, { status: 400 })
    }

    // Get webhook payload
    const body = await request.text()
    const signature = request.headers.get('x-signature') || 
                     request.headers.get('x-payfast-signature') || 
                     request.headers.get('x-ozow-signature') || ''

    // Verify webhook signature
    const isValid = gatewayInstance.verifyWebhook(body, signature)
    if (!isValid) {
      console.error('Invalid webhook signature', { gateway, signature })
      return NextResponse.json({
        success: false,
        error: 'Invalid webhook signature'
      }, { status: 401 })
    }

    // Parse webhook data
    let webhookData
    try {
      webhookData = JSON.parse(body)
    } catch {
      // Handle form-encoded data (PayFast style)
      webhookData = Object.fromEntries(new URLSearchParams(body))
    }

    // Extract order information
    const orderId = webhookData.custom_str1 || 
                   webhookData.orderId || 
                   webhookData.order_id ||
                   webhookData.m_payment_id

    if (!orderId) {
      console.error('No order ID found in webhook data', { gateway, webhookData })
      return NextResponse.json({
        success: false,
        error: 'Order ID not found in webhook data'
      }, { status: 400 })
    }

    // Determine payment status and event
    let paymentStatus = 'pending'
    let webhookEvent = WebhookEvent.PAYMENT_PENDING

    // Map gateway-specific status to our standard status
    if (gateway === PaymentGateway.PAYFAST) {
      const status = webhookData.payment_status
      switch (status) {
        case 'COMPLETE':
          paymentStatus = 'completed'
          webhookEvent = WebhookEvent.PAYMENT_COMPLETED
          break
        case 'FAILED':
          paymentStatus = 'failed'
          webhookEvent = WebhookEvent.PAYMENT_FAILED
          break
        case 'CANCELLED':
          paymentStatus = 'cancelled'
          webhookEvent = WebhookEvent.PAYMENT_CANCELLED
          break
        default:
          paymentStatus = 'pending'
      }
    } else if (gateway === PaymentGateway.OZOW) {
      const status = webhookData.Status || webhookData.status
      switch (status) {
        case 'Complete':
        case 'Completed':
          paymentStatus = 'completed'
          webhookEvent = WebhookEvent.PAYMENT_COMPLETED
          break
        case 'Cancelled':
        case 'Canceled':
          paymentStatus = 'cancelled'
          webhookEvent = WebhookEvent.PAYMENT_CANCELLED
          break
        case 'Error':
        case 'Failed':
          paymentStatus = 'failed'
          webhookEvent = WebhookEvent.PAYMENT_FAILED
          break
        default:
          paymentStatus = 'pending'
      }
    }

    // Update order status
    try {
      const orderResult = await orderService().getOrder(orderId)
      if (orderResult?.success && orderResult?.data) {
        const updateData = {
          paymentStatus,
          transactionId: webhookData.pf_payment_id || 
                        webhookData.TransactionId || 
                        webhookData.transaction_id,
          gatewayResponse: webhookData
        }

        // Update order payment status
        await orderService().updateOrder(orderId, updateData)

        console.log('Order payment status updated', {
          orderId,
          paymentStatus,
          gateway,
          transactionId: updateData.transactionId
        })
      }
    } catch (error) {
      console.error('Failed to update order payment status', {
        orderId,
        gateway,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Process webhook through gateway
    const webhookPayload = {
      gateway,
      event: webhookEvent,
      data: webhookData,
      signature,
      timestamp: new Date().toISOString()
    }

    await gatewayInstance.processWebhook(webhookPayload)

    return NextResponse.json({
      success: true,
      message: 'Webhook processed successfully'
    })

  } catch (error) {
    console.error('Webhook processing error:', error)
    return NextResponse.json({
      success: false,
      error: 'Webhook processing failed'
    }, { status: 500 })
  }
}