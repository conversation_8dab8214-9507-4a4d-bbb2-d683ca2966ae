// Add to Cart API Route
// POST /api/e-commerce/cart/add - Add item to cart

import { NextRequest, NextResponse } from 'next/server'
import { cartService, handleEcommerceError } from '@/lib/ecommerce'
import { AddToCartInput } from '@/lib/ecommerce/types/cart'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('Add to cart API: Request body', body)

    const { productId, quantity, variantId, variantOptions, customAttributes, userId, sessionId } = body

    if (!productId || !quantity) {
      console.log('Add to cart API: Missing required fields', { productId, quantity })
      return NextResponse.json(
        { success: false, error: 'Product ID and quantity are required' },
        { status: 400 }
      )
    }

    if (!userId && !sessionId) {
      console.log('Add to cart API: Missing user/session ID', { userId, sessionId })
      return NextResponse.json(
        { success: false, error: 'User ID or session ID is required' },
        { status: 400 }
      )
    }

    // Get or create cart first
    console.log('Add to cart API: Getting or creating cart', { userId, sessionId })
    const cartResult = await cartService().getOrCreateCart(userId, sessionId)

    if (!cartResult.success || !cartResult.data) {
      console.log('Add to cart API: Failed to get/create cart', cartResult.error)
      const error = handleEcommerceError(cartResult.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }

    const cartId = cartResult.data.id
    console.log('Add to cart API: Cart ID', cartId)

    // Add item to cart
    const addToCartInput: AddToCartInput = {
      productId,
      quantity,
      variantId,
      customAttributes
    }

    console.log('Add to cart API: Adding item to cart', addToCartInput)
    const result = await cartService().addToCart(cartId, addToCartInput)

    if (result.success) {
      console.log('Add to cart API: Success', result.data)
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      console.log('Add to cart API: Failed to add item', result.error)
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Add to cart API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
