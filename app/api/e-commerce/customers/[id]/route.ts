// Individual Customer API Routes
// GET /api/e-commerce/customers/[id] - Get customer by ID
// PUT /api/e-commerce/customers/[id] - Update customer
// DELETE /api/e-commerce/customers/[id] - Delete customer

import { NextRequest, NextResponse } from 'next/server'
import { CustomerService } from '@/lib/ecommerce/services/customer-service'
import { UpdateUserInput } from '@/lib/ecommerce/types'

const customerService = new CustomerService()

function handleEcommerceError(error: any) {
  return {
    message: error?.message || 'An error occurred',
    statusCode: error?.statusCode || 500
  }
}

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params

    const result = await customerService.getCustomerById(id)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Get customer API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    const body = await request.json()

    const input: UpdateUserInput = {
      id,
      email: body.email,
      firstName: body.firstName,
      lastName: body.lastName,
      displayName: body.displayName,
      phone: body.phone,
      dateOfBirth: body.dateOfBirth ? new Date(body.dateOfBirth) : undefined,
      gender: body.gender,
      acceptsMarketing: body.acceptsMarketing,
      preferredLanguage: body.preferredLanguage,
      preferredCurrency: body.preferredCurrency,
      timezone: body.timezone,
      avatar: body.avatar,
      bio: body.bio,
      isActive: body.isActive,
      isBlocked: body.isBlocked,
      loyaltyPoints: body.loyaltyPoints,
      loyaltyTier: body.loyaltyTier,
      tags: body.tags,
      notes: body.notes,
      metafields: body.metafields
    }

    const result = await customerService.updateCustomer(input)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Update customer API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params

    const result = await customerService.deleteCustomer(id)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: { deleted: true }
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Delete customer API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
