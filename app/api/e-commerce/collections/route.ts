// Collections API Routes
// GET /api/e-commerce/collections - List collections
// POST /api/e-commerce/collections - Create a new collection

import { NextRequest, NextResponse } from 'next/server'
import { handleEcommerceError } from '@/lib/ecommerce'
import { prisma } from '@/lib/ecommerce/config/database'
import { CollectionService } from '@/lib/ecommerce/services/collection-service'

const collectionService = new CollectionService()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const isVisible = searchParams.get('isVisible')
    const sortOrder = searchParams.get('sortOrder')

    // Build where clause
    const where: any = {}
    if (isVisible !== null) {
      where.isVisible = isVisible === 'true'
    }
    if (sortOrder) {
      where.sortOrder = sortOrder
    }

    // Fetch collections
    const collections = await prisma.productCollection.findMany({
      where,
      include: {
        products: {
          include: {
            product: {
              select: {
                id: true,
                title: true
              }
            }
          }
        }
      },
      orderBy: [
        { createdAt: 'desc' },
        { title: 'asc' }
      ]
    })

    // Transform data to include product count
    const transformedCollections = collections.map(collection => ({
      ...collection,
      productCount: collection.products.length
    }))

    return NextResponse.json({
      success: true,
      data: transformedCollections
    })
  } catch (error) {
    console.error('Collections API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.title) {
      return NextResponse.json(
        { success: false, error: 'Collection title is required' },
        { status: 400 }
      )
    }

    // Use the collection service with validation
    const result = await collectionService.createCollection({
      title: body.title,
      slug: body.slug,
      description: body.description,
      image: body.image,
      sortOrder: body.sortOrder,
      isVisible: body.isVisible,
      seoTitle: body.seoTitle,
      seoDescription: body.seoDescription,
      productIds: body.productIds
    })

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error.message },
        { status: result.error.code === 'VALIDATION_ERROR' ? 400 : 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: result.data
    }, { status: 201 })
  } catch (error) {
    console.error('Create collection API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}