// Product Variants API Routes
// GET /api/e-commerce/products/[id]/variants - Get product variants
// POST /api/e-commerce/products/[id]/variants - Create variants for product

import { NextRequest, NextResponse } from 'next/server'
import { EnhancedVariantService } from '@/lib/ecommerce/modules/products/enhanced-variant-service'
import { handleEcommerceError } from '@/lib/ecommerce'
import { CreateVariantInput } from '@/lib/ecommerce/types'

interface RouteParams {
  params: {
    id: string
  }
}

const variantService = new EnhancedVariantService()

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id: productId } = params

    // Get variants for the product
    const result = await variantService.getVariantsByProduct(productId)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Get variants API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { id: productId } = params
    const body = await request.json()

    // Validate request body
    if (!body.variants || !Array.isArray(body.variants)) {
      return NextResponse.json(
        { success: false, error: 'Variants array is required' },
        { status: 400 }
      )
    }

    // Transform request data to CreateVariantInput format
    const variants: CreateVariantInput[] = body.variants.map((variant: any) => ({
      title: variant.title,
      sku: variant.sku,
      price: {
        amount: parseFloat(variant.price?.amount || variant.price || 0),
        currency: variant.price?.currency || 'ZAR'
      },
      compareAtPrice: variant.compareAtPrice ? {
        amount: parseFloat(variant.compareAtPrice.amount || variant.compareAtPrice),
        currency: variant.compareAtPrice.currency || 'ZAR'
      } : undefined,
      costPerItem: variant.costPerItem ? {
        amount: parseFloat(variant.costPerItem.amount || variant.costPerItem),
        currency: variant.costPerItem.currency || 'ZAR'
      } : undefined,
      weight: variant.weight ? parseFloat(variant.weight) : undefined,
      weightUnit: variant.weightUnit || 'kg',
      inventoryQuantity: parseInt(variant.inventoryQuantity) || 0,
      inventoryPolicy: variant.inventoryPolicy || 'deny',
      fulfillmentService: variant.fulfillmentService || 'manual',
      inventoryManagement: variant.inventoryManagement ?? true,
      options: variant.options || [],
      barcode: variant.barcode,
      taxable: variant.taxable ?? true,
      requiresShipping: variant.requiresShipping ?? true,
      trackQuantity: variant.trackQuantity ?? true,
      continueSellingWhenOutOfStock: variant.continueSellingWhenOutOfStock ?? false,
      metafields: variant.metafields
    }))

    const result = await variantService.createVariants(productId, variants)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      }, { status: 201 })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Create variants API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id: productId } = params
    const body = await request.json()

    // Validate request body
    if (!body.variants || !Array.isArray(body.variants)) {
      return NextResponse.json(
        { success: false, error: 'Variants array is required' },
        { status: 400 }
      )
    }

    // Transform request data to UpdateVariantInput format
    const variants = body.variants.map((variant: any) => ({
      id: variant.id,
      title: variant.title,
      sku: variant.sku,
      price: variant.price ? {
        amount: parseFloat(variant.price.amount || variant.price),
        currency: variant.price.currency || 'ZAR'
      } : undefined,
      compareAtPrice: variant.compareAtPrice ? {
        amount: parseFloat(variant.compareAtPrice.amount || variant.compareAtPrice),
        currency: variant.compareAtPrice.currency || 'ZAR'
      } : undefined,
      costPerItem: variant.costPerItem ? {
        amount: parseFloat(variant.costPerItem.amount || variant.costPerItem),
        currency: variant.costPerItem.currency || 'ZAR'
      } : undefined,
      weight: variant.weight ? parseFloat(variant.weight) : undefined,
      weightUnit: variant.weightUnit,
      inventoryQuantity: variant.inventoryQuantity !== undefined ? parseInt(variant.inventoryQuantity) : undefined,
      inventoryPolicy: variant.inventoryPolicy,
      fulfillmentService: variant.fulfillmentService,
      inventoryManagement: variant.inventoryManagement,
      options: variant.options,
      barcode: variant.barcode,
      taxable: variant.taxable,
      requiresShipping: variant.requiresShipping,
      trackQuantity: variant.trackQuantity,
      continueSellingWhenOutOfStock: variant.continueSellingWhenOutOfStock,
      metafields: variant.metafields
    }))

    const result = await variantService.bulkUpdateVariants(variants)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Update variants API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id: productId } = params
    const { searchParams } = new URL(request.url)
    const variantIds = searchParams.get('variantIds')?.split(',') || []

    if (variantIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Variant IDs are required' },
        { status: 400 }
      )
    }

    const result = await variantService.deleteVariants(variantIds)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Delete variants API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
