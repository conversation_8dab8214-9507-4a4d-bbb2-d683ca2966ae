// Related Products API Route
// GET /api/e-commerce/products/[id]/related - Get related products

import { NextRequest, NextResponse } from 'next/server'
import { productService, handleEcommerceError } from '@/lib/ecommerce'

interface RouteParams {
  params: {
    id: string
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    const { searchParams } = new URL(request.url)
    
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 4

    const result = await productService.getRelatedProducts(id, limit)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Get related products API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
