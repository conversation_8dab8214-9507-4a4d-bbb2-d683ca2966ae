// Product Inventory API Routes
// PUT /api/e-commerce/products/[id]/inventory - Update product inventory

import { NextRequest, NextResponse } from 'next/server'
import { productService, handleEcommerceError } from '@/lib/ecommerce'

interface RouteParams {
  params: {
    id: string
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    const body = await request.json()
    
    // Validate required fields
    if (typeof body.quantity !== 'number') {
      return NextResponse.json(
        { success: false, error: 'Quantity is required and must be a number' },
        { status: 400 }
      )
    }

    // Update inventory through product service
    const result = await productService().updateInventory(id, {
      quantity: body.quantity,
      variantId: body.variantId,
      locationId: body.locationId,
      reason: body.reason || 'Manual adjustment',
      notes: body.notes
    })

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Update inventory API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    const { searchParams } = new URL(request.url)
    const locationId = searchParams.get('locationId')

    // Get inventory information for product
    const result = await productService().getProductInventory(id, locationId || undefined)

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Get inventory API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
