import { NextResponse } from 'next/server'
import { getProductBySlug } from '@/lib/ecommerce/actions/products'

export const runtime = 'edge'
export const fetchCache = 'force-no-store'
export const revalidate = 0

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  console.log('API Route - Received request for slug:', params.slug)
  
  try {
    const slug = params.slug
    if (!slug) {
      console.log('API Route - Missing slug parameter')
      return new NextResponse(
        JSON.stringify({ 
          success: false, 
          error: { code: 'MISSING_SLUG', message: 'Product slug is required' } 
        }),
        { 
          status: 400,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
    }

    console.log('API Route - Fetching product data for slug:', slug)
    const result = await getProductBySlug(slug)
    console.log('API Route - Service response:', {
      success: result.success,
      error: result.error,
      hasData: !!result.data
    })
    
    if (!result.success) {
      const statusCode = result.error?.code === 'PRODUCT_NOT_FOUND' ? 404 : 500
      console.log(`API Route - Returning ${statusCode} error:`, result.error)
      
      return new NextResponse(
        JSON.stringify(result),
        { 
          status: statusCode,
          headers: {
            'Content-Type': 'application/json'
          }
        }
      )
    }

    console.log('API Route - Successfully found product, returning data')
    return new NextResponse(
      JSON.stringify(result),
      { 
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      }
    )
    
  } catch (error) {
    console.error('API Route - Unexpected error:', error)
    return new NextResponse(
      JSON.stringify({ 
        success: false, 
        error: { 
          code: 'INTERNAL_ERROR', 
          message: error instanceof Error ? error.message : 'An unexpected error occurred'
        } 
      }),
      { 
        status: 500,
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )
  }
}
