import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { transformProductFromDb } from '@/lib/ecommerce/utils/product-transformers'

// GET /api/e-commerce/products/id/[id] - Get a product by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_PARAMS',
            message: 'Product ID is required'
          }
        },
        { status: 400 }
      )
    }

    const product = await db.product.findUnique({
      where: { id },
      include: {
        images: true,
        categories: {
          include: {
            category: true
          }
        },
        variants: true,
        options: true,
        tags: {
          include: {
            tag: true
          }
        },
        collections: {
          include: {
            collection: true
          }
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'PRODUCT_NOT_FOUND',
            message: `Product with ID ${id} not found`
          }
        },
        { status: 404 }
      )
    }

    // Transform the product data to match our domain model
    const transformedProduct = transformProductFromDb(product)

    return NextResponse.json({
      success: true,
      data: transformedProduct
    })
  } catch (error) {
    console.error('Error fetching product by ID:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An error occurred while fetching the product'
        }
      },
      { status: 500 }
    )
  }
}

// PUT or PATCH /api/e-commerce/products/id/[id] - Update a product
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return updateProduct(request, params)
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  return updateProduct(request, params)
}

async function updateProduct(
  request: NextRequest,
  { id }: { id: string }
) {
  try {
    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_PARAMS',
            message: 'Product ID is required'
          }
        },
        { status: 400 }
      )
    }

    // Check if product exists
    const existingProduct = await db.product.findUnique({
      where: { id }
    })

    if (!existingProduct) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'PRODUCT_NOT_FOUND',
            message: `Product with ID ${id} not found`
          }
        },
        { status: 404 }
      )
    }

    // Parse the request body
    const data = await request.json()

    // Update the product
    const updatedProduct = await db.product.update({
      where: { id },
      data,
      include: {
        images: true,
        categories: {
          include: {
            category: true
          }
        },
        variants: true,
        options: true,
        tags: {
          include: {
            tag: true
          }
        },
        collections: {
          include: {
            collection: true
          }
        }
      }
    })

    // Transform the product data to match our domain model
    const transformedProduct = transformProductFromDb(updatedProduct)

    return NextResponse.json({
      success: true,
      data: transformedProduct
    })
  } catch (error) {
    console.error('Error updating product:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An error occurred while updating the product'
        }
      },
      { status: 500 }
    )
  }
}

// DELETE /api/e-commerce/products/id/[id] - Delete a product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_PARAMS',
            message: 'Product ID is required'
          }
        },
        { status: 400 }
      )
    }

    // Check if product exists
    const existingProduct = await db.product.findUnique({
      where: { id }
    })

    if (!existingProduct) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'PRODUCT_NOT_FOUND',
            message: `Product with ID ${id} not found`
          }
        },
        { status: 404 }
      )
    }

    // Delete the product
    await db.product.delete({
      where: { id }
    })

    return NextResponse.json({
      success: true,
      data: { success: true }
    })
  } catch (error) {
    console.error('Error deleting product:', error)
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'An error occurred while deleting the product'
        }
      },
      { status: 500 }
    )
  }
}