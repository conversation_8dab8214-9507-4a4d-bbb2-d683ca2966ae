// Product Analytics API Route
// GET /api/e-commerce/analytics/products - Get product analytics data

import { NextRequest, NextResponse } from 'next/server'
import { handleEcommerceError } from '@/lib/ecommerce'
import { prisma } from '@/lib/ecommerce/config/database'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const timeRange = searchParams.get('timeRange') || '30d'

    // Calculate date range
    const now = new Date()
    let startDate: Date

    switch (timeRange) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    // Get basic product counts
    const [
      totalProducts,
      activeProducts,
      draftProducts,
      archivedProducts,
      lowStockProducts,
      outOfStockProducts
    ] = await Promise.all([
      prisma.product.count(),
      prisma.product.count({ where: { status: 'active' } }),
      prisma.product.count({ where: { status: 'draft' } }),
      prisma.product.count({ where: { status: 'archived' } }),
      prisma.product.count({ 
        where: { 
          trackQuantity: true,
          inventoryQuantity: { gt: 0, lte: 5 }
        } 
      }),
      prisma.product.count({ 
        where: { 
          trackQuantity: true,
          inventoryQuantity: { lte: 0 }
        } 
      })
    ])

    // Calculate total value and average price
    const priceAggregation = await prisma.product.aggregate({
      _sum: { price: true },
      _avg: { price: true },
      where: { status: 'active' }
    })

    const totalValue = priceAggregation._sum.price || 0
    const averagePrice = priceAggregation._avg.price || 0

    // Get category breakdown
    const categoryBreakdown = await prisma.productCategory.findMany({
      include: {
        _count: {
          select: { products: true }
        }
      },
      orderBy: {
        products: {
          _count: 'desc'
        }
      },
      take: 10
    })

    const categoryData = categoryBreakdown.map(category => ({
      name: category.name,
      count: category._count.products,
      percentage: totalProducts > 0 ? Math.round((category._count.products / totalProducts) * 100) : 0
    }))

    // Get recent activity (products created/updated in time range)
    const recentProducts = await prisma.product.findMany({
      where: {
        OR: [
          { createdAt: { gte: startDate } },
          { updatedAt: { gte: startDate } }
        ]
      },
      select: {
        id: true,
        title: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: { updatedAt: 'desc' },
      take: 10
    })

    const recentActivity = recentProducts.map(product => {
      const isNew = product.createdAt >= startDate
      return {
        id: product.id,
        type: isNew ? 'created' : 'updated',
        productTitle: product.title,
        timestamp: isNew ? product.createdAt : product.updatedAt,
        details: isNew ? 'New product added' : 'Product information updated'
      }
    })

    // Get top selling products
    const topProductsAgg = await prisma.orderItem.groupBy({
      by: ['productId'],
      where: { order: { createdAt: { gte: startDate } } },
      _sum: { quantity: true },
      orderBy: { _sum: { quantity: 'desc' } },
      take: 5,
    });

    const productIds = topProductsAgg.map(p => p.productId);

    const [topProducts, orderItemsForRevenue] = await Promise.all([
      prisma.product.findMany({
        where: { id: { in: productIds } },
        include: { images: { take: 1, orderBy: { position: 'asc' } } },
      }),
      prisma.orderItem.findMany({
        where: { 
          productId: { in: productIds },
          order: { createdAt: { gte: startDate } }
        },
        include: { variant: true }
      })
    ]);

    const revenueByProduct = orderItemsForRevenue.reduce((acc: Record<string, number>, item) => {
      const price = item.variant?.price;
      if (price) {
        acc[item.productId] = (acc[item.productId] || 0) + item.quantity * price.toNumber();
      }
      return acc;
    }, {});

    const productMap = new Map(topProducts.map(p => [p.id, p]));

    const topSellingData = topProductsAgg.map(agg => {
      const product = productMap.get(agg.productId);
      return {
        id: agg.productId,
        title: product?.title || 'Unknown Product',
        sales: agg._sum.quantity || 0,
        revenue: revenueByProduct[agg.productId] || 0,
        image: product?.images[0]?.url,
      };
    }).sort((a, b) => b.sales - a.sales);

    // Performance Metrics
    const [reviewsAggregation, totalViews, orders] = await Promise.all([
      prisma.productReview.aggregate({
        _avg: { rating: true },
        _count: { id: true },
        where: { createdAt: { gte: startDate } },
      }),
      prisma.userActivity.count({
        where: {
          type: 'product_view',
          createdAt: { gte: startDate },
        },
      }),
      prisma.order.count({
        where: { createdAt: { gte: startDate } },
      })
    ]);
    const totalSales = orders;

    const conversionRate = totalViews > 0 ? (totalSales / totalViews) * 100 : 0;

    const performanceMetrics = {
      totalViews,
      conversionRate: parseFloat(conversionRate.toFixed(2)),
      averageRating: parseFloat((reviewsAggregation._avg.rating || 0).toFixed(1)),
      totalReviews: reviewsAggregation._count.id || 0,
    };

    const analyticsData = {
      totalProducts,
      activeProducts,
      draftProducts,
      archivedProducts,
      totalValue,
      averagePrice,
      lowStockProducts,
      outOfStockProducts,
      topSellingProducts: topSellingData,
      categoryBreakdown: categoryData,
      recentActivity,
      performanceMetrics
    }

    return NextResponse.json({
      success: true,
      data: analyticsData
    })
  } catch (error) {
    console.error('Product analytics API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
