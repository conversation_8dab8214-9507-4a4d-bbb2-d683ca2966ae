const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000/api/e-commerce/orders';

describe('Orders API', () => {
  it('should list orders (GET)', async () => {
    const res = await fetch(`${BASE_URL}`);
    const data = await res.json();
    expect(res.status).toBe(200);
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data.data)).toBe(true);
  });

  it('should list orders with pagination (GET)', async () => {
    const res = await fetch(`${BASE_URL}?page=1&limit=2`);
    const data = await res.json();
    expect(res.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.data.pagination.limit).toBe(2);
  });

  it('should fail to create order with missing fields (POST)', async () => {
    const res = await fetch(`${BASE_URL}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({})
    });
    const data = await res.json();
    expect(res.status).toBe(400);
    expect(data.success).toBe(false);
  });

  it('should create order with valid payload (POST)', async () => {
    const res = await fetch(`${BASE_URL}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        customerEmail: '<EMAIL>',
        shippingAddress: {
          firstName: 'Test',
          lastName: 'User',
          address1: '123 Main St',
          city: 'Cape Town',
          province: 'Western Cape',
          postalCode: '8000',
          country: 'ZA',
          phone: '0123456789'
        },
        items: [
          {
            productId: 'prod_1',
            quantity: 1,
            price: 100,
            name: 'Test Product'
          }
        ]
      })
    });
    const data = await res.json();
    expect([200, 201]).toContain(res.status);
    expect(data.success).toBe(true);
    expect(data.data).toBeDefined();
  });
});
