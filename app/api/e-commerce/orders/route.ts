// Production-Ready Orders API Routes
// GET /api/e-commerce/orders - Search and list orders
// POST /api/e-commerce/orders - Create a new order

import { NextRequest, NextResponse } from 'next/server'
import { OrderService } from '@/lib/ecommerce/services/order-service'

const orderService = new OrderService()

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)

    // Parse query parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const paymentStatus = searchParams.get('paymentStatus')

    // Build filters for the service
    const filters = {
      page,
      limit,
      ...(status && { status }),
      ...(paymentStatus && { paymentStatus })
    }

    // Get orders and total count using the service
    const { orders, total } = await orderService.getAllOrders(filters)

    return NextResponse.json({
      success: true,
      data: orders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    })
  } catch (error) {
    console.error('Orders API error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch orders' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.customerEmail || !body.shippingAddress || !body.items?.length) {
      return NextResponse.json(
        { success: false, error: 'Customer email, shipping address, and items are required' },
        { status: 400 }
      )
    }

    // Transform the request data to match the OrderService interface
    const orderRequest = {
      customerId: body.userId,
      customerEmail: body.customerEmail,
      customerPhone: body.customerPhone,
      items: body.items.map((item: any) => ({
        productId: item.productId,
        variantId: item.variantId,
        quantity: item.quantity,
        price: item.price || 0, // Price should be provided or fetched from product
        name: item.name || item.productTitle || 'Product',
        image: item.image,
        color: item.color,
        size: item.size
      })),
      shippingAddress: {
        firstName: body.shippingAddress.firstName || body.customerFirstName || '',
        lastName: body.shippingAddress.lastName || body.customerLastName || '',
        company: body.shippingAddress.company,
        address1: body.shippingAddress.address1 || body.shippingAddress.line1 || '',
        address2: body.shippingAddress.address2 || body.shippingAddress.line2,
        city: body.shippingAddress.city || '',
        province: body.shippingAddress.province || body.shippingAddress.state || '',
        postalCode: body.shippingAddress.postalCode || body.shippingAddress.zip || '',
        country: body.shippingAddress.country || 'ZA',
        phone: body.shippingAddress.phone || body.customerPhone
      },
      billingAddress: body.billingAddress ? {
        firstName: body.billingAddress.firstName || body.customerFirstName || '',
        lastName: body.billingAddress.lastName || body.customerLastName || '',
        company: body.billingAddress.company,
        address1: body.billingAddress.address1 || body.billingAddress.line1 || '',
        address2: body.billingAddress.address2 || body.billingAddress.line2,
        city: body.billingAddress.city || '',
        province: body.billingAddress.province || body.billingAddress.state || '',
        postalCode: body.billingAddress.postalCode || body.billingAddress.zip || '',
        country: body.billingAddress.country || 'ZA',
        phone: body.billingAddress.phone || body.customerPhone
      } : undefined,
      shippingMethod: body.shippingMethod || 'standard',
      paymentMethod: body.paymentMethod || 'card',
      couponCode: body.couponCode,
      notes: body.customerNote
    }

    // Create order using the service
    const order = await orderService.createOrder(orderRequest)

    return NextResponse.json({
      success: true,
      data: order
    }, { status: 201 })

  } catch (error) {
    console.error('Create order API error:', error)
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'Failed to create order' },
      { status: 500 }
    )
  }
}
