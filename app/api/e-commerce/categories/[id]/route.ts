// Individual Category API Routes
// GET /api/e-commerce/categories/[id] - Get category by ID
// PUT /api/e-commerce/categories/[id] - Update category
// DELETE /api/e-commerce/categories/[id] - Delete category

import { NextRequest, NextResponse } from 'next/server'
import { handleEcommerceError } from '@/lib/ecommerce'
import { prisma } from '@/lib/ecommerce/config/database'

interface RouteParams {
  params: {
    id: string
  }
}

// Helper function to check if a category is a descendant of another
async function checkIfDescendant(categoryId: string, potentialAncestorId: string): Promise<boolean> {
  const category = await prisma.productCategory.findUnique({
    where: { id: potentialAncestorId },
    select: { parentId: true }
  })

  if (!category || !category.parentId) {
    return false
  }

  if (category.parentId === categoryId) {
    return true
  }

  return checkIfDescendant(categoryId, category.parentId)
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params

    const category = await prisma.productCategory.findUnique({
      where: { id },
      include: {
        parent: true,
        children: {
          include: {
            _count: {
              select: { products: true }
            }
          }
        },
        _count: {
          select: { products: true }
        }
      }
    })

    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        ...category,
        productCount: category._count.products,
        children: category.children.map(child => ({
          ...child,
          productCount: child._count.products
        }))
      }
    })
  } catch (error) {
    console.error('Get category API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    const body = await request.json()

    // Check if category exists
    const existingCategory = await prisma.productCategory.findUnique({
      where: { id }
    })

    if (!existingCategory) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      )
    }

    // Generate slug if name is being updated
    let slug = body.slug
    if (body.name && !slug) {
      slug = body.name.toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')
    }

    // Check if slug already exists (excluding current category)
    if (slug) {
      const slugExists = await prisma.productCategory.findFirst({
        where: {
          slug,
          id: { not: id }
        }
      })

      if (slugExists) {
        return NextResponse.json(
          { success: false, error: `Category with slug '${slug}' already exists` },
          { status: 400 }
        )
      }
    }

    // Validate parent category if provided
    if (body.parentId) {
      // Check if parent category exists
      const parentCategory = await prisma.productCategory.findUnique({
        where: { id: body.parentId }
      })

      if (!parentCategory) {
        return NextResponse.json(
          { success: false, error: `Parent category with ID '${body.parentId}' not found` },
          { status: 400 }
        )
      }

      // Prevent circular reference (category cannot be its own parent or descendant)
      if (body.parentId === id) {
        return NextResponse.json(
          { success: false, error: 'Category cannot be its own parent' },
          { status: 400 }
        )
      }

      // Check if the new parent is a descendant of this category
      const isDescendant = await checkIfDescendant(id, body.parentId)
      if (isDescendant) {
        return NextResponse.json(
          { success: false, error: 'Cannot set a descendant category as parent (would create circular reference)' },
          { status: 400 }
        )
      }
    }

    // Update category
    const category = await prisma.productCategory.update({
      where: { id },
      data: {
        name: body.name,
        slug: slug || existingCategory.slug,
        description: body.description,
        image: body.image,
        parentId: body.parentId || null,
        position: body.position,
        isVisible: body.isVisible,
        seoTitle: body.seoTitle,
        seoDescription: body.seoDescription
      },
      include: {
        _count: {
          select: { products: true }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        ...category,
        productCount: category._count.products
      }
    })
  } catch (error) {
    console.error('Update category API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params

    // Check if category exists
    const category = await prisma.productCategory.findUnique({
      where: { id },
      include: {
        children: true,
        _count: {
          select: { products: true }
        }
      }
    })

    if (!category) {
      return NextResponse.json(
        { success: false, error: 'Category not found' },
        { status: 404 }
      )
    }

    // Check if category has children
    if (category.children.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete category with subcategories. Please delete or move subcategories first.' },
        { status: 400 }
      )
    }

    // Check if category has products
    if (category._count.products > 0) {
      return NextResponse.json(
        { success: false, error: 'Cannot delete category with products. Please move or remove products first.' },
        { status: 400 }
      )
    }

    // Delete category
    await prisma.productCategory.delete({
      where: { id }
    })

    return NextResponse.json({
      success: true,
      data: { id }
    })
  } catch (error) {
    console.error('Delete category API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}
