import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { z } from 'zod'
import { NextJSLayout } from '@/lib/layout-builder/types/nextjs-types'

// Validation schema
const registerLayoutSchema = z.object({
  layout: z.object({
    id: z.string(),
    name: z.string(),
    nextjs: z.object({
      type: z.enum(['root', 'nested', 'template', 'group']),
      route: z.string(),
      metadata: z.any().optional(),
      appRouterFeatures: z.array(z.string())
    }),
    structure: z.record(z.any()),
    styling: z.record(z.any())
  })
})

// In-memory registry for demonstration
// In production, this would be stored in a database or cache
const layoutRegistry = new Map<string, RegisteredLayout>()

interface RegisteredLayout {
  id: string
  name: string
  route: string
  type: string
  structure: any
  styling: any
  features: string[]
  registeredAt: Date
  isActive: boolean
}

// POST /api/universal-renderer/register-layout - Register layout with Universal Renderer
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = registerLayoutSchema.parse(body)
    const { layout } = validatedData

    // Validate layout for Universal Renderer compatibility
    const validationResult = validateLayoutForUniversalRenderer(layout)
    if (!validationResult.isValid) {
      return NextResponse.json(
        { 
          error: 'Layout is not compatible with Universal Renderer',
          issues: validationResult.issues
        },
        { status: 400 }
      )
    }

    // Register the layout
    const registeredLayout: RegisteredLayout = {
      id: layout.id,
      name: layout.name,
      route: layout.nextjs.route,
      type: layout.nextjs.type,
      structure: layout.structure,
      styling: layout.styling,
      features: layout.nextjs.appRouterFeatures,
      registeredAt: new Date(),
      isActive: true
    }

    layoutRegistry.set(layout.id, registeredLayout)

    // TODO: In production, also register with actual Universal Renderer service
    // await registerWithUniversalRendererService(registeredLayout)

    return NextResponse.json({
      message: 'Layout registered successfully with Universal Renderer',
      layout: registeredLayout,
      registrySize: layoutRegistry.size
    })

  } catch (error) {
    console.error('Error registering layout:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to register layout' },
      { status: 500 }
    )
  }
}

// GET /api/universal-renderer/register-layout - Get registered layouts
export async function GET(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const isActive = searchParams.get('isActive')

    let layouts = Array.from(layoutRegistry.values())

    // Filter by type
    if (type) {
      layouts = layouts.filter(layout => layout.type === type)
    }

    // Filter by active status
    if (isActive !== null) {
      layouts = layouts.filter(layout => layout.isActive === (isActive === 'true'))
    }

    return NextResponse.json({
      layouts,
      total: layouts.length,
      registrySize: layoutRegistry.size
    })

  } catch (error) {
    console.error('Error fetching registered layouts:', error)
    return NextResponse.json(
      { error: 'Failed to fetch registered layouts' },
      { status: 500 }
    )
  }
}

// DELETE /api/universal-renderer/register-layout - Unregister layout
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const layoutId = searchParams.get('layoutId')

    if (!layoutId) {
      return NextResponse.json(
        { error: 'Layout ID is required' },
        { status: 400 }
      )
    }

    const layout = layoutRegistry.get(layoutId)
    if (!layout) {
      return NextResponse.json(
        { error: 'Layout not found in registry' },
        { status: 404 }
      )
    }

    // Remove from registry
    layoutRegistry.delete(layoutId)

    // TODO: In production, also unregister from actual Universal Renderer service
    // await unregisterFromUniversalRendererService(layoutId)

    return NextResponse.json({
      message: 'Layout unregistered successfully',
      layoutId,
      registrySize: layoutRegistry.size
    })

  } catch (error) {
    console.error('Error unregistering layout:', error)
    return NextResponse.json(
      { error: 'Failed to unregister layout' },
      { status: 500 }
    )
  }
}

// Validate layout for Universal Renderer compatibility
function validateLayoutForUniversalRenderer(layout: NextJSLayout): {
  isValid: boolean
  issues: string[]
} {
  const issues: string[] = []

  // Check required structure
  if (!layout.structure || Object.keys(layout.structure).length === 0) {
    issues.push('Layout must have at least one section')
  }

  // Check for main section
  const hasMainSection = Object.values(layout.structure).some(
    (section: any) => section?.type === 'main'
  )
  if (!hasMainSection) {
    issues.push('Layout must have a main section for content rendering')
  }

  // Check NextJS configuration
  if (!layout.nextjs.route) {
    issues.push('Layout must have a valid route defined')
  }

  // Check for valid NextJS type
  const validTypes = ['root', 'nested', 'template', 'group']
  if (!validTypes.includes(layout.nextjs.type)) {
    issues.push('Layout must have a valid NextJS type')
  }

  // Check for required App Router features for certain types
  if (layout.nextjs.type === 'root' && !layout.nextjs.appRouterFeatures.includes('metadata')) {
    issues.push('Root layouts should include metadata feature')
  }

  // Validate sections
  Object.entries(layout.structure).forEach(([key, section]: [string, any]) => {
    if (section) {
      // Check section structure
      if (!section.type || !section.name) {
        issues.push(`Section ${key} is missing required type or name`)
      }

      // Check blocks
      if (section.blocks) {
        section.blocks.forEach((block: any, index: number) => {
          if (!block.type || !block.name) {
            issues.push(`Block ${index} in section ${key} is missing required type or name`)
          }
        })
      }
    }
  })

  // Check styling compatibility
  if (layout.styling) {
    // Validate color scheme
    if (layout.styling.colorScheme && !['light', 'dark', 'auto'].includes(layout.styling.colorScheme)) {
      issues.push('Invalid color scheme. Must be light, dark, or auto')
    }
  }

  return {
    isValid: issues.length === 0,
    issues
  }
}

// TODO: Implement actual Universal Renderer service integration
async function registerWithUniversalRendererService(layout: RegisteredLayout): Promise<void> {
  // This would make an API call to the actual Universal Renderer service
  // For now, we'll just log it
  console.log('Registering layout with Universal Renderer service:', layout.id)
}

async function unregisterFromUniversalRendererService(layoutId: string): Promise<void> {
  // This would make an API call to the actual Universal Renderer service
  // For now, we'll just log it
  console.log('Unregistering layout from Universal Renderer service:', layoutId)
}