import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/lib/auth'
import { z } from 'zod'
import { NextJSLayout, NextJSCodeGenerationResult } from '@/lib/layout-builder/types/nextjs-types'

// Validation schema
const generateCodeSchema = z.object({
  layout: z.object({
    id: z.string(),
    name: z.string(),
    nextjs: z.object({
      type: z.enum(['root', 'nested', 'template', 'group']),
      route: z.string(),
      metadata: z.any().optional(),
      imports: z.array(z.string()),
      exports: z.array(z.any()),
      appRouterFeatures: z.array(z.string())
    }),
    structure: z.record(z.any()),
    styling: z.record(z.any())
  }),
  exportFormat: z.enum(['typescript', 'javascript']).default('typescript'),
  includeTypes: z.boolean().default(true),
  includeStyles: z.boolean().default(true)
})

// POST /api/nextjs-layout-generator - Generate NextJS code from layout
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = generateCodeSchema.parse(body)
    
    const { layout, exportFormat, includeTypes, includeStyles } = validatedData

    // Generate the code
    const result = await generateNextJSLayoutCode(
      layout as NextJSLayout,
      exportFormat,
      includeTypes,
      includeStyles
    )

    return NextResponse.json(result)

  } catch (error) {
    console.error('Error generating NextJS code:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Failed to generate NextJS code' },
      { status: 500 }
    )
  }
}

// Main code generation function
async function generateNextJSLayoutCode(
  layout: NextJSLayout,
  exportFormat: 'typescript' | 'javascript',
  includeTypes: boolean,
  includeStyles: boolean
): Promise<NextJSCodeGenerationResult> {
  const isTypeScript = exportFormat === 'typescript'
  const fileExtension = isTypeScript ? 'tsx' : 'jsx'
  
  // Generate main layout file
  const layoutCode = generateLayoutFile(layout, isTypeScript)
  
  // Generate component files
  const components = generateComponentFiles(layout, isTypeScript)
  
  // Generate types file (if TypeScript and includeTypes)
  const types = (isTypeScript && includeTypes) ? generateTypesFile(layout) : undefined
  
  // Generate styles file (if includeStyles)
  const styles = includeStyles ? generateStylesFile(layout) : undefined

  return {
    layout: {
      path: layout.nextjs.route,
      code: layoutCode
    },
    components,
    types,
    styles
  }
}

// Generate the main layout file
function generateLayoutFile(layout: NextJSLayout, isTypeScript: boolean): string {
  const { nextjs, structure, styling } = layout
  const fileExtension = isTypeScript ? 'tsx' : 'jsx'
  
  // Generate imports
  const imports = [
    ...nextjs.imports,
    "import './globals.css'",
    ...generateComponentImports(structure)
  ]

  // Generate metadata (if App Router)
  const metadataCode = nextjs.metadata ? generateMetadata(nextjs.metadata, isTypeScript) : ''

  // Generate layout component
  const layoutComponent = generateLayoutComponent(layout, isTypeScript)

  return `${imports.join('\n')}

${metadataCode}

${layoutComponent}`
}

// Generate component imports based on structure
function generateComponentImports(structure: any): string[] {
  const imports: string[] = []
  const components = new Set<string>()

  // Extract unique component types from structure
  Object.values(structure).forEach((section: any) => {
    if (section?.blocks) {
      section.blocks.forEach((block: any) => {
        const componentName = getComponentName(block.type)
        components.add(componentName)
      })
    }
  })

  // Generate import statements
  components.forEach(component => {
    imports.push(`import { ${component} } from '@/components/layout/${component}'`)
  })

  return imports
}

// Generate metadata export
function generateMetadata(metadata: any, isTypeScript: boolean): string {
  if (!isTypeScript) {
    return `export const metadata = ${JSON.stringify(metadata, null, 2)}`
  }

  return `import type { Metadata } from 'next'

export const metadata${isTypeScript ? ': Metadata' : ''} = ${JSON.stringify(metadata, null, 2)}`
}

// Generate the main layout component
function generateLayoutComponent(layout: NextJSLayout, isTypeScript: boolean): string {
  const { structure, nextjs } = layout
  const sections = Object.entries(structure).filter(([_, section]) => section !== undefined)
  
  // Generate props interface (TypeScript only)
  const propsInterface = isTypeScript ? `
interface RootLayoutProps {
  children: React.ReactNode
}` : ''

  // Generate component body
  const componentBody = generateComponentBody(sections, layout)

  // Generate the component
  return `${propsInterface}

export default function RootLayout(${isTypeScript ? '{ children }: RootLayoutProps' : '{ children }'}) {
  return (
    <html lang="en">
      <body>
        ${componentBody}
      </body>
    </html>
  )
}`
}

// Generate component body with sections
function generateComponentBody(sections: [string, any][], layout: NextJSLayout): string {
  const sectionElements = sections.map(([key, section]) => {
    if (!section || !section.isVisible) return ''
    
    const sectionTag = getSectionTag(section.type)
    const sectionClasses = generateSectionClasses(section)
    const sectionStyles = generateSectionStyles(section)
    
    const blocks = section.blocks
      .filter((block: any) => block.isVisible)
      .map((block: any) => generateBlockElement(block))
      .join('\n        ')

    return `      <${sectionTag} className="${sectionClasses}"${sectionStyles ? ` style={${sectionStyles}}` : ''}>
        ${blocks}
      </${sectionTag}>`
  }).filter(Boolean).join('\n')

  // Add children placeholder for main content
  const childrenPlacement = sections.find(([key, section]) => section?.type === 'main')
    ? '        {children}'
    : ''

  return `${sectionElements}
${childrenPlacement}`
}

// Generate individual block element
function generateBlockElement(block: any): string {
  const componentName = getComponentName(block.type)
  const blockProps = generateBlockProps(block)
  
  return `<${componentName}${blockProps} />`
}

// Generate block props
function generateBlockProps(block: any): string {
  const props: string[] = []
  
  if (block.content) {
    Object.entries(block.content).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (typeof value === 'string') {
          props.push(`${key}="${value}"`)
        } else {
          props.push(`${key}={${JSON.stringify(value)}}`)
        }
      }
    })
  }

  if (block.styling) {
    props.push(`styling={${JSON.stringify(block.styling)}}`)
  }

  return props.length > 0 ? ` ${props.join(' ')}` : ''
}

// Generate component files
function generateComponentFiles(layout: NextJSLayout, isTypeScript: boolean): Array<{
  name: string
  path: string
  code: string
}> {
  const components: Array<{ name: string; path: string; code: string }> = []
  const componentTypes = new Set<string>()

  // Extract unique component types
  Object.values(layout.structure).forEach((section: any) => {
    if (section?.blocks) {
      section.blocks.forEach((block: any) => {
        componentTypes.add(block.type)
      })
    }
  })

  // Generate component files
  componentTypes.forEach(type => {
    const componentName = getComponentName(type)
    const componentCode = generateComponentCode(type, isTypeScript)
    
    components.push({
      name: componentName,
      path: `components/layout/${componentName}.${isTypeScript ? 'tsx' : 'jsx'}`,
      code: componentCode
    })
  })

  return components
}

// Generate individual component code
function generateComponentCode(blockType: string, isTypeScript: boolean): string {
  const componentName = getComponentName(blockType)
  const propsInterface = isTypeScript ? generateComponentPropsInterface(blockType) : ''
  const componentBody = generateComponentImplementation(blockType)

  return `${isTypeScript ? "import React from 'react'" : "import React from 'react'"}

${propsInterface}

export function ${componentName}(${isTypeScript ? `props: ${componentName}Props` : 'props'}) {
  ${componentBody}
}`
}

// Generate component props interface
function generateComponentPropsInterface(blockType: string): string {
  const componentName = getComponentName(blockType)
  
  const baseProps = `
interface ${componentName}Props {
  className?: string
  styling?: any
}`

  // Add specific props based on block type
  switch (blockType) {
    case 'logo':
      return `${baseProps.slice(0, -1)}
  image?: string
  text?: string
  links?: Array<{ url: string; text: string; target: string }>
}`

    case 'navigation':
      return `${baseProps.slice(0, -1)}
  menu?: string
}`

    case 'search':
      return `${baseProps.slice(0, -1)}
  text?: string
  placeholder?: string
}`

    case 'content':
      return `${baseProps.slice(0, -1)}
  html?: string
  text?: string
}`

    default:
      return baseProps
  }
}

// Generate component implementation
function generateComponentImplementation(blockType: string): string {
  switch (blockType) {
    case 'logo':
      return `const { image, text = 'Logo', links, className, styling } = props
  const logoLink = links?.[0]?.url || '/'
  
  return (
    <a href={logoLink} className={className}>
      {image ? (
        <img src={image} alt={text} className="h-8 w-auto" />
      ) : (
        <span className="font-bold text-lg">{text}</span>
      )}
    </a>
  )`

    case 'navigation':
      return `const { menu, className, styling } = props
  
  return (
    <nav className={className}>
      {/* Navigation implementation */}
      <div>Navigation Menu</div>
    </nav>
  )`

    case 'search':
      return `const { text = 'Search...', className, styling } = props
  
  return (
    <div className={className}>
      <input
        type="search"
        placeholder={text}
        className="px-4 py-2 border rounded-md"
      />
    </div>
  )`

    case 'content':
      return `const { html, text, className, styling } = props
  
  if (html) {
    return (
      <div 
        className={className}
        dangerouslySetInnerHTML={{ __html: html }}
      />
    )
  }
  
  return (
    <div className={className}>
      {text && <p>{text}</p>}
    </div>
  )`

    default:
      return `const { className, styling } = props
  
  return (
    <div className={className}>
      {/* ${blockType} implementation */}
    </div>
  )`
  }
}

// Generate types file
function generateTypesFile(layout: NextJSLayout): { path: string; code: string } {
  return {
    path: 'types/layout.ts',
    code: `// Generated layout types

export interface LayoutProps {
  children: React.ReactNode
}

export interface BlockStyling {
  background?: any
  border?: any
  spacing?: any
  typography?: any
  colors?: any
  shadow?: any
}

export interface SectionStyling {
  background?: any
  border?: any
  spacing?: any
  shadow?: any
}

// Add more specific types based on your layout structure
`
  }
}

// Generate styles file
function generateStylesFile(layout: NextJSLayout): { path: string; code: string } {
  const { styling } = layout
  
  return {
    path: 'styles/layout.css',
    code: `/* Generated layout styles */

.layout-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.layout-header {
  /* Header styles */
}

.layout-main {
  flex: 1;
  /* Main content styles */
}

.layout-sidebar {
  /* Sidebar styles */
}

.layout-footer {
  /* Footer styles */
}

/* Add more styles based on your layout configuration */
`
  }
}

// Utility functions
function getComponentName(blockType: string): string {
  return blockType
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('') + 'Block'
}

function getSectionTag(sectionType: string): string {
  switch (sectionType) {
    case 'header': return 'header'
    case 'main': return 'main'
    case 'sidebar': return 'aside'
    case 'footer': return 'footer'
    default: return 'section'
  }
}

function generateSectionClasses(section: any): string {
  const classes: string[] = [`layout-${section.type}`]
  
  if (section.configuration?.alignment) {
    classes.push(`text-${section.configuration.alignment}`)
  }
  
  return classes.join(' ')
}

function generateSectionStyles(section: any): string | null {
  const styles: any = {}
  
  if (section.styling?.background?.color) {
    styles.backgroundColor = section.styling.background.color
  }
  
  if (section.styling?.spacing) {
    const { top, right, bottom, left } = section.styling.spacing
    styles.padding = `${top} ${right} ${bottom} ${left}`
  }
  
  return Object.keys(styles).length > 0 ? JSON.stringify(styles) : null
}