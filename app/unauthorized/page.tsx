import { <PERSON><PERSON><PERSON> } from "next"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ShieldAlert } from "lucide-react"

export const metadata: Metadata = {
  title: "Unauthorized | Coco Milk Store",
  description: "You don't have permission to access this page",
}

export default function UnauthorizedPage() {
  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full max-w-md flex-col items-center justify-center space-y-6 text-center">
        <ShieldAlert className="h-16 w-16 text-destructive" />
        
        <h1 className="text-3xl font-bold tracking-tight">Access Denied</h1>
        
        <p className="text-muted-foreground">
          You don't have permission to access this page. If you believe this is an error, please contact support.
        </p>
        
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-x-2 sm:space-y-0">
          <Button asChild>
            <Link href="/">Go to Homepage</Link>
          </Button>
          
          <Button variant="outline" asChild>
            <Link href="/account">Go to Account</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}