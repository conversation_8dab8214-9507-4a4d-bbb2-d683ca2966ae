'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRealTimeNotifications, useBrowserNotificationPermission } from '@/lib/notifications/hooks';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, 
  CheckCheck, 
  RefreshCw, 
  Settings, 
  Volume2, 
  VolumeX,
  BellOff,
  BellRing,
  ExternalLink,
  Trash2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDistanceToNow } from 'date-fns';
import { NotificationPriority } from '@/lib/notifications/types';

export default function NotificationsPage() {
  const { data: session } = useSession();
  const userId = session?.user?.id;
  const [notificationPreferences, setNotificationPreferences] = useState({
    enableBrowserNotifications: true,
    enableSound: true,
    orderUpdates: true,
    promotions: false,
    newArrivals: false,
    smsNotifications: false,
    accountUpdates: true
  });
  
  const {
    permission,
    requesting,
    requestPermission,
    isSupported
  } = useBrowserNotificationPermission();
  
  const {
    notifications,
    unreadCount,
    connected,
    error,
    markAsRead,
    markAllAsRead,
    refresh
  } = useRealTimeNotifications({
    userId,
    enabled: true,
    enableBrowserNotifications: notificationPreferences.enableBrowserNotifications,
    enableSound: notificationPreferences.enableSound
  });

  // Get priority badge
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case NotificationPriority.URGENT:
        return <Badge variant="destructive">Urgent</Badge>;
      case NotificationPriority.HIGH:
        return <Badge variant="secondary" className="bg-orange-100 text-orange-800">High</Badge>;
      case NotificationPriority.NORMAL:
        return <Badge variant="outline">Normal</Badge>;
      case NotificationPriority.LOW:
        return <Badge variant="outline" className="text-gray-500">Low</Badge>;
      default:
        return null;
    }
  };

  // Handle notification click
  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {
    markAsRead(notificationId);
    
    if (actionUrl) {
      if (actionUrl.startsWith('http')) {
        window.open(actionUrl, '_blank');
      } else {
        window.location.href = actionUrl;
      }
    }
  };

  // Toggle browser notifications
  const toggleBrowserNotifications = async () => {
    if (!notificationPreferences.enableBrowserNotifications) {
      // If enabling, request permission first
      if (permission !== 'granted' && isSupported) {
        const result = await requestPermission();
        if (result !== 'granted') {
          return; // Don't enable if permission not granted
        }
      }
    }
    
    setNotificationPreferences(prev => ({
      ...prev,
      enableBrowserNotifications: !prev.enableBrowserNotifications
    }));
  };

  // Toggle sound notifications
  const toggleSoundNotifications = () => {
    setNotificationPreferences(prev => ({
      ...prev,
      enableSound: !prev.enableSound
    }));
  };

  // Save notification preferences
  const savePreferences = async () => {
    // This would typically save to the server
    console.log('Saving preferences:', notificationPreferences);
    // Show success message
    alert('Notification preferences saved successfully!');
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Heading 
          title="Notifications" 
          description="Manage your notifications and preferences" 
        />
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={refresh}
            className="flex items-center gap-1"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
          {unreadCount > 0 && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => markAllAsRead()}
              className="flex items-center gap-1"
            >
              <CheckCheck className="h-4 w-4" />
              Mark All Read
            </Button>
          )}
        </div>
      </div>
      <Separator />
      
      <Tabs defaultValue="notifications">
        <TabsList className="mb-4">
          <TabsTrigger value="notifications">
            Notifications
            {unreadCount > 0 && (
              <Badge variant="destructive" className="ml-2">
                {unreadCount}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>
        
        <TabsContent value="notifications">
          <Card>
            <CardHeader>
              <CardTitle>Your Notifications</CardTitle>
              <CardDescription>
                {connected ? 'Real-time notifications are enabled' : 'Connecting to notification service...'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {error ? (
                <div className="p-4 text-center text-red-500">
                  Failed to load notifications. Please try again.
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-8 text-center">
                  <Bell className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">No notifications yet</h3>
                  <p className="text-muted-foreground">
                    You'll see your notifications here when you receive them.
                  </p>
                </div>
              ) : (
                <ScrollArea className="h-[500px] pr-4">
                  <div className="space-y-4">
                    {notifications.map((notification) => {
                      const isUnread = !notification.readAt;
                      const actionUrl = notification.metadata?.actionUrl;
                      
                      return (
                        <div 
                          key={notification.id}
                          className={cn(
                            "flex items-start space-x-4 p-4 border rounded-md cursor-pointer hover:bg-accent/50 transition-colors",
                            isUnread && "bg-blue-50 hover:bg-blue-100"
                          )}
                          onClick={() => handleNotificationClick(notification.id, actionUrl)}
                        >
                          <div className="mt-1">
                            <Bell className="h-5 w-5" />
                          </div>
                          <div className="flex-1 space-y-1">
                            <div className="flex items-center justify-between">
                              <h4 className="font-medium">
                                {notification.title}
                              </h4>
                              <div className="flex items-center space-x-2">
                                {getPriorityBadge(notification.priority)}
                              </div>
                            </div>
                            <p className="text-sm text-muted-foreground">
                              {notification.content}
                            </p>
                            <div className="flex items-center justify-between">
                              <p className="text-xs text-muted-foreground">
                                {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                              </p>
                              {actionUrl && (
                                <div className="flex items-center text-xs text-blue-600">
                                  <ExternalLink className="h-3 w-3 mr-1" />
                                  View details
                                </div>
                              )}
                            </div>
                          </div>
                          {isUnread && (
                            <div className="w-2 h-2 bg-blue-600 rounded-full mt-2" />
                          )}
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="preferences">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>Configure how you receive notifications</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {notificationPreferences.enableBrowserNotifications ? (
                      <BellRing className="h-5 w-5 text-primary" />
                    ) : (
                      <BellOff className="h-5 w-5 text-muted-foreground" />
                    )}
                    <div>
                      <div className="font-medium">Browser Notifications</div>
                      <div className="text-sm text-muted-foreground">
                        Show notifications in your browser
                      </div>
                    </div>
                  </div>
                  <Switch 
                    checked={notificationPreferences.enableBrowserNotifications}
                    onCheckedChange={toggleBrowserNotifications}
                    disabled={!isSupported}
                  />
                </div>
                
                {permission === 'denied' && (
                  <div className="text-sm text-amber-600 bg-amber-50 p-2 rounded">
                    Notifications are blocked by your browser. Please update your browser settings to enable notifications.
                  </div>
                )}
                
                <Separator />
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {notificationPreferences.enableSound ? (
                      <Volume2 className="h-5 w-5 text-primary" />
                    ) : (
                      <VolumeX className="h-5 w-5 text-muted-foreground" />
                    )}
                    <div>
                      <div className="font-medium">Sound Notifications</div>
                      <div className="text-sm text-muted-foreground">
                        Play a sound when you receive a notification
                      </div>
                    </div>
                  </div>
                  <Switch 
                    checked={notificationPreferences.enableSound}
                    onCheckedChange={toggleSoundNotifications}
                  />
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Notification Channels</CardTitle>
                <CardDescription>Choose which notifications you want to receive</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Order Updates</div>
                    <div className="text-sm text-muted-foreground">
                      Receive notifications about your order status
                    </div>
                  </div>
                  <Switch 
                    checked={notificationPreferences.orderUpdates}
                    onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                      ...prev, orderUpdates: checked
                    }))}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Promotions and Sales</div>
                    <div className="text-sm text-muted-foreground">
                      Receive notifications about promotions and sales
                    </div>
                  </div>
                  <Switch 
                    checked={notificationPreferences.promotions}
                    onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                      ...prev, promotions: checked
                    }))}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">New Arrivals</div>
                    <div className="text-sm text-muted-foreground">
                      Receive notifications about new product arrivals
                    </div>
                  </div>
                  <Switch 
                    checked={notificationPreferences.newArrivals}
                    onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                      ...prev, newArrivals: checked
                    }))}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">SMS Notifications</div>
                    <div className="text-sm text-muted-foreground">
                      Receive order updates via text message
                    </div>
                  </div>
                  <Switch 
                    checked={notificationPreferences.smsNotifications}
                    onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                      ...prev, smsNotifications: checked
                    }))}
                  />
                </div>
                <Separator />
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Account Updates</div>
                    <div className="text-sm text-muted-foreground">
                      Receive notifications about your account updates
                    </div>
                  </div>
                  <Switch 
                    checked={notificationPreferences.accountUpdates}
                    onCheckedChange={(checked) => setNotificationPreferences(prev => ({
                      ...prev, accountUpdates: checked
                    }))}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button onClick={savePreferences}>Save Preferences</Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}