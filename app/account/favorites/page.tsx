"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { useAuth } from "@/components/auth-provider"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Heart,
  Search,
  ShoppingCart,
  Trash2,
  Eye,
  ArrowRight
} from "lucide-react"
import { toast } from "sonner"

interface Product {
  id: number
  name: string
  price: string
  image: string
  category: string
  inStock: boolean
}

export default function FavoritesPage() {
  const { user } = useAuth()
  const [favorites, setFavorites] = useState<Product[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")

  useEffect(() => {
    // This would be replaced with an actual API call in production
    const fetchFavorites = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Mock data
        const mockFavorites = [
          {
            id: 1,
            name: "Organic Cotton Onesie",
            price: "$24.99",
            image: "/products/product-1.jpg",
            category: "Baby",
            inStock: true
          },
          {
            id: 2,
            name: "Dinosaur Print T-Shirt",
            price: "$19.50",
            image: "/products/product-2.jpg",
            category: "Toddler",
            inStock: true
          },
          {
            id: 3,
            name: "Floral Summer Dress",
            price: "$34.99",
            image: "/products/product-3.jpg",
            category: "Girls",
            inStock: false
          },
          {
            id: 4,
            name: "Striped Pajama Set",
            price: "$29.99",
            image: "/products/product-4.jpg",
            category: "Kids",
            inStock: true
          },
          {
            id: 5,
            name: "Knitted Baby Booties",
            price: "$14.99",
            image: "/products/product-5.jpg",
            category: "Baby",
            inStock: true
          },
          {
            id: 6,
            name: "Denim Overalls",
            price: "$39.99",
            image: "/products/product-6.jpg",
            category: "Toddler",
            inStock: true
          }
        ]
        
        setFavorites(mockFavorites)
      } catch (error) {
        console.error("Error fetching favorites:", error)
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchFavorites()
  }, [])

  const handleRemoveFromFavorites = (productId: number) => {
    setFavorites(prev => prev.filter(product => product.id !== productId))
    toast.success("Product removed from favorites")
  }

  const handleAddToCart = (productId: number) => {
    // This would be replaced with an actual API call in production
    toast.success("Product added to cart")
  }

  const filteredFavorites = favorites.filter(product => {
    if (!searchQuery) return true
    
    const query = searchQuery.toLowerCase()
    return (
      product.name.toLowerCase().includes(query) ||
      product.category.toLowerCase().includes(query) ||
      product.price.toLowerCase().includes(query)
    )
  })

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">My Favorites</h1>
        <p className="text-muted-foreground">
          Products you've saved for later
        </p>
      </div>

      <Card>
        <CardHeader>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <CardTitle>Saved Items</CardTitle>
              <CardDescription>
                Products you've added to your favorites
              </CardDescription>
            </div>
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search favorites..."
                className="w-full pl-8 sm:w-[300px]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-12">
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-pink-600"></div>
            </div>
          ) : filteredFavorites.length > 0 ? (
            <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
              {filteredFavorites.map((product) => (
                <div key={product.id} className="group relative overflow-hidden rounded-lg border">
                  <div className="aspect-square overflow-hidden">
                    <div className="relative h-full w-full">
                      {/* Placeholder for product image */}
                      <div className="h-full w-full bg-gray-100 flex items-center justify-center">
                        <span className="text-muted-foreground text-sm">Product Image</span>
                      </div>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-medium">{product.name}</h3>
                    <div className="mt-1 flex items-center justify-between">
                      <span className="font-medium">{product.price}</span>
                      <span className="text-sm text-muted-foreground">{product.category}</span>
                    </div>
                    <div className="mt-4 flex items-center justify-between gap-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="w-full"
                        onClick={() => handleAddToCart(product.id)}
                        disabled={!product.inStock}
                      >
                        <ShoppingCart className="mr-2 h-4 w-4" />
                        {product.inStock ? "Add to Cart" : "Out of Stock"}
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleRemoveFromFavorites(product.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Remove</span>
                      </Button>
                    </div>
                  </div>
                  <Link 
                    href={`/products/${product.id}`}
                    className="absolute inset-0 z-10"
                    aria-label={`View ${product.name}`}
                  >
                    <span className="sr-only">View product</span>
                  </Link>
                  <div className="absolute right-2 top-2 z-20">
                    <Button
                      variant="secondary"
                      size="icon"
                      className="rounded-full opacity-0 transition-opacity group-hover:opacity-100"
                      asChild
                    >
                      <Link href={`/products/${product.id}`}>
                        <Eye className="h-4 w-4" />
                        <span className="sr-only">View product</span>
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Heart className="mb-4 h-12 w-12 text-muted-foreground" />
              <h3 className="mb-2 text-xl font-medium">No favorites yet</h3>
              <p className="mb-6 text-sm text-muted-foreground">
                {searchQuery 
                  ? "No products match your search criteria." 
                  : "You haven't added any products to your favorites yet."}
              </p>
              <Button asChild>
                <Link href="/products">Browse Products</Link>
              </Button>
            </div>
          )}
        </CardContent>
        {filteredFavorites.length > 0 && (
          <CardFooter className="flex justify-between">
            <p className="text-sm text-muted-foreground">
              Showing {filteredFavorites.length} of {favorites.length} items
            </p>
            <Button variant="outline" asChild>
              <Link href="/products">
                Browse More Products
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardFooter>
        )}
      </Card>
    </div>
  )
}