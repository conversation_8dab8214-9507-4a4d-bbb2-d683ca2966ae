"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  User,
  Package,
  Heart,
  CreditCard,
  MapPin,
  Settings,
  LogOut,
  Gift,
  Truck,
  Clock,
  CheckCircle,
  AlertCircle,
  Edit,
  Plus,
  Trash,
  Loader2,
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"

// Import hooks for real data
import { useOrders } from '@/lib/ecommerce/hooks/use-orders'
import { useCustomer } from '@/lib/ecommerce/hooks/use-customer'
import { useAuth } from '@/lib/ecommerce/hooks/use-auth'
import NotificationPreferencesPanel from '@/components/notification-preferences-panel'

const mockPaymentMethods = [
  {
    id: "pm1",
    type: "visa",
    last4: "4242",
    expiry: "05/26",
    isDefault: true,
  },
  {
    id: "pm2",
    type: "mastercard",
    last4: "8888",
    expiry: "12/25",
    isDefault: false,
  },
]

const mockOrders = [
  {
    id: "CM12345",
    date: "May 20, 2025",
    items: 3,
    total: 89.99,
    status: "processing",
  },
  {
    id: "CM12344",
    date: "May 15, 2025",
    items: 2,
    total: 59.99,
    status: "shipped",
  },
  {
    id: "CM12343",
    date: "May 10, 2025",
    items: 1,
    total: 29.99,
    status: "delivered",
  },
  {
    id: "CM12342",
    date: "May 5, 2025",
    items: 4,
    total: 119.99,
    status: "delivered",
  },
]

export default function AccountDashboardPage() {
  const [activeTab, setActiveTab] = useState("overview")
  const [editingProfile, setEditingProfile] = useState(false)
    const [profile, setProfile] = useState({
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "(*************",
    birthdate: "1990-01-01",
  })

    const getStatusIcon = (status: string) => {
    switch (status) {
      case "processing":
        return <Clock className="h-4 w-4 text-blue-500" />
      case "shipped":
        return <Truck className="h-4 w-4 text-green-500" />
      case "delivered":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "cancelled":
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <Package className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "processing":
        return "Processing"
      case "shipped":
        return "Shipped"
      case "delivered":
        return "Delivered"
      case "cancelled":
        return "Cancelled"
      default:
        return "Unknown"
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "processing":
        return "bg-blue-100 text-blue-800"
      case "shipped":
        return "bg-green-100 text-green-800"
      case "delivered":
        return "bg-green-100 text-green-800"
      case "cancelled":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const handleProfileUpdate = (e: React.FormEvent) => {
    e.preventDefault()
    setEditingProfile(false)
    toast({
      title: "Profile updated",
      description: "Your profile information has been updated successfully.",
    })
  }

  const handleLogout = () => {
    toast({
      title: "Logged out",
      description: "You have been successfully logged out.",
    })
    // In a real app, this would redirect to the login page
  }

    return (
    <div className="container px-4 md:px-6 py-8 md:py-12">
      <div className="grid grid-cols-1 md:grid-cols-[240px_1fr] gap-8">
        {/* Sidebar */}
        <div className="space-y-6">
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12">
              <AvatarImage src="/placeholder.svg?height=48&width=48" alt="John Doe" />
              <AvatarFallback>JD</AvatarFallback>
            </Avatar>
            <div>
              <h2 className="font-medium">John Doe</h2>
              <p className="text-sm text-muted-foreground">Member since Jan 2025</p>
            </div>
          </div>

          <nav className="space-y-1">
            <Button
              variant={activeTab === "overview" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("overview")}
            >
              <User className="h-4 w-4 mr-2" />
              Overview
            </Button>
            <Button
              variant={activeTab === "orders" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("orders")}
            >
              <Package className="h-4 w-4 mr-2" />
              Orders
            </Button>
            <Button
              variant={activeTab === "wishlist" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("wishlist")}
            >
              <Heart className="h-4 w-4 mr-2" />
              Wishlist
            </Button>
            <Button
              variant={activeTab === "addresses" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("addresses")}
            >
              <MapPin className="h-4 w-4 mr-2" />
              Addresses
            </Button>
            <Button
              variant={activeTab === "payment" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("payment")}
            >
              <CreditCard className="h-4 w-4 mr-2" />
              Payment Methods
            </Button>
            <Button
              variant={activeTab === "rewards" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("rewards")}
            >
              <Gift className="h-4 w-4 mr-2" />
              Rewards
            </Button>
            <Button
              variant={activeTab === "settings" ? "default" : "ghost"}
              className="w-full justify-start"
              onClick={() => setActiveTab("settings")}
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="ghost" className="w-full justify-start text-red-500" onClick={handleLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </nav>
        </div>

        {/* Main Content */}
        <div className="space-y-6">
          {/* Overview Tab */}
          {activeTab === "overview" && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold font-montserrat">Account Overview</h1>
                <Button variant="outline" size="sm" onClick={() => setEditingProfile(!editingProfile)}>
                  <Edit className="h-4 w-4 mr-2" />
                  {editingProfile ? "Cancel" : "Edit Profile"}
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Profile Information */}
                <Card>
                  <CardHeader>
                    <CardTitle>Profile Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {editingProfile ? (
                      <form onSubmit={handleProfileUpdate} className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="firstName">First Name</Label>
                            <Input
                              id="firstName"
                              value={profile.firstName}
                              onChange={(e) => setProfile({ ...profile, firstName: e.target.value })}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="lastName">Last Name</Label>
                            <Input
                              id="lastName"
                              value={profile.lastName}
                              onChange={(e) => setProfile({ ...profile, lastName: e.target.value })}
                              required
                            />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email">Email</Label>
                          <Input
                            id="email"
                            type="email"
                            value={profile.email}
                            onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                            required
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="phone">Phone</Label>
                          <Input
                            id="phone"
                            value={profile.phone}
                            onChange={(e) => setProfile({ ...profile, phone: e.target.value })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="birthdate">Birthdate</Label>
                          <Input
                            id="birthdate"
                            type="date"
                            value={profile.birthdate}
                            onChange={(e) => setProfile({ ...profile, birthdate: e.target.value })}
                          />
                        </div>
                        <Button type="submit">Save Changes</Button>
                      </form>
                    ) : (
                      <div className="space-y-3">
                        <div className="grid grid-cols-2 gap-2">
                          <div>
                            <p className="text-sm text-muted-foreground">First Name</p>
                            <p>{profile.firstName}</p>
                          </div>
                          <div>
                            <p className="text-sm text-muted-foreground">Last Name</p>
                            <p>{profile.lastName}</p>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Email</p>
                          <p>{profile.email}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Phone</p>
                          <p>{profile.phone}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Birthdate</p>
                          <p>{new Date(profile.birthdate).toLocaleDateString()}</p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Account Summary */}
                <div className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Account Summary</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Total Orders</span>
                          <span className="font-medium">{mockOrders.length}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Wishlist Items</span>
                          <span className="font-medium">5</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Reward Points</span>
                          <span className="font-medium">250 pts</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Member Status</span>
                          <Badge variant="secondary">Silver</Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Activity</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4 text-sm">
                        <div className="flex justify-between">
                          <span>Order #CM12345 placed</span>
                          <span className="text-muted-foreground">May 20, 2025</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Added item to wishlist</span>
                          <span className="text-muted-foreground">May 18, 2025</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Account created</span>
                          <span className="text-muted-foreground">Jan 15, 2025</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* Recent Orders */}
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle>Recent Orders</CardTitle>
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/orders">View All Orders</Link>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {mockOrders.slice(0, 2).map((order) => (
                      <div key={order.id} className="flex justify-between items-center p-4 border rounded-md">
                        <div>
                          <div className="font-medium">Order #{order.id}</div>
                          <div className="text-sm text-muted-foreground">
                            {order.date} • {order.items} items
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <Badge className={getStatusColor(order.status)}>
                            <span className="flex items-center">
                              {getStatusIcon(order.status)}
                              <span className="ml-1">{getStatusText(order.status)}</span>
                            </span>
                          </Badge>
                          <div className="font-medium">${order.total.toFixed(2)}</div>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/orders/track?order=${order.id}`}>Track</Link>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Orders Tab */}
          {activeTab === "orders" && (
            <div className="space-y-6">
              <h1 className="text-2xl font-bold font-montserrat">My Orders</h1>

              <Tabs defaultValue="all" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="all">All</TabsTrigger>
                  <TabsTrigger value="processing">Processing</TabsTrigger>
                  <TabsTrigger value="shipped">Shipped</TabsTrigger>
                  <TabsTrigger value="delivered">Delivered</TabsTrigger>
                </TabsList>

                <TabsContent value="all" className="mt-6">
                  <div className="space-y-4">
                    {mockOrders.map((order) => (
                      <Card key={order.id}>
                        <CardContent className="p-6">
                          <div className="flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0">
                            <div>
                              <div className="font-medium">Order #{order.id}</div>
                              <div className="text-sm text-muted-foreground">
                                {order.date} • {order.items} items
                              </div>
                            </div>
                            <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4">
                              <Badge className={getStatusColor(order.status)}>
                                <span className="flex items-center">
                                  {getStatusIcon(order.status)}
                                  <span className="ml-1">{getStatusText(order.status)}</span>
                                </span>
                              </Badge>
                              <div className="font-medium">${order.total.toFixed(2)}</div>
                              <div className="flex space-x-2">
                                <Button variant="outline" size="sm" asChild>
                                  <Link href={`/orders/${order.id}`}>Details</Link>
                                </Button>
                                <Button variant="outline" size="sm" asChild>
                                  <Link href={`/orders/track?order=${order.id}`}>Track</Link>
                                </Button>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </TabsContent>

                <TabsContent value="processing" className="mt-6">
                  <div className="space-y-4">
                    {mockOrders
                      .filter((order) => order.status === "processing")
                      .map((order) => (
                        <Card key={order.id}>
                          <CardContent className="p-6">
                            <div className="flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0">
                              <div>
                                <div className="font-medium">Order #{order.id}</div>
                                <div className="text-sm text-muted-foreground">
                                  {order.date} • {order.items} items
                                </div>
                              </div>
                              <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4">
                                <Badge className={getStatusColor(order.status)}>
                                  <span className="flex items-center">
                                    {getStatusIcon(order.status)}
                                    <span className="ml-1">{getStatusText(order.status)}</span>
                                  </span>
                                </Badge>
                                <div className="font-medium">${order.total.toFixed(2)}</div>
                                <div className="flex space-x-2">
                                  <Button variant="outline" size="sm" asChild>
                                    <Link href={`/orders/${order.id}`}>Details</Link>
                                  </Button>
                                  <Button variant="outline" size="sm" asChild>
                                    <Link href={`/orders/track?order=${order.id}`}>Track</Link>
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                  </div>
                </TabsContent>

                <TabsContent value="shipped" className="mt-6">
                  <div className="space-y-4">
                    {mockOrders
                      .filter((order) => order.status === "shipped")
                      .map((order) => (
                        <Card key={order.id}>
                          <CardContent className="p-6">
                            <div className="flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0">
                              <div>
                                <div className="font-medium">Order #{order.id}</div>
                                <div className="text-sm text-muted-foreground">
                                  {order.date} • {order.items} items
                                </div>
                              </div>
                              <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4">
                                <Badge className={getStatusColor(order.status)}>
                                  <span className="flex items-center">
                                    {getStatusIcon(order.status)}
                                    <span className="ml-1">{getStatusText(order.status)}</span>
                                  </span>
                                </Badge>
                                <div className="font-medium">${order.total.toFixed(2)}</div>
                                <div className="flex space-x-2">
                                  <Button variant="outline" size="sm" asChild>
                                    <Link href={`/orders/${order.id}`}>Details</Link>
                                  </Button>
                                  <Button variant="outline" size="sm" asChild>
                                    <Link href={`/orders/track?order=${order.id}`}>Track</Link>
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                  </div>
                </TabsContent>

                <TabsContent value="delivered" className="mt-6">
                  <div className="space-y-4">
                    {mockOrders
                      .filter((order) => order.status === "delivered")
                      .map((order) => (
                        <Card key={order.id}>
                          <CardContent className="p-6">
                            <div className="flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0">
                              <div>
                                <div className="font-medium">Order #{order.id}</div>
                                <div className="text-sm text-muted-foreground">
                                  {order.date} • {order.items} items
                                </div>
                              </div>
                              <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4">
                                <Badge className={getStatusColor(order.status)}>
                                  <span className="flex items-center">
                                    {getStatusIcon(order.status)}
                                    <span className="ml-1">{getStatusText(order.status)}</span>
                                  </span>
                                </Badge>
                                <div className="font-medium">${order.total.toFixed(2)}</div>
                                <div className="flex space-x-2">
                                  <Button variant="outline" size="sm" asChild>
                                    <Link href={`/orders/${order.id}`}>Details</Link>
                                  </Button>
                                  <Button variant="outline" size="sm" asChild>
                                    <Link href={`/orders/track?order=${order.id}`}>Track</Link>
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}

          {/* Addresses Tab */}
          {activeTab === "addresses" && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold font-montserrat">My Addresses</h1>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add New Address
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {mockAddresses.map((address) => (
                  <Card key={address.id}>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <div className="font-medium">{address.name}</div>
                          <div className="text-sm text-muted-foreground">{address.type} Address</div>
                        </div>
                        <div className="flex space-x-2">
                          {address.isDefault && <Badge variant="outline">Default</Badge>}
                          <Badge variant="secondary">{address.type}</Badge>
                        </div>
                      </div>
                      <div className="space-y-1 text-sm mb-4">
                        <p>{address.street}</p>
                        <p>
                          {address.city}, {address.state} {address.zip}
                        </p>
                        <p>{address.country}</p>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm" className="text-red-500">
                          <Trash className="h-4 w-4 mr-2" />
                          Delete
                        </Button>
                        {!address.isDefault && (
                          <Button variant="outline" size="sm">
                            Set as Default
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Payment Methods Tab */}
          {activeTab === "payment" && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h1 className="text-2xl font-bold font-montserrat">Payment Methods</h1>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Payment Method
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {mockPaymentMethods.map((payment) => (
                  <Card key={payment.id}>
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-8 bg-gray-100 rounded flex items-center justify-center">
                            {payment.type === "visa" ? (
                              <span className="text-blue-600 font-bold">VISA</span>
                            ) : (
                              <span className="text-red-600 font-bold">MC</span>
                            )}
                          </div>
                          <div>
                            <div className="font-medium">
                              {payment.type === "visa" ? "Visa" : "Mastercard"} ending in {payment.last4}
                            </div>
                            <div className="text-sm text-muted-foreground">Expires {payment.expiry}</div>
                          </div>
                        </div>
                        {payment.isDefault && <Badge variant="outline">Default</Badge>}
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-2" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm" className="text-red-500">
                          <Trash className="h-4 w-4 mr-2" />
                          Delete
                        </Button>
                        {!payment.isDefault && (
                          <Button variant="outline" size="sm">
                            Set as Default
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Rewards Tab */}
          {activeTab === "rewards" && (
            <div className="space-y-6">
              <h1 className="text-2xl font-bold font-montserrat">Rewards Program</h1>

              <Card>
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0">
                    <div>
                      <div className="text-lg font-medium">Silver Member</div>
                      <div className="text-sm text-muted-foreground">250 points earned</div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-lg font-bold">250 pts</div>
                      <Button>Redeem Points</Button>
                    </div>
                  </div>

                  <Separator className="my-6" />

                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium mb-2">Membership Tiers</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card className="bg-gray-50">
                          <CardContent className="p-4">
                            <div className="text-center">
                              <div className="font-medium">Bronze</div>
                              <div className="text-sm text-muted-foreground">0-200 points</div>
                              <div className="mt-2 text-sm">
                                <div>5% off birthday discount</div>
                                <div>Free shipping on orders $75+</div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                        <Card className="bg-gradient-to-b from-gray-100 to-gray-200 border-gray-300">
                          <CardContent className="p-4">
                            <div className="text-center">
                              <div className="font-medium">Silver</div>
                              <div className="text-sm text-muted-foreground">201-500 points</div>
                              <div className="mt-2 text-sm">
                                <div>10% off birthday discount</div>
                                <div>Free shipping on orders $50+</div>
                                <div>Early access to sales</div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                        <Card className="bg-gradient-to-b from-yellow-50 to-yellow-100 border-yellow-200">
                          <CardContent className="p-4">
                            <div className="text-center">
                              <div className="font-medium">Gold</div>
                              <div className="text-sm text-muted-foreground">501+ points</div>
                              <div className="mt-2 text-sm">
                                <div>15% off birthday discount</div>
                                <div>Free shipping on all orders</div>
                                <div>Early access to sales</div>
                                <div>Exclusive products</div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-medium mb-2">Recent Activity</h3>
                      <div className="space-y-2">
                        <div className="flex justify-between items-center p-3 border rounded-md">
                          <div>
                            <div className="font-medium">Order #CM12345</div>
                            <div className="text-sm text-muted-foreground">May 20, 2025</div>
                          </div>
                          <div className="text-green-600 font-medium">+45 points</div>
                        </div>
                        <div className="flex justify-between items-center p-3 border rounded-md">
                          <div>
                            <div className="font-medium">Order #CM54321</div>
                            <div className="text-sm text-muted-foreground">April 15, 2025</div>
                          </div>
                          <div className="text-green-600 font-medium">+125 points</div>
                        </div>
                        <div className="flex justify-between items-center p-3 border rounded-md">
                          <div>
                            <div className="font-medium">Birthday Bonus</div>
                            <div className="text-sm text-muted-foreground">March 10, 2025</div>
                          </div>
                          <div className="text-green-600 font-medium">+50 points</div>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-lg font-medium mb-2">How to Earn Points</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                        <div className="p-4 border rounded-md">
                          <div className="font-medium mb-2">Make a Purchase</div>
                          <div className="text-sm text-muted-foreground">Earn 1 point for every $1 spent</div>
                        </div>
                        <div className="p-4 border rounded-md">
                          <div className="font-medium mb-2">Write a Review</div>
                          <div className="text-sm text-muted-foreground">Earn 10 points per review</div>
                        </div>
                        <div className="p-4 border rounded-md">
                          <div className="font-medium mb-2">Refer a Friend</div>
                          <div className="text-sm text-muted-foreground">Earn 50 points per referral</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === "settings" && (
            <div className="space-y-6">
              <h1 className="text-2xl font-bold font-montserrat">Account Settings</h1>

              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                  <CardDescription>Update your personal details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">First Name</Label>
                      <Input id="firstName" defaultValue="John" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input id="lastName" defaultValue="Doe" />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input id="email" type="email" defaultValue="<EMAIL>" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input id="phone" defaultValue="+****************" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="birthdate">Date of Birth</Label>
                    <Input id="birthdate" type="date" defaultValue="1990-01-01" />
                  </div>
                  <Button>Save Changes</Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Child Profiles</CardTitle>
                  <CardDescription>Manage your children's information for better size recommendations</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium">Emma Doe</div>
                          <div className="text-sm text-muted-foreground">
                            Age: 5 years • Size: 5T • Favorite Colors: Pink, Purple
                          </div>
                        </div>
                        <Button variant="outline" size="sm">Edit</Button>
                      </div>
                    </div>
                    <div className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium">Alex Doe</div>
                          <div className="text-sm text-muted-foreground">
                            Age: 8 years • Size: 8 • Favorite Colors: Blue, Green
                          </div>
                        </div>
                        <Button variant="outline" size="sm">Edit</Button>
                      </div>
                    </div>
                    <Button variant="outline">Add Child Profile</Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Shipping Addresses</CardTitle>
                  <CardDescription>Manage your delivery addresses</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <div className="font-medium">Home</div>
                          <div className="text-sm text-muted-foreground">
                            123 Main Street<br />
                            Anytown, ST 12345<br />
                            United States
                          </div>
                        </div>
                        <Button variant="outline" size="sm">Edit</Button>
                      </div>
                    </div>
                    <Button variant="outline">Add New Address</Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Payment Methods</CardTitle>
                  <CardDescription>Manage your saved payment methods</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-5 bg-blue-600 rounded text-white text-xs flex items-center justify-center">VISA</div>
                          <div>
                            <div className="font-medium">•••• •••• •••• 4242</div>
                            <div className="text-sm text-muted-foreground">Expires 12/25</div>
                          </div>
                        </div>
                        <Button variant="outline" size="sm">Edit</Button>
                      </div>
                    </div>
                    <Button variant="outline">Add Payment Method</Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Password & Security</CardTitle>
                  <CardDescription>Change your password and security settings</CardDescription>
                </CardHeader>
                <CardContent>
                  <form className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="current-password">Current Password</Label>
                      <Input id="current-password" type="password" required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="new-password">New Password</Label>
                      <Input id="new-password" type="password" required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="confirm-password">Confirm New Password</Label>
                      <Input id="confirm-password" type="password" required />
                    </div>
                    <Button type="submit">Update Password</Button>
                    <Separator className="my-4" />
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">Two-Factor Authentication</div>
                          <div className="text-sm text-muted-foreground">Add an extra layer of security to your account</div>
                        </div>
                        <Button variant="outline" size="sm">Enable</Button>
                      </div>
                    </div>
                  </form>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Notification Preferences</CardTitle>
                  <CardDescription>Manage your email and SMS notifications</CardDescription>
                </CardHeader>
                <CardContent>
                  <NotificationPreferencesPanel userId={user.id} />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Privacy & Data</CardTitle>
                  <CardDescription>Manage your privacy settings and data</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button variant="outline" className="w-full justify-start">
                    Download My Data
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    Privacy Policy
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    Terms of Service
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Delete Account</CardTitle>
                  <CardDescription>Permanently delete your account and all your data</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Once you delete your account, there is no going back. Please be certain.
                  </p>
                  <Button variant="destructive">Delete Account</Button>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
