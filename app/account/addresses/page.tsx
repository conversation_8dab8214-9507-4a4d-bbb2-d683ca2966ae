"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/components/auth-provider"
import { UserAddressList } from "@/components/user/address-list"
import { useUserAddresses } from "@/lib/hooks/use-user-addresses"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function AddressesPage() {
  const { user } = useAuth()
  
  const {
    addresses,
    isLoading,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
  } = useUserAddresses(user?.id)

  if (!user) {
    return (
      <div className="container mx-auto py-10">
        <div className="flex h-[50vh] items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold">Please sign in</h2>
            <p className="text-muted-foreground">You need to be signed in to view your addresses</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-10">
      <div className="mb-6">
        <Button variant="ghost" size="sm" asChild className="mb-4">
          <Link href="/account">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Account
          </Link>
        </Button>
        <h1 className="text-3xl font-bold">My Addresses</h1>
        <p className="text-muted-foreground">
          Manage your shipping and billing addresses
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Address Book</CardTitle>
          <CardDescription>
            Add and manage your shipping and billing addresses for faster checkout
          </CardDescription>
        </CardHeader>
        <CardContent>
          <UserAddressList 
            addresses={addresses}
            onAddAddress={addAddress}
            onUpdateAddress={updateAddress}
            onDeleteAddress={deleteAddress}
            onSetDefaultAddress={setDefaultAddress}
            isLoading={isLoading}
          />
        </CardContent>
      </Card>
    </div>
  )
}