"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useSearchParams } from "next/navigation"
import { useAuth } from "@/components/auth-provider"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  ShoppingCart, 
  Package, 
  Clock, 
  CheckCircle,
  ArrowRight,
  Calendar,
  Truck,
  Search,
  Filter,
  SortAsc
} from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface Order {
  id: number
  orderNumber: string
  date: string
  status: string
  total: string
  items: number
}

export default function OrdersPage() {
  const { user } = useAuth()
  const searchParams = useSearchParams()
  const statusFilter = searchParams.get('status')
  
  const [orders, setOrders] = useState<Order[]>([])
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [sortOrder, setSortOrder] = useState<"newest" | "oldest" | "highest" | "lowest">("newest")

  useEffect(() => {
    // This would be replaced with an actual API call in production
    const fetchOrders = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Mock data
        const mockOrders = [
          {
            id: 1,
            orderNumber: "ORD-2023-1001",
            date: "2023-05-15",
            status: "delivered",
            total: "$125.99",
            items: 3
          },
          {
            id: 2,
            orderNumber: "ORD-2023-1002",
            date: "2023-05-28",
            status: "shipped",
            total: "$89.50",
            items: 2
          },
          {
            id: 3,
            orderNumber: "ORD-2023-1003",
            date: "2023-06-10",
            status: "processing",
            total: "$210.75",
            items: 4
          },
          {
            id: 4,
            orderNumber: "ORD-2023-1004",
            date: "2023-07-05",
            status: "pending",
            total: "$45.25",
            items: 1
          },
          {
            id: 5,
            orderNumber: "ORD-2023-1005",
            date: "2023-07-22",
            status: "delivered",
            total: "$178.50",
            items: 5
          },
          {
            id: 6,
            orderNumber: "ORD-2023-1006",
            date: "2023-08-15",
            status: "shipped",
            total: "$67.99",
            items: 2
          },
          {
            id: 7,
            orderNumber: "ORD-2023-1007",
            date: "2023-09-03",
            status: "delivered",
            total: "$134.25",
            items: 3
          },
          {
            id: 8,
            orderNumber: "ORD-2023-1008",
            date: "2023-10-12",
            status: "processing",
            total: "$95.75",
            items: 2
          }
        ]
        
        setOrders(mockOrders)
      } catch (error) {
        console.error("Error fetching orders:", error)
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchOrders()
  }, [])

  // Apply filters and search
  useEffect(() => {
    let result = [...orders]
    
    // Apply status filter if present
    if (statusFilter) {
      result = result.filter(order => order.status === statusFilter)
    }
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      result = result.filter(order => 
        order.orderNumber.toLowerCase().includes(query) ||
        order.total.toLowerCase().includes(query) ||
        order.status.toLowerCase().includes(query)
      )
    }
    
    // Apply sorting
    switch (sortOrder) {
      case "newest":
        result.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
        break
      case "oldest":
        result.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
        break
      case "highest":
        result.sort((a, b) => parseFloat(b.total.replace('$', '')) - parseFloat(a.total.replace('$', '')))
        break
      case "lowest":
        result.sort((a, b) => parseFloat(a.total.replace('$', '')) - parseFloat(b.total.replace('$', '')))
        break
    }
    
    setFilteredOrders(result)
  }, [orders, statusFilter, searchQuery, sortOrder])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />
      case "processing":
        return <Package className="h-4 w-4 text-blue-500" />
      case "shipped":
        return <Truck className="h-4 w-4 text-purple-500" />
      case "delivered":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "Pending"
      case "processing":
        return "Processing"
      case "shipped":
        return "Shipped"
      case "delivered":
        return "Delivered"
      default:
        return status.charAt(0).toUpperCase() + status.slice(1)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">My Orders</h1>
        <p className="text-muted-foreground">
          View and track all your orders
        </p>
      </div>

      <Card>
        <CardHeader>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <CardTitle>Order History</CardTitle>
              <CardDescription>
                Track and manage your purchases
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search orders..."
                  className="w-full pl-8 sm:w-[200px] md:w-[300px]"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                    <span className="sr-only">Filter</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link href="/account/orders">All Orders</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/account/orders?status=pending">Pending</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/account/orders?status=processing">Processing</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/account/orders?status=shipped">Shipped</Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link href="/account/orders?status=delivered">Delivered</Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Select
                value={sortOrder}
                onValueChange={(value) => setSortOrder(value as any)}
              >
                <SelectTrigger className="w-[130px]">
                  <SortAsc className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                  <SelectItem value="highest">Highest Total</SelectItem>
                  <SelectItem value="lowest">Lowest Total</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-12">
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-pink-600"></div>
            </div>
          ) : filteredOrders.length > 0 ? (
            <div className="space-y-4">
              {filteredOrders.map((order) => (
                <div key={order.id} className="flex flex-col sm:flex-row sm:items-center justify-between rounded-lg border p-4">
                  <div className="grid gap-1">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(order.status)}
                      <span className="font-medium">{order.orderNumber}</span>
                    </div>
                    <div className="flex flex-wrap items-center gap-2 text-sm text-muted-foreground">
                      <Calendar className="h-3 w-3" />
                      <span>{new Date(order.date).toLocaleDateString()}</span>
                      <span>•</span>
                      <span>{getStatusText(order.status)}</span>
                      <span>•</span>
                      <span>{order.items} {order.items === 1 ? 'item' : 'items'}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-4 mt-3 sm:mt-0">
                    <span className="font-medium">{order.total}</span>
                    <Button variant="outline" size="sm" asChild>
                      <Link href={`/account/orders/${order.id}`}>
                        View Details
                      </Link>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <ShoppingCart className="mb-4 h-12 w-12 text-muted-foreground" />
              <h3 className="mb-2 text-xl font-medium">No orders found</h3>
              <p className="mb-6 text-sm text-muted-foreground">
                {statusFilter 
                  ? `You don't have any ${statusFilter} orders.` 
                  : searchQuery 
                    ? "No orders match your search criteria." 
                    : "You haven't placed any orders yet."}
              </p>
              <Button asChild>
                <Link href="/products">Start Shopping</Link>
              </Button>
            </div>
          )}
        </CardContent>
        {filteredOrders.length > 0 && (
          <CardFooter className="flex justify-between">
            <p className="text-sm text-muted-foreground">
              Showing {filteredOrders.length} of {orders.length} orders
            </p>
          </CardFooter>
        )}
      </Card>
    </div>
  )
}