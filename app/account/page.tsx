"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useAuth } from "@/components/auth-provider"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  ShoppingCart, 
  Package, 
  Heart, 
  User, 
  CreditCard, 
  MapPin, 
  Clock, 
  CheckCircle,
  ArrowRight,
  Calendar,
  Truck
} from "lucide-react"

interface OrderSummary {
  id: number
  orderNumber: string
  date: string
  status: string
  total: string
  items: number
}

interface RecentOrder {
  id: number
  orderNumber: string
  date: string
  status: string
  total: string
  items: number
}

export default function AccountPage() {
  const { user, customer } = useAuth()
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([])
  const [orderSummary, setOrderSummary] = useState({
    total: 0,
    pending: 0,
    processing: 0,
    shipped: 0,
    delivered: 0
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // This would be replaced with an actual API call in production
    const fetchData = async () => {
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Mock data
        setRecentOrders([
          {
            id: 1,
            orderNumber: "ORD-2023-1001",
            date: "2023-05-15",
            status: "delivered",
            total: "$125.99",
            items: 3
          },
          {
            id: 2,
            orderNumber: "ORD-2023-1002",
            date: "2023-05-28",
            status: "shipped",
            total: "$89.50",
            items: 2
          },
          {
            id: 3,
            orderNumber: "ORD-2023-1003",
            date: "2023-06-10",
            status: "processing",
            total: "$210.75",
            items: 4
          }
        ])
        
        setOrderSummary({
          total: 10,
          pending: 1,
          processing: 2,
          shipped: 3,
          delivered: 4
        })
      } catch (error) {
        console.error("Error fetching dashboard data:", error)
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchData()
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />
      case "processing":
        return <Package className="h-4 w-4 text-blue-500" />
      case "shipped":
        return <Truck className="h-4 w-4 text-purple-500" />
      case "delivered":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "Pending"
      case "processing":
        return "Processing"
      case "shipped":
        return "Shipped"
      case "delivered":
        return "Delivered"
      default:
        return status.charAt(0).toUpperCase() + status.slice(1)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-2">
        <h1 className="text-3xl font-bold tracking-tight">Welcome back, {user?.display_name || user?.first_name || "there"}!</h1>
        <p className="text-muted-foreground">
          Manage your account, track orders, and update your preferences.
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orderSummary.total}</div>
            <p className="text-xs text-muted-foreground">
              Lifetime orders placed
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orderSummary.processing + orderSummary.shipped}</div>
            <p className="text-xs text-muted-foreground">
              Orders being processed or shipped
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivered</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{orderSummary.delivered}</div>
            <p className="text-xs text-muted-foreground">
              Successfully delivered orders
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saved Items</CardTitle>
            <Heart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">
              Products in your favorites
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="orders" className="space-y-4">
        <TabsList>
          <TabsTrigger value="orders">Recent Orders</TabsTrigger>
          <TabsTrigger value="account">Account Info</TabsTrigger>
        </TabsList>
        <TabsContent value="orders" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Orders</CardTitle>
              <CardDescription>
                Track and manage your recent purchases
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="flex justify-center py-6">
                  <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-t-2 border-pink-600"></div>
                </div>
              ) : recentOrders.length > 0 ? (
                <div className="space-y-4">
                  {recentOrders.map((order) => (
                    <div key={order.id} className="flex items-center justify-between rounded-lg border p-4">
                      <div className="grid gap-1">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(order.status)}
                          <span className="font-medium">{order.orderNumber}</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          <span>{new Date(order.date).toLocaleDateString()}</span>
                          <span>•</span>
                          <span>{getStatusText(order.status)}</span>
                          <span>•</span>
                          <span>{order.items} {order.items === 1 ? 'item' : 'items'}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <span className="font-medium">{order.total}</span>
                        <Button variant="ghost" size="icon" asChild>
                          <Link href={`/account/orders/${order.id}`}>
                            <ArrowRight className="h-4 w-4" />
                            <span className="sr-only">View order</span>
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-6 text-center">
                  <ShoppingCart className="mb-2 h-10 w-10 text-muted-foreground" />
                  <h3 className="mb-1 text-lg font-medium">No orders yet</h3>
                  <p className="text-sm text-muted-foreground">
                    When you place orders, they will appear here.
                  </p>
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button asChild className="w-full">
                <Link href="/account/orders">View All Orders</Link>
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        <TabsContent value="account" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Account Information</CardTitle>
              <CardDescription>
                Your personal information and preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-4">
                  <div>
                    <h3 className="mb-2 text-sm font-medium">Personal Details</h3>
                    <div className="rounded-lg border p-3">
                      <div className="mb-2 flex items-center gap-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          {user?.first_name} {user?.last_name}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">{user?.email}</p>
                      <div className="mt-3 flex justify-end">
                        <Button variant="outline" size="sm" asChild>
                          <Link href="/account/profile">Edit</Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="mb-2 text-sm font-medium">Payment Methods</h3>
                    <div className="rounded-lg border p-3">
                      <div className="mb-2 flex items-center gap-2">
                        <CreditCard className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          {customer?.is_paying_customer ? "Payment methods saved" : "No payment methods"}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {customer?.is_paying_customer 
                          ? "You have saved payment methods on file" 
                          : "Add a payment method for faster checkout"}
                      </p>
                      <div className="mt-3 flex justify-end">
                        <Button variant="outline" size="sm" asChild>
                          <Link href="/account/payment-methods">Manage</Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div>
                    <h3 className="mb-2 text-sm font-medium">Shipping Address</h3>
                    <div className="rounded-lg border p-3">
                      <div className="mb-2 flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">
                          {customer?.shipping?.first_name ? "Default Address" : "No address saved"}
                        </span>
                      </div>
                      {customer?.shipping?.first_name ? (
                        <p className="text-sm text-muted-foreground">
                          {customer.shipping.address_1}, {customer.shipping.city}, {customer.shipping.postcode}
                        </p>
                      ) : (
                        <p className="text-sm text-muted-foreground">
                          Add a shipping address for faster checkout
                        </p>
                      )}
                      <div className="mt-3 flex justify-end">
                        <Button variant="outline" size="sm" asChild>
                          <Link href="/account/addresses">Manage</Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="mb-2 text-sm font-medium">Favorites</h3>
                    <div className="rounded-lg border p-3">
                      <div className="mb-2 flex items-center gap-2">
                        <Heart className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm font-medium">Saved Items</span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        You have 12 items in your favorites
                      </p>
                      <div className="mt-3 flex justify-end">
                        <Button variant="outline" size="sm" asChild>
                          <Link href="/account/favorites">View All</Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
