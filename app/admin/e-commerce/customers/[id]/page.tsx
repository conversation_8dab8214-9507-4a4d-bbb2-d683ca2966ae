'use client'

import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Separator } from '@/components/ui/separator'
import { 
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { 
  ArrowLeft, 
  Edit, 
  Mail, 
  Phone, 
  MapPin,
  Calendar,
  ShoppingBag,
  DollarSign,
  TrendingUp,
  UserIcon,
  Star,
  Gift
} from 'lucide-react'
import Link from 'next/link'
import { useZarFormatter } from '@/components/admin/zar-price-input'
import { useCustomer as useCustomerWithAddresses } from '@/lib/ecommerce/hooks/use-customer'
import { useCustomer } from '@/lib/ecommerce/hooks/use-customers'
import { useOrders } from '@/lib/ecommerce/hooks/use-orders'
import type { User } from '@/lib/ecommerce/types/user'
import type { CustomerAddress } from '@/lib/ecommerce/types/customer'

interface CustomerDetailPageProps {
  params: Promise<{ id: string }>
}

export default function CustomerDetailPage({ params }: CustomerDetailPageProps) {
  const router = useRouter()
  const { formatPrice } = useZarFormatter()
  const [customerId, setCustomerId] = useState<string>('')

  // Resolve params promise
  React.useEffect(() => {
    params.then(({ id }) => setCustomerId(id))
  }, [params])

  const { 
    customer, 
    loading, 
    error, 
    refetch,
    clearError
  } = useCustomer({ customerId, autoFetch: !!customerId })

  // Use the addresses hook for address functionality
  const {
    addresses,
    updateCustomer,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress
  } = useCustomerWithAddresses(customerId)

  const { 
    orders, 
    loading: ordersLoading, 
    error: ordersError,
    refetch: refetchOrders 
  } = useOrders({ 
    initialParams: { customerId: customerId || '', limit: 10 },
    autoFetch: !!customerId 
  })

  const [isEditing, setIsEditing] = useState(false)
  const [editForm, setEditForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: ''
  })

  useEffect(() => {
    if (customer) {
      setEditForm({
        firstName: customer.firstName || '',
        lastName: customer.lastName || '',
        email: customer.email || '',
        phone: customer.phone || '',
        dateOfBirth: customer.dateOfBirth || ''
      })
    }
  }, [customer])

  const handleSaveCustomer = async () => {
    const formData = {
      ...editForm,
      dateOfBirth: editForm.dateOfBirth ? new Date(editForm.dateOfBirth) : undefined
    }
    const success = await updateCustomer(formData)
    if (success) {
      setIsEditing(false)
    }
  }

  const handleDeleteAddress = async (addressId: string) => {
    if (confirm('Are you sure you want to delete this address?')) {
      await deleteAddress(addressId)
    }
  }

  const handleSetDefaultAddress = async (addressId: string) => {
    await setDefaultAddress(addressId)
  }

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase()
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/customers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Customers
            </Link>
          </Button>
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error || !customer) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/customers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Customers
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Customer not found</h3>
              <p className="mt-1 text-sm text-gray-500">
                The customer you're looking for doesn't exist or has been deleted.
              </p>
              <div className="mt-6">
                <Button asChild>
                  <Link href="/admin/customers">
                    Back to Customers
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const getLoyaltyBadge = (tier: string) => {
    switch (tier) {
      case 'VIP':
        return <Badge variant="default" className="bg-purple-600">VIP</Badge>
      case 'Gold':
        return <Badge variant="default" className="bg-yellow-600">Gold</Badge>
      case 'Silver':
        return <Badge variant="secondary">Silver</Badge>
      case 'Bronze':
        return <Badge variant="outline">Bronze</Badge>
      default:
        return <Badge variant="secondary">Standard</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/customers">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Customers
            </Link>
          </Button>
          <div className="flex items-center space-x-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={customer.avatar} alt={`${customer.firstName} ${customer.lastName}`} />
              <AvatarFallback className="text-lg">
                {getInitials(customer.firstName || '', customer.lastName || '')}
              </AvatarFallback>
            </Avatar>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                {customer.firstName} {customer.lastName}
              </h1>
              <p className="text-muted-foreground">
                Customer since {new Date(customer.createdAt).toLocaleDateString()}
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Mail className="mr-2 h-4 w-4" />
            Send Email
          </Button>
          <Button 
            variant="outline"
            onClick={() => setIsEditing(!isEditing)}
          >
            <Edit className="mr-2 h-4 w-4" />
            {isEditing ? 'Cancel Edit' : 'Edit Customer'}
          </Button>
          {isEditing && (
            <Button onClick={handleSaveCustomer}>
              Save Changes
            </Button>
          )}
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Spent</p>
                <p className="text-2xl font-bold">{formatPrice(customer.totalSpent || 0)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Orders</p>
                <p className="text-2xl font-bold">{customer.orderCount || 0}</p>
              </div>
              <ShoppingBag className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Order Value</p>
                <p className="text-2xl font-bold">
                  {formatPrice(customer.orderCount > 0 ? (customer.totalSpent || 0) / customer.orderCount : 0)}
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Loyalty Tier</p>
                {getLoyaltyBadge(customer.loyaltyTier || 'Standard')}
              </div>
              <Star className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="addresses">Addresses</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {isEditing ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium">First Name</label>
                        <input
                          type="text"
                          value={editForm.firstName}
                          onChange={(e) => setEditForm(prev => ({ ...prev, firstName: e.target.value }))}
                          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium">Last Name</label>
                        <input
                          type="text"
                          value={editForm.lastName}
                          onChange={(e) => setEditForm(prev => ({ ...prev, lastName: e.target.value }))}
                          className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                        />
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Email</label>
                      <input
                        type="email"
                        value={editForm.email}
                        onChange={(e) => setEditForm(prev => ({ ...prev, email: e.target.value }))}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Phone</label>
                      <input
                        type="tel"
                        value={editForm.phone}
                        onChange={(e) => setEditForm(prev => ({ ...prev, phone: e.target.value }))}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Date of Birth</label>
                      <input
                        type="date"
                        value={editForm.dateOfBirth}
                        onChange={(e) => setEditForm(prev => ({ ...prev, dateOfBirth: e.target.value }))}
                        className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
                      />
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="flex items-center space-x-3">
                      <Mail className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <p className="font-medium">{customer.email}</p>
                        <p className="text-sm text-muted-foreground">Email</p>
                      </div>
                    </div>
                    
                    {customer.phone && (
                      <div className="flex items-center space-x-3">
                        <Phone className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{customer.phone}</p>
                          <p className="text-sm text-muted-foreground">Phone</p>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center space-x-3">
                      <Calendar className="h-5 w-5 text-muted-foreground" />
                      <div>
                        <p className="font-medium">
                          {customer.dateOfBirth ? new Date(customer.dateOfBirth).toLocaleDateString() : 'Not provided'}
                        </p>
                        <p className="text-sm text-muted-foreground">Date of Birth</p>
                      </div>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Account Details */}
            <Card>
              <CardHeader>
                <CardTitle>Account Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Account Status</label>
                  <div className="mt-1">
                    <Badge variant={customer.isActive ? "default" : "destructive"}>
                      {customer.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Email Verified</label>
                  <div className="mt-1">
                    <Badge variant={customer.emailVerified ? "default" : "outline"}>
                      {customer.emailVerified ? "Verified" : "Unverified"}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Preferred Language</label>
                  <p className="mt-1">{customer.preferredLanguage || 'Not set'}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Preferred Currency</label>
                  <p className="mt-1">{customer.preferredCurrency || 'ZAR'}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Timezone</label>
                  <p className="mt-1">{customer.timezone || 'Not set'}</p>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Account Blocked</label>
                  <div className="mt-1">
                    <Badge variant={customer.isBlocked ? "destructive" : "outline"}>
                      {customer.isBlocked ? "Blocked" : "Not Blocked"}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">Last Order</label>
                  <p className="mt-1">
                    {customer.lastOrderAt ? new Date(customer.lastOrderAt).toLocaleDateString() : 'No orders yet'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Marketing Preferences */}
          <Card>
            <CardHeader>
              <CardTitle>Marketing Preferences</CardTitle>
              <CardDescription>
                Customer's communication and marketing preferences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Email Marketing</label>
                  <div className="mt-1">
                    <Badge variant={customer.acceptsMarketing ? "default" : "outline"}>
                      {customer.acceptsMarketing ? "Subscribed" : "Unsubscribed"}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">SMS Marketing</label>
                  <div className="mt-1">
                    <Badge variant="outline">Not Set</Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Newsletter</label>
                  <div className="mt-1">
                    <Badge variant="outline">Not Set</Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Promotions</label>
                  <div className="mt-1">
                    <Badge variant="outline">Not Set</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Information */}
          {(customer.bio || customer.tags?.length || customer.notes || customer.metafields) && (
            <Card>
              <CardHeader>
                <CardTitle>Additional Information</CardTitle>
                <CardDescription>
                  Extra details and metadata for this customer
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {customer.bio && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Bio</label>
                    <p className="mt-1 text-sm">{customer.bio}</p>
                  </div>
                )}

                {customer.tags && customer.tags.length > 0 && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Tags</label>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {customer.tags.map((tag: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {customer.notes && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Notes</label>
                    <p className="mt-1 text-sm whitespace-pre-wrap">{customer.notes}</p>
                  </div>
                )}

                {customer.metafields && Object.keys(customer.metafields).length > 0 && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Metafields</label>
                    <div className="mt-1 bg-gray-50 rounded-md p-3">
                      <pre className="text-xs overflow-x-auto">
                        {JSON.stringify(customer.metafields, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}

                {customer.loyaltyPoints && customer.loyaltyPoints > 0 && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Loyalty Points</label>
                    <p className="mt-1 font-medium">{customer.loyaltyPoints.toLocaleString()} points</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="orders" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Order History</CardTitle>
              <CardDescription>
                All orders placed by this customer ({orders.length} orders)
              </CardDescription>
            </CardHeader>
            <CardContent>
              {ordersLoading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-16 bg-gray-200 rounded animate-pulse"></div>
                  ))}
                </div>
              ) : ordersError ? (
                <div className="text-center py-8">
                  <p className="text-red-600">Error loading orders: {ordersError}</p>
                  <Button onClick={refetchOrders} className="mt-4">
                    Retry
                  </Button>
                </div>
              ) : orders.length === 0 ? (
                <div className="text-center py-8">
                  <ShoppingBag className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No Orders Yet</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    This customer hasn't placed any orders yet.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2">Order #</th>
                          <th className="text-left py-2">Date</th>
                          <th className="text-left py-2">Status</th>
                          <th className="text-left py-2">Total</th>
                          <th className="text-left py-2">Items</th>
                        </tr>
                      </thead>
                      <tbody>
                        {orders.map((order) => (
                          <tr 
                            key={order.id} 
                            className="border-b hover:bg-gray-50 cursor-pointer"
                            onClick={() => router.push(`/admin/orders/${order.id}`)}
                          >
                            <td className="py-3">
                              <span className="font-medium">#{order.orderNumber}</span>
                            </td>
                            <td className="py-3">
                              {new Date(order.createdAt).toLocaleDateString()}
                            </td>
                            <td className="py-3">
                              <Badge variant={
                                order.status === 'completed' ? 'default' :
                                order.status === 'cancelled' ? 'destructive' :
                                'secondary'
                              }>
                                {order.status}
                              </Badge>
                            </td>
                            <td className="py-3">
                              {formatPrice(order.total)}
                            </td>
                            <td className="py-3">
                              {order.items?.length || 0} items
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                  <div className="flex justify-between items-center">
                    <Button variant="outline" asChild>
                      <Link href={`/admin/orders?customer=${customerId}`}>
                        View All Orders
                      </Link>
                    </Button>
                    <Button onClick={refetchOrders} variant="outline">
                      Refresh
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="addresses" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Saved Addresses</CardTitle>
                  <CardDescription>
                    Customer's billing and shipping addresses ({addresses.length} addresses)
                  </CardDescription>
                </div>
                <Button 
                  onClick={() => {
                    // This would open an add address modal/form
                    console.log('Add address functionality would go here')
                  }}
                >
                  Add Address
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {addresses.length === 0 ? (
                <div className="text-center py-8">
                  <MapPin className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No Addresses Found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    This customer hasn't saved any addresses yet.
                  </p>
                </div>
              ) : (
                <div className="grid gap-4 md:grid-cols-2">
                  {addresses.map((address) => (
                    <Card key={address.id} className="relative">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <Badge variant={address.type === 'shipping' ? 'default' : 'secondary'}>
                                {address.type}
                              </Badge>
                              {address.isDefault && (
                                <Badge variant="outline">Default</Badge>
                              )}
                            </div>
                            <div>
                              <p className="font-medium">
                                {address.firstName} {address.lastName}
                              </p>
                              {address.company && (
                                <p className="text-sm text-muted-foreground">{address.company}</p>
                              )}
                              <p className="text-sm">{address.address1}</p>
                              {address.address2 && (
                                <p className="text-sm">{address.address2}</p>
                              )}
                              <p className="text-sm">
                                {address.city}, {address.province} {address.zip}
                              </p>
                              <p className="text-sm">{address.country}</p>
                              {address.phone && (
                                <p className="text-sm text-muted-foreground">{address.phone}</p>
                              )}
                            </div>
                          </div>
                          <div className="flex flex-col space-y-2">
                            {!address.isDefault && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleSetDefaultAddress(address.id)}
                              >
                                Set Default
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                // This would open an edit address modal/form
                                console.log('Edit address:', address.id)
                              }}
                            >
                              Edit
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDeleteAddress(address.id)}
                            >
                              Delete
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Activity</CardTitle>
              <CardDescription>
                Recent activity and engagement history
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                      <UserIcon className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>
                  <div>
                    <p className="font-medium">Account Created</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(customer.createdAt).toLocaleString()}
                    </p>
                  </div>
                </div>

                {customer.customerSince && (
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                        <Gift className="h-4 w-4 text-green-600" />
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">Became Customer</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(customer.customerSince).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                {customer.lastOrderAt && (
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                        <ShoppingBag className="h-4 w-4 text-purple-600" />
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">Last Order</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(customer.lastOrderAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
                      <Calendar className="h-4 w-4 text-gray-600" />
                    </div>
                  </div>
                  <div>
                    <p className="font-medium">Last Updated</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(customer.updatedAt).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
