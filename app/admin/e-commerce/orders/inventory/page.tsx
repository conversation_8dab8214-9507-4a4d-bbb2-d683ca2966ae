'use client'

import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Bread<PERSON>rumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { OrderInventoryManager } from '@/components/admin/orders/order-inventory-manager-list'
import { useOrders } from '@/lib/ecommerce/hooks/use-orders'
import { useInventory } from '@/lib/ecommerce/hooks/use-inventory'
import type { Order } from '@/lib/ecommerce/types/order'

export default function OrderInventoryPage() {
  const router = useRouter()
  
  const { orders, loading: ordersLoading, error: ordersError, refetch: refetchOrders } = useOrders({
    initialParams: {
      page: 1,
      limit: 50,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    },
    autoFetch: true
  })

  const { inventory, loading: inventoryLoading, error: inventoryError, refetch: refetchInventory } = useInventory()

  const handleViewOrder = useCallback((order: Order) => {
    router.push(`/admin/orders/${order.id}`)
  }, [router])

  const handleRefresh = useCallback(() => {
    refetchOrders()
    refetchInventory()
  }, [refetchOrders, refetchInventory])

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin/e-commerce/orders">Orders</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Inventory Manager</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <div>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/e-commerce/orders">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Orders
          </Link>
        </Button>
      </div>

      {/* Inventory Manager */}
      <OrderInventoryManager 
        orders={orders}
        inventory={inventory}
        onRefresh={handleRefresh}
        onViewOrder={handleViewOrder}
        loading={ordersLoading || inventoryLoading}
        error={ordersError || inventoryError}
      />
    </div>
  )
}