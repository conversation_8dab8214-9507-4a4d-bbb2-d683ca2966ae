'use client'

import { useState } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Loader2 } from 'lucide-react'
import { OrderManagementForm } from '@/components/admin/orders/order-management-form'
import { useOrder } from '@/lib/ecommerce/hooks/use-orders'
import { toast } from 'sonner'
import type { Order } from '@/lib/ecommerce/types/order'

export default function EditOrderPage() {
  const params = useParams()
  const router = useRouter()
  const orderId = params.id as string
  
  const { order, loading, error, refetch } = useOrder({ 
    orderId,
    autoFetch: true
  })

  const handleSuccess = (updatedOrder: Order) => {
    toast.success(`Order #${updatedOrder.orderNumber} updated successfully!`)
    router.push(`/admin/orders/${updatedOrder.id}`)
  }

  const handleCancel = () => {
    router.push(`/admin/orders/${orderId}`)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <span className="ml-2 text-muted-foreground">Loading order...</span>
      </div>
    )
  }

  if (error || !order) {
    return (
      <div className="space-y-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/e-commerce/orders">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Orders
          </Link>
        </Button>
        <div className="p-6 bg-red-50 border border-red-200 rounded-md">
          <h3 className="text-lg font-medium text-red-800">Error Loading Order</h3>
          <p className="mt-2 text-sm text-red-700">
            {error || "The order could not be found. It may have been deleted."}
          </p>
          <Button 
            className="mt-4" 
            variant="outline" 
            onClick={() => refetch()}
          >
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin/e-commerce/orders">Orders</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href={`/admin/orders/${orderId}`}>Order #{order.orderNumber}</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Edit</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <div>
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/admin/orders/${orderId}`}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Order
          </Link>
        </Button>
      </div>

      {/* Order Form */}
      <OrderManagementForm 
        order={order}
        onSuccess={handleSuccess}
        onCancel={handleCancel}
      />
    </div>
  )
}