'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Ta<PERSON>,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import { 
  ArrowLeft, 
  Edit, 
  Download, 
  ShoppingCart, 
  User,
  MapPin,
  CreditCard,
  Package,
  Truck,
  Calendar,
  Mail,
  Phone
} from 'lucide-react'
import Link from 'next/link'
import { useZarFormatter } from '@/components/admin/zar-price-input'
import { useOrder, useOrderMutations } from '@/lib/ecommerce/hooks/use-orders'
import { OrderStatus } from '@/components/admin/order-status'
import type { Order } from '@/lib/ecommerce/types/order'

export default function OrderDetailPage() {
  const params = useParams()
  const router = useRouter()
  const { formatPrice } = useZarFormatter()
  const orderId = params.id as string

  const { order, loading, error, refetch } = useOrder({ orderId })
  const { updateOrderStatus, cancelOrder, loading: mutationLoading, error: mutationError } = useOrderMutations()

  // Handle order status change
  const handleStatusChange = async (orderId: string, newStatus: Order['status']) => {
    const success = await updateOrderStatus(orderId, newStatus)
    if (success) {
      await refetch()
      console.log('Order status updated successfully')
    } else {
      console.error('Failed to update order status:', mutationError)
    }
  }

  // Handle order cancellation
  const handleCancelOrder = async () => {
    const success = await cancelOrder(orderId)
    if (success) {
      await refetch()
      console.log('Order cancelled successfully')
    } else {
      console.error('Failed to cancel order:', mutationError)
    }
  }

  // Handle payment status change
  const handlePaymentStatusChange = async (orderId: string, newStatus: Order['paymentStatus']) => {
    try {
      const response = await fetch('/api/e-commerce/orders/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'updatePaymentStatus',
          orderIds: [orderId],
          data: { paymentStatus: newStatus }
        })
      })

      const result = await response.json()

      if (result.success) {
        await refetch()
        console.log('Payment status updated successfully')
      } else {
        console.error('Failed to update payment status:', result.error)
      }
    } catch (error) {
      console.error('Payment status update error:', error)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/e-commerce/orders">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Orders
            </Link>
          </Button>
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error || !order) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/e-commerce/orders">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Orders
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <ShoppingCart className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Order not found</h3>
              <p className="mt-1 text-sm text-gray-500">
                The order you're looking for doesn't exist or has been deleted.
              </p>
              <div className="mt-6">
                <Button asChild>
                  <Link href="/admin/e-commerce/orders">
                    Back to Orders
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline">Pending</Badge>
      case 'confirmed':
        return <Badge variant="secondary">Confirmed</Badge>
      case 'processing':
        return <Badge variant="default">Processing</Badge>
      case 'shipped':
        return <Badge variant="default">Shipped</Badge>
      case 'delivered':
        return <Badge variant="default">Delivered</Badge>
      case 'cancelled':
        return <Badge variant="destructive">Cancelled</Badge>
      case 'refunded':
        return <Badge variant="destructive">Refunded</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline">Pending</Badge>
      case 'paid':
        return <Badge variant="default">Paid</Badge>
      case 'partially_paid':
        return <Badge variant="secondary">Partially Paid</Badge>
      case 'refunded':
        return <Badge variant="destructive">Refunded</Badge>
      case 'voided':
        return <Badge variant="destructive">Voided</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/e-commerce/orders">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Orders
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Order #{order.orderNumber}</h1>
            <p className="text-muted-foreground">
              Placed on {new Date(order.createdAt).toLocaleDateString()} • {order.customerEmail}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Download Invoice
          </Button>
          <Button variant="outline">
            <Edit className="mr-2 h-4 w-4" />
            Edit Order
          </Button>
          <Button 
            variant="destructive" 
            onClick={handleCancelOrder}
            disabled={mutationLoading || order.status === 'cancelled'}
          >
            Cancel Order
          </Button>
        </div>
      </div>

      {/* Status and Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Order Status</p>
                {getStatusBadge(order.status)}
              </div>
              <ShoppingCart className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Payment Status</p>
                {getPaymentStatusBadge(order.paymentStatus)}
              </div>
              <CreditCard className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total</p>
                <p className="text-2xl font-bold">{formatPrice(Number(order.total))}</p>
              </div>
              <Package className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Items</p>
                <p className="text-2xl font-bold">{order.itemCount}</p>
              </div>
              <Package className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="items">Items</TabsTrigger>
          <TabsTrigger value="customer">Customer</TabsTrigger>
          <TabsTrigger value="fulfillment">Fulfillment</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Order Status Management */}
          <OrderStatus
            order={order}
            onStatusChange={handleStatusChange}
            onPaymentStatusChange={handlePaymentStatusChange}
            showActions={true}
          />

          <div className="grid gap-6 md:grid-cols-2">
            {/* Order Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Order Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Subtotal</span>
                  <span>{formatPrice(Number(order.subtotal))}</span>
                </div>
                {Number(order.totalDiscount) > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span>Discount</span>
                    <span>-{formatPrice(Number(order.totalDiscount))}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span>Shipping</span>
                  <span>{formatPrice(Number(order.totalShipping))}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax</span>
                  <span>{formatPrice(Number(order.totalTax))}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-bold text-lg">
                  <span>Total</span>
                  <span>{formatPrice(Number(order.total))}</span>
                </div>
              </CardContent>
            </Card>

            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle>Customer Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <User className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">
                      {order.customerFirstName} {order.customerLastName}
                    </p>
                    <p className="text-sm text-muted-foreground">Customer</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{order.customerEmail}</p>
                    <p className="text-sm text-muted-foreground">Email</p>
                  </div>
                </div>

                {order.customerPhone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{order.customerPhone}</p>
                      <p className="text-sm text-muted-foreground">Phone</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Addresses */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Billing Address</CardTitle>
              </CardHeader>
              <CardContent>
                {typeof order.billingAddress === 'object' && order.billingAddress ? (
                  <div className="space-y-1">
                    <p className="font-medium">
                      {order.billingAddress.firstName} {order.billingAddress.lastName}
                    </p>
                    <p>{order.billingAddress.address1}</p>
                    {order.billingAddress.address2 && <p>{order.billingAddress.address2}</p>}
                    <p>
                      {order.billingAddress.city}, {order.billingAddress.province} {order.billingAddress.postalCode}
                    </p>
                    <p>{order.billingAddress.country}</p>
                  </div>
                ) : (
                  <p className="text-muted-foreground">No billing address provided</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Shipping Address</CardTitle>
              </CardHeader>
              <CardContent>
                {typeof order.shippingAddress === 'object' && order.shippingAddress ? (
                  <div className="space-y-1">
                    <p className="font-medium">
                      {order.shippingAddress.firstName} {order.shippingAddress.lastName}
                    </p>
                    <p>{order.shippingAddress.address1}</p>
                    {order.shippingAddress.address2 && <p>{order.shippingAddress.address2}</p>}
                    <p>
                      {order.shippingAddress.city}, {order.shippingAddress.province} {order.shippingAddress.postalCode}
                    </p>
                    <p>{order.shippingAddress.country}</p>
                  </div>
                ) : (
                  <p className="text-muted-foreground">No shipping address provided</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Additional Order Information */}
          <div className="grid gap-6 md:grid-cols-2">
            {/* Risk Assessment */}
            <Card>
              <CardHeader>
                <CardTitle>Risk Assessment</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span>Risk Level</span>
                  <Badge variant={order.riskLevel === 'high' ? 'destructive' : order.riskLevel === 'medium' ? 'secondary' : 'outline'}>
                    {order.riskLevel?.charAt(0).toUpperCase() + order.riskLevel?.slice(1) || 'Low'}
                  </Badge>
                </div>
                {order.riskReasons && order.riskReasons.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">Risk Reasons</p>
                    <ul className="text-sm space-y-1">
                      {order.riskReasons.map((reason, index) => (
                        <li key={index} className="text-muted-foreground">• {reason}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Tags and Attributes */}
            <Card>
              <CardHeader>
                <CardTitle>Additional Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {order.tags && order.tags.length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">Tags</p>
                    <div className="flex flex-wrap gap-2">
                      {order.tags.map((tag, index) => (
                        <Badge key={index} variant="outline">{tag}</Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                {order.attributes && Object.keys(order.attributes).length > 0 && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground mb-2">Custom Attributes</p>
                    <div className="space-y-2">
                      {Object.entries(order.attributes).map(([key, value]) => (
                        <div key={key} className="flex justify-between text-sm">
                          <span className="font-medium">{key}:</span>
                          <span className="text-muted-foreground">{String(value)}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {(!order.tags || order.tags.length === 0) && (!order.attributes || Object.keys(order.attributes).length === 0) && (
                  <p className="text-sm text-muted-foreground">No additional information available</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Applied Discounts */}
          {order.appliedDiscounts && order.appliedDiscounts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Applied Discounts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {order.appliedDiscounts.map((discount, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">{discount.title}</p>
                        {discount.description && (
                          <p className="text-sm text-muted-foreground">{discount.description}</p>
                        )}
                        {discount.code && (
                          <p className="text-sm text-muted-foreground">Code: {discount.code}</p>
                        )}
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-green-600">
                          -{formatPrice(Number(discount.amount))}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {discount.type === 'percentage' ? `${discount.value}%` : 'Fixed Amount'}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="items" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Order Items</CardTitle>
              <CardDescription>
                Products included in this order
              </CardDescription>
            </CardHeader>
            <CardContent>
              {order.items && order.items.length > 0 ? (
                <div className="space-y-4">
                  {order.items.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="h-16 w-16 rounded-md bg-gray-100 flex items-center justify-center">
                          {item.productImage ? (
                            <img
                              src={item.productImage}
                              alt={item.productTitle}
                              className="h-16 w-16 rounded-md object-cover"
                            />
                          ) : (
                            <Package className="h-8 w-8 text-gray-400" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium">{item.productTitle}</h4>
                          {item.variantTitle && (
                            <p className="text-sm text-muted-foreground">{item.variantTitle}</p>
                          )}
                          <p className="text-sm text-muted-foreground">SKU: {item.sku || 'N/A'}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatPrice(Number(item.unitPrice))} × {item.quantity}</p>
                        <p className="text-lg font-bold">{formatPrice(Number(item.totalPrice))}</p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Package className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No items found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    This order doesn't have any items.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="customer" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Customer Details</CardTitle>
              <CardDescription>
                Complete customer information and order history
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <User className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Customer Details Coming Soon</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Detailed customer information and order history will be available here.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="fulfillment" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Fulfillment</CardTitle>
              <CardDescription>
                Shipping and delivery information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Truck className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Fulfillment Coming Soon</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Shipping and fulfillment tracking will be available here.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Order Timeline</CardTitle>
              <CardDescription>
                History of order status changes and events
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                      <Calendar className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>
                  <div>
                    <p className="font-medium">Order Created</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(order.createdAt).toLocaleString()}
                    </p>
                  </div>
                </div>

                {order.confirmedAt && (
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                        <Calendar className="h-4 w-4 text-green-600" />
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">Order Confirmed</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(order.confirmedAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                {order.processedAt && (
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-yellow-100 flex items-center justify-center">
                        <Calendar className="h-4 w-4 text-yellow-600" />
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">Order Processed</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(order.processedAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                {order.shippedAt && (
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
                        <Truck className="h-4 w-4 text-purple-600" />
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">Order Shipped</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(order.shippedAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                {order.deliveredAt && (
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
                        <Package className="h-4 w-4 text-green-600" />
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">Order Delivered</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(order.deliveredAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}

                {order.cancelledAt && (
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center">
                        <Calendar className="h-4 w-4 text-red-600" />
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">Order Cancelled</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(order.cancelledAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
