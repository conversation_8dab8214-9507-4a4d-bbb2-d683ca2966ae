'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  ArrowLeft,
  Package,
  Eye,
  Sparkles,
  CheckCircle,
  Info,
  Zap
} from 'lucide-react'
import Link from 'next/link'
import { ProductForm } from '@/components/admin/products/product-form'
import { toast } from 'sonner'
import type { Product } from '@/lib/ecommerce/types/product'

export default function NewProductPage() {
  const router = useRouter()
  const [creationStep, setCreationStep] = useState<'form' | 'success'>('form')
  const [createdProduct, setCreatedProduct] = useState<Product | null>(null)

  const handleProductSuccess = (product: Product) => {
    setCreatedProduct(product)
    setCreationStep('success')
    toast.success('Product created successfully!')
  }

  const handleCancel = () => {
    router.push('/admin/e-commerce/products')
  }

  const handleContinueEditing = () => {
    if (createdProduct) {
      router.push(`/admin/e-commerce/products/${createdProduct.id}/edit`)
    }
  }

  const handleViewProduct = () => {
    if (createdProduct) {
      router.push(`/admin/e-commerce/products/${createdProduct.id}`)
    }
  }

  const handleCreateAnother = () => {
    setCreatedProduct(null)
    setCreationStep('form')
  }

  if (creationStep === 'success' && createdProduct) {
    return (
      <div className="space-y-6">
        {/* Success Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/admin/e-commerce/products">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Products
              </Link>
            </Button>
            <div>
              <div className="flex items-center space-x-2">
                <h1 className="text-3xl font-bold tracking-tight">Product Created!</h1>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
              <p className="text-muted-foreground">
                Your product "{createdProduct.title}" has been created successfully
              </p>
            </div>
          </div>
          <Badge variant="default" className="bg-green-500">
            Created
          </Badge>
        </div>

        {/* Success Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="mr-2 h-5 w-5" />
              What's Next?
            </CardTitle>
            <CardDescription>
              Choose your next action to complete your product setup
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Your product is currently in <strong>draft</strong> status.
                Complete the setup to make it available for sale.
              </AlertDescription>
            </Alert>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className="border-2 border-primary/20">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center">
                    <Sparkles className="mr-2 h-5 w-5 text-primary" />
                    Continue Editing
                  </CardTitle>
                  <CardDescription>
                    Add more details, images, variants, and SEO settings
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button onClick={handleContinueEditing} className="w-full">
                    <Sparkles className="mr-2 h-4 w-4" />
                    Enhanced Editor
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center">
                    <Eye className="mr-2 h-5 w-5" />
                    Preview Product
                  </CardTitle>
                  <CardDescription>
                    See how your product looks to customers
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Button variant="outline" onClick={handleViewProduct} className="w-full">
                    <Eye className="mr-2 h-4 w-4" />
                    View Product
                  </Button>
                </CardContent>
              </Card>
            </div>

            <div className="flex items-center justify-between pt-4 border-t">
              <Button variant="outline" onClick={handleCreateAnother}>
                <Package className="mr-2 h-4 w-4" />
                Create Another Product
              </Button>
              <Button variant="outline" asChild>
                <Link href="/admin/e-commerce/products">
                  Back to Products
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/e-commerce/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create New Product</h1>
            <p className="text-muted-foreground">
              Add a new product to your catalog
            </p>
          </div>
        </div>
      </div>

      {/* Creation Tips */}
      <Alert>
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Pro tip:</strong> Start with the basic information below.
          You can always add more details like variants, images, and SEO settings after creation.
        </AlertDescription>
      </Alert>

      {/* Product Form */}
      <ProductForm
        onSuccess={handleProductSuccess}
        onCancel={handleCancel}
      />
    </div>
  )
}
