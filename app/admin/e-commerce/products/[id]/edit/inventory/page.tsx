'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { ArrowLeft, Archive, Save, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { useProduct, useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import { toast } from 'sonner'
import type { Product } from '@/lib/ecommerce/types'

// Form validation schema for inventory tab
const inventoryFormSchema = z.object({
  trackQuantity: z.boolean(),
  inventoryQuantity: z.number().min(0, 'Inventory must be non-negative').int('Inventory must be a whole number'),
  continueSellingWhenOutOfStock: z.boolean(),
})

type InventoryFormData = z.infer<typeof inventoryFormSchema>

export default function ProductInventoryEditPage() {
  const params = useParams()
  const router = useRouter()
  const productId = params.id as string

  const { product, loading, error } = useProduct({ productId })
  const { updateProduct, loading: isUpdating } = useProductMutations()

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<InventoryFormData>({
    resolver: zodResolver(inventoryFormSchema),
    mode: 'onChange',
    defaultValues: {
      trackQuantity: true,
      inventoryQuantity: 0,
      continueSellingWhenOutOfStock: false,
    },
  })

  // Update form when product loads
  useEffect(() => {
    if (product) {
      setValue('trackQuantity', product.trackQuantity ?? true)
      setValue('inventoryQuantity', product.inventoryQuantity || 0)
      setValue('continueSellingWhenOutOfStock', product.continueSellingWhenOutOfStock ?? false)
    }
  }, [product, setValue])

  const onSubmit = async (data: InventoryFormData) => {
    if (!product) return

    try {
      const updateData = {
        id: product.id,
        trackQuantity: data.trackQuantity,
        inventoryQuantity: data.inventoryQuantity,
        continueSellingWhenOutOfStock: data.continueSellingWhenOutOfStock,
      }

      const result = await updateProduct(updateData as any)
      if (result) {
        toast.success('Inventory settings updated successfully')
        //router.push(`/admin/e-commerce/products/${productId}`)
      }
    } catch (error) {
      toast.error('Failed to update inventory settings')
      console.error('Error updating product inventory:', error)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Loading...</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="h-8 bg-muted animate-pulse rounded" />
              <div className="h-32 bg-muted animate-pulse rounded" />
              <div className="h-8 bg-muted animate-pulse rounded" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Error</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-red-500">{error?.message || 'Product not found'}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Inventory</h1>
            <p className="text-muted-foreground">{product.title}</p>
          </div>
        </div>
        <Button
          type="submit"
          
          className="min-w-[140px]"
        >
          {(isUpdating || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </div>

      {/* Form Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Archive className="mr-2 h-5 w-5" />
            Inventory Management
          </CardTitle>
          <CardDescription>
            Manage stock levels and inventory tracking
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Track Quantity Toggle */}
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="space-y-1">
              <Label htmlFor="trackQuantity" className="text-base font-medium">
                Track quantity
              </Label>
              <p className="text-sm text-muted-foreground">
                Enable inventory tracking for this product
              </p>
            </div>
            <Switch
              id="trackQuantity"
              checked={watch('trackQuantity')}
              onCheckedChange={(checked) => setValue('trackQuantity', checked)}
            />
          </div>

          {/* Inventory Quantity */}
          {watch('trackQuantity') && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="inventoryQuantity">Quantity in stock</Label>
                <Input
                  id="inventoryQuantity"
                  type="number"
                  min="0"
                  step="1"
                  {...register('inventoryQuantity', { valueAsNumber: true })}
                  placeholder="0"
                  className="max-w-xs"
                />
                {errors.inventoryQuantity && (
                  <p className="text-sm text-red-500">{errors.inventoryQuantity.message}</p>
                )}
                <p className="text-sm text-muted-foreground">
                  Current stock level for this product
                </p>
              </div>

              {/* Continue Selling When Out of Stock */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="space-y-1">
                  <Label htmlFor="continueSellingWhenOutOfStock" className="text-base font-medium">
                    Continue selling when out of stock
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Allow customers to purchase this product even when inventory reaches zero
                  </p>
                </div>
                <Switch
                  id="continueSellingWhenOutOfStock"
                  checked={watch('continueSellingWhenOutOfStock')}
                  onCheckedChange={(checked) => setValue('continueSellingWhenOutOfStock', checked)}
                />
              </div>

              {/* Inventory Status Display */}
              <div className="p-4 bg-muted rounded-lg">
                <h4 className="font-medium mb-2">Inventory Status</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Current Stock:</span>
                    <span className={watch('inventoryQuantity') > 0 ? 'text-green-600' : 'text-red-600'}>
                      {watch('inventoryQuantity')} units
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Status:</span>
                    <span className={watch('inventoryQuantity') > 0 ? 'text-green-600' : 'text-red-600'}>
                      {watch('inventoryQuantity') > 0 ? 'In Stock' : 'Out of Stock'}
                    </span>
                  </div>
                  {watch('inventoryQuantity') === 0 && (
                    <div className="flex justify-between">
                      <span>Can Sell:</span>
                      <span className={watch('continueSellingWhenOutOfStock') ? 'text-green-600' : 'text-red-600'}>
                        {watch('continueSellingWhenOutOfStock') ? 'Yes (Backorder)' : 'No'}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* When tracking is disabled */}
          {!watch('trackQuantity') && (
            <div className="p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">Inventory Tracking Disabled</h4>
              <p className="text-sm text-muted-foreground">
                This product will always be available for purchase regardless of stock levels.
                Enable quantity tracking to manage inventory.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </form>
  )
}
