'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Search, Save, Loader2, Plus, X } from 'lucide-react'
import Link from 'next/link'
import { useProduct, useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import { toast } from 'sonner'
import type { Product } from '@/lib/ecommerce/types'

// Form validation schema for SEO tab
const seoFormSchema = z.object({
  seoTitle: z.string().max(60, 'SEO title should be under 60 characters').optional(),
  seoDescription: z.string().max(160, 'SEO description should be under 160 characters').optional(),
  seoKeywords: z.array(z.string()).max(10, 'Maximum 10 keywords allowed').optional(),
})

type SeoFormData = z.infer<typeof seoFormSchema>

export default function ProductSeoEditPage() {
  const params = useParams()
  const router = useRouter()
  const productId = params.id as string

  const { product, loading, error } = useProduct({ productId })
  const { updateProduct, loading: isUpdating } = useProductMutations()
  const [newKeyword, setNewKeyword] = useState('')

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<SeoFormData>({
    resolver: zodResolver(seoFormSchema),
    mode: 'onChange',
    defaultValues: {
      seoTitle: '',
      seoDescription: '',
      seoKeywords: [],
    },
  })

  const watchedKeywords = watch('seoKeywords')

  // Update form when product loads
  useEffect(() => {
    if (product) {
      setValue('seoTitle', product.seo?.title || '')
      setValue('seoDescription', product.seo?.description || '')
      setValue('seoKeywords', product.seo?.keywords || [])
    }
  }, [product, setValue])

  const onSubmit = async (data: SeoFormData) => {
    if (!product) return

    try {
      const updateData = {
        id: product.id,
        seo: {
          title: data.seoTitle || product.title,
          description: data.seoDescription || product.description.substring(0, 160),
          keywords: data.seoKeywords || []
        }
      }

      const result = await updateProduct(updateData as any)
      if (result) {
        toast.success('SEO settings updated successfully')
        //router.push(`/admin/e-commerce/products/${productId}`)
      }
    } catch (error) {
      toast.error('Failed to update SEO settings')
      console.error('Error updating product SEO:', error)
    }
  }

  const addKeyword = () => {
    if (newKeyword.trim() && !watchedKeywords?.includes(newKeyword.trim())) {
      setValue('seoKeywords', [...(watchedKeywords || []), newKeyword.trim()])
      setNewKeyword('')
    }
  }

  const removeKeyword = (keywordToRemove: string) => {
    setValue('seoKeywords', watchedKeywords?.filter(keyword => keyword !== keywordToRemove) || [])
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Loading...</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="h-8 bg-muted animate-pulse rounded" />
              <div className="h-32 bg-muted animate-pulse rounded" />
              <div className="h-8 bg-muted animate-pulse rounded" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Error</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-red-500">{error?.message || 'Product not found'}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">SEO Settings</h1>
            <p className="text-muted-foreground">{product.title}</p>
          </div>
        </div>
        <Button
          type="submit"
          
          className="min-w-[140px]"
        >
          {(isUpdating || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </div>

      {/* Form Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Search className="mr-2 h-5 w-5" />
            SEO Settings
          </CardTitle>
          <CardDescription>
            Optimize your product for search engines to improve visibility and ranking
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="seoTitle">SEO Title</Label>
            <Input
              id="seoTitle"
              {...register('seoTitle')}
              placeholder={product.title}
            />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Recommended: 50-60 characters</span>
              <span>{watch('seoTitle')?.length || 0}/60</span>
            </div>
            {errors.seoTitle && (
              <p className="text-sm text-red-500">{errors.seoTitle.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="seoDescription">SEO Description</Label>
            <Textarea
              id="seoDescription"
              {...register('seoDescription')}
              placeholder={product.description.substring(0, 160)}
              rows={3}
            />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Recommended: 150-160 characters</span>
              <span>{watch('seoDescription')?.length || 0}/160</span>
            </div>
            {errors.seoDescription && (
              <p className="text-sm text-red-500">{errors.seoDescription.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label>SEO Keywords</Label>
            <div className="flex flex-wrap gap-2 mb-2">
              {watchedKeywords?.map((keyword, index) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {keyword}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => removeKeyword(keyword)}
                  />
                </Badge>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                value={newKeyword}
                onChange={(e) => setNewKeyword(e.target.value)}
                placeholder="Add a keyword"
                onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}
              />
              <Button type="button" variant="outline" onClick={addKeyword}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">
              Maximum 10 keywords. Press Enter or click + to add.
            </p>
            {errors.seoKeywords && (
              <p className="text-sm text-red-500">{errors.seoKeywords.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* SEO Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Search Engine Preview</CardTitle>
          <CardDescription>
            How your product might appear in search results
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg p-4 bg-muted/50">
            <div className="space-y-1">
              <h3 className="text-lg font-medium text-blue-600 hover:underline cursor-pointer">
                {watch('seoTitle') || product.title}
              </h3>
              <p className="text-sm text-green-600">
                yourstore.com/products/{product.slug}
              </p>
              <p className="text-sm text-gray-600">
                {watch('seoDescription') || product.description.substring(0, 160)}
                {(watch('seoDescription') || product.description).length > 160 && '...'}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* SEO Tips */}
      <Card>
        <CardHeader>
          <CardTitle>SEO Best Practices</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p>• <strong>Title:</strong> Include your main keyword and make it compelling</p>
            <p>• <strong>Description:</strong> Write a clear, concise summary that encourages clicks</p>
            <p>• <strong>Keywords:</strong> Use relevant terms your customers might search for</p>
            <p>• <strong>Length:</strong> Keep titles under 60 characters and descriptions under 160</p>
            <p>• <strong>Uniqueness:</strong> Make sure each product has unique SEO content</p>
            <p>• <strong>Natural language:</strong> Write for humans first, search engines second</p>
          </div>
        </CardContent>
      </Card>
    </form>
  )
}
