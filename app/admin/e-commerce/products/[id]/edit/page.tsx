'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import {
  ArrowLeft,
  Package,
  DollarSign,
  Archive,
  Palette,
  Image,
  Search,
  CheckCircle,
  AlertCircle,
  Clock,
  Eye
} from 'lucide-react'
import Link from 'next/link'
import { useProduct } from '@/lib/ecommerce/hooks/use-products'
import { ProductForm } from '@/components/admin/products/product-form'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'

interface TabConfig {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  href: string
  description: string
  required?: boolean
}

export default function ProductEditPage() {
  const params = useParams()
  const router = useRouter()
  const productId = params.id as string

  const { product, loading, error } = useProduct({ productId })
  const [completionStatus, setCompletionStatus] = useState<Record<string, boolean>>({})

  const tabs: TabConfig[] = [
    {
      id: 'general',
      label: 'General',
      icon: Package,
      href: `/admin/e-commerce/products/${productId}/edit/general`,
      description: 'Basic product information',
      required: true
    },
    {
      id: 'pricing',
      label: 'Pricing',
      icon: DollarSign,
      href: `/admin/e-commerce/products/${productId}/edit/pricing`,
      description: 'Pricing and cost settings',
      required: true
    },
    {
      id: 'inventory',
      label: 'Inventory',
      icon: Archive,
      href: `/admin/e-commerce/products/${productId}/edit/inventory`,
      description: 'Stock and inventory management'
    },
    {
      id: 'variants',
      label: 'Variants',
      icon: Palette,
      href: `/admin/e-commerce/products/${productId}/edit/variants`,
      description: 'Product variations and options'
    },
    {
      id: 'media',
      label: 'Media',
      icon: Image,
      href: `/admin/e-commerce/products/${productId}/edit/media`,
      description: 'Product images and media'
    },
    {
      id: 'seo',
      label: 'SEO',
      icon: Search,
      href: `/admin/e-commerce/products/${productId}/edit/seo`,
      description: 'Search engine optimization'
    }
  ]

  // Calculate completion status
  useEffect(() => {
    if (product) {
      const status = {
        general: !!(product.title && product.description),
        pricing: !!(product.price?.amount && product.price.amount > 0),
        inventory: true, // Always considered complete
        variants: true, // Optional, so always complete
        media: !!(product.images && product.images.length > 0),
        seo: !!(product.seo?.title || product.seo?.description)
      }
      setCompletionStatus(status)
    }
  }, [product])

  const completionPercentage = Object.values(completionStatus).filter(Boolean).length / tabs.length * 100

  const handleCancel = () => {
    router.push(`/admin/e-commerce/products/${productId}`)
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/e-commerce/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Loading Product...</h1>
            <p className="text-muted-foreground">Please wait while we load the product details</p>
          </div>
        </div>

        {/* Enhanced Loading State */}
        <div className="grid gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="h-6 bg-muted animate-pulse rounded w-1/3" />
                <div className="h-4 bg-muted animate-pulse rounded w-2/3" />
                <div className="grid grid-cols-6 gap-2 mt-6">
                  {tabs.map((_, i) => (
                    <div key={i} className="h-12 bg-muted animate-pulse rounded" />
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="h-8 bg-muted animate-pulse rounded" />
                <div className="h-32 bg-muted animate-pulse rounded" />
                <div className="h-8 bg-muted animate-pulse rounded" />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/e-commerce/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Product Not Found</h1>
            <p className="text-muted-foreground">The product you're looking for doesn't exist</p>
          </div>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium">Product not found</h3>
              <p className="mt-2 text-sm text-muted-foreground">
                The product you're trying to edit doesn't exist or has been deleted.
                {error && (
                  <span className="block mt-2 text-destructive">
                    Error: {error.message}
                  </span>
                )}
              </p>
              <div className="mt-6 flex gap-3 justify-center">
                <Button variant="outline" asChild>
                  <Link href="/admin/e-commerce/products">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Products
                  </Link>
                </Button>
                <Button asChild>
                  <Link href="/admin/e-commerce/products/new">
                    <Package className="mr-2 h-4 w-4" />
                    Create New Product
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/e-commerce/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Product</h1>
            <p className="text-muted-foreground">{product.title}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant={product.status === 'active' ? 'default' : 'secondary'}>
            {product.status}
          </Badge>
        </div>
      </div>

      {/* Progress Indicator */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Setup Progress</span>
            <span className="text-sm text-muted-foreground">
              {Math.round(completionPercentage)}% complete
            </span>
          </div>
          <Progress value={completionPercentage} className="h-2" />
          <p className="text-xs text-muted-foreground mt-2">
            Complete all required sections to publish your product
          </p>
        </CardContent>
      </Card>

      {/* Enhanced Tab Navigation */}
      <Card>
        <CardContent className="p-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-0 border-b">
            {tabs.map((tab) => {
              const Icon = tab.icon
              const isCompleted = completionStatus[tab.id]
              const isRequired = tab.required

              return (
                <Link
                  key={tab.id}
                  href={tab.href}
                  className={cn(
                    "flex flex-col items-center p-4 text-center border-r border-b md:border-b-0 last:border-r-0 hover:bg-muted/50 transition-colors",
                    "group relative"
                  )}
                >
                  <div className="flex items-center justify-center w-10 h-10 rounded-full mb-2 transition-colors group-hover:scale-105">
                    <Icon className={cn(
                      "h-5 w-5 transition-colors",
                      isCompleted
                        ? "text-green-600"
                        : isRequired
                          ? "text-orange-500"
                          : "text-muted-foreground"
                    )} />
                    {isCompleted && (
                      <CheckCircle className="absolute -top-1 -right-1 h-4 w-4 text-green-600 bg-background rounded-full" />
                    )}
                    {isRequired && !isCompleted && (
                      <Clock className="absolute -top-1 -right-1 h-4 w-4 text-orange-500 bg-background rounded-full" />
                    )}
                  </div>
                  <span className="text-sm font-medium mb-1">{tab.label}</span>
                  <span className="text-xs text-muted-foreground leading-tight">
                    {tab.description}
                  </span>
                  {isRequired && (
                    <Badge variant="outline" className="mt-2 text-xs">
                      Required
                    </Badge>
                  )}
                </Link>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Quick Actions</h3>
              <p className="text-sm text-muted-foreground">
                Common tasks for product management
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/admin/e-commerce/products/${productId}/edit/general`}>
                  <Package className="mr-2 h-4 w-4" />
                  Edit Details
                </Link>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href={`/admin/e-commerce/products/${productId}/edit/media`}>
                  <Image className="mr-2 h-4 w-4" />
                  Add Images
                </Link>
              </Button>
              <Button variant="outline" size="sm" asChild>
                <Link href={`/admin/e-commerce/products/${productId}/edit/variants`}>
                  <Palette className="mr-2 h-4 w-4" />
                  Manage Variants
                </Link>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Default content - show the full form 
      <ProductForm
        product={product as any}
        onSuccess={() => {
          toast.success('Product updated successfully')
          router.push(`/admin/e-commerce/products/${productId}`)
        }}
        onCancel={handleCancel}
      />*/}
    </div>
  )
}
