'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  ArrowLeft,
  Package,
  Save,
  Loader2,
  CheckCircle,
  AlertCircle,
  Info,
  Eye,
  Globe
} from 'lucide-react'
import Link from 'next/link'
import { useProduct, useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import type { Product } from '@/lib/ecommerce/types/product'

// Form validation schema for general tab
const generalFormSchema = z.object({
  title: z.string()
    .min(1, 'Product title is required')
    .max(255, 'Title is too long')
    .refine(val => val.trim().length > 0, 'Title cannot be empty'),
  description: z.string()
    .min(10, 'Description must be at least 10 characters')
    .max(5000, 'Description is too long'),
  slug: z.string()
    .optional()
    .refine(val => !val || /^[a-z0-9-]+$/.test(val), 'Slug can only contain lowercase letters, numbers, and hyphens'),
  vendor: z.string().max(100, 'Vendor name is too long').optional(),
  productType: z.string().max(100, 'Product type is too long').optional(),
  status: z.enum(['active', 'draft', 'archived']),
  weight: z.number().min(0, 'Weight cannot be negative').optional().nullable(),
  weightUnit: z.string().optional(),
  requiresShipping: z.boolean(),
  isTaxable: z.boolean(),
})

type GeneralFormData = z.infer<typeof generalFormSchema>

export default function ProductGeneralEditPage() {
  const params = useParams()
  const router = useRouter()
  const productId = params.id as string

  const { product, loading, error } = useProduct({ productId })
  const { updateProduct, loading: isUpdating } = useProductMutations()
  const [isPreviewMode, setIsPreviewMode] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<GeneralFormData>({
    resolver: zodResolver(generalFormSchema),
    mode: 'onChange',
    defaultValues: {
      title: '',
      description: '',
      slug: '',
      vendor: '',
      productType: '',
      status: 'draft',
      weight: null,
      weightUnit: 'kg',
      requiresShipping: true,
      isTaxable: true,
    },
  })

  const watchedTitle = watch('title')

  // Update form when product loads
  useEffect(() => {
    if (product) {
      setValue('title', product.title || '')
      setValue('description', product.description || '')
      setValue('slug', product.slug || '')
      setValue('vendor', product.vendor || '')
      setValue('productType', product.productType || '')
      setValue('status', product.status || 'draft')
      setValue('weight', product.weight || null)
      setValue('weightUnit', product.weightUnit || 'kg')
      setValue('requiresShipping', product.requiresShipping ?? true)
      setValue('isTaxable', product.isTaxable ?? true)
    }
  }, [product, setValue])

  // Auto-generate slug from title
  useEffect(() => {
    if (watchedTitle && !product?.slug) {
      const slug = watchedTitle
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')
      setValue('slug', slug)
    }
  }, [watchedTitle, setValue, product?.slug])

  const onSubmit = async (data: GeneralFormData) => {
    if (!product) return

    try {
      const updateData = {
        id: product.id,
        ...data,
        weight: data.weight || undefined,
      }

      const result = await updateProduct(updateData as any)
      if (result) {
        toast.success('General information updated successfully')
        //router.push(`/admin/e-commerce/products/${productId}`)
      }
    } catch (error) {
      toast.error('Failed to update product')
      console.error('Error updating product:', error)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Loading...</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="h-8 bg-muted animate-pulse rounded" />
              <div className="h-32 bg-muted animate-pulse rounded" />
              <div className="h-8 bg-muted animate-pulse rounded" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Error</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-red-500">{error?.message || 'Product not found'}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <div className="flex items-center space-x-2">
              <h1 className="text-3xl font-bold tracking-tight">General Information</h1>
              <Badge variant={product.status === 'active' ? 'default' : 'secondary'}>
                {product.status}
              </Badge>
            </div>
            <p className="text-muted-foreground">{product.title}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsPreviewMode(!isPreviewMode)}
          >
            <Eye className="mr-2 h-4 w-4" />
            {isPreviewMode ? 'Edit' : 'Preview'}
          </Button>
          <Button
            type="submit"
            className="min-w-[140px]"
          >
            {(isUpdating || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            <Save className="mr-2 h-4 w-4" />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Form Progress */}
      {isDirty && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            You have unsaved changes. Don't forget to save your work.
          </AlertDescription>
        </Alert>
      )}

      {/* Form Content */}
      {!isPreviewMode ? (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="mr-2 h-5 w-5" />
              Basic Information
            </CardTitle>
            <CardDescription>
              Essential product details and description. Required fields are marked with an asterisk (*).
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="title" className="flex items-center">
                  Product Title *
                  <span className="ml-1 text-xs text-muted-foreground">
                    ({watch('title')?.length || 0}/255)
                  </span>
                </Label>
                <Input
                  id="title"
                  {...register('title')}
                  placeholder="Enter a descriptive product title"
                  className={cn(
                    errors.title && "border-destructive focus:border-destructive"
                  )}
                />
                {errors.title ? (
                  <p className="text-sm text-destructive flex items-center">
                    <AlertCircle className="mr-1 h-3 w-3" />
                    {errors.title.message}
                  </p>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    Choose a clear, descriptive title that customers will search for
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug" className="flex items-center">
                  URL Slug
                  <span className="ml-1 text-xs text-muted-foreground">
                    (Auto-generated from title)
                  </span>
                </Label>
                <div className="relative">
                  <Input
                    id="slug"
                    {...register('slug')}
                    placeholder="product-url-slug"
                    className={cn(
                      errors.slug && "border-destructive focus:border-destructive"
                    )}
                  />
                  <Globe className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                </div>
                {errors.slug ? (
                  <p className="text-sm text-destructive flex items-center">
                    <AlertCircle className="mr-1 h-3 w-3" />
                    {errors.slug.message}
                  </p>
                ) : (
                  <p className="text-sm text-muted-foreground">
                    URL-friendly version of the product title
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="flex items-center">
                Description *
                <span className="ml-1 text-xs text-muted-foreground">
                  ({watch('description')?.length || 0}/5000)
                </span>
              </Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="Provide a detailed description of your product, including features, benefits, and specifications..."
                rows={6}
                className={cn(
                  "resize-none",
                  errors.description && "border-destructive focus:border-destructive"
                )}
              />
              {errors.description ? (
                <p className="text-sm text-destructive flex items-center">
                  <AlertCircle className="mr-1 h-3 w-3" />
                  {errors.description.message}
                </p>
              ) : (
                <p className="text-sm text-muted-foreground">
                  Write a compelling description that highlights key features and benefits
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="vendor">Vendor/Brand</Label>
                <Input
                  id="vendor"
                  {...register('vendor')}
                  placeholder="e.g., Nike, Apple, Local Brand"
                />
                <p className="text-sm text-muted-foreground">
                  The brand or manufacturer of this product
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="productType">Product Type</Label>
                <Input
                  id="productType"
                  {...register('productType')}
                  placeholder="e.g., T-Shirt, Dress, Shoes, Electronics"
                />
                <p className="text-sm text-muted-foreground">
                  Category or type of product for organization
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="status">Product Status</Label>
                <Select
                  value={watch('status')}
                  onValueChange={(value: 'active' | 'draft' | 'archived') =>
                    setValue('status', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="draft">
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2" />
                        Draft
                      </div>
                    </SelectItem>
                    <SelectItem value="active">
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-green-500 mr-2" />
                        Active
                      </div>
                    </SelectItem>
                    <SelectItem value="archived">
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-gray-500 mr-2" />
                        Archived
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-muted-foreground">
                  Controls product visibility in your store
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="weight">Weight</Label>
                <Input
                  id="weight"
                  type="number"
                  step="0.01"
                  min="0"
                  {...register('weight', { valueAsNumber: true })}
                  placeholder="0.00"
                  className={cn(
                    errors.weight && "border-destructive focus:border-destructive"
                  )}
                />
                {errors.weight && (
                  <p className="text-sm text-destructive flex items-center">
                    <AlertCircle className="mr-1 h-3 w-3" />
                    {errors.weight.message}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="weightUnit">Weight Unit</Label>
                <Select
                  value={watch('weightUnit')}
                  onValueChange={(value) => setValue('weightUnit', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="kg">Kilograms (kg)</SelectItem>
                    <SelectItem value="g">Grams (g)</SelectItem>
                    <SelectItem value="lb">Pounds (lb)</SelectItem>
                    <SelectItem value="oz">Ounces (oz)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium">Shipping & Tax Settings</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label htmlFor="requiresShipping" className="text-base font-medium">
                      Requires shipping
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      This product needs to be shipped to customers
                    </p>
                  </div>
                  <Switch
                    id="requiresShipping"
                    checked={watch('requiresShipping')}
                    onCheckedChange={(checked) => setValue('requiresShipping', checked)}
                  />
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label htmlFor="isTaxable" className="text-base font-medium">
                      Taxable product
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Apply taxes to this product
                    </p>
                  </div>
                  <Switch
                    id="isTaxable"
                    checked={watch('isTaxable')}
                    onCheckedChange={(checked) => setValue('isTaxable', checked)}
                  />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        /* Preview Mode */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Eye className="mr-2 h-5 w-5" />
              Product Preview
            </CardTitle>
            <CardDescription>
              How your product information will appear to customers
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div>
                <h2 className="text-2xl font-bold">{watch('title') || 'Product Title'}</h2>
                <div className="flex items-center space-x-2 mt-2">
                  <Badge variant={watch('status') === 'active' ? 'default' : 'secondary'}>
                    {watch('status')}
                  </Badge>
                  {watch('vendor') && (
                    <span className="text-sm text-muted-foreground">by {watch('vendor')}</span>
                  )}
                </div>
              </div>

              <div>
                <h3 className="font-medium mb-2">Description</h3>
                <p className="text-muted-foreground whitespace-pre-wrap">
                  {watch('description') || 'No description provided'}
                </p>
              </div>

              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                {watch('productType') && (
                  <div>
                    <span className="font-medium">Type:</span>
                    <p className="text-muted-foreground">{watch('productType')}</p>
                  </div>
                )}
                {watch('weight') && (
                  <div>
                    <span className="font-medium">Weight:</span>
                    <p className="text-muted-foreground">
                      {watch('weight')} {watch('weightUnit')}
                    </p>
                  </div>
                )}
                <div>
                  <span className="font-medium">Shipping:</span>
                  <p className="text-muted-foreground">
                    {watch('requiresShipping') ? 'Required' : 'Not required'}
                  </p>
                </div>
                <div>
                  <span className="font-medium">Taxable:</span>
                  <p className="text-muted-foreground">
                    {watch('isTaxable') ? 'Yes' : 'No'}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </form>
  )
}
