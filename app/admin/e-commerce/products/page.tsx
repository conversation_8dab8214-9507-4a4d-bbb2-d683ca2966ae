import { EnhancedProductDashboard } from '@/components/admin/products/enhanced-product-dashboard'
import { ProductService } from '@/lib/ecommerce/services/product-service'

async function getProductStats() {
  try {
    const productService = new ProductService()
    
    // Get all products
    const allProducts = await productService.searchProducts({
      filters: {},
      limit: 1000 // Adjust based on your needs
    })
    
    if (!allProducts.success || !allProducts.data) {
      throw new Error('Failed to fetch products')
    }
    
    const products = allProducts.data.data || []
    
    // Get active and draft counts
    const active = products.filter((p: any) => p.status === 'active').length
    const draft = products.filter((p: any) => p.status === 'draft').length
    
    // Get low stock and out of stock counts from the full product list
    // since we can't filter by inventory status directly
    const lowStock = products.filter((p: any) => 
      p.inventoryQuantity > 0 && p.inventoryQuantity <= (p.lowStockThreshold || 5)
    ).length
    
    const outOfStock = products.filter((p: any) => 
      p.inventoryQuantity <= 0
    ).length
    
    const total = products.length
    const totalValue = products.reduce((sum: number, p: any) => sum + (p.price || 0), 0)
    const averagePrice = total > 0 ? totalValue / total : 0
    
    const oneWeekAgo = new Date()
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
    const recentlyUpdated = products.filter((p: any) => 
      new Date(p.updatedAt) > oneWeekAgo
    ).length

    return {
      total,
      active,
      draft,
      lowStock,
      outOfStock,
      totalValue,
      averagePrice,
      recentlyUpdated
    }
  } catch (error) {
    console.error('Failed to fetch product stats:', error)
    return null
  }
}

export default async function ProductsPage() {
  const initialStats = await getProductStats()
  return <EnhancedProductDashboard initialStats={initialStats || undefined} />
}
