'use client'

import { useCallback } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'
import { OrderFulfillmentCenter as OrderFulfillmentCenterList } from '@/components/admin/orders/order-fulfillment-center-list'
import { useOrders } from '@/lib/ecommerce/hooks/use-orders'
import type { Order } from '@/lib/ecommerce/types'

export default function OrderFulfillmentPage() {
  const router = useRouter()
  
  const { orders, loading, error, refetch } = useOrders({
    initialParams: {
      page: 1,
      limit: 100,
      sortBy: 'createdAt',
      sortOrder: 'desc',
      filters: {
        status: ['processing', 'confirmed']
      }
    },
    autoFetch: true
  })

  const handleViewOrder = useCallback((order: Order) => {
    router.push(`/admin/orders/${order.id}`)
  }, [router])

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin/e-commerce/orders">Orders</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Fulfillment Center</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Back Button */}
      <div>
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/e-commerce/orders">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Orders
          </Link>
        </Button>
      </div>

      {/* Fulfillment Center */}
      <OrderFulfillmentCenterList 
        orders={orders}
        onRefresh={refetch}
        onViewOrder={handleViewOrder}
        loading={loading}
        error={error}
      />
    </div>
  )
}