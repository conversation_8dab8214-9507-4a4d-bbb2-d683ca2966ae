"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, Upload, Grid3X3, List, Filter, Plus } from "lucide-react";
import { useMedia, MediaFile } from "@/hooks/use-media";
import { MediaGrid } from "@/components/admin/media/media-grid";
import { MediaList } from "@/components/admin/media/media-list";
import { MediaUploadDialog } from "@/components/admin/media/media-upload-dialog";
import { MediaPreviewDialog } from "@/components/admin/media/media-preview-dialog";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { MediaUploadInline } from "@/components/admin/media/media-upload-inline";

export default function MediaPage() {
  const [viewMode, setViewMode] = useState<"grid" | "list" | "upload">("grid");
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [showPreviewDialog, setShowPreviewDialog] = useState(false);
  const [selectedFile, setSelectedFile] = useState<MediaFile | null>(null);

  const {
    files,
    pagination,
    loading,
    error,
    filters,
    setFilters,
    setPage,
    refetch,
    uploadFiles,
    deleteFile,
    updateFile,
  } = useMedia({
    autoFetch: true,
    initialLimit: 24,
  });

  const handleFilterChange = (key: string, value: string) => {
    setFilters({
      ...filters,
      [key]: value === "all" ? undefined : value,
    });
    setPage(1);
  };

  const handleSearch = (search: string) => {
    setFilters({
      ...filters,
      search: search || undefined,
    });
    setPage(1);
  };

  const handlePreview = (file: MediaFile) => {
    setSelectedFile(file);
    setShowPreviewDialog(true);
  };

  const handleDownload = (file: MediaFile) => {
    window.open(file.downloadUrl, "_blank");
  };

  const handleDelete = async (file: MediaFile) => {
    if (confirm(`Are you sure you want to delete "${file.name}"?`)) {
      await deleteFile(file.id);
    }
  };

  const handleUploadComplete = () => {
    setShowUploadDialog(false);
    refetch();
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
     
      <Card>
        <CardHeader>
          <CardTitle>Media Files</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Filters */}
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search files..."
                className="pl-10"
                value={filters.search || ""}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>

            {
              /*MEDIA <-> UPLOADER Buttons - Switches to uploader or media grid*/
              viewMode === "grid" || viewMode === "list" ? (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setViewMode("upload")}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Upload New File
                </Button>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setViewMode("grid")}
                >
                  <Grid3X3 className="mr-2 h-4 w-4" />
                  View Files
                </Button>
              )
            }

            <Select
              value={filters.type || "all"}
              onValueChange={(value) => handleFilterChange("type", value)}
            >
              <SelectTrigger className="w-[140px]">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="image">Images</SelectItem>
                <SelectItem value="video">Videos</SelectItem>
                <SelectItem value="audio">Audio</SelectItem>
                <SelectItem value="document">Documents</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex items-center space-x-1">
              <Button
                variant={viewMode === "grid" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("grid")}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === "list" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("list")}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Content */}
          <div className="min-h-[400px] max-h-[70vh] overflow-y-auto">
            {viewMode === "grid" ? (
              <MediaGrid
                files={files}
                loading={loading}
                onPreview={handlePreview}
                onDelete={handleDelete}
                onDownload={handleDownload}
                onUpdate={updateFile}
                showActions={true}
              />
            ) : viewMode === "list" ? (
              <MediaList
                files={files}
                loading={loading}
                onPreview={handlePreview}
                onDelete={handleDelete}
                onDownload={handleDownload}
              />
            ) : (
              <MediaUploadInline
                onUpload={uploadFiles}
                onClose={handleUploadComplete}
              />
            )}
          </div>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">
                Page {pagination.page} of {pagination.totalPages} (
                {pagination.total} files)
              </p>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(pagination.page + 1)}
                  disabled={pagination.page >= pagination.totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload Dialog */}
      <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
        <MediaUploadDialog
          onUpload={uploadFiles}
          onClose={handleUploadComplete}
        />
      </Dialog>

      {/* Preview Dialog */}
      {selectedFile && (
        <MediaPreviewDialog
          file={selectedFile}
          open={showPreviewDialog}
          onOpenChange={setShowPreviewDialog}
          onUpdate={updateFile}
          onDelete={handleDelete}
          onDownload={handleDownload}
        />
      )}
    </div>
  );
}
