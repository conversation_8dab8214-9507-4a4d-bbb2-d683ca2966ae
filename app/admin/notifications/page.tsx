'use client';

import { useState } from 'react';
import { useNotificationCenter } from '@/lib/notifications/hooks/use-notification-center';
import { useSession } from 'next-auth/react';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import { Tabs, Tabs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Bell, 
  Package, 
  ShoppingCart, 
  Users, 
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  DollarSign,
  Filter,
  RefreshCw,
  Search,
  Trash2,
  CheckCheck,
  ExternalLink
} from 'lucide-react';
import { NotificationType, NotificationPriority } from '@/lib/notifications/types';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';

export default function NotificationsPage() {
  const { data: session } = useSession();
  const userId = session?.user?.id;
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  
  const {
    notifications,
    groupedNotifications,
    unreadCount,
    totalCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refresh,
    setSearchFilter,
    setUnreadOnlyFilter,
    setPriorityFilter,
    setTypeFilter,
    clearFilters,
    activeFilters
  } = useNotificationCenter({
    userId,
    enableRealTime: true,
    maxNotifications: 100
  });

  // Map notification type to icon
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case NotificationType.ORDER_CONFIRMATION:
      case NotificationType.ORDER_STATUS_UPDATE:
        return ShoppingCart;
      case NotificationType.INVENTORY_ALERT:
        return Package;
      case NotificationType.WELCOME:
      case NotificationType.EMAIL_VERIFICATION:
      case NotificationType.CUSTOMER_REGISTERED:
        return Users;
      case NotificationType.PAYMENT_CONFIRMATION:
      case NotificationType.PAYMENT_FAILED:
        return DollarSign;
      case NotificationType.SHIPPING_UPDATE:
      case NotificationType.DELIVERY_CONFIRMATION:
        return TrendingUp;
      case NotificationType.PROMOTIONAL:
      case NotificationType.NEWSLETTER:
        return Bell;
      case NotificationType.PASSWORD_RESET:
        return AlertTriangle;
      case NotificationType.CUSTOM:
        return CheckCircle;
      default:
        return Bell;
    }
  };

  // Map priority to color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case NotificationPriority.URGENT:
        return 'text-red-600';
      case NotificationPriority.HIGH:
        return 'text-orange-600';
      case NotificationPriority.NORMAL:
        return 'text-blue-600';
      case NotificationPriority.LOW:
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  };

  // Map priority to badge
  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case NotificationPriority.URGENT:
        return <Badge variant="destructive">Urgent</Badge>;
      case NotificationPriority.HIGH:
        return <Badge variant="secondary" className="bg-orange-100 text-orange-800">High</Badge>;
      case NotificationPriority.NORMAL:
        return <Badge variant="outline">Normal</Badge>;
      case NotificationPriority.LOW:
        return <Badge variant="outline" className="text-gray-500">Low</Badge>;
      default:
        return null;
    }
  };

  // Format timestamp to relative time
  const formatTimestamp = (timestamp: string) => {
    const now = new Date();
    const notificationTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - notificationTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return notificationTime.toLocaleDateString('en-ZA', {
      month: 'short',
      day: 'numeric',
      year: notificationTime.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    });
  };

  // Handle notification click
  const handleNotificationClick = async (notificationId: string, actionUrl?: string) => {
    await markAsRead(notificationId);
    
    if (actionUrl) {
      if (actionUrl.startsWith('http')) {
        window.open(actionUrl, '_blank');
      } else {
        router.push(actionUrl);
      }
    }
  };

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    setSearchFilter(query);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Heading 
          title="Notifications" 
          description={`You have ${unreadCount} unread notifications`} 
        />
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={refresh}
            className="flex items-center gap-1"
          >
            <RefreshCw className="h-4 w-4" />
            Refresh
          </Button>
          {unreadCount > 0 && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => markAllAsRead()}
              className="flex items-center gap-1"
            >
              <CheckCheck className="h-4 w-4" />
              Mark All Read
            </Button>
          )}
        </div>
      </div>
      <Separator />
      
      <div className="flex flex-col md:flex-row gap-6">
        {/* Filters */}
        <div className="w-full md:w-64 space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Filters</CardTitle>
              <CardDescription>Filter your notifications</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <Input
                  type="text"
                  placeholder="Search notifications..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="h-9"
                />
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <div className="flex items-center gap-2">
                  <Button 
                    variant={activeFilters.unreadOnly ? "default" : "outline"} 
                    size="sm"
                    onClick={() => setUnreadOnlyFilter(!activeFilters.unreadOnly)}
                    className="flex-1"
                  >
                    Unread
                  </Button>
                  <Button 
                    variant={!activeFilters.unreadOnly ? "default" : "outline"} 
                    size="sm"
                    onClick={() => setUnreadOnlyFilter(false)}
                    className="flex-1"
                  >
                    All
                  </Button>
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Priority</label>
                <div className="grid grid-cols-2 gap-2">
                  <Button 
                    variant={activeFilters.priority === NotificationPriority.URGENT ? "default" : "outline"} 
                    size="sm"
                    onClick={() => setPriorityFilter(
                      activeFilters.priority === NotificationPriority.URGENT ? null : NotificationPriority.URGENT
                    )}
                  >
                    Urgent
                  </Button>
                  <Button 
                    variant={activeFilters.priority === NotificationPriority.HIGH ? "default" : "outline"} 
                    size="sm"
                    onClick={() => setPriorityFilter(
                      activeFilters.priority === NotificationPriority.HIGH ? null : NotificationPriority.HIGH
                    )}
                  >
                    High
                  </Button>
                  <Button 
                    variant={activeFilters.priority === NotificationPriority.NORMAL ? "default" : "outline"} 
                    size="sm"
                    onClick={() => setPriorityFilter(
                      activeFilters.priority === NotificationPriority.NORMAL ? null : NotificationPriority.NORMAL
                    )}
                  >
                    Normal
                  </Button>
                  <Button 
                    variant={activeFilters.priority === NotificationPriority.LOW ? "default" : "outline"} 
                    size="sm"
                    onClick={() => setPriorityFilter(
                      activeFilters.priority === NotificationPriority.LOW ? null : NotificationPriority.LOW
                    )}
                  >
                    Low
                  </Button>
                </div>
              </div>
              
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={clearFilters}
                className="w-full"
              >
                Clear Filters
              </Button>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Total:</span>
                <Badge variant="outline">{totalCount}</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Unread:</span>
                <Badge>{unreadCount}</Badge>
              </div>
              <Separator />
              <div className="space-y-1">
                <span className="text-sm font-medium">By Priority</span>
                <div className="space-y-1 pl-2">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-red-600">Urgent</span>
                    <Badge variant="outline" className="text-red-600">{
                      notifications.filter(n => n.priority === NotificationPriority.URGENT).length
                    }</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-orange-600">High</span>
                    <Badge variant="outline" className="text-orange-600">{
                      notifications.filter(n => n.priority === NotificationPriority.HIGH).length
                    }</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-blue-600">Normal</span>
                    <Badge variant="outline" className="text-blue-600">{
                      notifications.filter(n => n.priority === NotificationPriority.NORMAL).length
                    }</Badge>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-600">Low</span>
                    <Badge variant="outline" className="text-gray-600">{
                      notifications.filter(n => n.priority === NotificationPriority.LOW).length
                    }</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Notification List */}
        <div className="flex-1">
          <Card className="h-full">
            <CardHeader className="pb-3">
              <CardTitle>All Notifications</CardTitle>
              <CardDescription>
                {activeFilters.search || activeFilters.unreadOnly || activeFilters.priority ? 
                  'Filtered notifications' : 
                  'Your recent notifications'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="space-y-4">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <div key={i} className="flex items-start space-x-4 p-4 border rounded-md">
                      <Skeleton className="h-8 w-8 rounded-full" />
                      <div className="space-y-2 flex-1">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-3 w-full" />
                        <Skeleton className="h-3 w-1/2" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : error ? (
                <div className="p-4 text-center text-red-500">
                  Failed to load notifications. Please try again.
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-4 text-center text-muted-foreground">
                  {activeFilters.search || activeFilters.unreadOnly || activeFilters.priority ? 
                    'No notifications match your filters' : 
                    'No notifications yet'}
                </div>
              ) : (
                <ScrollArea className="h-[calc(100vh-20rem)]">
                  {groupedNotifications.map((group) => (
                    <div key={group.date} className="mb-6">
                      <h3 className="text-sm font-medium mb-2 px-2">{group.date}</h3>
                      <div className="space-y-2">
                        {group.notifications.map((notification) => {
                          const Icon = getNotificationIcon(notification.type);
                          const isUnread = !notification.readAt;
                          const actionUrl = notification.metadata?.actionUrl;
                          
                          return (
                            <div 
                              key={notification.id}
                              className={cn(
                                "flex items-start space-x-4 p-4 border rounded-md cursor-pointer hover:bg-accent/50 transition-colors",
                                isUnread && "bg-blue-50 hover:bg-blue-100"
                              )}
                              onClick={() => handleNotificationClick(notification.id, actionUrl)}
                            >
                              <div className={cn("mt-1", getPriorityColor(notification.priority))}>
                                <Icon className="h-5 w-5" />
                              </div>
                              <div className="flex-1 space-y-1">
                                <div className="flex items-center justify-between">
                                  <h4 className="font-medium">
                                    {notification.title}
                                  </h4>
                                  <div className="flex items-center space-x-2">
                                    {getPriorityBadge(notification.priority)}
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-8 w-8 p-0"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        deleteNotification(notification.id);
                                      }}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                                <p className="text-sm text-muted-foreground">
                                  {notification.content}
                                </p>
                                <div className="flex items-center justify-between">
                                  <p className="text-xs text-muted-foreground">
                                    {formatTimestamp(notification.createdAt)}
                                  </p>
                                  {actionUrl && (
                                    <div className="flex items-center text-xs text-blue-600">
                                      <ExternalLink className="h-3 w-3 mr-1" />
                                      {actionUrl.includes('/admin/') ? 'View details' : 'Open link'}
                                    </div>
                                  )}
                                </div>
                              </div>
                              {isUnread && (
                                <div className="w-2 h-2 bg-blue-600 rounded-full mt-2" />
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  ))}
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}