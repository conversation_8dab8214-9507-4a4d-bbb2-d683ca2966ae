'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Heading } from '@/components/ui/heading';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Cloud, HardDrive, Database, Info } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useMediaSettings } from '@/hooks/use-media-settings';

const formSchema = z.object({
  storageProvider: z.enum(['local', 's3', 'cloudinary', 'google', 'appwrite']),
  localSettings: z.object({
    uploadDir: z.string().min(1, 'Upload directory is required'),
    maxFileSize: z.number().min(1, 'Max file size must be at least 1MB'),
    allowedFileTypes: z.string().min(1, 'Allowed file types are required'),
  }).optional(),
  s3Settings: z.object({
    accessKeyId: z.string().min(1, 'Access Key ID is required'),
    secretAccessKey: z.string().min(1, 'Secret Access Key is required'),
    region: z.string().min(1, 'Region is required'),
    bucket: z.string().min(1, 'Bucket name is required'),
    endpoint: z.string().optional(),
  }).optional(),
  cloudinarySettings: z.object({
    cloudName: z.string().min(1, 'Cloud name is required'),
    apiKey: z.string().min(1, 'API key is required'),
    apiSecret: z.string().min(1, 'API secret is required'),
    uploadPreset: z.string().optional(),
  }).optional(),
  googleSettings: z.object({
    projectId: z.string().min(1, 'Project ID is required'),
    bucketName: z.string().min(1, 'Bucket name is required'),
    keyFilePath: z.string().min(1, 'Key file path is required'),
  }).optional(),
  appwriteSettings: z.object({
    bucketId: z.string().min(1, 'Bucket ID is required'),
    maximumFileSize: z.number().min(1, 'Maximum file size must be at least 1 byte'),
    allowedFileExtensions: z.array(z.string()).min(1, 'At least one file extension is required'),
    compression: z.boolean().default(false),
    encryption: z.boolean().default(false),
    antivirus: z.boolean().default(false),
  }).optional(),
  imageOptimization: z.object({
    enabled: z.boolean(),
    quality: z.number().min(1).max(100).optional(),
    maxWidth: z.number().min(100).optional(),
    maxHeight: z.number().min(100).optional(),
    formats: z.array(z.string()).optional(),
  }),
  cacheControl: z.object({
    enabled: z.boolean(),
    maxAge: z.number().min(0).optional(),
    staleWhileRevalidate: z.number().min(0).optional(),
  }),
});

type FormValues = z.infer<typeof formSchema>;

// Default values for the form
const defaultValues: FormValues = {
  storageProvider: 'appwrite',
  localSettings: {
    uploadDir: '/uploads',
    maxFileSize: 10,
    allowedFileTypes: 'jpg,jpeg,png,gif,webp,svg,pdf',
  },
  s3Settings: {
    accessKeyId: '',
    secretAccessKey: '',
    region: 'us-east-1',
    bucket: '',
    endpoint: '',
  },
  cloudinarySettings: {
    cloudName: '',
    apiKey: '',
    apiSecret: '',
    uploadPreset: '',
  },
  googleSettings: {
    projectId: '',
    bucketName: '',
    keyFilePath: '',
  },
  appwriteSettings: {
    bucketId: 'media',
    maximumFileSize: 52428800, // 50MB
    allowedFileExtensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'pdf'],
    compression: true,
    encryption: true,
    antivirus: true,
  },
  imageOptimization: {
    enabled: true,
    quality: 80,
    maxWidth: 1920,
    maxHeight: 1080,
    formats: ['webp', 'avif', 'jpg'],
  },
  cacheControl: {
    enabled: true,
    maxAge: 86400,
    staleWhileRevalidate: 43200,
  },
};

export default function MediaSettingsPage() {
  const { toast } = useToast();
  const { 
    settings, 
    isLoading: initialLoading, 
    isSubmitting: loading, 
    updateSettings 
  } = useMediaSettings();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });
  
  // Set form values when settings are loaded
  useEffect(() => {
    if (settings) {
      form.reset(settings);
    }
  }, [settings, form]);

  const storageProvider = form.watch('storageProvider');
  const imageOptimizationEnabled = form.watch('imageOptimization.enabled');
  const cacheControlEnabled = form.watch('cacheControl.enabled');

  const onSubmit = async (data: FormValues) => {
    try {
      // The form schema now ensures that appwriteSettings fields are always boolean (not undefined)
      await updateSettings(data);
    } catch (error) {
      // Error is already handled in the hook
      console.error('Form submission error:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Heading
          title="Media Storage Settings"
          description="Configure how and where media files are stored and served."
        />
      </div>
      
      {initialLoading ? (
        <div className="flex items-center justify-center py-8">
          <div className="flex flex-col items-center space-y-2">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">Loading settings...</p>
          </div>
        </div>
      ) : (
        <>
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Important</AlertTitle>
            <AlertDescription>
              Changing storage providers will not automatically migrate existing files. Please ensure you have a backup of all media before changing providers.
            </AlertDescription>
          </Alert>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Storage Provider</CardTitle>
              <CardDescription>
                Select where your media files will be stored
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FormField
                control={form.control}
                name="storageProvider"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Provider</FormLabel>
                    <Select
                      disabled={loading}
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a storage provider" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="appwrite">
                          <div className="flex items-center">
                            <Database className="mr-2 h-4 w-4" />
                            <span>Appwrite Storage</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="local">
                          <div className="flex items-center">
                            <HardDrive className="mr-2 h-4 w-4" />
                            <span>Local Storage</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="s3">
                          <div className="flex items-center">
                            <Cloud className="mr-2 h-4 w-4" />
                            <span>Amazon S3</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="cloudinary">
                          <div className="flex items-center">
                            <Cloud className="mr-2 h-4 w-4" />
                            <span>Cloudinary</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="google">
                          <div className="flex items-center">
                            <Database className="mr-2 h-4 w-4" />
                            <span>Google Cloud Storage</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Choose where your media files will be stored and served from
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
          </Card>

          <Tabs defaultValue="provider" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="provider">Provider Settings</TabsTrigger>
              <TabsTrigger value="optimization">Image Optimization</TabsTrigger>
              <TabsTrigger value="cache">Cache Control</TabsTrigger>
            </TabsList>

            <TabsContent value="provider" className="space-y-4 mt-4">
              {storageProvider === 'local' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Local Storage Settings</CardTitle>
                    <CardDescription>
                      Configure settings for storing files on the local server
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="localSettings.uploadDir"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Upload Directory</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={loading} />
                          </FormControl>
                          <FormDescription>
                            Directory where files will be stored (relative to public directory)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="localSettings.maxFileSize"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Max File Size (MB)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                              disabled={loading}
                            />
                          </FormControl>
                          <FormDescription>
                            Maximum file size allowed for uploads in megabytes
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="localSettings.allowedFileTypes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Allowed File Types</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={loading} />
                          </FormControl>
                          <FormDescription>
                            Comma-separated list of allowed file extensions (e.g., jpg,png,pdf)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              )}

              {storageProvider === 's3' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Amazon S3 Settings</CardTitle>
                    <CardDescription>
                      Configure settings for storing files on Amazon S3
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="s3Settings.accessKeyId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Access Key ID</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={loading} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="s3Settings.secretAccessKey"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Secret Access Key</FormLabel>
                          <FormControl>
                            <Input type="password" {...field} disabled={loading} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="s3Settings.region"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Region</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={loading} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="s3Settings.bucket"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bucket Name</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={loading} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="s3Settings.endpoint"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Custom Endpoint (Optional)</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={loading} />
                          </FormControl>
                          <FormDescription>
                            For S3-compatible services like DigitalOcean Spaces or MinIO
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              )}

              {storageProvider === 'cloudinary' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Cloudinary Settings</CardTitle>
                    <CardDescription>
                      Configure settings for storing files on Cloudinary
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="cloudinarySettings.cloudName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Cloud Name</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={loading} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="cloudinarySettings.apiKey"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>API Key</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={loading} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="cloudinarySettings.apiSecret"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>API Secret</FormLabel>
                          <FormControl>
                            <Input type="password" {...field} disabled={loading} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="cloudinarySettings.uploadPreset"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Upload Preset (Optional)</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={loading} />
                          </FormControl>
                          <FormDescription>
                            For unsigned uploads and client-side upload widget
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              )}

              {storageProvider === 'google' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Google Cloud Storage Settings</CardTitle>
                    <CardDescription>
                      Configure settings for storing files on Google Cloud Storage
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="googleSettings.projectId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Project ID</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={loading} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="googleSettings.bucketName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bucket Name</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={loading} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="googleSettings.keyFilePath"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Key File Path</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={loading} />
                          </FormControl>
                          <FormDescription>
                            Path to the service account key file (JSON)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              )}
              
              {storageProvider === 'appwrite' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Appwrite Storage Settings</CardTitle>
                    <CardDescription>
                      Configure settings for storing files using Appwrite Storage
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="appwriteSettings.bucketId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bucket ID</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={loading} />
                          </FormControl>
                          <FormDescription>
                            Unique identifier for the Appwrite storage bucket
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="appwriteSettings.maximumFileSize"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Maximum File Size (bytes)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value))}
                              disabled={loading}
                            />
                          </FormControl>
                          <FormDescription>
                            Maximum file size in bytes (50MB = 52428800)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <div className="space-y-2">
                      <FormLabel>Allowed File Extensions</FormLabel>
                      <FormDescription>
                        List of file extensions that can be uploaded
                      </FormDescription>
                      <div className="grid grid-cols-2 gap-2">
                        {form.watch('appwriteSettings.allowedFileExtensions')?.map((ext, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <Input
                              value={ext}
                              onChange={(e) => {
                                const newExtensions = [...form.watch('appwriteSettings.allowedFileExtensions')];
                                newExtensions[index] = e.target.value;
                                form.setValue('appwriteSettings.allowedFileExtensions', newExtensions);
                              }}
                              disabled={loading}
                              className="flex-1"
                            />
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              onClick={() => {
                                const newExtensions = [...form.watch('appwriteSettings.allowedFileExtensions')];
                                newExtensions.splice(index, 1);
                                form.setValue('appwriteSettings.allowedFileExtensions', newExtensions);
                              }}
                              disabled={loading}
                            >
                              ✕
                            </Button>
                          </div>
                        ))}
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const currentExtensions = form.watch('appwriteSettings.allowedFileExtensions') || [];
                          form.setValue('appwriteSettings.allowedFileExtensions', [...currentExtensions, '']);
                        }}
                        disabled={loading}
                        className="mt-2"
                      >
                        Add Extension
                      </Button>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4">
                      <FormField
                        control={form.control}
                        name="appwriteSettings.compression"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Compression</FormLabel>
                              <FormDescription>
                                Enable Gzip compression
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={loading}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="appwriteSettings.encryption"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Encryption</FormLabel>
                              <FormDescription>
                                Enable file encryption
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={loading}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={form.control}
                        name="appwriteSettings.antivirus"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Antivirus</FormLabel>
                              <FormDescription>
                                Enable antivirus scanning
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={loading}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="optimization" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Image Optimization</CardTitle>
                  <CardDescription>
                    Configure automatic image optimization settings
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="imageOptimization.enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Enable Image Optimization</FormLabel>
                          <FormDescription>
                            Automatically optimize images during upload and delivery
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={loading}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {imageOptimizationEnabled && (
                    <>
                      <Separator />
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="imageOptimization.quality"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Quality (%)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="1"
                                  max="100"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                  disabled={loading}
                                />
                              </FormControl>
                              <FormDescription>
                                Image quality percentage (1-100)
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="imageOptimization.maxWidth"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Max Width (px)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="100"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                  disabled={loading}
                                />
                              </FormControl>
                              <FormDescription>
                                Maximum width for resized images
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="imageOptimization.maxHeight"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Max Height (px)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="100"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                  disabled={loading}
                                />
                              </FormControl>
                              <FormDescription>
                                Maximum height for resized images
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="cache" className="space-y-4 mt-4">
              <Card>
                <CardHeader>
                  <CardTitle>Cache Control</CardTitle>
                  <CardDescription>
                    Configure caching settings for media files
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="cacheControl.enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Enable Cache Control</FormLabel>
                          <FormDescription>
                            Set cache control headers for media files
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={loading}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {cacheControlEnabled && (
                    <>
                      <Separator />
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="cacheControl.maxAge"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Max Age (seconds)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                  disabled={loading}
                                />
                              </FormControl>
                              <FormDescription>
                                How long browsers should cache the file (in seconds)
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="cacheControl.staleWhileRevalidate"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Stale While Revalidate (seconds)</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  min="0"
                                  {...field}
                                  onChange={(e) => field.onChange(Number(e.target.value))}
                                  disabled={loading}
                                />
                              </FormControl>
                              <FormDescription>
                                How long browsers should serve stale content while revalidating
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          <Button type="submit" disabled={loading}>
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Save Settings
          </Button>
            </form>
          </Form>
        </>
      )}
    </div>
  );
}