'use client';

import { ContentSettingsForm } from '@/components/admin/settings/content-form';
import { useContentSettings } from '@/hooks/use-content-settings';

const ContentSettingsPage = () => {
  const { settings, isLoading } = useContentSettings();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex-col">
      <div className="flex-1 space-y-4 p-8 pt-6">
        <ContentSettingsForm initialData={settings} />
      </div>
    </div>
  );
};

export default ContentSettingsPage;
