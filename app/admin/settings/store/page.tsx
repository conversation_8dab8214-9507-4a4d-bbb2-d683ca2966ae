'use client';

import { StoreSettingsForm } from '@/components/admin/settings/store-form';
import { useStoreSettings } from '@/hooks/use-store-settings';

const StoreSettingsPage = () => {
  const { settings, isLoading } = useStoreSettings();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex-col">
      <div className="flex-1 space-y-4 p-8 pt-6">
        <StoreSettingsForm initialData={settings} />
      </div>
    </div>
  );
};

export default StoreSettingsPage;
