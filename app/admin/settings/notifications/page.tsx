'use client';

import { NotificationsSettingsForm } from '@/components/admin/settings/notifications-form';
import { AdminNotificationSettings } from '@/components/admin/settings/admin-notification-settings';
import { NotificationDemo } from '@/components/admin/notification-demo';
import { useNotificationsSettings } from '@/hooks/use-notifications-settings';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';

const NotificationsSettingsPage = () => {
  const { settings, isLoading, isError } = useNotificationsSettings();

  if (isLoading) {
    return (
      <div className="flex-col space-y-6">
        <div className="flex-1 space-y-4 p-8 pt-6">
          <Skeleton className="h-8 w-[250px]" />
          <Skeleton className="h-4 w-[350px]" />
          <div className="space-y-4 mt-6">
            <Skeleton className="h-[300px] w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex-col">
        <div className="flex-1 space-y-4 p-8 pt-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              Failed to load notification settings. Please try refreshing the page or contact support if the problem persists.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-col">
      <div className="flex-1 space-y-4 p-8 pt-6">
        <Tabs defaultValue="advanced" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="basic">Basic Settings</TabsTrigger>
            <TabsTrigger value="advanced">Advanced Settings</TabsTrigger>
            <TabsTrigger value="demo">Test Notifications</TabsTrigger>
          </TabsList>
          
          <TabsContent value="basic" className="mt-6">
            <NotificationsSettingsForm initialData={settings} />
          </TabsContent>
          
          <TabsContent value="advanced" className="mt-6">
            <AdminNotificationSettings initialData={settings} />
          </TabsContent>
          
          <TabsContent value="demo" className="mt-6">
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-medium">Test Notification System</h2>
                <p className="text-sm text-muted-foreground">
                  Use this panel to test different types of notifications and see how they appear in the system.
                </p>
              </div>
              <Separator />
              <NotificationDemo />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default NotificationsSettingsPage;
