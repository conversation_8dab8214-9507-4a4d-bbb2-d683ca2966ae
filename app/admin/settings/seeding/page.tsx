'use client';

import { SeedingSettingsForm } from '@/components/admin/settings/seeding-form';
import { useSeedingSettings } from '@/hooks/use-seeding-settings';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

const SeedingSettingsPage = () => {
  const { settings, isLoading, isError } = useSeedingSettings();

  if (isLoading) {
    return (
      <div className="flex-col space-y-6">
        <div className="flex-1 space-y-4 p-8 pt-6">
          <Skeleton className="h-8 w-[250px]" />
          <Skeleton className="h-4 w-[350px]" />
          <div className="space-y-4 mt-6">
            <Skeleton className="h-[300px] w-full" />
            <Skeleton className="h-[400px] w-full" />
          </div>
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex-col">
        <div className="flex-1 space-y-4 p-8 pt-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              Failed to load seeding settings. Please try refreshing the page or contact support if the problem persists.
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-col">
      <div className="flex-1 space-y-4 p-8 pt-6">
        <SeedingSettingsForm initialData={settings} />
      </div>
    </div>
  );
};

export default SeedingSettingsPage;