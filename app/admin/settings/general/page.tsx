'use client';

import { GeneralSettingsForm } from '@/components/admin/settings/general-form';
import { useGeneralSettings } from '@/hooks/use-general-settings';

const GeneralSettingsPage = () => {
  const { settings, isLoading } = useGeneralSettings();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex-col">
      <div className="flex-1 space-y-4 p-8 pt-6">
        <GeneralSettingsForm initialData={settings} />
      </div>
    </div>
  );
};

export default GeneralSettingsPage;
