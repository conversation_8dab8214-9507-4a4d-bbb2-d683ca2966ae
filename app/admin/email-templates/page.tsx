'use client';

import React, { useState } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';

export default function EmailTemplatesPage() {
  const [activeTab, setActiveTab] = useState('welcome');
  const [previewHtml, setPreviewHtml] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  
  // Welcome email form
  const [welcomeForm, setWelcomeForm] = useState({
    to: '',
    firstName: '',
    customMessage: '',
  });
  
  // Order confirmation form
  const [orderForm, setOrderForm] = useState({
    to: '',
    customerName: '',
    orderNumber: '12345',
    orderDate: new Date().toLocaleDateString(),
    subtotal: 'R499.00',
    shipping: 'R50.00',
    tax: 'R75.00',
    total: 'R624.00',
    paymentMethod: 'Credit Card',
  });
  
  // Password reset form
  const [passwordForm, setPasswordForm] = useState({
    to: '',
    firstName: '',
    resetLink: 'https://cocomilkkids.com/reset-password?token=example',
  });
  
  // Account notification form
  const [notificationForm, setNotificationForm] = useState({
    to: '',
    firstName: '',
    notificationType: 'info',
    title: 'Account Update',
    message: 'Your account information has been updated successfully.',
  });
  
  const handleWelcomeChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setWelcomeForm(prev => ({ ...prev, [name]: value }));
  };
  
  const handleOrderChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setOrderForm(prev => ({ ...prev, [name]: value }));
  };
  
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setPasswordForm(prev => ({ ...prev, [name]: value }));
  };
  
  const handleNotificationChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNotificationForm(prev => ({ ...prev, [name]: value }));
  };
  
  const handleNotificationTypeChange = (value: string) => {
    setNotificationForm(prev => ({ ...prev, notificationType: value }));
  };
  
  const sendTestEmail = async (type: string, data: any) => {
    setLoading(true);
    try {
      const response = await fetch('/api/email/send-test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailType: type,
          to: data.to,
          data,
        }),
      });
      
      const result = await response.json();
      
      if (response.ok) {
        toast({
          title: 'Email Sent',
          description: `Test email has been sent to ${data.to}`,
        });
      } else {
        toast({
          title: 'Error',
          description: result.error || 'Failed to send email',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };
  
  const previewEmail = async (type: string, data: any) => {
    setLoading(true);
    try {
      // In a real implementation, you would fetch the preview HTML from an API
      // For now, we'll just show a placeholder message
      setPreviewHtml(`
        <div style="padding: 20px; background-color: #f8f9fa; border-radius: 8px;">
          <h3>Email Preview</h3>
          <p>This is a placeholder for the email preview. In a production environment, you would render the actual email template here.</p>
          <p>Email Type: ${type}</p>
          <p>Recipient: ${data.to}</p>
          <pre>${JSON.stringify(data, null, 2)}</pre>
        </div>
      `);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to generate preview',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Email Templates</h1>
      <p className="text-gray-500 mb-8">
        Preview and send test emails using the Coco Milk Kids branded templates.
      </p>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger value="welcome">Welcome</TabsTrigger>
              <TabsTrigger value="order">Order</TabsTrigger>
              <TabsTrigger value="password">Password</TabsTrigger>
              <TabsTrigger value="notification">Notification</TabsTrigger>
            </TabsList>
            
            <TabsContent value="welcome">
              <Card>
                <CardHeader>
                  <CardTitle>Welcome Email</CardTitle>
                  <CardDescription>
                    Sent to new users after registration to welcome them to Coco Milk Kids.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="welcome-to">Recipient Email</Label>
                    <Input 
                      id="welcome-to" 
                      name="to" 
                      value={welcomeForm.to} 
                      onChange={handleWelcomeChange} 
                      placeholder="<EMAIL>" 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="welcome-firstName">First Name</Label>
                    <Input 
                      id="welcome-firstName" 
                      name="firstName" 
                      value={welcomeForm.firstName} 
                      onChange={handleWelcomeChange} 
                      placeholder="John" 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="welcome-customMessage">Custom Message (Optional)</Label>
                    <Textarea 
                      id="welcome-customMessage" 
                      name="customMessage" 
                      value={welcomeForm.customMessage} 
                      onChange={handleWelcomeChange} 
                      placeholder="Add a personalized message..." 
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    variant="outline" 
                    onClick={() => previewEmail('welcome', welcomeForm)}
                    disabled={loading}
                  >
                    Preview
                  </Button>
                  <Button 
                    onClick={() => sendTestEmail('welcome', welcomeForm)}
                    disabled={loading || !welcomeForm.to || !welcomeForm.firstName}
                  >
                    Send Test Email
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            <TabsContent value="order">
              <Card>
                <CardHeader>
                  <CardTitle>Order Confirmation Email</CardTitle>
                  <CardDescription>
                    Sent to customers after they complete a purchase.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="order-to">Recipient Email</Label>
                    <Input 
                      id="order-to" 
                      name="to" 
                      value={orderForm.to} 
                      onChange={handleOrderChange} 
                      placeholder="<EMAIL>" 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="order-customerName">Customer Name</Label>
                    <Input 
                      id="order-customerName" 
                      name="customerName" 
                      value={orderForm.customerName} 
                      onChange={handleOrderChange} 
                      placeholder="John Doe" 
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="order-orderNumber">Order Number</Label>
                      <Input 
                        id="order-orderNumber" 
                        name="orderNumber" 
                        value={orderForm.orderNumber} 
                        onChange={handleOrderChange} 
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="order-orderDate">Order Date</Label>
                      <Input 
                        id="order-orderDate" 
                        name="orderDate" 
                        value={orderForm.orderDate} 
                        onChange={handleOrderChange} 
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="order-subtotal">Subtotal</Label>
                      <Input 
                        id="order-subtotal" 
                        name="subtotal" 
                        value={orderForm.subtotal} 
                        onChange={handleOrderChange} 
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="order-shipping">Shipping</Label>
                      <Input 
                        id="order-shipping" 
                        name="shipping" 
                        value={orderForm.shipping} 
                        onChange={handleOrderChange} 
                      />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="order-tax">Tax</Label>
                      <Input 
                        id="order-tax" 
                        name="tax" 
                        value={orderForm.tax} 
                        onChange={handleOrderChange} 
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="order-total">Total</Label>
                      <Input 
                        id="order-total" 
                        name="total" 
                        value={orderForm.total} 
                        onChange={handleOrderChange} 
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="order-paymentMethod">Payment Method</Label>
                    <Input 
                      id="order-paymentMethod" 
                      name="paymentMethod" 
                      value={orderForm.paymentMethod} 
                      onChange={handleOrderChange} 
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    variant="outline" 
                    onClick={() => previewEmail('order-confirmation', orderForm)}
                    disabled={loading}
                  >
                    Preview
                  </Button>
                  <Button 
                    onClick={() => sendTestEmail('order-confirmation', orderForm)}
                    disabled={loading || !orderForm.to || !orderForm.customerName}
                  >
                    Send Test Email
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            <TabsContent value="password">
              <Card>
                <CardHeader>
                  <CardTitle>Password Reset Email</CardTitle>
                  <CardDescription>
                    Sent when a user requests a password reset.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="password-to">Recipient Email</Label>
                    <Input 
                      id="password-to" 
                      name="to" 
                      value={passwordForm.to} 
                      onChange={handlePasswordChange} 
                      placeholder="<EMAIL>" 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password-firstName">First Name</Label>
                    <Input 
                      id="password-firstName" 
                      name="firstName" 
                      value={passwordForm.firstName} 
                      onChange={handlePasswordChange} 
                      placeholder="John" 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password-resetLink">Reset Link</Label>
                    <Input 
                      id="password-resetLink" 
                      name="resetLink" 
                      value={passwordForm.resetLink} 
                      onChange={handlePasswordChange} 
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    variant="outline" 
                    onClick={() => previewEmail('password-reset', passwordForm)}
                    disabled={loading}
                  >
                    Preview
                  </Button>
                  <Button 
                    onClick={() => sendTestEmail('password-reset', passwordForm)}
                    disabled={loading || !passwordForm.to || !passwordForm.firstName}
                  >
                    Send Test Email
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
            
            <TabsContent value="notification">
              <Card>
                <CardHeader>
                  <CardTitle>Account Notification Email</CardTitle>
                  <CardDescription>
                    Sent for various account-related notifications.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="notification-to">Recipient Email</Label>
                    <Input 
                      id="notification-to" 
                      name="to" 
                      value={notificationForm.to} 
                      onChange={handleNotificationChange} 
                      placeholder="<EMAIL>" 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="notification-firstName">First Name</Label>
                    <Input 
                      id="notification-firstName" 
                      name="firstName" 
                      value={notificationForm.firstName} 
                      onChange={handleNotificationChange} 
                      placeholder="John" 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="notification-type">Notification Type</Label>
                    <Select 
                      value={notificationForm.notificationType} 
                      onValueChange={handleNotificationTypeChange}
                    >
                      <SelectTrigger id="notification-type">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="info">Information</SelectItem>
                        <SelectItem value="warning">Warning</SelectItem>
                        <SelectItem value="success">Success</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="notification-title">Title</Label>
                    <Input 
                      id="notification-title" 
                      name="title" 
                      value={notificationForm.title} 
                      onChange={handleNotificationChange} 
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="notification-message">Message</Label>
                    <Textarea 
                      id="notification-message" 
                      name="message" 
                      value={notificationForm.message} 
                      onChange={handleNotificationChange} 
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button 
                    variant="outline" 
                    onClick={() => previewEmail('account-notification', notificationForm)}
                    disabled={loading}
                  >
                    Preview
                  </Button>
                  <Button 
                    onClick={() => sendTestEmail('account-notification', notificationForm)}
                    disabled={loading || !notificationForm.to || !notificationForm.firstName}
                  >
                    Send Test Email
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
        
        <div>
          <Card className="h-full">
            <CardHeader>
              <CardTitle>Email Preview</CardTitle>
              <CardDescription>
                Preview how your email will look to recipients.
              </CardDescription>
            </CardHeader>
            <CardContent>
              {previewHtml ? (
                <div 
                  className="border rounded-md p-4 h-[600px] overflow-auto bg-white"
                  dangerouslySetInnerHTML={{ __html: previewHtml }}
                />
              ) : (
                <div className="border rounded-md p-4 h-[600px] flex items-center justify-center bg-gray-50">
                  <p className="text-gray-500 text-center">
                    Click the "Preview" button to see how your email will look.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}