"use client"

import { useState, useEffect } from "react"
import { EmailEditor as BaseEmailEditor } from "@/lib/email/composer/components/EmailEditor"
import { useEmailComposer } from "@/lib/email/composer/context"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Eye, Code, Save } from "lucide-react"

interface CampaignEmailEditorProps {
  readOnly?: boolean
  onSave?: () => void
}

export function CampaignEmailEditor({ readOnly = false, onSave }: CampaignEmailEditorProps) {
  const { state, actions } = useEmailComposer()
  const [viewMode, setViewMode] = useState<"visual" | "code" | "preview">("visual")
  const [htmlCode, setHtmlCode] = useState("")

  // Generate HTML code when in code view
  useEffect(() => {
    if (viewMode === "code" && state.currentTemplate) {
      // This is a simplified example - in a real implementation, 
      // you would use a proper HTML generator based on the template blocks
      const generatedHtml = `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${state.currentTemplate.subject || "Email Campaign"}</title>
  <style>
    body { 
      font-family: ${state.currentTemplate.settings?.fontFamily || "Arial, sans-serif"}; 
      margin: 0; 
      padding: 0; 
      background-color: #f5f5f5;
    }
    .email-container {
      max-width: ${state.currentTemplate.settings?.width || "600px"};
      margin: 0 auto;
      background-color: #ffffff;
    }
    /* More styles would be generated based on template settings */
  </style>
</head>
<body>
  <div class="email-container">
    ${state.currentTemplate.blocks.map(block => {
      // This would be replaced with actual block rendering logic
      return `<!-- ${block.type} block with id ${block.id} -->\n<div>${JSON.stringify(block.content || {})}</div>`;
    }).join("\n")}
  </div>
</body>
</html>
      `.trim();
      
      setHtmlCode(generatedHtml);
    }
  }, [viewMode, state.currentTemplate]);

  const handleSave = () => {
    actions.saveTemplate();
    if (onSave) {
      onSave();
    }
  };

  return (
    <div className="flex flex-col h-full">
      <div className="border-b border-gray-200 bg-white p-2 flex justify-between items-center">
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as any)} className="w-auto">
          <TabsList>
            <TabsTrigger value="visual">Visual Editor</TabsTrigger>
            <TabsTrigger value="code">HTML Code</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>
        </Tabs>
        
        {!readOnly && (
          <Button onClick={handleSave} disabled={state.isSaving}>
            <Save className="mr-2 h-4 w-4" />
            {state.isSaving ? "Saving..." : "Save"}
          </Button>
        )}
      </div>
      
      <div className="flex-1 overflow-hidden">
        <TabsContent value="visual" className="h-full m-0 data-[state=active]:flex-1">
          <BaseEmailEditor className="h-full" />
        </TabsContent>
        
        <TabsContent value="code" className="h-full m-0 data-[state=active]:flex-1">
          <div className="h-full bg-gray-900 text-gray-100 p-4 overflow-auto">
            <pre className="text-sm">
              <code>{htmlCode}</code>
            </pre>
          </div>
        </TabsContent>
        
        <TabsContent value="preview" className="h-full m-0 data-[state=active]:flex-1">
          <div className="h-full bg-gray-100 p-4 overflow-auto">
            <div className="mx-auto bg-white shadow-md" style={{ maxWidth: state.currentTemplate?.settings?.width || "600px" }}>
              {/* This would be replaced with an actual email preview renderer */}
              <div className="p-4">
                <h2 className="text-xl font-bold mb-4">{state.currentTemplate?.subject || "Email Preview"}</h2>
                <div className="text-sm text-gray-500 mb-4">{state.currentTemplate?.previewText}</div>
                
                {state.currentTemplate?.blocks.map((block, index) => (
                  <div key={block.id} className="py-2 border-b border-gray-200 last:border-0">
                    <div className="text-sm text-gray-500">Block {index + 1}: {block.type}</div>
                    <div>{JSON.stringify(block.content || {})}</div>
                  </div>
                ))}
                
                {(!state.currentTemplate?.blocks || state.currentTemplate.blocks.length === 0) && (
                  <div className="py-8 text-center text-gray-500">
                    <p>No content blocks added yet</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </TabsContent>
      </div>
    </div>
  )
}