"use client"

import { useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { ArrowLeft, Save, Send } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { CampaignEmailEditor } from "../../components/campaign-email-editor"
import { PreviewPanel } from "@/lib/email/composer/components/PreviewPanel"
import { VariablesPanel } from "@/lib/email/composer/components/VariablesPanel"
import { DataQueryPanel } from "@/lib/email/composer/components/DataQueryPanel"

export default function NewCampaignPage() {
  const router = useRouter()
  const [campaignName, setCampaignName] = useState("Untitled Campaign")
  const [subject, setSubject] = useState("")
  const [previewText, setPreviewText] = useState("")
  const [isSaving, setIsSaving] = useState(false)
  const [activeTab, setActiveTab] = useState("design")

  const handleSave = async (isDraft = true) => {
    setIsSaving(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    setIsSaving(false)
    
    // Redirect back to campaigns list
    router.push("/admin/tools/coco-mailer")
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Link href="/admin/tools/coco-mailer">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">New Campaign</h1>
            <p className="text-muted-foreground">Create a new email marketing campaign</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => handleSave(true)} disabled={isSaving}>
            <Save className="mr-2 h-4 w-4" />
            Save Draft
          </Button>
          <Button onClick={() => handleSave(false)} disabled={isSaving}>
            <Send className="mr-2 h-4 w-4" />
            {activeTab === "design" ? "Continue to Recipients" : "Schedule Campaign"}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Campaign Details</CardTitle>
            <CardDescription>Basic information about your campaign</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="campaign-name">Campaign Name</Label>
                <Input 
                  id="campaign-name" 
                  value={campaignName} 
                  onChange={(e) => setCampaignName(e.target.value)}
                  placeholder="Enter campaign name"
                />
                <p className="text-xs text-muted-foreground">
                  This is for your reference only and won't be visible to recipients
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="subject">Email Subject</Label>
                <Input 
                  id="subject" 
                  value={subject} 
                  onChange={(e) => setSubject(e.target.value)}
                  placeholder="Enter email subject line"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="preview-text">Preview Text</Label>
                <Input 
                  id="preview-text" 
                  value={previewText} 
                  onChange={(e) => setPreviewText(e.target.value)}
                  placeholder="Enter preview text"
                />
                <p className="text-xs text-muted-foreground">
                  This text appears in the inbox preview after the subject line
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="design">Design Email</TabsTrigger>
          <TabsTrigger value="recipients">Recipients</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
        </TabsList>
        
        <TabsContent value="design" className="space-y-4">
          <div className="grid grid-cols-12 gap-6 h-[calc(100vh-350px)]">
            <div className="col-span-9 bg-background border rounded-md overflow-hidden">
              <CampaignEmailEditor onSave={() => setIsSaving(false)} />
            </div>
            <div className="col-span-3 space-y-4">
              <Tabs defaultValue="preview" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="preview">Preview</TabsTrigger>
                  <TabsTrigger value="variables">Variables</TabsTrigger>
                  <TabsTrigger value="data">Data</TabsTrigger>
                </TabsList>
                
                <TabsContent value="preview" className="h-[calc(100vh-400px)] overflow-auto">
                  <PreviewPanel />
                </TabsContent>
                
                <TabsContent value="variables" className="h-[calc(100vh-400px)] overflow-auto">
                  <VariablesPanel />
                </TabsContent>
                
                <TabsContent value="data" className="h-[calc(100vh-400px)] overflow-auto">
                  <DataQueryPanel />
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="recipients">
          <Card>
            <CardHeader>
              <CardTitle>Select Recipients</CardTitle>
              <CardDescription>Choose who will receive this campaign</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center py-12 text-muted-foreground">
                Recipient selection will be available in the next update
              </p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="schedule">
          <Card>
            <CardHeader>
              <CardTitle>Schedule Campaign</CardTitle>
              <CardDescription>Choose when to send your campaign</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center py-12 text-muted-foreground">
                Campaign scheduling will be available in the next update
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}