"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"

export default function CampaignsRedirect() {
  const router = useRouter()
  
  useEffect(() => {
    // Redirect to the main Coco Mailer page
    router.push("/admin/tools/coco-mailer")
  }, [router])
  
  return (
    <div className="flex items-center justify-center h-[50vh]">
      <p className="text-muted-foreground">Redirecting...</p>
    </div>
  )
}