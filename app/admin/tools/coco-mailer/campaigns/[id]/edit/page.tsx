'use client';

import React from 'react';
import { EmailComposerProvider, EmailEditor } from '@/lib/email/composer';
import { useSidebar } from '@/components/ui/sidebar';

export default function EmailComposerPage() {
  const { open, setOpen } = useSidebar();

  React.useEffect(() => {
    setOpen(false);
  }, [open]);

  return (
    <div className="h-[100vh] flex flex-col p-0">
      <EmailComposerProvider>
        <EmailEditor className="flex-1 p-0" />
      </EmailComposerProvider>
    </div>
  );
}