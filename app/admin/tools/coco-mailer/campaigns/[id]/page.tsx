"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, Calendar, Clock, Copy, Edit, Eye, Mail, MoreHorizontal, RefreshCw, Send, Trash2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { CampaignEmailEditor } from "../../components/campaign-email-editor"
import { PreviewPanel } from "@/lib/email/composer/components/PreviewPanel"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

// Mock data for email campaigns
const mockCampaigns = [
  {
    id: "camp-001",
    name: "Summer Collection Launch",
    status: "draft",
    subject: "Introducing Our Summer Collection 2025",
    previewText: "Check out our latest summer styles with exclusive discounts",
    created: "2025-06-10T14:30:00Z",
    modified: "2025-06-12T09:15:00Z",
    stats: { sent: 0, opened: 0, clicked: 0 }
  },
  {
    id: "camp-002",
    name: "Monthly Newsletter - June",
    status: "scheduled",
    subject: "Coco Milk Store - June Updates & New Arrivals",
    previewText: "See what's new this month at Coco Milk Store",
    created: "2025-06-05T11:20:00Z",
    modified: "2025-06-11T16:45:00Z",
    scheduledFor: "2025-06-15T08:00:00Z",
    stats: { sent: 0, opened: 0, clicked: 0 }
  },
  {
    id: "camp-003",
    name: "Special Discount Weekend",
    status: "sent",
    subject: "48 Hours Only: 25% Off Everything!",
    previewText: "Limited time offer - Shop now for the best selection",
    created: "2025-05-28T10:00:00Z",
    modified: "2025-05-29T14:30:00Z",
    sentAt: "2025-05-30T09:00:00Z",
    stats: { sent: 1250, opened: 876, clicked: 432 }
  },
]

export default function CampaignDetailPage() {
  const params = useParams()
  const router = useRouter()
  const campaignId = params.id as string
  
  const [campaign, setCampaign] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isDeleting, setIsDeleting] = useState(false)
  const [activeTab, setActiveTab] = useState("overview")

  useEffect(() => {
    // Simulate API call to fetch campaign details
    const fetchCampaign = async () => {
      setIsLoading(true)
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800))
      
      const foundCampaign = mockCampaigns.find(c => c.id === campaignId)
      
      if (foundCampaign) {
        setCampaign(foundCampaign)
      } else {
        // Redirect if campaign not found
        router.push("/admin/tools/coco-mailer")
      }
      
      setIsLoading(false)
    }
    
    fetchCampaign()
  }, [campaignId, router])

  const handleDelete = async () => {
    setIsDeleting(true)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    setIsDeleting(false)
    
    // Redirect back to campaigns list
    router.push("/admin/tools/coco-mailer")
  }

  const handleDuplicate = async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Redirect to the new campaign
    router.push("/admin/tools/coco-mailer/campaigns/new")
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (isLoading || !campaign) {
    return (
      <div className="container mx-auto py-6 flex items-center justify-center h-[50vh]">
        <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <Link href="/admin/tools/coco-mailer">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-3xl font-bold tracking-tight">{campaign.name}</h1>
              <Badge 
                variant={
                  campaign.status === 'sent' ? 'default' : 
                  campaign.status === 'scheduled' ? 'secondary' : 
                  'outline'
                }
              >
                {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
              </Badge>
            </div>
            <p className="text-muted-foreground">
              {campaign.subject}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {campaign.status === 'draft' && (
            <Button>
              <Send className="mr-2 h-4 w-4" />
              Send Campaign
            </Button>
          )}
          
          {campaign.status === 'scheduled' && (
            <Button variant="outline">
              <Calendar className="mr-2 h-4 w-4" />
              Edit Schedule
            </Button>
          )}
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Campaign Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              
              {campaign.status === 'draft' && (
                <Link href={`/admin/tools/coco-mailer/campaigns/${campaign.id}/edit`} passHref legacyBehavior>
                  <DropdownMenuItem onClick={() => setActiveTab("content")}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit
                  </DropdownMenuItem>
                </Link>
              )}
              
              <DropdownMenuItem onClick={handleDuplicate}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate
              </DropdownMenuItem>
              
              <DropdownMenuItem>
                <Eye className="mr-2 h-4 w-4" />
                Preview
              </DropdownMenuItem>
              
              <DropdownMenuSeparator />
              
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()} className="text-destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Campaign
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This will permanently delete the campaign "{campaign.name}".
                      This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleDelete} disabled={isDeleting}>
                      {isDeleting ? "Deleting..." : "Delete"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="content">Content</TabsTrigger>
          <TabsTrigger value="recipients">Recipients</TabsTrigger>
          <TabsTrigger value="analytics" disabled={campaign.status !== 'sent'}>
            Analytics
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Campaign Details</CardTitle>
              </CardHeader>
              <CardContent>
                <dl className="space-y-2 text-sm">
                  <div>
                    <dt className="text-muted-foreground">Status</dt>
                    <dd>
                      <Badge 
                        variant={
                          campaign.status === 'sent' ? 'default' : 
                          campaign.status === 'scheduled' ? 'secondary' : 
                          'outline'
                        }
                        className="mt-1"
                      >
                        {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                      </Badge>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-muted-foreground">Created</dt>
                    <dd>{formatDate(campaign.created)}</dd>
                  </div>
                  <div>
                    <dt className="text-muted-foreground">Last Modified</dt>
                    <dd>{formatDate(campaign.modified)}</dd>
                  </div>
                  {campaign.status === 'scheduled' && (
                    <div>
                      <dt className="text-muted-foreground">Scheduled For</dt>
                      <dd>{formatDate(campaign.scheduledFor)}</dd>
                    </div>
                  )}
                  {campaign.status === 'sent' && (
                    <div>
                      <dt className="text-muted-foreground">Sent At</dt>
                      <dd>{formatDate(campaign.sentAt)}</dd>
                    </div>
                  )}
                </dl>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Email Content</CardTitle>
              </CardHeader>
              <CardContent>
                <dl className="space-y-2 text-sm">
                  <div>
                    <dt className="text-muted-foreground">Subject Line</dt>
                    <dd>{campaign.subject}</dd>
                  </div>
                  <div>
                    <dt className="text-muted-foreground">Preview Text</dt>
                    <dd>{campaign.previewText}</dd>
                  </div>
                </dl>
              </CardContent>
              <CardFooter>
                <Button variant="outline" size="sm" className="w-full" onClick={() => setActiveTab("content")}>
                  <Eye className="mr-2 h-4 w-4" />
                  View Content
                </Button>
              </CardFooter>
            </Card>
            
            {campaign.status === 'sent' && (
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="text-center">
                      <p className="text-2xl font-bold">{campaign.stats.sent}</p>
                      <p className="text-xs text-muted-foreground">Sent</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{campaign.stats.opened}</p>
                      <p className="text-xs text-muted-foreground">Opened</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">{campaign.stats.clicked}</p>
                      <p className="text-xs text-muted-foreground">Clicked</p>
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" size="sm" className="w-full" onClick={() => setActiveTab("analytics")}>
                    View Analytics
                  </Button>
                </CardFooter>
              </Card>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="content">
          <div className="grid grid-cols-12 gap-6 h-[calc(100vh-250px)]">
            <div className="col-span-9 bg-background border rounded-md overflow-hidden">
              <CampaignEmailEditor readOnly={campaign.status !== 'draft'} />
            </div>
            <div className="col-span-3">
              <Card className="h-full">
                <CardHeader>
                  <CardTitle>Preview</CardTitle>
                  <CardDescription>
                    How your email will appear to recipients
                  </CardDescription>
                </CardHeader>
                <CardContent className="h-[calc(100vh-350px)] overflow-auto">
                  <PreviewPanel />
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>
        
        <TabsContent value="recipients">
          <Card>
            <CardHeader>
              <CardTitle>Recipients</CardTitle>
              <CardDescription>
                Who will receive this campaign
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center py-12 text-muted-foreground">
                Recipient details will be available in the next update
              </p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Campaign Analytics</CardTitle>
              <CardDescription>
                Performance metrics for this campaign
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center py-12 text-muted-foreground">
                Detailed analytics will be available in the next update
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}