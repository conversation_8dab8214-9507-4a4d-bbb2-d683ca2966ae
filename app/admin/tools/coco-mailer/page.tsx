"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { Mail, Plus, RefreshCw, Search, Workflow } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

// Mock data for email campaigns
const mockCampaigns = [
  {
    id: "camp-001",
    name: "Summer Collection Launch",
    status: "draft",
    created: "2025-06-10T14:30:00Z",
    modified: "2025-06-12T09:15:00Z",
    stats: { sent: 0, opened: 0, clicked: 0 }
  },
  {
    id: "camp-002",
    name: "Monthly Newsletter - June",
    status: "scheduled",
    created: "2025-06-05T11:20:00Z",
    modified: "2025-06-11T16:45:00Z",
    scheduledFor: "2025-06-15T08:00:00Z",
    stats: { sent: 0, opened: 0, clicked: 0 }
  },
  {
    id: "camp-003",
    name: "Special Discount Weekend",
    status: "sent",
    created: "2025-05-28T10:00:00Z",
    modified: "2025-05-29T14:30:00Z",
    sentAt: "2025-05-30T09:00:00Z",
    stats: { sent: 1250, opened: 876, clicked: 432 }
  },
]

export default function CocoMailerDashboard() {
  const [campaigns, setCampaigns] = useState(mockCampaigns)
  const [isLoading, setIsLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")

  // Filter campaigns based on search query
  const filteredCampaigns = campaigns.filter(campaign => 
    campaign.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Simulate loading data
  const refreshData = () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
    }, 800)
  }

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Coco Mailer</h1>
          <p className="text-muted-foreground">Create and manage email marketing campaigns</p>
        </div>
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={refreshData} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          </Button>
          <Link href="/admin/tools/coco-mailer/campaigns/new">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              New Campaign
            </Button>
          </Link>
        </div>
      </div>

      <Tabs defaultValue="campaigns" className="w-full">
        <TabsList>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="subscribers">Subscribers</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="campaigns" className="space-y-4">
          <div className="flex justify-between items-center">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search campaigns..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          <div className="grid gap-4">
            {filteredCampaigns.map((campaign) => (
              <Link key={campaign.id} href={`/admin/tools/coco-mailer/campaigns/${campaign.id}`}>
                <Card className="hover:bg-muted/50 transition-colors">
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle>{campaign.name}</CardTitle>
                        <CardDescription>
                          Created: {formatDate(campaign.created)}
                        </CardDescription>
                      </div>
                      <Badge 
                        variant={
                          campaign.status === 'sent' ? 'default' : 
                          campaign.status === 'scheduled' ? 'secondary' : 
                          'outline'
                        }
                      >
                        {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-sm">
                      {campaign.status === 'scheduled' && (
                        <p>Scheduled for: {formatDate(campaign.scheduledFor!)}</p>
                      )}
                      {campaign.status === 'sent' && (
                        <div className="flex gap-6">
                          <div>
                            <p className="font-medium">{campaign.stats.sent}</p>
                            <p className="text-xs text-muted-foreground">Sent</p>
                          </div>
                          <div>
                            <p className="font-medium">{campaign.stats.opened}</p>
                            <p className="text-xs text-muted-foreground">Opened</p>
                          </div>
                          <div>
                            <p className="font-medium">{campaign.stats.clicked}</p>
                            <p className="text-xs text-muted-foreground">Clicked</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="pt-1 text-xs text-muted-foreground">
                    Last modified: {formatDate(campaign.modified)}
                  </CardFooter>
                </Card>
              </Link>
            ))}

            {filteredCampaigns.length === 0 && (
              <Card className="flex flex-col items-center justify-center py-12">
                <Mail className="h-12 w-12 text-muted-foreground mb-4" />
                <p className="text-muted-foreground">No campaigns found</p>
                <Link href="/admin/tools/coco-mailer/campaigns/new" className="mt-4">
                  <Button variant="outline">Create your first campaign</Button>
                </Link>
              </Card>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="templates">
          <Card>
            <CardHeader>
              <CardTitle>Email Templates</CardTitle>
              <CardDescription>
                Create and manage reusable email templates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center py-8 text-muted-foreground">
                Template management will be available in the next update
              </p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="subscribers">
          <Card>
            <CardHeader>
              <CardTitle>Subscriber Lists</CardTitle>
              <CardDescription>
                Manage your subscriber lists and segments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center py-8 text-muted-foreground">
                Subscriber management will be available in the next update
              </p>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Email Analytics</CardTitle>
              <CardDescription>
                Track and analyze your email campaign performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-center py-8 text-muted-foreground">
                Analytics dashboard will be available in the next update
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}