/**
 * Checkout Cancel Page
 * 
 * This page is displayed when a payment is cancelled.
 */

'use client'

import { useEffect, useState } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Loader2, ShoppingBag, Home, XCircle, RefreshCw } from 'lucide-react'
import PaymentStatusDisplay from '@/components/checkout/PaymentStatusDisplay'
import { PaymentStatus, PaymentGateway } from '@/lib/payment-core/types'

interface OrderDetails {
  orderNumber: string
  paymentStatus: PaymentStatus
  orderStatus: string
  transactionId?: string
  reference?: string
  gateway?: PaymentGateway
  total?: number
  currency?: string
}

export default function CheckoutCancelPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const orderId = searchParams.get('orderId')
  const transactionId = searchParams.get('transactionId')
  const reference = searchParams.get('reference')
  
  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  useEffect(() => {
    if (!orderId && !transactionId) {
      router.push('/checkout')
      return
    }
    
    async function fetchOrderStatus() {
      try {
        setLoading(true)
        
        // Fetch order status
        const response = await fetch(`/api/checkout/payment/status?${orderId ? `orderId=${orderId}` : `transactionId=${transactionId}`}`)
        const data = await response.json()
        
        if (data.success) {
          setOrderDetails({
            orderNumber: data.orderNumber || orderId || 'N/A',
            paymentStatus: data.paymentStatus || PaymentStatus.CANCELLED,
            orderStatus: data.orderStatus || 'pending',
            transactionId: data.transactionId || transactionId,
            reference: data.reference || reference,
            gateway: data.gateway || PaymentGateway.PAYFAST,
            total: data.total,
            currency: data.currency
          })
        } else {
          // If API fails, use fallback data
          setOrderDetails({
            orderNumber: orderId || 'N/A',
            paymentStatus: PaymentStatus.CANCELLED,
            orderStatus: 'pending',
            transactionId: transactionId,
            reference: reference,
            gateway: PaymentGateway.PAYFAST
          })
        }
      } catch (err) {
        console.error('Error loading order details:', err)
        setError(err instanceof Error ? err.message : 'Failed to load order details')
        
        // Use fallback data even on error
        setOrderDetails({
          orderNumber: orderId || 'N/A',
          paymentStatus: PaymentStatus.CANCELLED,
          orderStatus: 'pending',
          transactionId: transactionId,
          reference: reference,
          gateway: PaymentGateway.PAYFAST
        })
      } finally {
        setLoading(false)
      }
    }
    
    fetchOrderStatus()
  }, [orderId, transactionId, reference, router])
  
  if (loading) {
    return (
      <div className="max-w-3xl mx-auto p-6 text-center">
        <Card className="p-8">
          <CardContent className="pt-6 flex flex-col items-center justify-center">
            <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
            <p className="text-lg">Loading order details...</p>
          </CardContent>
        </Card>
      </div>
    )
  }
  
  if (error && !orderDetails) {
    return (
      <div className="max-w-3xl mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-red-600">Error Loading Order</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-red-50 border border-red-300 p-4 rounded-md text-red-700 mb-4">
              <p>{error}</p>
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild>
              <Link href="/">
                <Home className="h-4 w-4 mr-2" />
                Return to home page
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }
  
  return (
    <div className="max-w-3xl mx-auto p-6">
      <Card className="overflow-hidden">
        <CardHeader className="bg-yellow-50 text-center">
          <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <XCircle className="h-8 w-8 text-yellow-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-yellow-600">Payment Cancelled</CardTitle>
          <p className="text-gray-600">
            Your payment was cancelled. Don't worry, your order is still saved.
          </p>
        </CardHeader>
        
        <CardContent className="pt-6">
          {orderDetails && (
            <div className="space-y-6">
              <PaymentStatusDisplay 
                status={PaymentStatus.CANCELLED}
                transactionId={orderDetails.transactionId}
                reference={orderDetails.reference}
                showDetails={true}
                iconSize="lg"
              />
              
              <Separator />
              
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="font-medium">Order Number:</span>
                  <span>{orderDetails.orderNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">Order Status:</span>
                  <span className="capitalize">{orderDetails.orderStatus}</span>
                </div>
                {orderDetails.total && (
                  <div className="flex justify-between">
                    <span className="font-medium">Total Amount:</span>
                    <span>{orderDetails.currency || 'R'} {orderDetails.total.toFixed(2)}</span>
                  </div>
                )}
              </div>
              
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4 text-blue-800 text-sm">
                <p>
                  You can try again or choose a different payment method.
                  Your order will remain in our system for 24 hours.
                </p>
              </div>
            </div>
          )}
        </CardContent>
        
        <CardFooter className="flex flex-col sm:flex-row gap-4 justify-center pt-2 pb-6">
          <Button asChild>
            <Link href={`/checkout/payment?orderId=${orderDetails?.orderNumber || orderId}`}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/">
              <Home className="h-4 w-4 mr-2" />
              Continue Shopping
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}