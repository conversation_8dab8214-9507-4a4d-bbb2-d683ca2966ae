"use client"

import { useState, Suspense, useEffect, useMemo } from "react"
import dynamic from "next/dynamic"
import { useCartStore2 } from "@/lib/ecommerce/hooks/use-cart-store"
import { useCheckout } from "@/lib/ecommerce/hooks/use-checkout-store"
import { Button } from "@/components/ui/button"
import { ShoppingBag, Loader2 } from "lucide-react"
import Link from "next/link"
import { AuthProvider } from "@/components/auth-provider"
import { useSearchParams } from "next/navigation"
import { CheckoutProgressWrapper } from "@/components/storefront/checkout/checkout-progress-wrapper"

// Dynamic imports to prevent SSR issues
const AuthCheckoutForm = dynamic(() => import("@/components/storefront/checkout/auth-checkout-form").then(mod => ({ default: mod.AuthCheckoutForm })), {
  loading: () => <div className="flex items-center justify-center py-8"><Loader2 className="h-8 w-8 animate-spin" /></div>,
  ssr: false
})

const CheckoutSummary = dynamic(() => import("@/components/storefront/checkout/checkout-summary").then(mod => ({ default: mod.CheckoutSummary })), {
  loading: () => <div className="flex items-center justify-center py-8"><Loader2 className="h-8 w-8 animate-spin" /></div>,
  ssr: false
})

const OrderConfirmation = dynamic(() => import("@/components/storefront/orders/order-confirmation").then(mod => ({ default: mod.OrderConfirmation })), {
  loading: () => <div className="flex items-center justify-center py-8"><Loader2 className="h-8 w-8 animate-spin" /></div>,
  ssr: false
})

// Generate a random order ID - moved outside component to avoid re-initialization
const generateOrderId = () => {
  return Math.floor(Math.random() * 10000000).toString().padStart(7, '0');
};

export default function CheckoutPage() {
  const searchParams = useSearchParams()
  // Ensure sessionId is always present
  const [sessionId, setSessionIdState] = useState<string | null>(null)
  
  // All state hooks must be called before any conditional returns
  const [orderComplete, setOrderComplete] = useState(false)
  const [orderId, setOrderId] = useState(() => generateOrderId())
  
  // Use checkout store for state management
  const checkoutStore = useCheckout()

  // Use the cart store instead of the useCart hook - must be called before any conditional returns
  const cartStoreOptions = useMemo(() => ({
    autoFetch: false, // We'll manually trigger fetch after sessionId is set
    sessionId: sessionId || undefined
  }), [sessionId])
  
  const cartStore = useCartStore2(cartStoreOptions)
  const { cart, enhancedCart, loading, error } = cartStore

  // All useEffect hooks must be called before any conditional returns
  useEffect(() => {
    let id = searchParams.get('sessionId')
    if (!id && typeof window !== 'undefined') {
      id = sessionStorage.getItem('sessionId')
    }
    if (!id && typeof window !== 'undefined') {
      // Generate a new sessionId if missing
      id = `session_${Date.now()}_${Math.random().toString(36).slice(2, 10)}`
      sessionStorage.setItem('sessionId', id)
    }
    setSessionIdState(id)
  }, [searchParams])

  // Ensure we have the latest cart data - only refetch when sessionId changes
  useEffect(() => {
    if (sessionId && cartStore.sessionId !== sessionId) {
      cartStore.setSessionId(sessionId)
      cartStore.refetch()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId])

  // Debug logging
  useEffect(() => {
    const isEmpty = !cart || cart.items.length === 0
    console.log('Checkout: sessionId', sessionId)
    console.log('Checkout: cart', cart)
    console.log('Checkout: enhancedCart', enhancedCart)
    console.log('Checkout: isEmpty', isEmpty)
  }, [sessionId, cart, enhancedCart])

  // Calculate derived state
  const isEmpty = !cart || cart.items.length === 0

  // Wait for sessionId to be initialized
  if (!sessionId) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <Loader2 className="h-16 w-16 animate-spin text-primary" />
          <h1 className="text-2xl font-bold font-montserrat">Loading your cart...</h1>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <Loader2 className="h-16 w-16 animate-spin text-primary" />
          <h1 className="text-2xl font-bold font-montserrat">Loading your cart...</h1>
          <p className="text-muted-foreground">Please wait while we prepare your checkout experience.</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <ShoppingBag className="h-16 w-16 text-destructive" />
          <h1 className="text-2xl font-bold font-montserrat">Something went wrong</h1>
          <p className="text-muted-foreground">We couldn't load your cart. Please try again.</p>
          <p className="text-sm text-destructive">{error.message}</p>
          <Button asChild>
            <Link href="/cart">Return to Cart</Link>
          </Button>
        </div>
      </div>
    )
  }

  if (isEmpty) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <ShoppingBag className="h-16 w-16 text-muted-foreground" />
          <h1 className="text-2xl font-bold font-montserrat">Your cart is empty</h1>
          <p className="text-muted-foreground">You need to add items to your cart before checking out.</p>
          <Button asChild>
            <Link href="/products">Continue Shopping</Link>
          </Button>
        </div>
      </div>
    )
  }

  const handleCompleteOrder = (orderId: string, paymentUrl?: string) => {
    setOrderComplete(true);
    // Store order ID for confirmation page
    setOrderId(orderId);

    // If there's a payment URL, the checkout form will handle the redirect
    // Otherwise, show the order confirmation page
    if (!paymentUrl) {
      // Order completed without external payment (e.g., cash on collection)
      console.log('Order completed:', orderId);
    }
  };

  // If order is complete, show the order confirmation page
  if (orderComplete) {
    return (
      <Suspense fallback={<div className="flex items-center justify-center py-8"><Loader2 className="h-8 w-8 animate-spin" /></div>}>
        <OrderConfirmation
          orderId={orderId}
        />
      </Suspense>
    );
  }

  return (
    <AuthProvider>
      <div className="container px-4 md:px-6 py-6 md:py-10">
        <h1 className="text-2xl md:text-3xl font-bold font-montserrat mb-6">Checkout</h1>
        
        <div className="grid lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <Suspense fallback={<div className="flex items-center justify-center py-8"><Loader2 className="h-8 w-8 animate-spin" /></div>}>
              <AuthCheckoutForm
                onCompleteOrder={handleCompleteOrder}
                sessionId={sessionId}
              />
            </Suspense>
          </div>
          <div>
            <Suspense fallback={<div className="flex items-center justify-center py-8"><Loader2 className="h-8 w-8 animate-spin" /></div>}>
              <CheckoutSummary
                sessionId={sessionId}
              />
            </Suspense>
          </div>
        </div>
      </div>
    </AuthProvider>
  )
}