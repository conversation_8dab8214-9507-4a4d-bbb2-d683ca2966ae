'use client'

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { PaymentStatus } from '@/lib/payment-core/types'

export default function PaymentStatusPage() {
  const searchParams = useSearchParams()
  const [status, setStatus] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  const transactionId = searchParams.get('transactionId')
  const reference = searchParams.get('reference')
  const gateway = searchParams.get('gateway')
  const orderId = searchParams.get('orderId')
  
  useEffect(() => {
    async function checkPaymentStatus() {
      if (!transactionId && !reference) {
        setError('No transaction ID or reference provided')
        setLoading(false)
        return
      }
      
      try {
        // Call API to check payment status
        const response = await fetch(`/api/payments/status?${
          transactionId ? `transactionId=${transactionId}` : ''
        }${
          reference ? `&reference=${reference}` : ''
        }${
          gateway ? `&gateway=${gateway}` : ''
        }`)
        
        if (!response.ok) {
          throw new Error('Failed to fetch payment status')
        }
        
        const data = await response.json()
        setStatus(data.status)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }
    
    checkPaymentStatus()
    
    // Poll for status updates every 5 seconds if payment is still processing
    const interval = setInterval(() => {
      if (status === PaymentStatus.PENDING || status === PaymentStatus.PROCESSING) {
        checkPaymentStatus()
      }
    }, 5000)
    
    return () => clearInterval(interval)
  }, [transactionId, reference, gateway, status])
  
  // Render different UI based on payment status
  const renderStatusContent = () => {
    if (loading) {
      return (
        <div className="flex flex-col items-center justify-center p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
          <h2 className="text-xl font-semibold mb-2">Checking payment status...</h2>
          <p className="text-gray-600">Please wait while we verify your payment.</p>
        </div>
      )
    }
    
    if (error) {
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-red-700 mb-2">Error checking payment</h2>
          <p className="text-red-600 mb-4">{error}</p>
          <div className="flex gap-4">
            <Link 
              href="/checkout" 
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
            >
              Return to checkout
            </Link>
            <button 
              onClick={() => window.location.reload()} 
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
            >
              Try again
            </button>
          </div>
        </div>
      )
    }
    
    switch (status) {
      case PaymentStatus.COMPLETED:
        return (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-green-700 mb-2">Payment successful!</h2>
            <p className="text-green-600 mb-4">
              Your payment has been successfully processed.
              {orderId && ` Order #${orderId} has been confirmed.`}
            </p>
            <div className="flex gap-4">
              <Link 
                href={orderId ? `/account/orders/${orderId}` : "/account/orders"} 
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                View order details
              </Link>
              <Link 
                href="/" 
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
              >
                Continue shopping
              </Link>
            </div>
          </div>
        )
        
      case PaymentStatus.PENDING:
      case PaymentStatus.PROCESSING:
        return (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-yellow-700 mb-2">Payment processing</h2>
            <p className="text-yellow-600 mb-4">
              Your payment is being processed. This page will automatically update when complete.
              Please do not close this page.
            </p>
            <div className="flex items-center gap-2 mb-4">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-yellow-600"></div>
              <span className="text-yellow-600">Checking status...</span>
            </div>
            <div className="flex gap-4">
              <Link 
                href="/" 
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
              >
                Continue shopping
              </Link>
            </div>
          </div>
        )
        
      case PaymentStatus.FAILED:
        return (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-red-700 mb-2">Payment failed</h2>
            <p className="text-red-600 mb-4">
              Unfortunately, your payment could not be processed.
              Please try again or use a different payment method.
            </p>
            <div className="flex gap-4">
              <Link 
                href="/checkout" 
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                Return to checkout
              </Link>
              <Link 
                href="/contact" 
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
              >
                Contact support
              </Link>
            </div>
          </div>
        )
        
      case PaymentStatus.CANCELLED:
        return (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-700 mb-2">Payment cancelled</h2>
            <p className="text-gray-600 mb-4">
              Your payment was cancelled.
              You can return to checkout to try again.
            </p>
            <div className="flex gap-4">
              <Link 
                href="/checkout" 
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                Return to checkout
              </Link>
              <Link 
                href="/" 
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
              >
                Continue shopping
              </Link>
            </div>
          </div>
        )
        
      case PaymentStatus.REFUNDED:
        return (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-blue-700 mb-2">Payment refunded</h2>
            <p className="text-blue-600 mb-4">
              Your payment has been refunded.
              The refund may take a few days to appear in your account.
            </p>
            <div className="flex gap-4">
              <Link 
                href="/account/orders" 
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                View orders
              </Link>
              <Link 
                href="/" 
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
              >
                Continue shopping
              </Link>
            </div>
          </div>
        )
        
      default:
        return (
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-700 mb-2">Payment status unknown</h2>
            <p className="text-gray-600 mb-4">
              We couldn't determine the status of your payment.
              Please contact customer support for assistance.
            </p>
            <div className="flex gap-4">
              <Link 
                href="/checkout" 
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                Return to checkout
              </Link>
              <Link 
                href="/contact" 
                className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
              >
                Contact support
              </Link>
            </div>
          </div>
        )
    }
  }
  
  return (
    <div className="container mx-auto max-w-4xl py-12 px-4">
      <h1 className="text-3xl font-bold mb-8">Payment Status</h1>
      
      {renderStatusContent()}
      
      {/* Payment details */}
      {!loading && !error && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Payment Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {reference && (
              <div>
                <p className="text-sm text-gray-500">Reference</p>
                <p className="font-medium">{reference}</p>
              </div>
            )}
            {transactionId && (
              <div>
                <p className="text-sm text-gray-500">Transaction ID</p>
                <p className="font-medium">{transactionId}</p>
              </div>
            )}
            {gateway && (
              <div>
                <p className="text-sm text-gray-500">Payment Method</p>
                <p className="font-medium">{gateway}</p>
              </div>
            )}
            {status && (
              <div>
                <p className="text-sm text-gray-500">Status</p>
                <p className="font-medium">{status}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}