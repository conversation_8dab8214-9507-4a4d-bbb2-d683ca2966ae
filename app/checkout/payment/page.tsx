/**
 * Checkout Payment Page
 * 
 * This page demonstrates how to use the enhanced payment components with PayFast integration.
 */

'use client'

import { useState } from 'react'
import { useSearchParams } from 'next/navigation'
import EnhancedPaymentForm from '@/components/checkout/EnhancedPaymentForm'
import { PaymentMethod, PaymentGateway, PaymentStatus } from '@/lib/payment-core/types'
import { Card, CardContent } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { Shield } from 'lucide-react'

export default function CheckoutPaymentPage() {
  const searchParams = useSearchParams()
  const orderId = searchParams.get('orderId') || `ORDER-${Date.now()}`
  const amount = parseFloat(searchParams.get('amount') || '100')
  const customerEmail = searchParams.get('email') || '<EMAIL>'
  const customerName = searchParams.get('name') || 'Test Customer'
  
  // Split customer name into first and last name
  const nameParts = customerName.split(' ')
  const firstName = nameParts[0]
  const lastName = nameParts.slice(1).join(' ') || 'Customer'
  
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null)
  
  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">Checkout Payment</h1>
      
      <div className="grid gap-8 md:grid-cols-2">
        <div>
          <Card>
            <CardContent className="pt-6">
              <h2 className="text-xl font-semibold mb-4">Order Summary</h2>
              
              <Separator className="my-4" />
              
              <div className="space-y-2">
                <div className="flex justify-between mb-2">
                  <span>Order ID:</span>
                  <span className="font-medium">{orderId}</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span>Subtotal:</span>
                  <span>R {(amount * 0.85).toFixed(2)}</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span>VAT (15%):</span>
                  <span>R {(amount * 0.15).toFixed(2)}</span>
                </div>
                <Separator className="my-2" />
                <div className="flex justify-between font-bold">
                  <span>Total:</span>
                  <span>R {amount.toFixed(2)}</span>
                </div>
              </div>
              
              <Separator className="my-4" />
              
              <div>
                <h3 className="font-medium mb-2">Customer Information</h3>
                <div className="text-sm text-gray-600 space-y-1">
                  <p>{firstName} {lastName}</p>
                  <p>{customerEmail}</p>
                </div>
              </div>
              
              <div className="mt-6">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-yellow-800 mb-2">Test Mode Notice</h3>
                  <p className="text-xs text-yellow-700">
                    This is a test environment. No actual payments will be processed.
                    You can use test card details for card payments.
                  </p>
                </div>
              </div>
              
              <div className="mt-6 flex items-center justify-center gap-2 text-xs text-gray-500">
                <Shield className="h-3 w-3" />
                <span>Secured by PayFast payment protection</span>
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div>
          <Card>
            <CardContent className="pt-6">
              <h2 className="text-xl font-semibold mb-4">Payment Details</h2>
              
              <EnhancedPaymentForm
                amount={amount}
                currency="ZAR"
                orderId={orderId}
                customerEmail={customerEmail}
                customerFirstName={firstName}
                customerLastName={lastName}
                items={[
                  {
                    id: 'test-item',
                    name: 'Test Product',
                    quantity: 1,
                    unitPrice: amount,
                    totalPrice: amount
                  }
                ]}
                onSuccess={(data) => {
                  console.log('Payment success:', data)
                }}
                onError={(error) => {
                  console.error('Payment error:', error)
                }}
                onStatusChange={(status) => {
                  setPaymentStatus(status)
                }}
              />
            </CardContent>
          </Card>
        </div>
      </div>
      
      <div className="mt-8 text-center text-sm text-gray-500">
        <p>
          By proceeding with the payment, you agree to our{' '}
          <a href="/terms" className="text-primary hover:underline">Terms of Service</a>{' '}
          and{' '}
          <a href="/privacy" className="text-primary hover:underline">Privacy Policy</a>.
        </p>
      </div>
    </div>
  )
}