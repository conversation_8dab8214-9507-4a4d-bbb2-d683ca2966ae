import type React from "react"
import "./globals.css"
import { Inter } from "next/font/google"
import localFont from "next/font/local"
import { LayoutWrapper } from "@/components/layout-wrapper"
import { QueryProvider } from "@/providers/query-provider"
import { AuthProvider } from "@/providers/auth-provider"

const inter = Inter({ 
  subsets: ["latin"],
  variable: "--font-inter",
  display: 'swap',
})

const p22 = localFont({
  src: "./fonts/P22Underground-Light.otf",
  variable: "--font-p22",
  display: 'swap',
  weight: '300 400 500 600 700',
  style: 'normal',
})

export const metadata = {
  title: {
    default: 'Coco Milk Kids | Premium Children\'s Clothing',
    template: '%s | Coco Milk Kids'
  },
  description: 'Discover stylish, comfortable, and sustainable children\'s clothing at Coco Milk Kids. Premium quality for growing adventures.',
  keywords: ['kids clothing', 'children\'s fashion', 'sustainable kids clothes', 'premium children\'s wear', 'South African kids fashion'],
  authors: [{ name: 'Coco Milk Kids' }],
  creator: 'Coco Milk Kids',
  publisher: 'Coco Milk Kids',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'https://cocomilkkids.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_ZA',
    url: '/',
    siteName: 'Coco Milk Kids',
    title: 'Coco Milk Kids | Premium Children\'s Clothing',
    description: 'Discover stylish, comfortable, and sustainable children\'s clothing at Coco Milk Kids. Premium quality for growing adventures.',
    images: [
      {
        url: '/images/og-default.jpg',
        width: 1200,
        height: 630,
        alt: 'Coco Milk Kids - Premium Children\'s Clothing',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    site: '@cocomilkkids',
    creator: '@cocomilkkids',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
  },
}



export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <AuthProvider>
      <html lang="en-ZA" className={`${inter.variable} ${p22.variable}`} suppressHydrationWarning>
        <head>
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <meta name="theme-color" content="#ffffff" />
          <link rel="icon" href="/favicon.ico" />
          <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
          <link rel="manifest" href="/manifest.json" />
          <link rel="preload" href="/fonts/P22Underground-Light.otf" as="font" type="font/otf" crossOrigin="anonymous" />
        </head>
        <body className="min-h-screen bg-background font-sans antialiased">
          <QueryProvider>
            <LayoutWrapper>
              {children}
            </LayoutWrapper>
          </QueryProvider>
        </body>
      </html>
    </AuthProvider>
  )
}
