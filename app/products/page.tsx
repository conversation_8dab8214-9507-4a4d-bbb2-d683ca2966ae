import { ProductGrid } from "@/components/storefront/products/product-grid"
import { ProductFilters } from "@/components/storefront/products/product-filters"
import { ProductSort } from "@/components/storefront/products/product-sort"
import { MobileFilters } from "@/components/mobile-filters"
import { Suspense } from "react"
import { Skeleton } from "@/components/ui/skeleton"

export default async function ProductsPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const { sort, category, color, size } = await searchParams

  return (
    <div className="container px-4 md:px-6 py-6 md:py-8">
      <div className="flex flex-col md:flex-row justify-between items-start gap-8">
        {/* Desktop Filters - Hidden on Mobile */}
        <div className="hidden md:block w-full md:w-1/4 lg:w-1/5 sticky top-20">
          <ProductFilters
            selectedCategory={category as string}
            selectedColor={color as string}
            selectedSize={size as string}
          />
        </div>

        <div className="w-full md:w-3/4 lg:w-4/5">
          {/* Mobile Header with Filter Button */}
          <div className="flex justify-between items-center mb-6 pb-4 border-b border-gray-100">
            <h1 className="text-2xl font-normal">All Products</h1>
            <div className="flex items-center gap-2">
              {/* Mobile Filter Button */}
              <div className="md:hidden">
                <MobileFilters
                  selectedCategory={category as string}
                  selectedColor={color as string}
                  selectedSize={size as string}
                />
              </div>
              <ProductSort selectedSort={sort as string} />
            </div>
          </div>

          <Suspense fallback={<ProductGridSkeleton />}>
            <ProductGrid
              sort={sort as string}
              category={category as string}
              color={color as string}
              size={size as string}
            />
          </Suspense>
        </div>
      </div>
    </div>
  )
}

function ProductGridSkeleton() {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-x-4 gap-y-8">
      {Array.from({ length: 12 }).map((_, i) => (
        <div key={i} className="space-y-3">
          <Skeleton className="aspect-[3/4] w-full" />
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-3 w-1/2" />
        </div>
      ))}
    </div>
  )
}
