'use client'

import { useState, useEffect, Suspense } from "react"
import { EnhancedProductGallery } from "@/components/storefront/products/enhanced-product-gallery"
import { EnhancedProductInfo } from "@/components/storefront/products/enhanced-product-info"
import { AIProductRecommendations } from "@/components/storefront/products/ai-product-recommendations"
import { ProductShare } from "@/components/storefront/products/product-share"
import { Breadcrumb } from "@/components/ui/breadcrumb"
import { Skeleton } from "@/components/ui/skeleton"
import type { StorefrontVariant } from "@/lib/ecommerce/utils/product-transformers"
import type { Product } from "@/lib/ecommerce/types/product"

interface ProductDetailProps {
  product: Product
}

export default function ProductDetail({ product }: ProductDetailProps) {
  const [selectedVariant, setSelectedVariant] = useState<StorefrontVariant | null>(null)
  const [browsingHistory, setBrowsingHistory] = useState<string[]>([])

  useEffect(() => {
    // Load browsing history from localStorage
    try {
      const history = JSON.parse(localStorage.getItem('browsingHistory') || '[]')
      setBrowsingHistory(history)
      
      // Add current product to history
      if (!history.includes(product.slug)) {
        const updatedHistory = [product.slug, ...history].slice(0, 10) // Keep last 10 items
        localStorage.setItem('browsingHistory', JSON.stringify(updatedHistory))
        setBrowsingHistory(updatedHistory)
      }
    } catch (error) {
      console.error('Error managing browsing history:', error)
      // Fail silently and continue with empty history
      setBrowsingHistory([])
    }
  }, [product.slug])

  const breadcrumbItems = [
    { label: "Products", href: "/products" },
    { label: product.categories?.[0]?.name || "Category", href: `/products?category=${product.categories?.[0]?.id}` },
    { label: product.title }
  ]

  return (
    <>
      <Breadcrumb items={breadcrumbItems} />
      
      <div className="grid md:grid-cols-2 gap-8 mb-12">
        {/* Enhanced Product Gallery */}
        <Suspense fallback={<Skeleton className="aspect-square w-full" />}>
          <EnhancedProductGallery
            images={product.images.map(img => img.url)}
            productName={product.title}
            selectedVariant={selectedVariant}
          />
        </Suspense>

        <div className="flex flex-col gap-6">
          {/* Share Button */}
          <div className="flex justify-end">
            <ProductShare 
              productName={product.title}
              productUrl={typeof window !== 'undefined' ? window.location.href : ''}
            />
          </div>

          {/* Enhanced Product Info */}
          <EnhancedProductInfo
            product={{
              id: product.id,
              name: product.title,
              description: product.description || '',
              price: typeof product.price === 'object' ? product.price.amount : product.price,
              compareAtPrice: product.compareAtPrice ? 
                (typeof product.compareAtPrice === 'object' ? product.compareAtPrice.amount : product.compareAtPrice) : 
                undefined,
              colors: product.options?.find(opt => opt.name.toLowerCase().includes('color'))?.values.map(v => ({
                name: v,
                value: v.toLowerCase()
              })) || [],
              sizes: product.options?.find(opt => opt.name.toLowerCase().includes('size'))?.values || [],
              slug: product.slug,
              categoryId: product.categories?.[0]?.id || 'default',
              hasVariants: Boolean(product.options?.length)
            }}
            onVariantChange={(variant) => {
              if (variant) {
                setSelectedVariant({
                  id: variant.id,
                  title: variant.title,
                  sku: variant.id,
                  price: variant.price || 0,
                  available: true,
                  inventoryQuantity: 100, // This should come from the product data
                  options: variant.options || [],
                  compareAtPrice: variant.compareAtPrice
                })
              } else {
                setSelectedVariant(null)
              }
            }}
          />
        </div>
      </div>
    </>
  )
}