import { Suspense } from "react"
import { notFound } from "next/navigation"
import { getProductBySlug } from "@/lib/ecommerce/actions/products"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import ProductDetail from "./product-detail-client"
import type { Metadata } from 'next'

// Export a default async Server Component
export default async function ProductPage({
  params
}: {
  params: { slug: string }
}) {
  console.log('Page - Fetching product with slug:', params.slug)

  // Fetch product data server-side directly using the service
  const result = await getProductBySlug(params.slug)
  
  console.log('Page - Service response:', {
    success: result.success,
    error: result.error,
    hasData: !!result.data
  })

  if (!result.success || !result.data) {
    console.log('Page - Product fetch failed:', result.error)

    if (result.error?.code === 'PRODUCT_NOT_FOUND') {
      notFound()
    }

    return (
      <Alert variant="destructive" className="container mx-auto px-4 md:px-6 py-6">
        <AlertCircle className="h-4 w-4" />
        <span>Error loading product: {result.error?.message}</span>
      </Alert>
    )
  }

  const product = result.data

  return (
    <div className="container mx-auto px-4 md:px-6 py-6 md:py-8">
      <Suspense
        fallback={
          <div>
            <Skeleton className="h-6 w-48 mb-6" />
            <div className="grid md:grid-cols-2 gap-8">
              <Skeleton className="aspect-square w-full" />
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-10 w-32" />
                <Skeleton className="h-12 w-full" />
              </div>
            </div>
          </div>
        }
      >
        <ProductDetail product={product} />
      </Suspense>
    </div>
  )
}

// Generate metadata for the page
export async function generateMetadata({ 
  params 
}: { 
  params: { slug: string } 
}): Promise<Metadata> {
  console.log('Page - Generating metadata for slug:', params.slug)

  const result = await getProductBySlug(params.slug)

  if (!result.success || !result.data) {
    console.log('Page - Metadata generation failed:', result.error)
    return {
      title: 'Product Not Found',
      description: 'The requested product could not be found.'
    }
  }

  const product = result.data

  return {
    title: product.seo?.title || product.title,
    description: product.seo?.description || product.description,
    openGraph: product.images?.[0]?.url ? {
      title: product.seo?.ogTitle || product.title,
      description: product.seo?.ogDescription || product.description,
      images: [product.images[0].url]
    } : undefined
  }
}