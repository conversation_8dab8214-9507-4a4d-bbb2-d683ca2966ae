"use client"

import { useEffect, useState } from "react"
import { usePara<PERSON>, useSearchParams } from "next/navigation"
import { OrderConfirmation } from "@/components/storefront/orders/order-confirmation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle, XCircle, Clock, AlertTriangle } from "lucide-react"
import Link from "next/link"
import { useOrder } from "@/lib/ecommerce/hooks/use-orders"

export default function OrderPage() {
  const params = useParams()
  const searchParams = useSearchParams()
  const orderId = params.id as string
  const paymentStatus = searchParams.get('payment')
  
  const { order, loading, error } = useOrder({ orderId, autoFetch: true })
  const [paymentMessage, setPaymentMessage] = useState<{
    type: 'success' | 'error' | 'pending' | 'cancelled'
    title: string
    description: string
  } | null>(null)

  useEffect(() => {
    if (paymentStatus) {
      switch (paymentStatus) {
        case 'success':
          setPaymentMessage({
            type: 'success',
            title: 'Payment Successful!',
            description: 'Your payment has been processed successfully. You will receive a confirmation email shortly.'
          })
          break
        case 'cancelled':
          setPaymentMessage({
            type: 'cancelled',
            title: 'Payment Cancelled',
            description: 'Your payment was cancelled. Your order is still pending - you can retry payment from your account.'
          })
          break
        case 'failed':
          setPaymentMessage({
            type: 'error',
            title: 'Payment Failed',
            description: 'There was an issue processing your payment. Please try again or contact support.'
          })
          break
        case 'pending':
          setPaymentMessage({
            type: 'pending',
            title: 'Payment Pending',
            description: 'Your payment is being processed. This may take a few minutes to complete.'
          })
          break
      }
    }
  }, [paymentStatus])

  const getPaymentIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-12 w-12 text-green-600" />
      case 'error':
        return <XCircle className="h-12 w-12 text-red-600" />
      case 'pending':
        return <Clock className="h-12 w-12 text-yellow-600" />
      case 'cancelled':
        return <AlertTriangle className="h-12 w-12 text-orange-600" />
      default:
        return <CheckCircle className="h-12 w-12 text-green-600" />
    }
  }

  const getPaymentBgColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200'
      case 'error':
        return 'bg-red-50 border-red-200'
      case 'pending':
        return 'bg-yellow-50 border-yellow-200'
      case 'cancelled':
        return 'bg-orange-50 border-orange-200'
      default:
        return 'bg-green-50 border-green-200'
    }
  }

  if (loading) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-[#012169]"></div>
          <h1 className="text-2xl font-bold font-montserrat">Loading Order...</h1>
          <p className="text-muted-foreground">Please wait while we fetch your order details.</p>
        </div>
      </div>
    )
  }

  if (error || !order) {
    return (
      <div className="container px-4 md:px-6 py-16">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <XCircle className="h-16 w-16 text-red-600" />
          <h1 className="text-2xl font-bold font-montserrat">Order Not Found</h1>
          <p className="text-muted-foreground">
            {error || "We couldn't find the order you're looking for."}
          </p>
          <div className="flex space-x-4">
            <Button asChild variant="outline">
              <Link href="/account">View All Orders</Link>
            </Button>
            <Button asChild>
              <Link href="/products">Continue Shopping</Link>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white">
      {/* Payment Status Message */}
      {paymentMessage && (
        <div className="border-b">
          <div className="container px-4 md:px-6 py-8">
            <Card className={`${getPaymentBgColor(paymentMessage.type)} border-2`}>
              <CardContent className="pt-6">
                <div className="flex flex-col items-center text-center space-y-4">
                  {getPaymentIcon(paymentMessage.type)}
                  <div>
                    <h2 className="text-xl font-semibold mb-2">{paymentMessage.title}</h2>
                    <p className="text-muted-foreground">{paymentMessage.description}</p>
                  </div>
                  {paymentMessage.type === 'cancelled' && (
                    <div className="flex space-x-4">
                      <Button asChild>
                        <Link href={`/frontend/checkout?retry=${orderId}`}>Retry Payment</Link>
                      </Button>
                      <Button variant="outline" asChild>
                        <Link href="/account">View Account</Link>
                      </Button>
                    </div>
                  )}
                  {paymentMessage.type === 'error' && (
                    <div className="flex space-x-4">
                      <Button asChild>
                        <Link href={`/frontend/checkout?retry=${orderId}`}>Try Again</Link>
                      </Button>
                      <Button variant="outline" asChild>
                        <Link href="/contact">Contact Support</Link>
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Order Confirmation */}
      <OrderConfirmation 
        orderId={orderId} 
        shippingMethod={order.shippingMethod}
      />

      {/* Additional Actions */}
      <div className="border-t">
        <div className="container px-4 md:px-6 py-8">
          <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <Button asChild variant="outline">
              <Link href="/account">View All Orders</Link>
            </Button>
            <Button asChild variant="outline">
              <Link href="/products">Continue Shopping</Link>
            </Button>
            <Button asChild>
              <Link href="/contact">Contact Support</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
