import { Suspense } from 'react'
import { PageRenderer } from '@/lib/page-builder/components/page-renderer'
import { PageData } from '@/lib/page-builder/types'

// This demonstrates how the hardcoded products page can be converted to use the page builder
// The page will render exactly the same as the hardcoded version but using the page builder system

export default async function ProductsDynamicPage({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>
}) {
  const resolvedSearchParams = await searchParams
  // Create a page configuration that uses the Product Listing Block
  const pageData: PageData = {
    id: 'products-dynamic',
    title: 'All Products',
    slug: 'products-dynamic',
    description: 'Browse our complete collection of products',
    status: 'published',
    type: 'custom',
    blocks: [
      {
        id: 'product-listing-1',
        type: 'product-listing',
        position: 0,
        isVisible: true,
        configuration: {
          title: 'All Products',
          showFilters: true,
          showSort: true,
          showMobileFilters: true,
          filtersPosition: 'left'
        },
        content: {},
        styling: {},
        responsive: {
          desktop: { isVisible: true },
          tablet: { isVisible: true },
          mobile: { isVisible: true }
        }
      }
    ],
    settings: {
      title: 'All Products',
      description: 'Browse our complete collection of products',
      seoTitle: 'All Products - Coco Milk Kids',
      seoDescription: 'Discover our complete collection of premium children\'s clothing at Coco Milk Kids.',
      requiresAuth: false,
      allowComments: false
    }
  }

  return (
    <div>
      {/* This will render exactly like the hardcoded products page */}
      <DynamicProductListingRenderer pageData={pageData} searchParams={resolvedSearchParams} />
    </div>
  )
}

// Custom renderer that passes searchParams to the Product Listing Block
function DynamicProductListingRenderer({ 
  pageData, 
  searchParams 
}: { 
  pageData: PageData
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  // Clone the page data and inject searchParams into the block
  const enhancedPageData = {
    ...pageData,
    blocks: pageData.blocks.map(block => {
      if (block.type === 'product-listing') {
        return {
          ...block,
          configuration: {
            ...block.configuration,
            searchParams
          }
        }
      }
      return block
    })
  }

  return <PageRenderer page={enhancedPageData} />
}
