import { Metada<PERSON> } from "next"
import { AuthCard } from "@/components/auth/auth-card"
import { PasswordResetForm } from "@/components/auth/forms/password-reset-form"
import { Button } from "@/components/ui/button"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Reset Password | Coco Milk Store",
  description: "Reset your password",
}

interface ResetPasswordPageProps {
  searchParams: { token?: string }
}

export default function ResetPasswordPage({ searchParams }: ResetPasswordPageProps) {
  const { token } = searchParams
  const mode = token ? "reset" : "request"

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">
            {mode === "request" ? "Reset your password" : "Create new password"}
          </h1>
          <p className="text-sm text-muted-foreground">
            {mode === "request" 
              ? "Enter your email and we'll send you a reset link" 
              : "Enter your new password below"}
          </p>
        </div>
        
        <AuthCard
          title={mode === "request" ? "Reset Password" : "Create New Password"}
          description={
            mode === "request"
              ? "Enter your email to receive a password reset link"
              : "Enter and confirm your new password"
          }
          footer={
            <Button variant="link" asChild className="mx-auto">
              <Link href="/login">Back to login</Link>
            </Button>
          }
        >
          <PasswordResetForm 
            mode={mode} 
            token={token} 
            onSuccess={() => {}} 
          />
        </AuthCard>
      </div>
    </div>
  )
}