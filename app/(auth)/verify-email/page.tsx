import { Metada<PERSON> } from "next"
import { AuthCard } from "@/components/auth/auth-card"
import { EmailVerificationForm } from "@/components/auth/forms/email-verification-form"
import { Button } from "@/components/ui/button"
import Link from "next/link"

export const metadata: Metadata = {
  title: "Verify Email | Coco Milk Store",
  description: "Verify your email address",
}

interface VerifyEmailPageProps {
  searchParams: { token?: string; email?: string }
}

export default function VerifyEmailPage({ searchParams }: VerifyEmailPageProps) {
  const { token, email } = searchParams

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">
            Verify your email
          </h1>
          <p className="text-sm text-muted-foreground">
            {token 
              ? "Verifying your email address..." 
              : "Enter the verification code sent to your email"}
          </p>
        </div>
        
        <AuthCard
          title="Email Verification"
          description={
            token
              ? "Please wait while we verify your email address"
              : "Enter the verification code from your email"
          }
          footer={
            <Button variant="link" asChild className="mx-auto">
              <Link href="/login">Back to login</Link>
            </Button>
          }
        >
          <EmailVerificationForm 
            token={token} 
            email={email} 
            redirectUrl="/login" 
          />
        </AuthCard>
      </div>
    </div>
  )
}