import { Metadata } from "next"
import { AuthFormContainer } from "@/components/auth/auth-form-container"

export const metadata: Metadata = {
  title: "Login | Coco Milk Store",
  description: "Login to your account",
}

interface LoginPageProps {
  searchParams: { redirect?: string }
}

export default function LoginPage({ searchParams }: LoginPageProps) {
  const { redirect } = searchParams
  const redirectUrl = redirect ? decodeURIComponent(redirect) : "/account"

  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">
            Welcome back
          </h1>
          <p className="text-sm text-muted-foreground">
            Enter your credentials to sign in to your account
          </p>
        </div>
        <AuthFormContainer 
          defaultMode="login" 
          redirectUrl={redirectUrl} 
          showSocialLogin={true}
        />
      </div>
    </div>
  )
}