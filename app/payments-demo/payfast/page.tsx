"use client"

import { useState } from 'react'
import { EnhancedPayfastPayment } from '@/components/payments/enhanced-payfast-payment'
import { 
  PaymentRequest, 
  PaymentResponse, 
  PaymentMethod, 
  PaymentGateway 
} from '@/lib/payment-core/types'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { CheckCircle, AlertCircle, CreditCard, Building2, Smartphone } from 'lucide-react'

export default function PayfastDemoPage() {
  const [amount, setAmount] = useState('100.00')
  const [reference, setReference] = useState(`ORDER-${Date.now()}`)
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>(PaymentMethod.CARD)
  const [paymentResponse, setPaymentResponse] = useState<PaymentResponse | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  const generatePaymentRequest = (): PaymentRequest => {
    return {
      amount: {
        amount: parseFloat(amount),
        currency: 'ZAR'
      },
      customer: {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '0123456789'
      },
      items: [
        {
          id: '1',
          name: 'Demo Product',
          description: 'This is a demo product',
          quantity: 1,
          unitPrice: parseFloat(amount),
          totalPrice: parseFloat(amount)
        }
      ],
      metadata: {
        orderId: reference,
        source: 'payfast-demo'
      },
      returnUrl: `${window.location.origin}/payments-demo/payfast/success?reference=${reference}`,
      cancelUrl: `${window.location.origin}/payments-demo/payfast/cancel?reference=${reference}`,
      notifyUrl: `${window.location.origin}/api/webhooks/payfast/notify`,
      reference: reference,
      description: 'Demo Payment'
    }
  }

  const handleSuccess = (response: PaymentResponse) => {
    setPaymentResponse(response)
    setSuccess('Payment URL generated successfully')
    setError(null)
  }

  const handleError = (errorMessage: string) => {
    setError(errorMessage)
    setSuccess(null)
  }

  const handleCancel = () => {
    setPaymentResponse(null)
    setSuccess(null)
    setError(null)
  }

  const handleMethodChange = (method: PaymentMethod) => {
    setSelectedMethod(method)
  }

  const resetDemo = () => {
    setAmount('100.00')
    setReference(`ORDER-${Date.now()}`)
    setPaymentResponse(null)
    setError(null)
    setSuccess(null)
  }

  return (
    <div className="container mx-auto py-10 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">PayFast Payment Demo</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Payment Configuration</CardTitle>
              <CardDescription>
                Configure your test payment details
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {success && (
                <Alert className="bg-green-50 border-green-200">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertTitle>Success</AlertTitle>
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}
              
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              
              <div className="space-y-2">
                <Label htmlFor="amount">Amount (ZAR)</Label>
                <Input
                  id="amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  disabled={!!paymentResponse}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="reference">Order Reference</Label>
                <Input
                  id="reference"
                  value={reference}
                  onChange={(e) => setReference(e.target.value)}
                  disabled={!!paymentResponse}
                />
              </div>
              
              <div className="space-y-2">
                <Label>Payment Method</Label>
                <Tabs defaultValue="card" className="w-full" onValueChange={(value) => {
                  switch (value) {
                    case 'card':
                      handleMethodChange(PaymentMethod.CARD)
                      break
                    case 'eft':
                      handleMethodChange(PaymentMethod.EFT)
                      break
                    case 'mobile':
                      handleMethodChange(PaymentMethod.MOBILE_MONEY)
                      break
                  }
                }}>
                  <TabsList className="grid grid-cols-3 w-full">
                    <TabsTrigger value="card" className="flex items-center gap-1">
                      <CreditCard className="h-4 w-4" />
                      <span className="hidden sm:inline">Card</span>
                    </TabsTrigger>
                    <TabsTrigger value="eft" className="flex items-center gap-1">
                      <Building2 className="h-4 w-4" />
                      <span className="hidden sm:inline">EFT</span>
                    </TabsTrigger>
                    <TabsTrigger value="mobile" className="flex items-center gap-1">
                      <Smartphone className="h-4 w-4" />
                      <span className="hidden sm:inline">Mobile</span>
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
              
              {paymentResponse && (
                <div className="space-y-2">
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={resetDemo}
                  >
                    Reset Demo
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
        
        <div>
          <EnhancedPayfastPayment
            paymentRequest={generatePaymentRequest()}
            method={selectedMethod}
            onSuccess={handleSuccess}
            onError={handleError}
            onCancel={handleCancel}
            showLogo={true}
            showSecurityBadge={true}
          />
        </div>
      </div>
      
      {paymentResponse && (
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Payment Response</CardTitle>
            <CardDescription>
              Details of the payment request
            </CardDescription>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-50 p-4 rounded-md overflow-auto text-sm">
              {JSON.stringify(paymentResponse, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  )
}