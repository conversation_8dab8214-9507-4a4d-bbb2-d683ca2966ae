"use client"

import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { CheckCircle, ArrowLeft, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { PaymentStatus } from '@/lib/payment-core/types'

export default function PayfastSuccessPage() {
  const searchParams = useSearchParams()
  const reference = searchParams.get('reference')
  const [loading, setLoading] = useState(true)
  const [status, setStatus] = useState<PaymentStatus | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (reference) {
      // In a real implementation, you would check the payment status
      // For demo purposes, we'll simulate a successful payment
      const timer = setTimeout(() => {
        setStatus(PaymentStatus.COMPLETED)
        setLoading(false)
      }, 1500)

      return () => clearTimeout(timer)
    } else {
      setError('Missing reference parameter')
      setLoading(false)
    }
  }, [reference])

  return (
    <div className="container mx-auto py-10 max-w-md">
      <Card>
        <CardHeader className="text-center">
          {loading ? (
            <div className="mx-auto w-12 h-12 flex items-center justify-center mb-4">
              <Loader2 className="h-8 w-8 text-blue-600 animate-spin" />
            </div>
          ) : status === PaymentStatus.COMPLETED ? (
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          ) : (
            <div className="mx-auto w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mb-4">
              <AlertDescription className="h-8 w-8 text-yellow-600" />
            </div>
          )}
          
          <CardTitle className="text-2xl">
            {loading ? 'Verifying Payment...' : 
             status === PaymentStatus.COMPLETED ? 'Payment Successful!' : 
             'Payment Status Unknown'}
          </CardTitle>
          
          <CardDescription>
            {loading ? 'Please wait while we verify your payment' : 
             status === PaymentStatus.COMPLETED ? 'Your payment has been successfully processed' : 
             'We could not verify the status of your payment'}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          {!loading && (
            <div className="bg-gray-50 p-4 rounded-md">
              <div className="flex justify-between mb-2">
                <span className="text-gray-600">Reference:</span>
                <span className="font-mono">{reference || 'N/A'}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Status:</span>
                <span className="font-medium">
                  {status === PaymentStatus.COMPLETED ? 'Completed' : 
                   status === PaymentStatus.PENDING ? 'Pending' : 
                   status === PaymentStatus.FAILED ? 'Failed' : 
                   'Unknown'}
                </span>
              </div>
            </div>
          )}
        </CardContent>
        
        <CardFooter>
          <Button asChild className="w-full">
            <Link href="/payments-demo/payfast">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Return to Demo
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}