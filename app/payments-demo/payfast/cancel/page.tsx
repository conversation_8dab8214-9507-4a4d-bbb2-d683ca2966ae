"use client"

import { useSearchParams } from 'next/navigation'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { XCircle, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function PayfastCancelPage() {
  const searchParams = useSearchParams()
  const reference = searchParams.get('reference')

  return (
    <div className="container mx-auto py-10 max-w-md">
      <Card>
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <XCircle className="h-8 w-8 text-red-600" />
          </div>
          
          <CardTitle className="text-2xl">Payment Cancelled</CardTitle>
          
          <CardDescription>
            Your payment has been cancelled. No charges have been made.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="bg-gray-50 p-4 rounded-md">
            <div className="flex justify-between mb-2">
              <span className="text-gray-600">Reference:</span>
              <span className="font-mono">{reference || 'N/A'}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-gray-600">Status:</span>
              <span className="font-medium">Cancelled</span>
            </div>
          </div>
        </CardContent>
        
        <CardFooter>
          <Button asChild className="w-full">
            <Link href="/payments-demo/payfast">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Return to Demo
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}