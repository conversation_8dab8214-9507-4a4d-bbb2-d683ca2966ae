"use client"

import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  PaymentForm,
  PaymentGatewaySelector,
  PayFastPayment,
  OzowPayment,
  PaymentStatus,
  PaymentProcessing,
  PaymentSuccess,
  PaymentError,
  PaymentPending
} from '@/components/payments'
import { 
  PaymentRequest, 
  PaymentResponse, 
  PaymentGateway, 
  PaymentMethod,
  PaymentStatus as PaymentStatusEnum
} from '@/lib/payments/types'
// import { toast } from 'sonner' // Removed to avoid dependency issues

// Sample payment request for demo
const createSamplePaymentRequest = (): PaymentRequest => ({
  amount: {
    amount: 299.99,
    currency: 'ZAR',
    formatted: 'R299.99',
  },
  customer: {
    id: 'demo-customer-123',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    phone: '0123456789',
    address: {
      line1: '123 Main Street',
      line2: 'Apartment 4B',
      city: 'Cape Town',
      state: 'Western Cape',
      postalCode: '8001',
      country: 'ZA',
    },
  },
  items: [
    {
      id: 'item-1',
      name: 'Premium Cotton T-Shirt',
      description: 'High-quality cotton t-shirt in navy blue',
      quantity: 2,
      unitPrice: 149.99,
      totalPrice: 299.98,
      sku: 'TSHIRT-NAVY-L',
      category: 'Clothing',
    },
  ],
  metadata: {
    orderId: 'ORDER-2024-001',
    customerId: 'demo-customer-123',
    source: 'web',
    campaign: 'summer-sale',
  },
  returnUrl: typeof window !== 'undefined' ? `${window.location.origin}/payment/success` : '/payment/success',
  cancelUrl: typeof window !== 'undefined' ? `${window.location.origin}/payment/cancelled` : '/payment/cancelled',
  notifyUrl: typeof window !== 'undefined' ? `${window.location.origin}/api/webhooks/payment` : '/api/webhooks/payment',
  reference: `PAY-${Date.now()}`,
  description: 'Payment for Premium Cotton T-Shirt x2',
})

export default function PaymentsDemoPage() {
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway>()
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>()
  const [paymentRequest] = useState<PaymentRequest>(createSamplePaymentRequest())
  const [demoStatus, setDemoStatus] = useState<PaymentStatusEnum>(PaymentStatusEnum.PENDING)

  const handlePaymentSuccess = (response: PaymentResponse) => {
    alert('Payment initiated successfully!')
    console.log('Payment response:', response)
  }

  const handlePaymentError = (error: string) => {
    alert(`Payment failed: ${error}`)
    console.error('Payment error:', error)
  }

  const handlePaymentCancel = () => {
    alert('Payment cancelled')
  }

  const statusDemos = [
    { status: PaymentStatusEnum.PENDING, label: 'Pending' },
    { status: PaymentStatusEnum.PROCESSING, label: 'Processing' },
    { status: PaymentStatusEnum.COMPLETED, label: 'Completed' },
    { status: PaymentStatusEnum.FAILED, label: 'Failed' },
    { status: PaymentStatusEnum.CANCELLED, label: 'Cancelled' },
    { status: PaymentStatusEnum.REFUNDED, label: 'Refunded' },
  ]

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">Payment Components Demo</h1>
          <p className="text-gray-600 mb-4">
            Interactive demonstration of PayFast and Ozow payment components
          </p>
          <div className="flex justify-center gap-2">
            <Badge variant="secondary">PayFast</Badge>
            <Badge variant="secondary">Ozow</Badge>
            <Badge variant="outline">Demo Mode</Badge>
          </div>
        </div>

        <Tabs defaultValue="full-flow" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="full-flow">Full Payment Flow</TabsTrigger>
            <TabsTrigger value="gateway-selector">Gateway Selector</TabsTrigger>
            <TabsTrigger value="payfast">PayFast</TabsTrigger>
            <TabsTrigger value="ozow">Ozow</TabsTrigger>
            <TabsTrigger value="status">Status Components</TabsTrigger>
          </TabsList>

          {/* Full Payment Flow */}
          <TabsContent value="full-flow" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Complete Payment Flow</CardTitle>
                <CardDescription>
                  Full payment experience with gateway selection and payment processing
                </CardDescription>
              </CardHeader>
              <CardContent>
                <PaymentForm
                  paymentRequest={paymentRequest}
                  onSuccess={handlePaymentSuccess}
                  onError={handlePaymentError}
                  onCancel={handlePaymentCancel}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Gateway Selector */}
          <TabsContent value="gateway-selector" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Payment Gateway Selector</CardTitle>
                <CardDescription>
                  Choose between PayFast and Ozow payment gateways
                </CardDescription>
              </CardHeader>
              <CardContent>
                <PaymentGatewaySelector
                  amount={paymentRequest.amount.amount}
                  currency={paymentRequest.amount.currency}
                  selectedGateway={selectedGateway}
                  selectedMethod={selectedMethod}
                  onGatewayChange={setSelectedGateway}
                  onMethodChange={setSelectedMethod}
                  onProceed={() => alert('Proceeding to payment...')}
                />
              </CardContent>
            </Card>
          </TabsContent>

          {/* PayFast Component */}
          <TabsContent value="payfast" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>PayFast - Credit Card</CardTitle>
                  <CardDescription>
                    Credit/Debit card payment with PayFast
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PayFastPayment
                    paymentRequest={paymentRequest}
                    method={PaymentMethod.CARD}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                    onCancel={handlePaymentCancel}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>PayFast - EFT</CardTitle>
                  <CardDescription>
                    Electronic Funds Transfer with PayFast
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <PayFastPayment
                    paymentRequest={paymentRequest}
                    method={PaymentMethod.EFT}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                    onCancel={handlePaymentCancel}
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Ozow Component */}
          <TabsContent value="ozow" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Ozow - Instant EFT</CardTitle>
                  <CardDescription>
                    Instant EFT payment with Ozow
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <OzowPayment
                    paymentRequest={paymentRequest}
                    method={PaymentMethod.INSTANT_EFT}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                    onCancel={handlePaymentCancel}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Ozow - Regular EFT</CardTitle>
                  <CardDescription>
                    Regular EFT payment with Ozow
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <OzowPayment
                    paymentRequest={paymentRequest}
                    method={PaymentMethod.EFT}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                    onCancel={handlePaymentCancel}
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Status Components */}
          <TabsContent value="status" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Payment Status Components</CardTitle>
                <CardDescription>
                  Different payment status displays and interactions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Status Selector */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {statusDemos.map(({ status, label }) => (
                    <Button
                      key={status}
                      variant={demoStatus === status ? "default" : "outline"}
                      size="sm"
                      onClick={() => setDemoStatus(status)}
                    >
                      {label}
                    </Button>
                  ))}
                </div>

                <Separator />

                {/* Status Display */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium mb-4">Generic Status Component</h3>
                    <PaymentStatus
                      status={demoStatus}
                      details={{
                        reference: paymentRequest.reference,
                        transactionId: 'TXN-123456789',
                        amount: paymentRequest.amount.formatted,
                        gateway: 'PayFast',
                        method: 'Credit Card',
                        timestamp: new Date().toLocaleString(),
                      }}
                      actions={
                        demoStatus === PaymentStatusEnum.FAILED ? (
                          <div className="space-y-2 w-full">
                            <Button className="w-full">Try Again</Button>
                            <Button variant="outline" className="w-full">
                              Choose Different Method
                            </Button>
                          </div>
                        ) : demoStatus === PaymentStatusEnum.COMPLETED ? (
                          <Button className="w-full">View Order</Button>
                        ) : undefined
                      }
                    />
                  </div>

                  <div>
                    <h3 className="font-medium mb-4">Specialized Components</h3>
                    <div className="space-y-4">
                      {demoStatus === PaymentStatusEnum.PROCESSING && (
                        <PaymentProcessing
                          title="Processing Your Payment"
                          description="Please wait while we securely process your payment..."
                          details={{
                            reference: paymentRequest.reference,
                            amount: paymentRequest.amount.formatted,
                          }}
                        />
                      )}

                      {demoStatus === PaymentStatusEnum.COMPLETED && (
                        <PaymentSuccess
                          title="Payment Successful!"
                          description="Your payment has been processed successfully."
                          details={{
                            reference: paymentRequest.reference,
                            transactionId: 'TXN-123456789',
                            amount: paymentRequest.amount.formatted,
                          }}
                          actions={
                            <Button className="w-full">Continue Shopping</Button>
                          }
                        />
                      )}

                      {demoStatus === PaymentStatusEnum.FAILED && (
                        <PaymentError
                          title="Payment Failed"
                          description="Your payment could not be processed. Please try again."
                          details={{
                            reference: paymentRequest.reference,
                            amount: paymentRequest.amount.formatted,
                          }}
                          actions={
                            <Button className="w-full">Retry Payment</Button>
                          }
                        />
                      )}

                      {demoStatus === PaymentStatusEnum.PENDING && (
                        <PaymentPending
                          title="Payment Pending"
                          description="Your payment is being processed. We'll notify you once it's complete."
                          details={{
                            reference: paymentRequest.reference,
                            amount: paymentRequest.amount.formatted,
                          }}
                        />
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Footer */}
        <div className="mt-12 text-center text-sm text-gray-500">
          <p>
            This is a demonstration of payment components. No actual payments will be processed.
          </p>
          <p className="mt-1">
            Components support both PayFast and Ozow payment gateways with full South African banking integration.
          </p>
        </div>
      </div>
    </div>
  )
}