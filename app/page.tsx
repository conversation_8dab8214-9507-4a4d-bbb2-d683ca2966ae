"use client"

import { useState, useEffect } from "react"
import { Loader2 } from "lucide-react"
import { HeroSection } from "@/components/storefront/design/hero-section"
import { FeaturedCategories } from "@/components/storefront/design/featured-categories"
import { NewArrivalsSection } from "@/components/storefront/design/new-arrivals-section"
import { EditorialSection } from "@/components/editorial-section"
import { SpecialOffersBanner } from "@/components/special-offers-banner"
import { NewsletterSignup } from "@/components/newsletter-signup"

// Default homepage component
function DefaultHomepage() {
  return (
    <main className="bg-white">
      {/* Hero Section */}
      <HeroSection />

      {/* Featured Categories - Zara-style 2x2 Grid */}
      <FeaturedCategories />

      {/* New Arrivals */}
      <NewArrivalsSection />

      {/* Editorial Section - Zara-style asymmetric grid */}
      <EditorialSection />

      {/* Special Offers Banner - Simplified */}
      <SpecialOffersBanner />

      {/* Newsletter Signup - Minimal */}
      <NewsletterSignup />
    </main>
  )
}

export default function Home() {
        // For client deployment, use the hardcoded homepage
  // Dynamic homepage functionality can be enabled later if needed
  return <DefaultHomepage />
}
