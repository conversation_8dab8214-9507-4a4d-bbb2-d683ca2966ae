/**
 * Payment Core Library Example
 * 
 * This example demonstrates how to use the payment core library.
 * 
 * Usage:
 * ```
 * npx ts-node examples/payment-example.ts
 * ```
 */

import { 
  initializePaymentCore,
  paymentService, 
  PaymentGateway, 
  PaymentMethod, 
  PaymentStatus,
  generateTransactionId,
  generatePaymentReference,
  formatAmount,
  handleError,
  logger
} from '../lib/payment-core';

/**
 * Main example function
 */
async function runPaymentExample() {
  try {
    console.log('Payment Core Library Example');
    console.log('===========================');
    
    // Initialize the payment core library
    console.log('\nInitializing payment core library...');
    const initialized = await initializePaymentCore({
      validateConfig: true,
      throwOnError: false
    });
    
    if (!initialized) {
      console.error('Failed to initialize payment core library. Check the logs for details.');
      return;
    }
    
    console.log('Payment core library initialized successfully!');
    
    // Get available payment methods
    console.log('\nGetting available payment methods...');
    const methods = await paymentService.getAvailablePaymentMethods();
    console.log(`Found ${methods.length} payment methods:`);
    methods.forEach(method => {
      console.log(`- ${method.displayName} (${method.method}): ${method.gateways.join(', ')}`);
    });
    
    // Create a payment
    console.log('\nCreating a test payment...');
    const orderId = `order-${Date.now()}`;
    const reference = generatePaymentReference(orderId);
    const transactionId = generateTransactionId();
    
    const paymentRequest = {
      amount: {
        amount: 100.00,
        currency: 'ZAR',
        formatted: formatAmount(100.00, 'ZAR')
      },
      customer: {
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        phone: '+27123456789'
      },
      items: [
        {
          id: 'product-1',
          name: 'Test Product',
          quantity: 1,
          unitPrice: 100.00,
          totalPrice: 100.00
        }
      ],
      metadata: {
        orderId,
        customerId: 'customer-123',
        source: 'example-script'
      },
      returnUrl: 'http://localhost:3000/checkout/success',
      cancelUrl: 'http://localhost:3000/checkout/cancel',
      notifyUrl: 'http://localhost:3000/api/webhooks/payments',
      reference,
      description: 'Test payment',
      paymentMethod: PaymentMethod.CARD
    };
    
    const paymentResponse = await paymentService.createPayment(paymentRequest, PaymentGateway.PAYFAST);
    
    if (paymentResponse.success) {
      console.log('Payment created successfully!');
      console.log(`Transaction ID: ${paymentResponse.transactionId}`);
      console.log(`Payment URL: ${paymentResponse.paymentUrl}`);
      console.log(`Status: ${paymentResponse.status}`);
    } else {
      console.log('Payment creation failed:');
      console.log(`Error: ${paymentResponse.error?.message}`);
      console.log(`Code: ${paymentResponse.error?.code}`);
    }
    
    // Get payment status
    console.log('\nChecking payment status...');
    const statusTransactionId = paymentResponse.transactionId || transactionId;
    const status = await paymentService.getPaymentStatus(statusTransactionId, PaymentGateway.PAYFAST);
    console.log(`Payment status for ${statusTransactionId}: ${status}`);
    
    // Simulate webhook
    console.log('\nSimulating webhook...');
    const webhookResponse = await paymentService.handleWebhook({
      gateway: PaymentGateway.PAYFAST,
      payload: {
        m_payment_id: statusTransactionId,
        pf_payment_id: 'PF12345',
        payment_status: 'COMPLETE',
        amount_gross: '100.00',
        amount_fee: '-2.90',
        amount_net: '97.10',
        custom_str1: reference,
        custom_str2: orderId,
        custom_str3: 'customer-123',
        item_name: 'Test payment',
        item_description: 'Test payment description',
        email_address: '<EMAIL>'
      }
    });
    
    if (webhookResponse.success) {
      console.log('Webhook processed successfully!');
      console.log(`Transaction ID: ${webhookResponse.transactionId}`);
      console.log(`Event: ${webhookResponse.event}`);
    } else {
      console.log('Webhook processing failed:');
      console.log(`Error: ${webhookResponse.error}`);
    }
    
    // Get updated payment status
    console.log('\nChecking updated payment status...');
    const updatedStatus = await paymentService.getPaymentStatus(statusTransactionId, PaymentGateway.PAYFAST);
    console.log(`Updated payment status for ${statusTransactionId}: ${updatedStatus}`);
    
    // Get transaction
    console.log('\nGetting transaction details...');
    const transaction = await paymentService.getTransaction(statusTransactionId);
    
    if (transaction) {
      console.log('Transaction found:');
      console.log(`ID: ${transaction.id}`);
      console.log(`Order ID: ${transaction.orderId}`);
      console.log(`Status: ${transaction.status}`);
      console.log(`Amount: ${transaction.amount.amount} ${transaction.amount.currency}`);
      console.log(`Created: ${transaction.createdAt}`);
      console.log(`Updated: ${transaction.updatedAt}`);
    } else {
      console.log('Transaction not found');
    }
    
    console.log('\nExample completed successfully!');
  } catch (error) {
    const paymentError = handleError(error, 'payment-example', 'Example failed');
    console.error('Example failed with error:');
    console.error(`Code: ${paymentError.code}`);
    console.error(`Message: ${paymentError.message}`);
    console.error(`Retryable: ${paymentError.retryable}`);
    
    if (paymentError.details) {
      console.error('Details:', paymentError.details);
    }
  }
}

// Run the example
runPaymentExample().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});