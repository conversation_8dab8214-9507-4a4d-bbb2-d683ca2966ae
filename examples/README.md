# Payment Core Library Examples

This directory contains examples demonstrating how to use the Payment Core Library.

## Available Examples

### Basic Payment Processing

The `payment-example.ts` file demonstrates the basic usage of the payment core library:

- Initializing the payment core library
- Getting available payment methods
- Creating a payment
- Checking payment status
- Handling webhooks
- Getting transaction details

To run this example:

```bash
npx ts-node examples/payment-example.ts
```

### Environment Variables

For the examples to work properly, you should set the following environment variables:

```bash
# PayFast (default gateway)
export PAYFAST_MERCHANT_ID=your-merchant-id
export PAYFAST_MERCHANT_KEY=your-merchant-key
export PAYFAST_PASSPHRASE=your-passphrase
export PAYFAST_SANDBOX=true

# Security
export PAYMENT_ENCRYPTION_KEY=your-encryption-key
export PAYMENT_WEBHOOK_SECRET=your-webhook-secret
```

Alternatively, you can create a `.env` file in the project root with these variables.

## Testing in Development Mode

In development mode, the payment core library will use mock implementations for the payment gateways, so you don't need to set up real gateway credentials. However, you should still set the environment variables to avoid validation warnings.

## Production Usage

In production, you must set up at least one payment gateway with valid credentials. The payment core library will validate the configuration and throw an error if required credentials are missing.

See the main [README.md](../lib/payment-core/README.md) for more details on production configuration.