import { PrismaClient } from '@prisma/client';
import { faker } from '@faker-js/faker';

const prisma = new PrismaClient();

async function clearDatabase() {
  console.log('Clearing database...');
  
  // Define the order of tables to clear based on dependencies with their mapped names
  const tables = [
    'order_fulfillment_items',
    'order_fulfillments',
    'order_return_items',
    'order_returns',
    'order_refund_items',
    'order_refunds',
    'payment_refunds',
    'payments',
    'order_items',
    'orders',
    'cart_items',
    'carts',
    'product_variant_options',
    'product_variants',
    'product_images',
    'product_options',
    'product_category_relations',
    'product_collection_relations',
    'product_tag_relations',
    'product_relations',
    'product_reviews',
    'product_attribute_values',
    'product_bundle_items',
    'wishlist_items',
    'wishlists',
    'products',
    'user_addresses',
    'user_activities',
    'user_sessions',
    'user_roles',
    'email_verification_tokens',
    'password_reset_tokens',
    'payment_methods',
    'users'
  ];

  // Clear each table
  for (const table of tables) {
    try {
      // Using executeRaw for a more direct approach
      const deleteCount = await prisma.$executeRawUnsafe(`DELETE FROM "${table}"`);
      console.log(`Cleared table ${table}`);
    } catch (error) {
      console.log(`Error clearing table ${table}: ${error.message}`);
      // Continue with other tables even if one fails
    }
  }
  
  console.log('Database cleared successfully');
}

async function main() {
  console.log('Starting seed...');
  
  // Clear the database first
  await clearDatabase();

  // Create 5 users
  const users = [];
  for (let i = 0; i < 5; i++) {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const email = faker.internet.email({ firstName, lastName });

    const user = await prisma.user.create({
      data: {
        email,
        firstName,
        lastName,
        displayName: `${firstName} ${lastName}`,
        phone: faker.phone.number(),
        password: faker.internet.password(),
        emailVerified: true,
        customerSince: faker.date.past(),
        addresses: {
          create: [
            {
              firstName,
              lastName,
              address1: faker.location.streetAddress(),
              address2: faker.location.secondaryAddress(),
              city: faker.location.city(),
              province: faker.location.state(),
              country: 'South Africa',
              postalCode: faker.location.zipCode(),
              phone: faker.phone.number(),
              isDefault: true,
              type: 'both',
            },
          ],
        },
      },
    });

    users.push(user);
    console.log(`Created user: ${user.email}`);
  }

  // Create 10 products
  const products = [];
  const categories = ['Tops', 'Bottoms', 'Dresses', 'Accessories', 'Shoes'];
  const sizes = ['XS', 'S', 'M', 'L', 'XL'];
  const colors = ['Red', 'Blue', 'Green', 'Yellow', 'Black', 'White', 'Pink', 'Purple'];

  // Create products one by one
  for (let i = 0; i < 10; i++) {
    try {
      const productName = `${faker.commerce.productAdjective()} ${faker.commerce.product()}`;
      const timestamp = Date.now() + i; // Ensure uniqueness
      const slug = `${productName.toLowerCase().replace(/[^a-z0-9]+/g, '-')}-${i}-${timestamp}`;
      const handle = `${slug}-${faker.string.alphanumeric(4)}`;
      const price = parseFloat(faker.commerce.price({ min: 100, max: 500 }));
      const category = categories[Math.floor(Math.random() * categories.length)];

      // Create the product first
      const product = await prisma.product.create({
        data: {
          title: productName,
          slug,
          handle,
          description: faker.commerce.productDescription(),
          price,
          compareAtPrice: price * 1.2,
          currency: 'ZAR',
          vendor: 'Coco Milk Kids',
          productType: category,
          status: 'active',
          publishedAt: faker.date.past(),
          inventoryQuantity: faker.number.int({ min: 10, max: 100 }),
          seoTitle: productName,
          seoDescription: faker.commerce.productDescription(),
          isVisible: true,
          isAvailable: true,
          availableForSale: true,
          images: {
            create: [
              {
                url: faker.image.url(),
                altText: productName,
                position: 1,
              },
            ],
          },
          options: {
            create: [
              {
                name: 'Size',
                position: 1,
                values: sizes,
              },
              {
                name: 'Color',
                position: 2,
                values: colors,
              },
            ],
          },
        },
      });

      // Create variants separately
      for (let j = 0; j < sizes.length; j++) {
        const size = sizes[j];
        const color = colors[Math.floor(Math.random() * colors.length)];
        const uniqueSku = `${slug}-${size}-${color}-${timestamp}-${j}`.substring(0, 20);
        
        const variant = await prisma.productVariant.create({
          data: {
            productId: product.id,
            sku: uniqueSku,
            title: `${size} / ${color}`,
            price,
            compareAtPrice: price * 1.2,
            inventoryQuantity: faker.number.int({ min: 5, max: 20 }),
            options: {
              create: [
                {
                  name: 'Size',
                  value: size,
                },
                {
                  name: 'Color',
                  value: color,
                },
              ],
            },
          },
        });
      }
      
      products.push(product);
      console.log(`Created product: ${product.title}`);
    } catch (error) {
      console.error(`Error creating product ${i}:`, error.message);
    }
  }

  // Create 10 orders
  const orderStatuses = ['pending', 'processing', 'fulfilled', 'delivered', 'cancelled'];
  const paymentStatuses = ['pending', 'paid', 'failed', 'refunded'];
  const fulfillmentStatuses = ['unfulfilled', 'fulfilled', 'partially_fulfilled'];

  for (let i = 0; i < 10; i++) {
    try {
      const user = users[Math.floor(Math.random() * users.length)];
      const orderNumber = `ORD-${faker.string.alphanumeric(8).toUpperCase()}`;
      const orderDate = faker.date.recent({ days: 30 });
      const status = orderStatuses[Math.floor(Math.random() * orderStatuses.length)];
      const paymentStatus = paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];
      const fulfillmentStatus = fulfillmentStatuses[Math.floor(Math.random() * fulfillmentStatuses.length)];

      // Generate between 1 and 4 items for this order
      const orderItemsData = [];
      const itemCount = faker.number.int({ min: 1, max: 4 });
      let subtotal = 0;

      // Get available products and variants
      const availableProducts = await prisma.product.findMany({
        include: {
          variants: true,
        },
        take: 10,
      });

      if (availableProducts.length === 0) {
        console.log('No products available for orders');
        continue;
      }

      for (let j = 0; j < itemCount; j++) {
        const product = availableProducts[Math.floor(Math.random() * availableProducts.length)];
        
        if (!product.variants || product.variants.length === 0) {
          console.log(`No variants for product ${product.id}`);
          continue;
        }
        
        const variant = product.variants[Math.floor(Math.random() * product.variants.length)];
        const quantity = faker.number.int({ min: 1, max: 3 });
        const unitPrice = parseFloat(variant.price.toString());
        const totalPrice = unitPrice * quantity;
        subtotal += totalPrice;

        orderItemsData.push({
          productId: product.id,
          variantId: variant.id,
          quantity,
          unitPrice,
          totalPrice,
          productTitle: product.title,
          productSlug: product.slug,
          variantTitle: variant.title,
          sku: variant.sku,
          fulfillmentStatus,
          fulfillableQuantity: quantity,
          returnableQuantity: quantity,
          refundableQuantity: quantity,
        });
      }

      // Calculate order totals
      const totalTax = subtotal * 0.15; // 15% VAT
      const totalShipping = faker.number.int({ min: 50, max: 150 });
      const total = subtotal + totalTax + totalShipping;

      // Create the address data
      const address = {
        firstName: user.firstName,
        lastName: user.lastName,
        address1: faker.location.streetAddress(),
        address2: faker.location.secondaryAddress(),
        city: faker.location.city(),
        province: faker.location.state(),
        country: 'South Africa',
        postalCode: faker.location.zipCode(),
        phone: faker.phone.number(),
      };

      // Create the order
      const order = await prisma.order.create({
        data: {
          orderNumber,
          userId: user.id,
          customerEmail: user.email,
          customerFirstName: user.firstName,
          customerLastName: user.lastName,
          customerPhone: user.phone,
          billingAddress: address,
          shippingAddress: address,
          itemCount,
          subtotal,
          totalTax,
          totalShipping,
          total,
          currency: 'ZAR',
          paymentStatus,
          fulfillmentStatus,
          status,
          processedAt: orderDate,
          items: {
            create: orderItemsData,
          },
        },
      });

      // Create payment for the order
      if (paymentStatus === 'paid') {
        await prisma.payment.create({
          data: {
            paymentNumber: `PAY-${faker.string.alphanumeric(8).toUpperCase()}`,
            orderId: order.id,
            amount: total,
            amountCaptured: total,
            currency: 'ZAR',
            status: 'succeeded',
            gatewayId: 'stripe',
            gatewayPaymentId: `pi_${faker.string.alphanumeric(24)}`,
            gatewayChargeId: `ch_${faker.string.alphanumeric(24)}`,
            authorizedAt: orderDate,
            capturedAt: orderDate,
            receiptEmail: user.email,
            receiptUrl: `https://pay.stripe.com/receipts/${faker.string.alphanumeric(16)}`,
          },
        });
      }

      // Create fulfillment for fulfilled orders
      if (fulfillmentStatus === 'fulfilled') {
        const fulfillment = await prisma.orderFulfillment.create({
          data: {
            orderId: order.id,
            status: 'success',
            trackingNumber: `TRK${faker.string.numeric(10)}`,
            trackingUrl: `https://tracking.courier.com/${faker.string.alphanumeric(12)}`,
            trackingCompany: 'Courier Express',
            shippedAt: new Date(orderDate.getTime() + 24 * 60 * 60 * 1000), // 1 day after order
            estimatedDeliveryAt: new Date(orderDate.getTime() + 3 * 24 * 60 * 60 * 1000), // 3 days after order
            notifyCustomer: true,
            emailSent: true,
          },
        });

        // Get the created order items
        const createdOrderItems = await prisma.orderItem.findMany({
          where: { orderId: order.id },
        });

        // Create fulfillment items
        for (const item of createdOrderItems) {
          await prisma.orderFulfillmentItem.create({
            data: {
              fulfillmentId: fulfillment.id,
              orderItemId: item.id,
              quantity: item.quantity,
            },
          });
        }
      }

      console.log(`Created order: ${order.orderNumber} for ${user.email}`);
    } catch (error) {
      console.error(`Error creating order ${i}:`, error.message);
    }
  }

  console.log('Seed completed successfully!');
}

main()
  .catch((e) => {
    console.error('Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });