import NextAuth from "next-auth"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { db } from "@/lib/db"
import authConfig from "@/auth.config"
import { getUserById } from "@/lib/auth/user"

export const {
  handlers,
  auth,
  signIn,
  signOut,
} = NextAuth({
  adapter: PrismaAdapter(db),
  session: { strategy: "jwt" },
  events: {
    async linkAccount({ user }) {
      // Update user emailVerified when they link an OAuth account
      await db.user.update({
        where: { id: user.id },
        data: { emailVerified: true }
      })
    },
    async createUser({ user }) {
      // Create a default role for new users
      try {
        // Find the customer role
        const customerRole = await db.role.findFirst({
          where: { slug: "customer" }
        })

        if (customerRole) {
          // Assign the role to the user
          await db.userRole.create({
            data: {
              userId: user.id,
              roleId: customerRole.id,
              assignedBy: user.id, // Self-assigned
              isActive: true,
            }
          })
        } else {
          console.error("Customer role not found in database")
        }
      } catch (error) {
        console.error("Error assigning default role to user:", error)
      }
    }
  },
  ...authConfig,
  // Override the callbacks from authConfig with extended functionality
  callbacks: {
    async jwt({ token, user }) {
      // First apply the base jwt callback from authConfig
      if (authConfig.callbacks?.jwt) {
        token = await authConfig.callbacks.jwt({ token, user });
      }
      // Add custom logic if needed
      return token;
    },
    async session({ session, token }) {
      if (token.sub && session.user) {
        session.user.id = token.sub
        
        // Add role from token
        if (token.role) {
          session.user.role = token.role as string;
        }
        
        // Add additional user data from token
        session.user.firstName = token.firstName as string || null;
        session.user.lastName = token.lastName as string || null;
        session.user.displayName = token.displayName as string || null;
        session.user.avatar = token.avatar as string || null;
        // Convert boolean to Date if necessary
        session.user.emailVerified = token.emailVerified ? new Date() : null;
      }
      // Call the original session callback from authConfig if it exists
      if (authConfig.callbacks?.session) {
        // Create a user object from token to satisfy the type requirements
        const user = {
          id: token.sub as string,
          role: token.role as string,
          firstName: token.firstName as string || null,
          lastName: token.lastName as string || null,
          displayName: token.displayName as string || null,
          avatar: token.avatar as string || null,
          emailVerified: token.emailVerified ? new Date() : null,
          email: session.user?.email || "",
          name: session.user?.name || ""
        };
        
        // Call the original callback with a structure that includes newSession
        const result = await authConfig.callbacks.session({ 
          session, 
          token, 
          user,
          newSession: session
        });
        return result;
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Call the original redirect callback from authConfig if it exists
      if (authConfig.callbacks?.redirect) {
        return await authConfig.callbacks.redirect({ url, baseUrl });
      }
      return url.startsWith(baseUrl) ? url : baseUrl;
    }
  }
})